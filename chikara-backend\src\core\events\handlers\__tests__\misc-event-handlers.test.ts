import { describe, expect, it, vi, beforeEach } from "vitest";
import { handleStatsTrainedEvent, handleGamblingPerformedEvent } from "../misc-event-handlers.js";
import * as SharedQuestUtils from "../../../quest.service.js";
import type { StatsTrainedPayload, GamblingPerformedPayload } from "../../event-types.js";

// Mock the shared quest utils
vi.mock("../../../quest.service.js", () => ({
    handleStatsTraining: vi.fn(),
    handleGambling: vi.fn(),
}));

describe("Misc Event Handlers", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("handleStatsTrainedEvent", () => {
        it("should handle stats trained event correctly", async () => {
            const payload: StatsTrainedPayload = {
                userId: 123,
                amount: 25,
            };

            await handleStatsTrainedEvent(payload);

            expect(SharedQuestUtils.handleStatsTraining).toHaveBeenCalledWith(123, 25);
        });

        it("should handle errors gracefully", async () => {
            const payload: StatsTrainedPayload = {
                userId: 123,
                amount: 25,
            };

            // Mock handleStatsTraining to throw an error
            vi.mocked(SharedQuestUtils.handleStatsTraining).mockRejectedValue(new Error("Database error"));

            // Should not throw
            await expect(handleStatsTrainedEvent(payload)).resolves.toBeUndefined();
        });
    });

    describe("handleGamblingPerformedEvent", () => {
        it("should handle slots gambling event correctly", async () => {
            const payload: GamblingPerformedPayload = {
                userId: 456,
                gameType: "slots",
                amount: 1000,
            };

            await handleGamblingPerformedEvent(payload);

            expect(SharedQuestUtils.handleGambling).toHaveBeenCalledWith(456, "slots", 1000);
        });

        it("should handle non-slots gambling events correctly", async () => {
            const payload: GamblingPerformedPayload = {
                userId: 456,
                gameType: "roulette",
                amount: 1000,
            };

            await handleGamblingPerformedEvent(payload);

            expect(SharedQuestUtils.handleGambling).toHaveBeenCalledWith(456, "roulette", 1000);
        });

        it("should handle slots gambling events without amount", async () => {
            const payload: GamblingPerformedPayload = {
                userId: 456,
                gameType: "slots",
            };

            await handleGamblingPerformedEvent(payload);

            expect(SharedQuestUtils.handleGambling).toHaveBeenCalledWith(456, "slots", undefined);
        });

        it("should handle errors gracefully", async () => {
            const payload: GamblingPerformedPayload = {
                userId: 456,
                gameType: "slots",
                amount: 1000,
            };

            // Mock handleGambling to throw an error
            vi.mocked(SharedQuestUtils.handleGambling).mockRejectedValue(new Error("Database error"));

            // Should not throw
            await expect(handleGamblingPerformedEvent(payload)).resolves.toBeUndefined();
        });
    });
});
