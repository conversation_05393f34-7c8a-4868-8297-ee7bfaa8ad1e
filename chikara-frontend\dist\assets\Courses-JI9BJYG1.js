import{b as y,c as m,e as b,g as j,y as f,t as N,j as e,bH as k,N as g,C as v,an as u}from"./index--cEnoMkg.js";import{g as C}from"./greenTick-CRl6ZXFn.js";const w=(r={})=>y(m.courses.courseList.queryOptions({staleTime:5*60*1e3,...r})),S=()=>{const r=b();return j(m.courses.startCourse.mutationOptions({onSuccess:()=>{r.invalidateQueries({queryKey:f.USER.CURRENTUSERINFO}),r.invalidateQueries({queryKey:m.courses.courseList.key()})}}))};function d(...r){return r.filter(Boolean).join(" ")}const h=r=>Math.floor(r/864e5);function L(){const{isLoading:r,data:n}=w(),{data:a}=N(),t=S(),l=s=>{if(a.activeCourseId&&a.activeCourseId>0){u.error("You're already on a course!");return}const i=n?.find(o=>o.id===s);if(i&&i.cost>a.cash){u.error("You don't have enough cash to start this course!");return}t.mutate({courseId:s})},c=n?.reduce((s,i)=>{const o=i.stat;return s[o]||(s[o]=[]),s[o].push(i),s},{}),x=[];for(const s in c)x.push({stat:s,coursesData:c[s]});if(r)return e.jsx("p",{children:"Loading..."});if(a?.level<8)return null;const p=n.find(s=>s.id===a?.activeCourseId);return e.jsxs("div",{className:"px-4 text-shadow sm:px-6 md:mx-auto md:max-w-6xl lg:px-8",children:[p?e.jsx("div",{className:"sm:flex sm:items-center",children:e.jsxs("div",{className:"mt-3 rounded-lg bg-gray-800 px-5 py-3 md:mx-auto md:px-8",children:[e.jsxs("h1",{className:"font-normal text-2xl text-gray-900 leading-6 dark:text-slate-400",children:["Current Course: ",e.jsx("span",{className:"text-custom-yellow",children:p?.name})]}),e.jsxs("p",{className:"mt-2 text-base text-gray-700 dark:text-slate-400",children:["Your course will end in"," ",e.jsx("span",{className:"text-indigo-400",children:k(Number.parseInt(a?.courseEnds))})]})]})}):null,x?.map(s=>e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"mt-7 mb-2 text-white text-xl uppercase",children:g(s.stat)}),e.jsx(T,{courses:s.coursesData,handleStartCourse:l,currentUser:a})]}))]})}const E=r=>{switch(r){case"endurance":return"END";case"intelligence":return"INT";case"dexterity":return"DEX";case"strength":return"STR";case"vitality":return"VIT";case"defence":return"DEF";default:return""}},T=({courses:r,handleStartCourse:n,currentUser:a})=>e.jsx("div",{className:"-mx-4 overflow-hidden bg-white ring-1 ring-gray-300 sm:mx-0 sm:rounded-lg dark:bg-slate-800 dark:ring-gray-600",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-300 dark:divide-gray-600",children:[e.jsx("thead",{className:"text-gray-900 dark:bg-gray-800 dark:text-gray-200 dark:text-stroke-sm ",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"py-3.5 pr-3 pl-4 text-left font-semibold text-sm sm:pl-6 dark:font-normal",children:"Course"}),e.jsx("th",{scope:"col",className:"hidden px-3 py-3.5 text-left font-semibold text-sm lg:table-cell dark:font-normal",children:"Stat"}),e.jsx("th",{scope:"col",className:"hidden px-3 py-3.5 text-left font-semibold text-sm lg:table-cell dark:font-normal",children:"Amount"}),e.jsx("th",{scope:"col",className:"px-2 py-3.5 text-left font-semibold text-sm sm:px-3 lg:table-cell dark:font-normal",children:"Days"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left font-semibold text-sm dark:font-normal",children:"Cost"}),e.jsx("th",{scope:"col",className:"relative py-3.5 pr-4 pl-3 sm:pr-6",children:e.jsx("span",{className:"sr-only",children:"Select"})})]})}),e.jsx("tbody",{children:r?.map((t,l)=>e.jsxs("tr",{children:[e.jsxs("td",{className:d(l===0?"":"border-transparent border-t","relative py-4 pl-4 text-sm text-stroke-sm sm:pr-3 sm:pl-6 md:w-80"),children:[e.jsx("div",{className:"font-medium text-custom-yellow text-stroke-md",children:t.name}),e.jsxs("div",{className:"mt-1 flex flex-col text-gray-500 sm:block lg:hidden dark:text-gray-200",children:[e.jsx("span",{className:"hidden sm:inline",children:"·"}),e.jsxs("span",{className:"text-green-500",children:["+",t.amount,"",e.jsx("span",{className:"ml-1 text-green-500",children:E(t.stat)})]})]}),l!==0?e.jsx("div",{className:"-top-px absolute right-0 left-6 h-px bg-gray-200 dark:bg-gray-600"}):null]}),e.jsx("td",{className:d(l===0?"":"border-gray-200 border-t dark:border-gray-600","hidden px-3 py-3.5 text-custom-yellow text-sm text-stroke-sm lg:table-cell"),children:g(t.stat)}),e.jsxs("td",{className:d(l===0?"":"border-gray-200 border-t dark:border-gray-600","hidden px-3 py-3.5 text-green-500 text-sm text-stroke-sm lg:table-cell"),children:["+",t.amount]}),e.jsx("td",{className:d(l===0?"":"border-gray-200 border-t dark:border-gray-600","hidden px-3 py-3.5 text-gray-500 text-sm text-stroke-md lg:table-cell dark:text-gray-200"),children:h(t.time)}),e.jsx("td",{className:d(l===0?"":"border-gray-200 border-t dark:border-gray-600","px-3 py-3.5 text-gray-500 text-sm text-stroke-md sm:hidden dark:text-gray-200"),children:e.jsx("div",{children:h(t.time)})}),e.jsx("td",{className:d(l===0?"":"border-gray-200 border-t dark:border-gray-600","px-3 py-3.5 text-gray-500 text-sm text-stroke-md dark:text-gray-200"),children:e.jsxs("div",{children:["¥",t.cost]})}),e.jsxs("td",{className:d(l===0?"":"border-transparent border-t","relative py-3.5 pr-4 pl-3 text-right font-medium text-sm sm:pr-6"),children:[t.completed||t.id===a?.activeCourseId?e.jsx("div",{className:"md:h-10! text-sm! w-20! md:w-32! flex",children:t.id===a?.activeCourseId?e.jsx("img",{className:"m-auto h-7 w-auto rotate-90 animate-pulse",src:"https://cloudflare-image.jamessut.workers.dev/ui-images/pyBEK4l.png",alt:""}):e.jsx("img",{className:"m-auto h-7 w-auto",src:C,alt:""})}):e.jsxs(v,{type:"primary",className:"md:h-10! text-sm! w-20! md:w-32!",disabled:a?.activeCourseId&&a?.activeCourseId>0,onClick:()=>n(t.id),children:["Sign Up",e.jsxs("span",{className:"sr-only",children:[", ",t.name]})]}),l!==0?e.jsx("div",{className:"-top-px absolute right-6 left-0 h-px bg-gray-200 dark:bg-gray-600"}):null]})]},t.id))})]})});export{L as default};
