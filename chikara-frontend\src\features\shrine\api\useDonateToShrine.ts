import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import toast from "react-hot-toast";

export const useDonateToShrine = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.shrine.donate.mutationOptions({
            onSuccess: () => {
                // Invalidate shrine-related queries
                queryClient.invalidateQueries({ queryKey: api.shrine.dailyGoal.key() });
                queryClient.invalidateQueries({ queryKey: api.shrine.getDonations.key() });
                queryClient.invalidateQueries({ queryKey: api.shrine.activeShrineBuffer.key() });

                // Invalidate user data to update cash
                queryClient.invalidateQueries({ queryKey: api.user.currentUserInfo.key() });

                toast.success("Donation Successful!");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error) => {
                console.error("Shrine donation error:", error);
                toast.error(error.message || "Donation failed. Please try again.");
            },
        })
    );
};
