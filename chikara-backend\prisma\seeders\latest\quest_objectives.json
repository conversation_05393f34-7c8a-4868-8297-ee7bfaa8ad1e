[{"questId": 1, "objectiveType": "tutorial_train_stats", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 2, "objectiveType": "tutorial_buy_and_equip", "target": 1, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": 1, "isRequired": true}, {"questId": 3, "objectiveType": "tutorial_roguelike", "target": 2, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 4, "objectiveType": "npc_kill", "target": 2, "targetAction": null, "quantity": 3, "location": "school", "description": "", "creatureId": 2, "itemId": null, "isRequired": true}, {"questId": 5, "objectiveType": "tutorial_get_job", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 6, "objectiveType": "fetch", "target": 172, "targetAction": null, "quantity": 3, "location": "church", "description": "", "creatureId": null, "itemId": 172, "isRequired": true}, {"questId": 6, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 172, "isRequired": true}, {"questId": 7, "objectiveType": "tutorial_ability_equip", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 8, "objectiveType": "roguelike_level", "target": 10, "targetAction": null, "quantity": null, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 9, "objectiveType": "tutorial_crafting", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 10, "objectiveType": "fetch", "target": 157, "targetAction": null, "quantity": 3, "location": "school", "description": "", "creatureId": null, "itemId": 157, "isRequired": true}, {"questId": 10, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 157, "isRequired": true}, {"questId": 11, "objectiveType": "npc_kill", "target": 68, "targetAction": null, "quantity": 1, "location": "church", "description": "", "creatureId": 68, "itemId": null, "isRequired": true}, {"questId": 12, "objectiveType": "fetch", "target": 158, "targetAction": null, "quantity": 5, "location": "sewers", "description": "", "creatureId": null, "itemId": 158, "isRequired": true}, {"questId": 12, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 5, "location": null, "description": "", "creatureId": null, "itemId": 158, "isRequired": true}, {"questId": 13, "objectiveType": "roguelike_level", "target": 20, "targetAction": null, "quantity": null, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 14, "objectiveType": "npc_kill_any", "target": null, "targetAction": null, "quantity": 8, "location": "school", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 15, "objectiveType": "npc_kill_any", "target": null, "targetAction": null, "quantity": 12, "location": "church", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 16, "objectiveType": "npc_kill_low_damage", "target": 25, "targetAction": null, "quantity": 6, "location": "school", "description": "", "creatureId": 25, "itemId": null, "isRequired": true}, {"questId": 17, "objectiveType": "roguelike_level", "target": 30, "targetAction": null, "quantity": null, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 18, "objectiveType": "fetch", "target": 159, "targetAction": null, "quantity": 1, "location": "school", "description": "", "creatureId": null, "itemId": 159, "isRequired": true}, {"questId": 18, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": 159, "isRequired": true}, {"questId": 19, "objectiveType": "pvp_kill", "target": 5, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 20, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "mug", "quantity": 2, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 21, "objectiveType": "pvp_kill", "target": 7, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 22, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "leave", "quantity": 4, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 23, "objectiveType": "pvp_kill", "target": 10, "targetAction": null, "quantity": 5, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 24, "objectiveType": "pvp_kill", "target": 15, "targetAction": null, "quantity": 6, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 25, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "mug", "quantity": 5, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 26, "objectiveType": "pvp_kill", "target": 20, "targetAction": null, "quantity": 5, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 27, "objectiveType": "pvp_kill", "target": 22, "targetAction": null, "quantity": 7, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 28, "objectiveType": "pvp_post_battle_choice", "target": 24, "targetAction": "cripple", "quantity": 5, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 29, "objectiveType": "pvp_kill", "target": 25, "targetAction": null, "quantity": 7, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 30, "objectiveType": "pvp_kill", "target": 30, "targetAction": null, "quantity": 10, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 31, "objectiveType": "pvp_post_battle_choice", "target": 28, "targetAction": "leave", "quantity": 8, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 32, "objectiveType": "pvp_post_battle_choice", "target": 28, "targetAction": "cripple", "quantity": 8, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 33, "objectiveType": "pvp_post_battle_choice", "target": 28, "targetAction": "mug", "quantity": 8, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 34, "objectiveType": "roguelike_level", "target": 3, "targetAction": null, "quantity": null, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 35, "objectiveType": "fetch", "target": 156, "targetAction": null, "quantity": 3, "location": "mall", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 35, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 156, "isRequired": true}, {"questId": 36, "objectiveType": "npc_kill", "target": 19, "targetAction": null, "quantity": 4, "location": "mall", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 37, "objectiveType": "place_bounty", "target": 500, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 38, "objectiveType": "fetch", "target": 171, "targetAction": null, "quantity": 3, "location": "shrine", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 38, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 171, "isRequired": true}, {"questId": 39, "objectiveType": "npc_kill_any", "target": null, "targetAction": null, "quantity": 7, "location": "mall", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 40, "objectiveType": "fetch", "target": 160, "targetAction": null, "quantity": 1, "location": "shrine", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 40, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": 160, "isRequired": true}, {"questId": 41, "objectiveType": "npc_kill", "target": 21, "targetAction": null, "quantity": 4, "location": "mall", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 42, "objectiveType": "fetch", "target": 175, "targetAction": null, "quantity": 3, "location": "mall", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 42, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 175, "isRequired": true}, {"questId": 43, "objectiveType": "npc_kill_any", "target": 1, "targetAction": null, "quantity": 5, "location": "sewers", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 44, "objectiveType": "npc_kill", "target": 69, "targetAction": null, "quantity": 1, "location": "sewers", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 45, "objectiveType": "npc_kill_turns", "target": 3, "targetAction": null, "quantity": 6, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 46, "objectiveType": "npc_kill_low_damage", "target": 15, "targetAction": null, "quantity": 5, "location": "shrine", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 47, "objectiveType": "npc_kill_any", "target": null, "targetAction": null, "quantity": 15, "location": "shrine", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 48, "objectiveType": "craft", "target": 125, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 48, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 125, "isRequired": true}, {"questId": 49, "objectiveType": "craft", "target": 198, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 49, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 198, "isRequired": true}, {"questId": 50, "objectiveType": "craft", "target": 161, "targetAction": null, "quantity": 4, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 50, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 4, "location": null, "description": "", "creatureId": null, "itemId": 161, "isRequired": true}, {"questId": 51, "objectiveType": "craft", "target": 138, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 51, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 138, "isRequired": true}, {"questId": 52, "objectiveType": "craft", "target": 196, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 52, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 196, "isRequired": true}, {"questId": 53, "objectiveType": "fetch", "target": 195, "targetAction": null, "quantity": 4, "location": "church", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 53, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 4, "location": null, "description": "", "creatureId": null, "itemId": 195, "isRequired": true}, {"questId": 54, "objectiveType": "craft", "target": 176, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 54, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": 176, "isRequired": true}, {"questId": 55, "objectiveType": "fetch", "target": 164, "targetAction": null, "quantity": 5, "location": "sewers", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 56, "objectiveType": "craft", "target": 177, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 56, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": 177, "isRequired": true}, {"questId": 57, "objectiveType": "craft", "target": 202, "targetAction": null, "quantity": 4, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 57, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 4, "location": null, "description": "", "creatureId": null, "itemId": 202, "isRequired": true}, {"questId": 58, "objectiveType": "craft", "target": 209, "targetAction": null, "quantity": 2, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 58, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 2, "location": null, "description": "", "creatureId": null, "itemId": 209, "isRequired": true}, {"questId": 59, "objectiveType": "craft", "target": 149, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 59, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": 149, "isRequired": true}, {"questId": 60, "objectiveType": "craft", "target": 188, "targetAction": null, "quantity": 2, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 60, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 2, "location": null, "description": "", "creatureId": null, "itemId": 188, "isRequired": true}, {"questId": 61, "objectiveType": "unique", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 62, "objectiveType": "npc_kill_any", "target": null, "targetAction": null, "quantity": 12, "location": "sewers", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 63, "objectiveType": "npc_kill_any", "target": null, "targetAction": null, "quantity": 14, "location": "alley", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 64, "objectiveType": "fetch", "target": 173, "targetAction": null, "quantity": 1, "location": "alley", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 64, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": 173, "isRequired": true}, {"questId": 65, "objectiveType": "fetch", "target": 163, "targetAction": null, "quantity": 6, "location": "sewers", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 65, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 6, "location": null, "description": "", "creatureId": null, "itemId": 163, "isRequired": true}, {"questId": 66, "objectiveType": "craft", "target": 106, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 67, "objectiveType": "fetch", "target": 165, "targetAction": null, "quantity": 5, "location": "shrine", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 67, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 5, "location": null, "description": "", "creatureId": null, "itemId": 165, "isRequired": true}, {"questId": 68, "objectiveType": "fetch", "target": 166, "targetAction": null, "quantity": 8, "location": "sewers", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 68, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 8, "location": null, "description": "", "creatureId": null, "itemId": 166, "isRequired": true}, {"questId": 69, "objectiveType": "npc_kill", "target": 70, "targetAction": null, "quantity": 1, "location": "mall", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 70, "objectiveType": "fetch", "target": 167, "targetAction": null, "quantity": 3, "location": "school", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 70, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 3, "location": null, "description": "", "creatureId": null, "itemId": 167, "isRequired": true}, {"questId": 71, "objectiveType": "fetch", "target": 168, "targetAction": null, "quantity": 5, "location": "church", "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 71, "objectiveType": "item_hand_in", "target": null, "targetAction": null, "quantity": 5, "location": null, "description": "", "creatureId": null, "itemId": 168, "isRequired": true}, {"questId": 72, "objectiveType": "pvp_post_battle_choice", "target": 30, "targetAction": "cripple", "quantity": 5, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 73, "objectiveType": "unique", "target": 1, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 74, "objectiveType": "tutorial_suggestion", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 75, "objectiveType": "unique", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 76, "objectiveType": "unique", "target": null, "targetAction": null, "quantity": 1, "location": null, "description": "", "creatureId": null, "itemId": null, "isRequired": true}, {"questId": 77, "objectiveType": "fetch", "target": 309, "targetAction": null, "quantity": 3, "location": "shrine", "description": "", "creatureId": null, "itemId": null, "isRequired": true}]