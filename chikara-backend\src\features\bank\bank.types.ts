import { BankTransactionTypes } from "@prisma/client";

export interface DepositParams {
    userId: number;
    amount: number;
}

export interface WithdrawParams {
    userId: number;
    amount: number;
}

export interface TransferParams {
    userId: number;
    recipientId: number;
    transferAmount: number;
}

export interface TransactionHistoryParams {
    userId: number;
}

export interface TransactionResponse {
    data?: {
        balance: number;
        cash: number;
        fee: number;
    };
    error?: string;
    statusCode?: number;
}

export interface TransactionHistory {
    transaction_type: BankTransactionTypes;
    cash: number;
    transactionFee: number;
    initiatorId: number | null;
    secondPartyId: number | null;
    createdAt: Date;
    cashBalance?: number;
    bankBalance?: number;
}

export interface TransactionHistoryResponse {
    data?: TransactionHistory[];
}
