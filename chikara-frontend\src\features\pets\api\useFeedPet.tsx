import type { FeedPetParams } from "@/features/pets/types/pets";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface UseFeedPetOptions {
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
}

const useFeedPet = (options: UseFeedPetOptions = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.pets.feed.mutationOptions({
            onSuccess: (data) => {
                // Invalidate pets list to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.pets.list.key(),
                });
                options.onSuccess?.(data);
            },
            onError: (error) => {
                console.error("Feed pet error:", error);
                options.onError?.(error);
            },
        })
    );
};

export default useFeedPet;
