import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { APIROUTES } from "@/helpers/apiRoutes";

export const useClaimCompletionReward = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.dailyQuest.claimDailyCompletionReward.mutationOptions({
            onSuccess: (data) => {
                toast.success(`You received 1x Daily Chest!`);
                // TODO - set the dailyQuestsRewardClaimed manually
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
            },
            onError: (error) => {
                toast.error(`Error: ${error.message}`);
            },
        })
    );
};
