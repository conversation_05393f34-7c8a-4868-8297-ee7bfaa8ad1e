import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-hot-toast";

const useCreateGang = (setOpen) => {
    const [gangName, setGangName] = useState("");
    const [gangDescription, setGangDescription] = useState("");
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.createGang.mutationOptions({
            onSuccess: () => {
                toast.success(`Gang created!`);
                queryClient.invalidateQueries({
                    queryKey: orpc.user.getCurrentUser.key(),
                });
                setOpen(false);
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    const createGang = () => {
        if (gangName.length < 4) {
            toast.error("Gang name must be at least 4 characters");
            return;
        }

        mutation.mutate({
            name: gangName,
            description: gangDescription,
        });
    };

    return {
        gangName,
        setGangName,
        gangDescription,
        setGangDescription,
        createGang,
    };
};

export default useCreateGang;
