import { defaultMockUser } from "../../../__tests__/prismaModelMocks.js";
import { NotificationTypes } from "../../../types/notification.js";
import { BankTransactionTypes } from "@prisma/client";
import { describe, expect, it, vi } from "vitest";

import * as NotificationService from "../../../core/notification.service.js";
import * as UserService from "../../../core/user.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import * as BankController from "../bank.controller.js";
import * as BankRepository from "../../../repositories/bank.repository.js";
import { logAction } from "../../../lib/actionLogger.js";
import { getNow } from "../../../utils/dateHelpers.js";

vi.mock("../../../core/user.service.js");
vi.mock("../../../core/notification.service.js");
vi.mock("../../../repositories/user.repository.js");
vi.mock("../../../repositories/bank.repository.js");

describe("Bank Controller", () => {
    describe("deposit", () => {
        it("should successfully deposit money", async () => {
            // Setup mocks
            const userId = 1;
            const depositAmount = 1000;
            const feeAmount = Math.ceil(depositAmount * 0.15); // 150
            const initialUser = {
                ...defaultMockUser,
                id: userId,
                cash: 2000,
                bank_balance: 500,
            };
            const updatedUser = {
                ...initialUser,
                cash: initialUser.cash - depositAmount,
                bank_balance: initialUser.bank_balance + (depositAmount - feeAmount),
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(initialUser);
            vi.mocked(UserService.updateUser).mockResolvedValueOnce(updatedUser);
            vi.mocked(BankRepository.createBankTransaction).mockResolvedValueOnce({ id: 1 } as any);

            // Execute the method
            const result = await BankController.deposit({ userId, amount: depositAmount });

            // Assertions
            expect(UserRepository.getUserById).toHaveBeenCalledWith(userId);
            expect(UserService.updateUser).toHaveBeenCalledWith(userId, {
                bank_balance: { increment: depositAmount - feeAmount },
                cash: { decrement: depositAmount },
            });
            expect(BankRepository.createBankTransaction).toHaveBeenCalledWith({
                transaction_type: "bank_deposit",
                cash: depositAmount,
                transactionFee: feeAmount,
                initiator: { connect: { id: userId } },
                initiatorCashBalance: updatedUser.cash,
                initiatorBankBalance: updatedUser.bank_balance,
            });
            expect(logAction).toHaveBeenCalledWith({
                action: "BANK_DEPOSIT",
                userId,
                info: { amount: depositAmount, fee: feeAmount },
            });
            expect(result).toEqual({
                data: {
                    balance: updatedUser.bank_balance,
                    cash: updatedUser.cash,
                    fee: feeAmount,
                },
            });
        });

        it("should handle deposit error when amount is less than minimum", async () => {
            // Setup
            const userId = 1;
            const depositAmount = 200; // Less than minimum 300
            const initialUser = {
                ...defaultMockUser,
                id: userId,
                cash: 2000,
                bank_balance: 500,
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(initialUser);

            // Execute the method
            const result = await BankController.deposit({ userId, amount: depositAmount });

            // Assertions
            expect(UserService.updateUser).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid deposit amount",
                statusCode: 400,
            });
        });

        it("should handle deposit error when user does not have enough cash", async () => {
            // Setup
            const userId = 1;
            const depositAmount = 3000; // More than available cash
            const initialUser = {
                ...defaultMockUser,
                id: userId,
                cash: 2000,
                bank_balance: 500,
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(initialUser);

            // Execute the method
            const result = await BankController.deposit({ userId, amount: depositAmount });

            // Assertions
            expect(UserService.updateUser).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid deposit amount",
                statusCode: 400,
            });
        });

        it("should handle deposit error when user is not found", async () => {
            // Setup
            const userId = 999; // Non-existent user
            const depositAmount = 1000;

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(null);

            // Execute the method
            const result = await BankController.deposit({ userId, amount: depositAmount });

            // Assertions
            expect(UserService.updateUser).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid deposit amount",
                statusCode: 400,
            });
        });
    });

    describe("withdraw", () => {
        it("should successfully withdraw money", async () => {
            // Setup mocks
            const userId = 1;
            const withdrawAmount = 500;
            const initialUser = {
                ...defaultMockUser,
                id: userId,
                cash: 1000,
                bank_balance: 2000,
            };
            const updatedUser = {
                ...initialUser,
                cash: initialUser.cash + withdrawAmount,
                bank_balance: initialUser.bank_balance - withdrawAmount,
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(initialUser);
            vi.mocked(UserService.updateUser).mockResolvedValueOnce(updatedUser);
            vi.mocked(BankRepository.createBankTransaction).mockResolvedValueOnce({ id: 1 } as any);

            // Execute the method
            const result = await BankController.withdraw({ userId, amount: withdrawAmount });

            // Assertions
            expect(UserRepository.getUserById).toHaveBeenCalledWith(userId);
            expect(UserService.updateUser).toHaveBeenCalledWith(userId, {
                bank_balance: { decrement: withdrawAmount },
                cash: { increment: withdrawAmount },
            });
            expect(BankRepository.createBankTransaction).toHaveBeenCalledWith({
                transaction_type: "bank_withdrawl",
                cash: withdrawAmount,
                transactionFee: 0, // No fee for withdrawal
                initiator: { connect: { id: userId } },
                initiatorCashBalance: updatedUser.cash,
                initiatorBankBalance: updatedUser.bank_balance,
            });
            expect(logAction).toHaveBeenCalledWith({
                action: "BANK_WITHDRAWAL",
                userId,
                info: { amount: withdrawAmount, fee: 0 },
            });
            expect(result).toEqual({
                data: {
                    balance: updatedUser.bank_balance,
                    cash: updatedUser.cash,
                    fee: 0,
                },
            });
        });

        it("should handle withdraw error when amount is less than minimum", async () => {
            // Setup
            const userId = 1;
            const withdrawAmount = 50; // Less than minimum 100
            const initialUser = {
                ...defaultMockUser,
                id: userId,
                cash: 1000,
                bank_balance: 2000,
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(initialUser);

            // Execute the method
            const result = await BankController.withdraw({ userId, amount: withdrawAmount });

            // Assertions
            expect(UserService.updateUser).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid withdrawl amount",
                statusCode: 400,
            });
        });

        it("should handle withdraw error when user does not have enough bank balance", async () => {
            // Setup
            const userId = 1;
            const withdrawAmount = 3000; // More than available balance
            const initialUser = {
                ...defaultMockUser,
                id: userId,
                cash: 1000,
                bank_balance: 2000,
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(initialUser);

            // Execute the method
            const result = await BankController.withdraw({ userId, amount: withdrawAmount });

            // Assertions
            expect(UserService.updateUser).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid withdrawl amount",
                statusCode: 400,
            });
        });

        it("should handle withdraw error when user is not found", async () => {
            // Setup
            const userId = 999; // Non-existent user
            const withdrawAmount = 500;

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(null);

            // Execute the method
            const result = await BankController.withdraw({ userId, amount: withdrawAmount });

            // Assertions
            expect(UserService.updateUser).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid withdrawl amount",
                statusCode: 400,
            });
        });
    });

    describe("transfer", () => {
        it("should successfully transfer money between users", async () => {
            // Setup mocks
            const userId = 1;
            const recipientId = 2;
            const transferAmount = 500;
            const feeAmount = Math.ceil(transferAmount * 0.15); // 75

            const sender = {
                ...defaultMockUser,
                id: userId,
                username: "sender",
                bank_balance: 2000,
                cash: 1000,
            };

            const recipient = {
                ...defaultMockUser,
                id: recipientId,
                username: "recipient",
                bank_balance: 1000,
                cash: 500,
            };

            const updatedSender = {
                ...sender,
                bank_balance: sender.bank_balance - transferAmount,
            };

            const updatedRecipient = {
                ...recipient,
                bank_balance: recipient.bank_balance + (transferAmount - feeAmount),
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById)
                .mockResolvedValueOnce(sender) // First call for sender
                .mockResolvedValueOnce(recipient); // Second call for recipient

            vi.mocked(BankRepository.updateBothUsersTransaction).mockResolvedValueOnce([
                updatedSender,
                updatedRecipient,
            ]);

            vi.mocked(BankRepository.createBankTransaction).mockResolvedValueOnce({ id: 1 } as any);

            // Execute the method
            const result = await BankController.transfer({
                userId,
                recipientId,
                transferAmount,
            });

            // Assertions
            expect(UserRepository.getUserById).toHaveBeenCalledWith(userId);
            expect(UserRepository.getUserById).toHaveBeenCalledWith(recipientId);
            expect(BankRepository.updateBothUsersTransaction).toHaveBeenCalledWith(
                userId,
                recipientId,
                transferAmount,
                feeAmount
            );
            expect(NotificationService.NotifyUser).toHaveBeenCalledTimes(2);
            expect(NotificationService.NotifyUser).toHaveBeenCalledWith(userId, NotificationTypes.transfer_sent, {
                user: recipientId,
                amount: transferAmount,
                fee: feeAmount,
            });
            expect(NotificationService.NotifyUser).toHaveBeenCalledWith(
                recipientId,
                NotificationTypes.transfer_received,
                {
                    user: userId,
                    amount: transferAmount,
                }
            );
            expect(BankRepository.createBankTransaction).toHaveBeenCalledWith({
                transaction_type: "bank_transfer",
                cash: transferAmount,
                transactionFee: feeAmount,
                initiator: { connect: { id: userId } },
                secondParty: { connect: { id: recipientId } },
                initiatorCashBalance: updatedSender.cash,
                initiatorBankBalance: updatedSender.bank_balance,
                secondPartyCashBalance: updatedRecipient.cash,
                secondPartyBankBalance: updatedRecipient.bank_balance,
            });
            expect(logAction).toHaveBeenCalledWith({
                action: "BANK_TRANSFER",
                userId,
                info: {
                    amount: transferAmount,
                    fee: feeAmount,
                    recipientId,
                },
            });
            expect(result).toEqual({
                data: {
                    balance: updatedSender.bank_balance,
                    cash: updatedSender.cash,
                    fee: feeAmount,
                },
            });
        });

        it("should prevent transferring to self", async () => {
            // Setup
            const userId = 1;
            const transferAmount = 500;

            // Execute the method
            const result = await BankController.transfer({
                userId,
                recipientId: userId, // Same as sender
                transferAmount,
            });

            // Assertions
            expect(UserRepository.getUserById).not.toHaveBeenCalled();
            expect(BankRepository.updateBothUsersTransaction).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Can't transfer money to yourself!",
                statusCode: 400,
            });
        });

        it("should handle transfer error when amount is less than minimum", async () => {
            // Setup
            const userId = 1;
            const recipientId = 2;
            const transferAmount = 50; // Less than minimum 100
            const sender = {
                ...defaultMockUser,
                id: userId,
                bank_balance: 2000,
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(sender);

            // Execute the method
            const result = await BankController.transfer({
                userId,
                recipientId,
                transferAmount,
            });

            // Assertions
            expect(UserRepository.getUserById).toHaveBeenCalledWith(userId);
            expect(BankRepository.updateBothUsersTransaction).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid transfer amount",
                statusCode: 400,
            });
        });

        it("should handle transfer error when sender does not have enough bank balance", async () => {
            // Setup
            const userId = 1;
            const recipientId = 2;
            const transferAmount = 3000; // More than available balance
            const sender = {
                ...defaultMockUser,
                id: userId,
                bank_balance: 2000,
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(sender);

            // Execute the method
            const result = await BankController.transfer({
                userId,
                recipientId,
                transferAmount,
            });

            // Assertions
            expect(UserRepository.getUserById).toHaveBeenCalledWith(userId);
            expect(BankRepository.updateBothUsersTransaction).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid transfer amount",
                statusCode: 400,
            });
        });

        it("should handle transfer error when recipient is not found", async () => {
            // Setup
            const userId = 1;
            const recipientId = 999; // Non-existent user
            const transferAmount = 500;
            const sender = {
                ...defaultMockUser,
                id: userId,
                bank_balance: 2000,
            };

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById)
                .mockResolvedValueOnce(sender) // First call for sender
                .mockResolvedValueOnce(null); // Second call for recipient

            // Execute the method
            const result = await BankController.transfer({
                userId,
                recipientId,
                transferAmount,
            });

            // Assertions
            expect(UserRepository.getUserById).toHaveBeenCalledWith(userId);
            expect(UserRepository.getUserById).toHaveBeenCalledWith(recipientId);
            expect(BankRepository.updateBothUsersTransaction).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "User does not exist",
                statusCode: 400,
            });
        });

        it("should handle transfer error when sender is not found", async () => {
            // Setup
            const userId = 999; // Non-existent user
            const recipientId = 2;
            const transferAmount = 500;

            // Mock the dependencies
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(null);

            // Execute the method
            const result = await BankController.transfer({
                userId,
                recipientId,
                transferAmount,
            });

            // Assertions
            expect(UserRepository.getUserById).toHaveBeenCalledWith(userId);
            expect(BankRepository.updateBothUsersTransaction).not.toHaveBeenCalled();
            expect(BankRepository.createBankTransaction).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "Invalid transfer amount",
                statusCode: 400,
            });
        });
    });

    describe("getTransactionHistory", () => {
        it("should return transaction history for a user", async () => {
            // Setup mocks
            const userId = 1;
            const mockTransactions = [
                {
                    id: 1,
                    transaction_type: BankTransactionTypes.bank_deposit,
                    cash: 1000,
                    transactionFee: 150,
                    initiatorId: userId,
                    secondPartyId: null,
                    createdAt: getNow(),
                    initiatorCashBalance: 900,
                    initiatorBankBalance: 2850,
                    secondPartyCashBalance: null,
                    secondPartyBankBalance: null,
                },
                {
                    id: 2,
                    transaction_type: BankTransactionTypes.bank_withdrawl,
                    cash: 500,
                    transactionFee: 0,
                    initiatorId: userId,
                    secondPartyId: null,
                    createdAt: getNow(),
                    initiatorCashBalance: 1400,
                    initiatorBankBalance: 2350,
                    secondPartyCashBalance: null,
                    secondPartyBankBalance: null,
                },
                {
                    id: 3,
                    transaction_type: BankTransactionTypes.bank_transfer,
                    cash: 300,
                    transactionFee: 45,
                    initiatorId: userId,
                    secondPartyId: 2,
                    createdAt: getNow(),
                    initiatorCashBalance: 1400,
                    initiatorBankBalance: 2005,
                    secondPartyCashBalance: 1000,
                    secondPartyBankBalance: 1255,
                },
            ];

            // Mock the dependencies
            vi.mocked(BankRepository.getTransactionHistory).mockResolvedValueOnce(mockTransactions);

            // Execute the method
            const result = await BankController.getTransactionHistory({ userId });

            // Assertions
            expect(BankRepository.getTransactionHistory).toHaveBeenCalledWith(userId, 10);

            expect(result).toEqual({
                data: mockTransactions.map((tx) => ({
                    transaction_type: tx.transaction_type,
                    cash: tx.cash,
                    transactionFee: tx.transactionFee,
                    initiatorId: tx.initiatorId,
                    secondPartyId: tx.secondPartyId,
                    createdAt: tx.createdAt,
                    cashBalance: tx.initiatorCashBalance,
                    bankBalance: tx.initiatorBankBalance,
                })),
            });
        });

        it("should return transaction history where user is the second party", async () => {
            // Setup mocks
            const userId = 2;
            const mockTransactions = [
                {
                    id: 3,
                    transaction_type: BankTransactionTypes.bank_transfer,
                    cash: 300,
                    transactionFee: 45,
                    initiatorId: 1,
                    secondPartyId: userId,
                    createdAt: getNow(),
                    initiatorCashBalance: 1400,
                    initiatorBankBalance: 2005,
                    secondPartyCashBalance: 1000,
                    secondPartyBankBalance: 1255,
                },
            ];

            // Mock the dependencies
            vi.mocked(BankRepository.getTransactionHistory).mockResolvedValueOnce(mockTransactions);

            // Execute the method
            const result = await BankController.getTransactionHistory({ userId });

            // Assertions
            expect(BankRepository.getTransactionHistory).toHaveBeenCalledWith(userId, 10);

            expect(result).toEqual({
                data: mockTransactions.map((tx) => ({
                    transaction_type: tx.transaction_type,
                    cash: tx.cash,
                    transactionFee: tx.transactionFee,
                    initiatorId: tx.initiatorId,
                    secondPartyId: tx.secondPartyId,
                    createdAt: tx.createdAt,
                    cashBalance: tx.secondPartyCashBalance,
                    bankBalance: tx.secondPartyBankBalance,
                })),
            });
        });

        it("should handle empty transaction history", async () => {
            // Setup mocks
            const userId = 1;

            // Mock the dependencies
            vi.mocked(BankRepository.getTransactionHistory).mockResolvedValueOnce([]);

            // Execute the method
            const result = await BankController.getTransactionHistory({ userId });

            // Assertions
            expect(BankRepository.getTransactionHistory).toHaveBeenCalledWith(userId, 10);
            expect(result).toEqual({ data: [] });
        });
    });

    describe("error handling", () => {
        it("should handle errors in deposit function", async () => {
            // Setup mocks
            const userId = 1;
            const depositAmount = 1000;
            const error = new Error("Test error");

            // Mock the dependencies to throw an error
            vi.mocked(UserRepository.getUserById).mockRejectedValueOnce(error);

            // Execute and catch error
            await expect(BankController.deposit({ userId, amount: depositAmount })).rejects.toThrowError();
        });

        it("should handle errors in withdraw function", async () => {
            // Setup mocks
            const userId = 1;
            const withdrawAmount = 500;
            const error = new Error("Test error");

            // Mock the dependencies to throw an error
            vi.mocked(UserRepository.getUserById).mockRejectedValueOnce(error);

            // Execute and catch error
            await expect(BankController.withdraw({ userId, amount: withdrawAmount })).rejects.toThrowError();
        });

        it("should handle errors in transfer function", async () => {
            // Setup mocks
            const userId = 1;
            const recipientId = 2;
            const transferAmount = 500;
            const error = new Error("Test error");

            // Mock the dependencies to throw an error
            vi.mocked(UserRepository.getUserById).mockRejectedValueOnce(error);

            // Execute and catch error
            await expect(
                BankController.transfer({
                    userId,
                    recipientId,
                    transferAmount,
                })
            ).rejects.toThrowError();
        });

        it("should handle errors in getTransactionHistory function", async () => {
            // Setup mocks
            const userId = 1;
            const error = new Error("Test error");

            // Mock the dependencies to throw an error
            vi.mocked(BankRepository.getTransactionHistory).mockRejectedValueOnce(error);

            // Execute and catch error
            await expect(BankController.getTransactionHistory({ userId })).rejects.toThrowError();
        });
    });
});
