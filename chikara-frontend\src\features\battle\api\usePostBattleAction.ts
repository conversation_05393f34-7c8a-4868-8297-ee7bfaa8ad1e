import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const usePostBattleAction = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.battle.postBattleAction.mutationOptions({
            onSuccess: () => {
                // Invalidate battle status and user info to refresh UI
                queryClient.invalidateQueries({ queryKey: api.battle.status.key() });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
        })
    );
};

export default usePostBattleAction;
