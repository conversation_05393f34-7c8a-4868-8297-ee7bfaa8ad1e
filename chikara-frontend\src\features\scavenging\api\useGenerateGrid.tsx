import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useGenerateGrid = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.scavenging.generateGrid.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.scavenging.activeSession.key() });
            },
        })
    );
};

export default useGenerateGrid;
