import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useHospitalCheckIn = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.infirmary.checkIn.mutationOptions({
            onSuccess: () => {
                toast.success(`Checked in successfully!`);
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.INFIRMARY.INFIRMARYLIST,
                });
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    return {
        hospitalCheckIn: mutation,
    };
};

export default useHospitalCheckIn;
