import {
  COMMON_ORPC_ERROR_DEFS,
  ORPCError,
  createORPC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  isORPCError<PERSON><PERSON>,
  isORPCError<PERSON>tatus,
  mapEventIterator,
  toORPCError
} from "./chunk-DRWM7KGB.js";
import {
  ErrorEvent,
  EventDecoderStream,
  encodeEventMessage,
  generateContentDisposition,
  getEventMeta,
  getFilenameFromContentDisposition,
  mergeStandardHeaders,
  withEventMeta
} from "./chunk-KMQCVE4M.js";
import {
  AsyncIteratorClass,
  intercept,
  isAsyncIteratorObject,
  isObject,
  isTypescriptObject,
  once,
  parseEmptyableJSON,
  stringifyJSON,
  toArray,
  value
} from "./chunk-C2MRSQUB.js";
import "./chunk-G3PMV62Z.js";

// ../node_modules/@orpc/standard-server-fetch/dist/index.mjs
function toEventIterator(stream) {
  const eventStream = stream?.pipeThrough(new TextDecoderStream()).pipeThrough(new EventDecoderStream());
  const reader = eventStream?.getReader();
  return new AsyncIteratorClass(async () => {
    while (true) {
      if (reader === void 0) {
        return { done: true, value: void 0 };
      }
      const { done, value: value2 } = await reader.read();
      if (done) {
        return { done: true, value: void 0 };
      }
      switch (value2.event) {
        case "message": {
          let message = parseEmptyableJSON(value2.data);
          if (isTypescriptObject(message)) {
            message = withEventMeta(message, value2);
          }
          return { done: false, value: message };
        }
        case "error": {
          let error = new ErrorEvent({
            data: parseEmptyableJSON(value2.data)
          });
          error = withEventMeta(error, value2);
          throw error;
        }
        case "done": {
          let done2 = parseEmptyableJSON(value2.data);
          if (isTypescriptObject(done2)) {
            done2 = withEventMeta(done2, value2);
          }
          return { done: true, value: done2 };
        }
      }
    }
  }, async () => {
    await reader?.cancel();
  });
}
function toEventStream(iterator, options = {}) {
  const keepAliveEnabled = options.eventIteratorKeepAliveEnabled ?? true;
  const keepAliveInterval = options.eventIteratorKeepAliveInterval ?? 5e3;
  const keepAliveComment = options.eventIteratorKeepAliveComment ?? "";
  let cancelled = false;
  let timeout;
  const stream = new ReadableStream({
    async pull(controller) {
      try {
        if (keepAliveEnabled) {
          timeout = setInterval(() => {
            controller.enqueue(encodeEventMessage({
              comments: [keepAliveComment]
            }));
          }, keepAliveInterval);
        }
        const value2 = await iterator.next();
        clearInterval(timeout);
        if (cancelled) {
          return;
        }
        const meta = getEventMeta(value2.value);
        if (!value2.done || value2.value !== void 0 || meta !== void 0) {
          controller.enqueue(encodeEventMessage({
            ...meta,
            event: value2.done ? "done" : "message",
            data: stringifyJSON(value2.value)
          }));
        }
        if (value2.done) {
          controller.close();
        }
      } catch (err) {
        clearInterval(timeout);
        if (cancelled) {
          return;
        }
        controller.enqueue(encodeEventMessage({
          ...getEventMeta(err),
          event: "error",
          data: err instanceof ErrorEvent ? stringifyJSON(err.data) : void 0
        }));
        controller.close();
      }
    },
    async cancel() {
      cancelled = true;
      clearInterval(timeout);
      await iterator.return?.();
    }
  }).pipeThrough(new TextEncoderStream());
  return stream;
}
async function toStandardBody(re) {
  const contentDisposition = re.headers.get("content-disposition");
  if (typeof contentDisposition === "string") {
    const fileName = getFilenameFromContentDisposition(contentDisposition) ?? "blob";
    const blob2 = await re.blob();
    return new File([blob2], fileName, {
      type: blob2.type
    });
  }
  const contentType = re.headers.get("content-type");
  if (!contentType || contentType.startsWith("application/json")) {
    const text = await re.text();
    return parseEmptyableJSON(text);
  }
  if (contentType.startsWith("multipart/form-data")) {
    return await re.formData();
  }
  if (contentType.startsWith("application/x-www-form-urlencoded")) {
    const text = await re.text();
    return new URLSearchParams(text);
  }
  if (contentType.startsWith("text/event-stream")) {
    return toEventIterator(re.body);
  }
  if (contentType.startsWith("text/plain")) {
    return await re.text();
  }
  const blob = await re.blob();
  return new File([blob], "blob", {
    type: blob.type
  });
}
function toFetchBody(body, headers, options = {}) {
  const currentContentDisposition = headers.get("content-disposition");
  headers.delete("content-type");
  headers.delete("content-disposition");
  if (body === void 0) {
    return void 0;
  }
  if (body instanceof Blob) {
    headers.set("content-type", body.type);
    headers.set("content-length", body.size.toString());
    headers.set(
      "content-disposition",
      currentContentDisposition ?? generateContentDisposition(body instanceof File ? body.name : "blob")
    );
    return body;
  }
  if (body instanceof FormData) {
    return body;
  }
  if (body instanceof URLSearchParams) {
    return body;
  }
  if (isAsyncIteratorObject(body)) {
    headers.set("content-type", "text/event-stream");
    return toEventStream(body, options);
  }
  headers.set("content-type", "application/json");
  return stringifyJSON(body);
}
function toStandardHeaders(headers, standardHeaders = {}) {
  for (const [key, value2] of headers) {
    if (Array.isArray(standardHeaders[key])) {
      standardHeaders[key].push(value2);
    } else if (standardHeaders[key] !== void 0) {
      standardHeaders[key] = [standardHeaders[key], value2];
    } else {
      standardHeaders[key] = value2;
    }
  }
  return standardHeaders;
}
function toFetchHeaders(headers, fetchHeaders = new Headers()) {
  for (const [key, value2] of Object.entries(headers)) {
    if (Array.isArray(value2)) {
      for (const v of value2) {
        fetchHeaders.append(key, v);
      }
    } else if (value2 !== void 0) {
      fetchHeaders.append(key, value2);
    }
  }
  return fetchHeaders;
}
function toFetchRequest(request, options = {}) {
  const headers = toFetchHeaders(request.headers);
  const body = toFetchBody(request.body, headers, options);
  return new Request(request.url, {
    signal: request.signal,
    method: request.method,
    headers,
    body
  });
}
function toStandardLazyResponse(response) {
  return {
    body: once(() => toStandardBody(response)),
    status: response.status,
    get headers() {
      const headers = toStandardHeaders(response.headers);
      Object.defineProperty(this, "headers", { value: headers, writable: true });
      return headers;
    },
    set headers(value2) {
      Object.defineProperty(this, "headers", { value: value2, writable: true });
    }
  };
}

// ../node_modules/@orpc/client/dist/shared/client.DwfV9Oyl.mjs
var CompositeStandardLinkPlugin = class {
  plugins;
  constructor(plugins = []) {
    this.plugins = [...plugins].sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
  }
  init(options) {
    for (const plugin of this.plugins) {
      plugin.init?.(options);
    }
  }
};
var StandardLink = class {
  constructor(codec, sender, options = {}) {
    this.codec = codec;
    this.sender = sender;
    const plugin = new CompositeStandardLinkPlugin(options.plugins);
    plugin.init(options);
    this.interceptors = toArray(options.interceptors);
    this.clientInterceptors = toArray(options.clientInterceptors);
  }
  interceptors;
  clientInterceptors;
  call(path, input, options) {
    return intercept(this.interceptors, { ...options, path, input }, async ({ path: path2, input: input2, ...options2 }) => {
      const output = await this.#call(path2, input2, options2);
      return output;
    });
  }
  async #call(path, input, options) {
    const request = await this.codec.encode(path, input, options);
    const response = await intercept(
      this.clientInterceptors,
      { ...options, input, path, request },
      ({ input: input2, path: path2, request: request2, ...options2 }) => this.sender.call(request2, options2, path2, input2)
    );
    const output = await this.codec.decode(response, options, path, input);
    return output;
  }
};
var STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES = {
  BIGINT: 0,
  DATE: 1,
  NAN: 2,
  UNDEFINED: 3,
  URL: 4,
  REGEXP: 5,
  SET: 6,
  MAP: 7
};
var StandardRPCJsonSerializer = class {
  customSerializers;
  constructor(options = {}) {
    this.customSerializers = options.customJsonSerializers ?? [];
    if (this.customSerializers.length !== new Set(this.customSerializers.map((custom) => custom.type)).size) {
      throw new Error("Custom serializer type must be unique.");
    }
  }
  serialize(data, segments = [], meta = [], maps = [], blobs = []) {
    for (const custom of this.customSerializers) {
      if (custom.condition(data)) {
        const result = this.serialize(custom.serialize(data), segments, meta, maps, blobs);
        meta.push([custom.type, ...segments]);
        return result;
      }
    }
    if (data instanceof Blob) {
      maps.push(segments);
      blobs.push(data);
      return [data, meta, maps, blobs];
    }
    if (typeof data === "bigint") {
      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.BIGINT, ...segments]);
      return [data.toString(), meta, maps, blobs];
    }
    if (data instanceof Date) {
      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.DATE, ...segments]);
      if (Number.isNaN(data.getTime())) {
        return [null, meta, maps, blobs];
      }
      return [data.toISOString(), meta, maps, blobs];
    }
    if (Number.isNaN(data)) {
      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.NAN, ...segments]);
      return [null, meta, maps, blobs];
    }
    if (data instanceof URL) {
      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.URL, ...segments]);
      return [data.toString(), meta, maps, blobs];
    }
    if (data instanceof RegExp) {
      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.REGEXP, ...segments]);
      return [data.toString(), meta, maps, blobs];
    }
    if (data instanceof Set) {
      const result = this.serialize(Array.from(data), segments, meta, maps, blobs);
      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.SET, ...segments]);
      return result;
    }
    if (data instanceof Map) {
      const result = this.serialize(Array.from(data.entries()), segments, meta, maps, blobs);
      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.MAP, ...segments]);
      return result;
    }
    if (Array.isArray(data)) {
      const json = data.map((v, i) => {
        if (v === void 0) {
          meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.UNDEFINED, ...segments, i]);
          return v;
        }
        return this.serialize(v, [...segments, i], meta, maps, blobs)[0];
      });
      return [json, meta, maps, blobs];
    }
    if (isObject(data)) {
      const json = {};
      for (const k in data) {
        if (k === "toJSON" && typeof data[k] === "function") {
          continue;
        }
        json[k] = this.serialize(data[k], [...segments, k], meta, maps, blobs)[0];
      }
      return [json, meta, maps, blobs];
    }
    return [data, meta, maps, blobs];
  }
  deserialize(json, meta, maps, getBlob) {
    const ref = { data: json };
    if (maps && getBlob) {
      maps.forEach((segments, i) => {
        let currentRef = ref;
        let preSegment = "data";
        segments.forEach((segment) => {
          currentRef = currentRef[preSegment];
          preSegment = segment;
        });
        currentRef[preSegment] = getBlob(i);
      });
    }
    for (const item of meta) {
      const type = item[0];
      let currentRef = ref;
      let preSegment = "data";
      for (let i = 1; i < item.length; i++) {
        currentRef = currentRef[preSegment];
        preSegment = item[i];
      }
      for (const custom of this.customSerializers) {
        if (custom.type === type) {
          currentRef[preSegment] = custom.deserialize(currentRef[preSegment]);
          break;
        }
      }
      switch (type) {
        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.BIGINT:
          currentRef[preSegment] = BigInt(currentRef[preSegment]);
          break;
        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.DATE:
          currentRef[preSegment] = new Date(currentRef[preSegment] ?? "Invalid Date");
          break;
        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.NAN:
          currentRef[preSegment] = Number.NaN;
          break;
        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.UNDEFINED:
          currentRef[preSegment] = void 0;
          break;
        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.URL:
          currentRef[preSegment] = new URL(currentRef[preSegment]);
          break;
        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.REGEXP: {
          const [, pattern, flags] = currentRef[preSegment].match(/^\/(.*)\/([a-z]*)$/);
          currentRef[preSegment] = new RegExp(pattern, flags);
          break;
        }
        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.SET:
          currentRef[preSegment] = new Set(currentRef[preSegment]);
          break;
        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.MAP:
          currentRef[preSegment] = new Map(currentRef[preSegment]);
          break;
      }
    }
    return ref.data;
  }
};
function toHttpPath(path) {
  return `/${path.map(encodeURIComponent).join("/")}`;
}
function getMalformedResponseErrorCode(status) {
  return Object.entries(COMMON_ORPC_ERROR_DEFS).find(([, def]) => def.status === status)?.[0] ?? "MALFORMED_ORPC_ERROR_RESPONSE";
}
var StandardRPCLinkCodec = class {
  constructor(serializer, options) {
    this.serializer = serializer;
    this.baseUrl = options.url;
    this.maxUrlLength = options.maxUrlLength ?? 2083;
    this.fallbackMethod = options.fallbackMethod ?? "POST";
    this.expectedMethod = options.method ?? this.fallbackMethod;
    this.headers = options.headers ?? {};
  }
  baseUrl;
  maxUrlLength;
  fallbackMethod;
  expectedMethod;
  headers;
  async encode(path, input, options) {
    const expectedMethod = await value(this.expectedMethod, options, path, input);
    let headers = await value(this.headers, options, path, input);
    const baseUrl = await value(this.baseUrl, options, path, input);
    const url = new URL(baseUrl);
    url.pathname = `${url.pathname.replace(/\/$/, "")}${toHttpPath(path)}`;
    if (options.lastEventId !== void 0) {
      headers = mergeStandardHeaders(headers, { "last-event-id": options.lastEventId });
    }
    const serialized = this.serializer.serialize(input);
    if (expectedMethod === "GET" && !(serialized instanceof FormData) && !isAsyncIteratorObject(serialized)) {
      const maxUrlLength = await value(this.maxUrlLength, options, path, input);
      const getUrl = new URL(url);
      getUrl.searchParams.append("data", stringifyJSON(serialized));
      if (getUrl.toString().length <= maxUrlLength) {
        return {
          body: void 0,
          method: expectedMethod,
          headers,
          url: getUrl,
          signal: options.signal
        };
      }
    }
    return {
      url,
      method: expectedMethod === "GET" ? this.fallbackMethod : expectedMethod,
      headers,
      body: serialized,
      signal: options.signal
    };
  }
  async decode(response) {
    const isOk = !isORPCErrorStatus(response.status);
    const deserialized = await (async () => {
      let isBodyOk = false;
      try {
        const body = await response.body();
        isBodyOk = true;
        return this.serializer.deserialize(body);
      } catch (error) {
        if (!isBodyOk) {
          throw new Error("Cannot parse response body, please check the response body and content-type.", {
            cause: error
          });
        }
        throw new Error("Invalid RPC response format.", {
          cause: error
        });
      }
    })();
    if (!isOk) {
      if (isORPCErrorJson(deserialized)) {
        throw createORPCErrorFromJson(deserialized);
      }
      throw new ORPCError(getMalformedResponseErrorCode(response.status), {
        status: response.status,
        data: { ...response, body: deserialized }
      });
    }
    return deserialized;
  }
};
var StandardRPCSerializer = class {
  constructor(jsonSerializer) {
    this.jsonSerializer = jsonSerializer;
  }
  serialize(data) {
    if (isAsyncIteratorObject(data)) {
      return mapEventIterator(data, {
        value: async (value2) => this.#serialize(value2, false),
        error: async (e) => {
          return new ErrorEvent({
            data: this.#serialize(toORPCError(e).toJSON(), false),
            cause: e
          });
        }
      });
    }
    return this.#serialize(data, true);
  }
  #serialize(data, enableFormData) {
    const [json, meta_, maps, blobs] = this.jsonSerializer.serialize(data);
    const meta = meta_.length === 0 ? void 0 : meta_;
    if (!enableFormData || blobs.length === 0) {
      return {
        json,
        meta
      };
    }
    const form = new FormData();
    form.set("data", stringifyJSON({ json, meta, maps }));
    blobs.forEach((blob, i) => {
      form.set(i.toString(), blob);
    });
    return form;
  }
  deserialize(data) {
    if (isAsyncIteratorObject(data)) {
      return mapEventIterator(data, {
        value: async (value2) => this.#deserialize(value2),
        error: async (e) => {
          if (!(e instanceof ErrorEvent)) {
            return e;
          }
          const deserialized = this.#deserialize(e.data);
          if (isORPCErrorJson(deserialized)) {
            return createORPCErrorFromJson(deserialized, { cause: e });
          }
          return new ErrorEvent({
            data: deserialized,
            cause: e
          });
        }
      });
    }
    return this.#deserialize(data);
  }
  #deserialize(data) {
    if (!(data instanceof FormData)) {
      return this.jsonSerializer.deserialize(data.json, data.meta ?? []);
    }
    const serialized = JSON.parse(data.get("data"));
    return this.jsonSerializer.deserialize(
      serialized.json,
      serialized.meta ?? [],
      serialized.maps,
      (i) => data.get(i.toString())
    );
  }
};
var StandardRPCLink = class extends StandardLink {
  constructor(linkClient, options) {
    const jsonSerializer = new StandardRPCJsonSerializer(options);
    const serializer = new StandardRPCSerializer(jsonSerializer);
    const linkCodec = new StandardRPCLinkCodec(serializer, options);
    super(linkCodec, linkClient, options);
  }
};

// ../node_modules/@orpc/client/dist/adapters/fetch/index.mjs
var LinkFetchClient = class {
  fetch;
  toFetchRequestOptions;
  constructor(options) {
    this.fetch = options?.fetch ?? globalThis.fetch.bind(globalThis);
    this.toFetchRequestOptions = options;
  }
  async call(request, options, path, input) {
    const fetchRequest = toFetchRequest(request, this.toFetchRequestOptions);
    const fetchResponse = await this.fetch(fetchRequest, { redirect: "manual" }, options, path, input);
    const lazyResponse = toStandardLazyResponse(fetchResponse);
    return lazyResponse;
  }
};
var RPCLink = class extends StandardRPCLink {
  constructor(options) {
    const linkClient = new LinkFetchClient(options);
    super(linkClient, options);
  }
};
export {
  LinkFetchClient,
  RPCLink
};
//# sourceMappingURL=@orpc_client_fetch.js.map
