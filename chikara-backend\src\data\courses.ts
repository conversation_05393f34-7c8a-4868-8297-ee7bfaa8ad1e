import ms from "ms";
import { type SkillType } from "@prisma/client";

const TIER_ONE = { time: ms("2d"), amount: 50, cost: 5000 } as const;
const TIER_TWO = { time: ms("6d"), amount: 200, cost: 12_000 } as const;
const TIER_THREE = { time: ms("12d"), amount: 400, cost: 60_000 } as const;

const tiers = [TIER_ONE, TIER_TWO, TIER_THREE] as const;

export const CourseRewardType = {
    STAT: "stat",
    CRAFTING_RECIPE: "crafting_recipe",
    TALENT_POINTS: "talent_points",
} as const;
export type CourseRewardType = (typeof CourseRewardType)[keyof typeof CourseRewardType];

const courses = [
    {
        id: 1,
        name: "Powerlifting Fundamentals",
        rewardType: CourseRewardType.STAT,
        stat: "strength",
        tier: 1,
    },
    {
        id: 2,
        name: "Advanced Powerlifting",
        rewardType: CourseRewardType.STAT,
        stat: "strength",
        tier: 2,
    },
    {
        id: 3,
        name: "Heavy Weapon Mastery",
        rewardType: CourseRewardType.STAT,
        stat: "strength",
        tier: 3,
    },
    {
        id: 4,
        name: "Aikido Fundamentals",
        rewardType: CourseRewardType.STAT,
        stat: "defence",
        tier: 1,
    },
    {
        id: 5,
        name: "Intermediate Aikido",
        rewardType: CourseRewardType.STAT,
        stat: "defence",
        tier: 2,
    },
    {
        id: 6,
        name: "Advanced Aikido",
        rewardType: CourseRewardType.STAT,
        stat: "defence",
        tier: 3,
    },
    {
        id: 7,
        name: "Archery Fundamentals",
        rewardType: CourseRewardType.STAT,
        stat: "dexterity",
        tier: 1,
    },
    {
        id: 8,
        name: "Advanced Archery",
        rewardType: CourseRewardType.STAT,
        stat: "dexterity",
        tier: 2,
    },
    {
        id: 9,
        name: "Fencing Masterclass",
        rewardType: CourseRewardType.STAT,
        stat: "dexterity",
        tier: 3,
    },
    {
        id: 10,
        name: "Hacking Fundamentals",
        rewardType: CourseRewardType.STAT,
        stat: "intelligence",
        tier: 1,
    },
    {
        id: 11,
        name: "Strategic Mind Games",
        rewardType: CourseRewardType.STAT,
        stat: "intelligence",
        tier: 2,
    },
    {
        id: 12,
        name: "Psychological Warfare Principles",
        rewardType: CourseRewardType.STAT,
        stat: "intelligence",
        tier: 3,
    },
    {
        id: 13,
        name: "Interval Training",
        rewardType: CourseRewardType.STAT,
        stat: "endurance",
        tier: 1,
    },
    {
        id: 14,
        name: "Advanced Breath Control",
        rewardType: CourseRewardType.STAT,
        stat: "endurance",
        tier: 2,
    },
    {
        id: 15,
        name: "Urban Survival Resilience",
        rewardType: CourseRewardType.STAT,
        stat: "endurance",
        tier: 3,
    },
    // Add new courses with different reward types
    {
        id: 16,
        name: "Crafting Essentials",
        rewardType: CourseRewardType.CRAFTING_RECIPE,
        recipeId: 1, // ID of the recipe to unlock
        tier: 1,
    },
    {
        id: 17,
        name: "Advanced Weapon Crafting",
        rewardType: CourseRewardType.CRAFTING_RECIPE,
        recipeId: 2,
        tier: 2,
    },
    {
        id: 18,
        name: "Combat Tactics",
        rewardType: CourseRewardType.TALENT_POINTS,
        amount: 2, // Number of talent points to award
        tier: 2,
    },
    {
        id: 19,
        name: "Strategic Leadership",
        rewardType: CourseRewardType.TALENT_POINTS,
        amount: 3,
        tier: 3,
    },
] as const;

const mappedCourses = courses.map((course) => {
    const tierData = tiers[course.tier - 1];
    const baseCourse = {
        id: course.id,
        name: course.name,
        rewardType: course.rewardType,
        time: tierData.time,
        cost: tierData.cost,
    };

    // Add specific properties based on reward type
    switch (course.rewardType) {
        case CourseRewardType.STAT: {
            return {
                ...baseCourse,
                stat: course.stat,
                amount: tierData.amount,
            };
        }
        case CourseRewardType.CRAFTING_RECIPE: {
            return {
                ...baseCourse,
                recipeId: course.recipeId,
            };
        }
        case CourseRewardType.TALENT_POINTS: {
            return {
                ...baseCourse,
                amount: course.amount || tierData.amount,
            };
        }
        // No default
    }

    return baseCourse;
});

export interface CourseType {
    id: number;
    name: string;
    rewardType: CourseRewardType;
    time: number;
    cost: number;
    // Optional fields based on reward type
    stat?: SkillType;
    amount?: number;
    recipeId?: number;
}

export type mappedCourses = CourseType[];
export default mappedCourses;
