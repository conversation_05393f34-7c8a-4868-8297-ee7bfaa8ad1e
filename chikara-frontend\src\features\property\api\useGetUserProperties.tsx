import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery } from "@tanstack/react-query";

/**
 * Hook to get current user's property details
 */
export const useGetUserProperties = (options: QueryOptions = {}) => {
    return useQuery(
        api.property.getUserProperties.queryOptions({
            staleTime: 60000, // 1 minute
            ...options,
        })
    );
};
