import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Hook to get available polls for user
 */
export const useGetAvailablePolls = (options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.availablePolls.queryOptions({
            ...options,
        })
    );
};

/**
 * Hook to get poll results
 */
export const useGetPollResults = (pollId: number, options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.pollResults.queryOptions({
            input: { pollId },
            ...options,
        })
    );
};

/**
 * Hook to submit poll response
 */
export const useSubmitPollResponse = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.createPollResponse.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.availablePolls.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.pollResults.key(),
                });
                toast.success("Poll response submitted successfully!");
            },
            onError: (error) => {
                console.error("Submit poll response error:", error);
                toast.error(error.message || "Failed to submit poll response");
            },
        })
    );
};
