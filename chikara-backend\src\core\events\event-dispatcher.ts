import { logger, LogErrorStack } from "../../utils/log.js";
import { GameEventType, GameEventPayloadMap } from "./event-types.js";

/**
 * Type-safe event handler function signature
 */
export type EventHandler<T extends GameEventType> = (payload: GameEventPayloadMap[T]) => Promise<void> | void;

/**
 * Registry of event handlers for each event type
 */
const eventHandlers: {
    [K in GameEventType]?: EventHandler<K>[];
} = {};

/**
 * Register an event handler for a specific event type
 * @param eventType - The type of event to listen for
 * @param handler - The handler function to execute when the event is dispatched
 */
export const registerEventHandler = <T extends GameEventType>(eventType: T, handler: EventHandler<T>): void => {
    if (!eventHandlers[eventType]) {
        eventHandlers[eventType] = [];
    }
    (eventHandlers[eventType] as EventHandler<T>[]).push(handler);
    logger.debug(`Registered event handler for ${eventType}`);
};

/**
 * Unregister an event handler for a specific event type
 * @param eventType - The type of event to stop listening for
 * @param handler - The handler function to remove
 */
export const unregisterEventHandler = <T extends GameEventType>(eventType: T, handler: EventHandler<T>): void => {
    const handlers = eventHandlers[eventType] as EventHandler<T>[] | undefined;
    if (handlers) {
        const index = handlers.indexOf(handler);
        if (index !== -1) {
            handlers.splice(index, 1);
            logger.debug(`Unregistered event handler for ${eventType}`);
        }
    }
};

/**
 * Dispatch an event to all registered handlers
 * @param eventType - The type of event to dispatch
 * @param payload - The event payload data
 */
export const dispatchEvent = async <T extends GameEventType>(
    eventType: T,
    payload: GameEventPayloadMap[T]
): Promise<void> => {
    const handlers = eventHandlers[eventType] as EventHandler<T>[] | undefined;

    if (!handlers || handlers.length === 0) {
        logger.debug(`No handlers registered for event type: ${eventType}`);
        return;
    }

    logger.debug(`Dispatching event ${eventType} to ${handlers.length} handler(s)`);

    // Execute all handlers concurrently
    const promises = handlers.map(async (handler) => {
        try {
            await handler(payload);
        } catch (error) {
            LogErrorStack({ message: `Error in event handler for ${eventType}`, error });
            // Continue processing other handlers even if one fails
        }
    });

    await Promise.allSettled(promises);
};

/**
 * Get the number of registered handlers for an event type
 * @param eventType - The event type to check
 * @returns The number of registered handlers
 */
export const getHandlerCount = (eventType: GameEventType): number => {
    return eventHandlers[eventType]?.length ?? 0;
};

/**
 * Clear all handlers for a specific event type
 * @param eventType - The event type to clear handlers for
 */
export const clearEventHandlers = (eventType: GameEventType): void => {
    delete eventHandlers[eventType];
    logger.debug(`Cleared all handlers for event type: ${eventType}`);
};

/**
 * Clear all event handlers (useful for testing)
 */
export const clearAllEventHandlers = (): void => {
    for (const eventType of Object.keys(eventHandlers)) {
        delete eventHandlers[eventType as GameEventType];
    }
    logger.debug("Cleared all event handlers");
};
