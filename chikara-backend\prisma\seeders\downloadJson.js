import { exec } from "node:child_process";
/* eslint-disable no-console */
import { promises as fs } from "node:fs";
import { createWriteStream } from "node:fs";
import path from "node:path";
import { pipeline } from "node:stream/promises";
import { promisify } from "node:util";
import { dirname } from "path";
import { fileURLToPath } from "url";
import AdmZip from "adm-zip";

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const ZIP_URL = "http://nextcloud-j0kws44gg800s00w4koss40s.**************.sslip.io/s/ZS7Gb2qGmT9J6SC/download";

async function downloadFile(url, outputPath) {
    const response = await fetch(url);
    if (!response.ok) {
        throw new Error(`Failed to download: ${response.statusText} (${response.status})`);
    }

    // Stream the download to file instead of loading it all into memory
    const fileStream = createWriteStream(outputPath);
    await pipeline(response.body, fileStream);
    console.log(`File downloaded to ${outputPath}`);
    return outputPath;
}

async function extractZipWithAdmZip(zipPath) {
    try {
        console.log("Attempting to extract zip file with adm-zip...");
        const zip = new AdmZip(zipPath);
        zip.extractAllTo(__dirname, true);
        console.log("Files extracted successfully with adm-zip");
        return true;
    } catch (error) {
        console.error("Error extracting zip with AdmZip:", error.message);
        return false;
    }
}

async function extractZipWithNativeTool(zipPath) {
    try {
        console.log("Attempting to extract zip file with native tools...");

        // Detect OS platform
        const isWindows = process.platform === "win32";

        if (isWindows) {
            // Use PowerShell's Expand-Archive on Windows
            const destDir = __dirname;
            console.log(`Extracting to: ${destDir}`);

            const command = `powershell.exe -Command "Expand-Archive -Path '${zipPath}' -DestinationPath '${destDir}' -Force"`;
            console.log(`Running command: ${command}`);

            const { stdout, stderr } = await execAsync(command);

            if (stderr) {
                console.error(`PowerShell stderr: ${stderr}`);
            }

            if (stdout) {
                console.log(`PowerShell stdout: ${stdout}`);
            }

            return true;
        } else {
            // Use unzip on Unix-based systems
            const { stdout, stderr } = await execAsync(`unzip -o "${zipPath}" -d "${__dirname}"`);

            if (stderr) {
                console.error(`unzip stderr: ${stderr}`);
            }

            if (stdout) {
                console.log(`unzip stdout: ${stdout}`);
            }

            return true;
        }
    } catch (error) {
        console.error("Error extracting zip with native tools:", error.message);
        return false;
    }
}

async function downloadAndExtractFiles() {
    console.log("Starting download...");
    const zipPath = path.join(__dirname, "temp.zip");

    try {
        // Clean up any previous zip file
        try {
            await fs.unlink(zipPath);
        } catch (error) {
            // Ignore if file doesn't exist
        }

        // Download the zip file
        await downloadFile(ZIP_URL, zipPath);
        console.log("Zip file downloaded successfully");

        // Verify zip file exists and has content
        const stats = await fs.stat(zipPath);
        console.log(`Zip file size: ${stats.size} bytes`);

        if (stats.size === 0) {
            throw new Error("Downloaded zip file is empty");
        }

        // Try to extract with adm-zip first
        let extractResult = await extractZipWithAdmZip(zipPath);

        // If adm-zip fails, try native tool
        if (!extractResult) {
            console.log("Trying alternative extraction method with native tools...");
            extractResult = await extractZipWithNativeTool(zipPath);

            if (!extractResult) {
                throw new Error(
                    "All zip extraction methods failed. Please check the file format or try a different download source."
                );
            } else {
                console.log("Files extracted successfully with native tools");
            }
        }

        // Clean up the temporary zip file
        await fs.unlink(zipPath);
        console.log("Temporary zip file removed");

        console.log("All operations completed successfully!");
    } catch (error) {
        console.error("Error during download/extraction:", error);

        // Try to clean up the zip file in case of error
        try {
            await fs.unlink(zipPath);
            console.log("Temporary zip file removed after error");
        } catch (cleanupError) {
            // Ignore cleanup errors
        }
    }
}

// Execute the download and extraction
await downloadAndExtractFiles();
