[{"id": 1, "name": "melee_damage_increase", "tree": "strength", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.1, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 2, "name": "berserker", "tree": "strength", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 3, "staminaCost": null, "tier1Modifier": 15.0, "tier2Modifier": 25.0, "tier3Modifier": 35.0, "secondaryModifier": 1.5}, {"id": 3, "name": "bully", "tree": "strength", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 3, "staminaCost": null, "tier1Modifier": 1.35, "tier2Modifier": 1.35, "tier3Modifier": 1.35, "secondaryModifier": null}, {"id": 4, "name": "offensive_offhands", "tree": "strength", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 5, "name": "combat_regeneration", "tree": "strength", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.04, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 6, "name": "rage", "tree": "strength", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 40, "tier1Modifier": 0.6, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 7, "name": "cripple", "tree": "strength", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 50, "tier1Modifier": 0.35, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 8, "name": "stun", "tree": "strength", "statRequired": 750, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 70, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 9, "name": "headbutt", "tree": "strength", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 70, "tier1Modifier": 0.3, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 10, "name": "active_defence", "tree": "defence", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.1, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 11, "name": "strong_bones", "tree": "defence", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 2, "staminaCost": null, "tier1Modifier": 0.5, "tier2Modifier": 1.0, "tier3Modifier": null, "secondaryModifier": null}, {"id": 12, "name": "good_stomach", "tree": "defence", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 1.2, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 13, "name": "shieldbearer", "tree": "defence", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 14, "name": "deflect_damage", "tree": "defence", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.25, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 15, "name": "shockwave", "tree": "defence", "statRequired": 750, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 70, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 16, "name": "high_guard", "tree": "defence", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 70, "tier1Modifier": 0.75, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": 0.4}, {"id": 17, "name": "shield_bash", "tree": "defence", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 60, "tier1Modifier": 0.25, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": 50.0}, {"id": 18, "name": "mitigation", "tree": "defence", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.85, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": 0.01}, {"id": 19, "name": "healthy_caster", "tree": "intelligence", "statRequired": 99, "pointsInTreeRequired": 0, "pointsCost": 600, "maxPoints": 3, "staminaCost": null, "tier1Modifier": 90.0, "tier2Modifier": 80.0, "tier3Modifier": 70.0, "secondaryModifier": null}, {"id": 20, "name": "cunning_rat", "tree": "intelligence", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 1.25, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 21, "name": "speed_crafter", "tree": "intelligence", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.75, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 22, "name": "multitasker", "tree": "intelligence", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 1.0, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 23, "name": "investor", "tree": "intelligence", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 2, "staminaCost": null, "tier1Modifier": 1.1, "tier2Modifier": 1.25, "tier3Modifier": null, "secondaryModifier": null}, {"id": 24, "name": "learner", "tree": "intelligence", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 1.15, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 25, "name": "heal", "tree": "intelligence", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 40, "tier1Modifier": 0.25, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 26, "name": "heal_over_time", "tree": "intelligence", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 60, "tier1Modifier": 0.15, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 27, "name": "sleep", "tree": "intelligence", "statRequired": 750, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 70, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 28, "name": "skill_efficiency", "tree": "intelligence", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.5, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 29, "name": "energetic", "tree": "endurance", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 3, "staminaCost": null, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 30, "name": "outside_combat_regeneration", "tree": "endurance", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 1.3, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 31, "name": "recovery", "tree": "endurance", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 2.0, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 32, "name": "mugger", "tree": "endurance", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 1.3, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 33, "name": "free_movement", "tree": "endurance", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.3, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 34, "name": "built", "tree": "endurance", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 200.0, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 35, "name": "rejuvenation", "tree": "endurance", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 2, "staminaCost": null, "tier1Modifier": 0.05, "tier2Modifier": 0.1, "tier3Modifier": null, "secondaryModifier": null}, {"id": 36, "name": "max_hp_heal", "tree": "endurance", "statRequired": 750, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 80, "tier1Modifier": 0.75, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 37, "name": "sap_sleep", "tree": "endurance", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 30, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 38, "name": "disarm", "tree": "endurance", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 65, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 39, "name": "self_harm", "tree": "endurance", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 20, "tier1Modifier": 1.4, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": 0.1}, {"id": 40, "name": "ranger", "tree": "dexterity", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.1, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 41, "name": "escape_artist", "tree": "dexterity", "statRequired": 25, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.7, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 42, "name": "quiver", "tree": "dexterity", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 3, "staminaCost": null, "tier1Modifier": 1.0, "tier2Modifier": 2.0, "tier3Modifier": 3.0, "secondaryModifier": null}, {"id": 43, "name": "quick_turn_taker", "tree": "dexterity", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 44, "name": "exhaust", "tree": "dexterity", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 50, "tier1Modifier": 0.35, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 45, "name": "shadow_step", "tree": "dexterity", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.3, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": 0.03}, {"id": 46, "name": "reload", "tree": "dexterity", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 100, "tier1Modifier": null, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 47, "name": "spray", "tree": "dexterity", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 50, "tier1Modifier": 0.25, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 48, "name": "toxic_dart", "tree": "dexterity", "statRequired": 500, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 65, "tier1Modifier": 0.1, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": 30.0}, {"id": 49, "name": "giant_killing_slingshot", "tree": "dexterity", "statRequired": 750, "pointsInTreeRequired": 0, "pointsCost": 2, "maxPoints": 1, "staminaCost": 60, "tier1Modifier": 0.4, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 50, "name": "revive", "tree": "intelligence", "statRequired": 250, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 3.0, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}, {"id": 51, "name": "coward", "tree": "endurance", "statRequired": 50, "pointsInTreeRequired": 0, "pointsCost": 1, "maxPoints": 1, "staminaCost": null, "tier1Modifier": 0.2, "tier2Modifier": null, "tier3Modifier": null, "secondaryModifier": null}]