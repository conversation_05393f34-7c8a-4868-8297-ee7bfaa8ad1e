import { ItemQuality } from "@prisma/client";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import type { ItemModel, UserModel } from "../../lib/db.js";
import * as LootService from "../loot.service.js";

// Mock dependencies
vi.mock("@/features/roguelike/roguelike.helpers.js", () => ({
    GetDropId: vi.fn(),
}));

vi.mock("@/repositories/item.repository.js", () => ({
    findItemById: vi.fn(),
}));

vi.mock("@/core/inventory.service.js", () => ({
    AddItemToUser: vi.fn(),
}));

vi.mock("@/core/equipment.service.js", () => ({
    IsItemEquippable: vi.fn(),
}));

import { IsItemEquippable } from "../equipment.service.js";
import * as InventoryService from "../inventory.service.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import * as RogueLikeHelper from "../../features/roguelike/roguelike.helpers.js";
import { logPlayerAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";

describe("loot.service", () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // Reset Math.random to a predictable value for testing
        vi.spyOn(Math, "random").mockReturnValue(0.5);
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe("getItemDropQuality", () => {
        const mockItem = {
            id: 1,
            name: "Test Item",
            description: "Test Description",
        } as unknown as ItemModel;

        it("should return undefined for non-equippable items", () => {
            vi.mocked(IsItemEquippable).mockReturnValue(false);

            const result = LootService.getItemDropQuality(mockItem, false);

            expect(result).toBeUndefined();
            expect(IsItemEquippable).toHaveBeenCalledWith(mockItem);
        });

        it("should return normal drop quality for non-boss drops", () => {
            vi.mocked(IsItemEquippable).mockReturnValue(true);
            // Math.random() returns 0.5, so index should be 1 (middle of array)

            const result = LootService.getItemDropQuality(mockItem, false);

            expect(result).toBe(ItemQuality.normal);
            expect(IsItemEquippable).toHaveBeenCalledWith(mockItem);
        });

        it("should return boss drop quality for boss drops", () => {
            vi.mocked(IsItemEquippable).mockReturnValue(true);
            // Math.random() returns 0.5, so index should be 1 (middle of array)

            const result = LootService.getItemDropQuality(mockItem, true);

            expect(result).toBe(ItemQuality.superior);
            expect(IsItemEquippable).toHaveBeenCalledWith(mockItem);
        });

        it("should return first quality when random is 0", () => {
            vi.mocked(IsItemEquippable).mockReturnValue(true);
            vi.spyOn(Math, "random").mockReturnValue(0);

            const normalResult = LootService.getItemDropQuality(mockItem, false);
            const bossResult = LootService.getItemDropQuality(mockItem, true);

            expect(normalResult).toBe(ItemQuality.shoddy);
            expect(bossResult).toBe(ItemQuality.fine);
        });

        it("should return last quality when random is close to 1", () => {
            vi.mocked(IsItemEquippable).mockReturnValue(true);
            vi.spyOn(Math, "random").mockReturnValue(0.99);

            const normalResult = LootService.getItemDropQuality(mockItem, false);
            const bossResult = LootService.getItemDropQuality(mockItem, true);

            expect(normalResult).toBe(ItemQuality.fine);
            expect(bossResult).toBe(ItemQuality.masterwork);
        });
    });

    describe("generateNPCItemDrop", () => {
        const mockUser = {
            id: 123,
            name: "Test User",
        } as unknown as UserModel;

        const mockItem = {
            id: 456,
            name: "Test Item",
            description: "Test Description",
        } as unknown as ItemModel;

        beforeEach(() => {
            const mockUserItemWithItem = {
                id: 1,
                userId: 123,
                itemId: 456,
                count: 1,
                isTradeable: true,
                upgradeLevel: 0,
                quality: null,
                createdAt: new Date(),
                updatedAt: new Date(),
                item: mockItem,
            };
            vi.mocked(InventoryService.AddItemToUser).mockResolvedValue(mockUserItemWithItem);
        });

        it("should return null when no drop ID is generated", async () => {
            vi.mocked(RogueLikeHelper.GetDropId).mockResolvedValue(null);

            const result = await LootService.generateNPCItemDrop(mockUser, false);

            expect(result).toBeNull();
            expect(RogueLikeHelper.GetDropId).toHaveBeenCalledWith(mockUser);
            expect(ItemRepository.findItemById).not.toHaveBeenCalled();
        });

        it("should return null when drop ID is 0 or negative", async () => {
            vi.mocked(RogueLikeHelper.GetDropId).mockResolvedValue(0);

            const result = await LootService.generateNPCItemDrop(mockUser, false);

            expect(result).toBeNull();
        });

        it("should log error and return null when item is not found", async () => {
            vi.mocked(RogueLikeHelper.GetDropId).mockResolvedValue(456);
            vi.mocked(ItemRepository.findItemById).mockResolvedValue(null);

            const result = await LootService.generateNPCItemDrop(mockUser, false);

            expect(result).toBeNull();
            expect(ItemRepository.findItemById).toHaveBeenCalledWith(456);
            expect(LogErrorStack).toHaveBeenCalledWith({
                error: expect.any(Error),
            });
            expect(InventoryService.AddItemToUser).not.toHaveBeenCalled();
        });

        it("should successfully drop non-equippable item without quality", async () => {
            vi.mocked(RogueLikeHelper.GetDropId).mockResolvedValue(456);
            vi.mocked(ItemRepository.findItemById).mockResolvedValue(mockItem);
            vi.mocked(IsItemEquippable).mockReturnValue(false);

            const result = await LootService.generateNPCItemDrop(mockUser, false);

            expect(result).toEqual(mockItem);
            expect(InventoryService.AddItemToUser).toHaveBeenCalledWith({
                userId: 123,
                itemId: 456,
                amount: 1,
                isTradeable: true,
            });
        });

        it("should successfully drop equippable item with quality for normal enemy", async () => {
            vi.mocked(RogueLikeHelper.GetDropId).mockResolvedValue(456);
            vi.mocked(ItemRepository.findItemById).mockResolvedValue(mockItem);
            vi.mocked(IsItemEquippable).mockReturnValue(true);

            const result = await LootService.generateNPCItemDrop(mockUser, false);

            expect(result).toEqual(mockItem);
            expect(InventoryService.AddItemToUser).toHaveBeenCalledWith({
                userId: 123,
                itemId: 456,
                amount: 1,
                isTradeable: true,
                quality: ItemQuality.normal, // Based on mocked Math.random(0.5)
            });
        });

        it("should successfully drop equippable item with quality for boss enemy", async () => {
            vi.mocked(RogueLikeHelper.GetDropId).mockResolvedValue(456);
            vi.mocked(ItemRepository.findItemById).mockResolvedValue(mockItem);
            vi.mocked(IsItemEquippable).mockReturnValue(true);

            const result = await LootService.generateNPCItemDrop(mockUser, true);

            expect(result).toEqual(mockItem);
            expect(InventoryService.AddItemToUser).toHaveBeenCalledWith({
                userId: 123,
                itemId: 456,
                amount: 1,
                isTradeable: true,
                quality: ItemQuality.superior, // Based on mocked Math.random(0.5)
            });
        });
    });

    describe("generateCrateDrop", () => {
        const mockUser = {
            id: 123,
            name: "Test User",
            gangId: 456,
        } as unknown as UserModel;

        const mockCrateItem = {
            id: 279,
            name: "Crate",
            description: "A mysterious crate",
        } as unknown as ItemModel;

        beforeEach(() => {
            vi.mocked(InventoryService.AddItemToUser).mockResolvedValue();
            vi.mocked(logPlayerAction).mockImplementation(() => {});
        });

        it("should return null when user has no gang", async () => {
            const userWithoutGang = { ...mockUser, gangId: null };

            const result = await LootService.generateCrateDrop(userWithoutGang);

            expect(result).toBeNull();
            expect(ItemRepository.findItemById).not.toHaveBeenCalled();
        });

        it("should return null when crate drop chance fails", async () => {
            // Math.random() returns 0.5, which is > 0.15, so no crate drop
            vi.spyOn(Math, "random").mockReturnValue(0.5);

            const result = await LootService.generateCrateDrop(mockUser);

            expect(result).toBeNull();
            expect(ItemRepository.findItemById).not.toHaveBeenCalled();
        });

        it("should return null when crate item is not found", async () => {
            vi.spyOn(Math, "random").mockReturnValue(0.1); // 10% < 15%, so crate should drop
            vi.mocked(ItemRepository.findItemById).mockResolvedValue(null);

            const result = await LootService.generateCrateDrop(mockUser);

            expect(result).toBeNull();
            expect(ItemRepository.findItemById).toHaveBeenCalledWith(279);
            expect(InventoryService.AddItemToUser).not.toHaveBeenCalled();
        });

        it("should successfully drop crate when conditions are met", async () => {
            vi.spyOn(Math, "random").mockReturnValue(0.1); // 10% < 15%, so crate should drop
            vi.mocked(ItemRepository.findItemById).mockResolvedValue(mockCrateItem);

            const result = await LootService.generateCrateDrop(mockUser);

            expect(result).toEqual(mockCrateItem);
            expect(ItemRepository.findItemById).toHaveBeenCalledWith(279);
            expect(InventoryService.AddItemToUser).toHaveBeenCalledWith({
                userId: 123,
                itemId: 279,
                amount: 1,
                isTradeable: true,
            });
            expect(logPlayerAction).toHaveBeenCalledWith(
                "BATTLE_CRATE_DROPPED",
                {
                    itemId: 279,
                    itemName: "Crate",
                },
                123
            );
        });

        it("should handle exactly 15% chance correctly", async () => {
            vi.spyOn(Math, "random").mockReturnValue(0.15);
            vi.mocked(ItemRepository.findItemById).mockResolvedValue(mockCrateItem);

            const result = await LootService.generateCrateDrop(mockUser);

            expect(result).toEqual(mockCrateItem);
        });

        it("should not drop crate when chance is just above 15%", async () => {
            vi.spyOn(Math, "random").mockReturnValue(0.151);

            const result = await LootService.generateCrateDrop(mockUser);

            expect(result).toBeNull();
        });
    });
});
