import { Server as HttpServer } from "node:http";
import { startRouletteInterval } from "../features/casino/roulette.controller.js";
import * as chatService from "../features/chat/chat.controller.js";
import { db } from "../lib/db.js";
import socketSession, { SocketSessionRequest } from "../middleware/socketSession.js";
import { NotificationTypes } from "../types/notification.js";
import { LogErrorStack, logger } from "../utils/log.js";
import { Prisma } from "@prisma/client";
import { NextFunction } from "express";
import { Server, Socket } from "socket.io";
import * as UserRepository from "../repositories/user.repository.js";

let loaded = false;
let io: Server | null = null;
const socketMap: Record<number, string[]> = {};

interface MessageData {
    message: string;
    room: {
        id: number;
    };
    parentMessageId?: number;
}

interface SessionSocket extends Socket {
    request: SocketSessionRequest;
}

function Init(server: HttpServer): void {
    chatService.startRoomNamesRefreshInterval();
    io = new Server(server, {
        cors: {
            credentials: true,
            origin: true,
        },
    });

    io.use((socket, next) => {
        socketSession(socket.request, next as NextFunction);
    });

    io.on("connection", (socket: SessionSocket) => {
        if (!socket.request.session) {
            logger.info("Unauthenticated user, disconnecting");
            socket.disconnect();
            return;
        }

        const user = socket.request.user;
        if (!user) {
            LogErrorStack({ error: new Error("Failed to get user, disconnecting") });
            socket.disconnect();
            return;
        }

        socket.join("global");

        socket.on("join room", (roomId: number) => {
            const roomName = chatService.getRoomName(roomId);
            if (roomName) {
                socket.join(roomName);
                logger.debug(`User ${user.id} joined room ${roomName}`);
            } else {
                logger.warn(`Room ID ${roomId} does not exist`);
            }
        });

        socket.on("leave room", (roomId: number) => {
            const roomName = chatService.getRoomName(roomId);
            if (roomName) {
                socket.leave(roomName);
                logger.debug(`User (socket ${socket.id}) left room ${roomId}`);
            }
        });

        socket.on("chat message", async (msg: MessageData) => {
            const currentUser = await UserRepository.findUserForChat(user.id);

            if (!currentUser) {
                logger.error(`User not found: ${user.id}`);
                return;
            }

            await chatService.handleChatMessage(io!, msg, currentUser);
        });

        if (socketMap[user.id]) {
            socketMap[user.id].push(socket.id);
        } else {
            socketMap[user.id] = [socket.id];
        }

        socket.on("disconnect", () => {
            const index = socketMap[user.id].indexOf(socket.id);
            if (index !== -1) {
                socketMap[user.id].splice(index, 1);
                if (socketMap[user.id].length === 0) {
                    delete socketMap[user.id];
                }
            }
        });
    });

    startRouletteInterval(io);
}

export const setupSockets = (server: HttpServer): void => {
    if (!loaded) {
        loaded = true;
        Init(server);
        logger.info("Sockets initialized");
    }
};

/**
 * Closes all socket connections gracefully.
 * This should be called during server shutdown.
 */
export const closeSockets = async (): Promise<void> => {
    if (io) {
        logger.info("Closing all socket connections...");

        // Return a promise that resolves when the sockets have closed
        return new Promise<void>((resolve) => {
            io!.close(() => {
                logger.info("All socket connections closed successfully");
                io = null;
                loaded = false;
                // Clear the socket map
                for (const key of Object.keys(socketMap)) {
                    delete socketMap[Number(key)];
                }
                resolve();
            });
        });
    }
    return;
};

interface NotificationMessage {
    type: NotificationTypes;
    details: string;
}

export const SendNotification = (userId: number, message: NotificationMessage): void => {
    const socketIds = socketMap[userId];
    if (!socketIds) {
        logger.debug("No connected sockets for user id " + userId);
        return;
    }

    for (const socketId of socketIds) {
        io?.to(socketId).emit("notification", {
            type: message.type,
            details: message.details,
        });
    }
};

export const NotifyMessageRemoved = (messageId?: number): void => {
    io?.emit("message removed", messageId);
};

export const EmitGlobalChatMessage = (chatMessage: Prisma.chat_messageCreateInput): void => {
    io?.to("global").emit("chat message", chatMessage);
};

export const GetOnlinePlayersCount = (): number => {
    return Object.keys(socketMap).length;
};

export default {
    SendNotification,
    NotifyMessageRemoved,
    EmitGlobalChatMessage,
    setupSockets,
    closeSockets,
    GetOnlinePlayersCount,
};
