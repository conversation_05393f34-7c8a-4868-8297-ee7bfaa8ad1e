[{"id": 1, "title": "Remove angel", "content": "Noob shit go away", "state": "Denied", "upvotes": 3, "downvotes": 1, "createdAt": "20/4/2024 14:46:59", "updatedAt": "29/4/2024 21:03:35", "userId": 11, "totalComments": 0}, {"id": 2, "title": "Give me money", "content": "¥1,000,000,000,000", "state": "Denied", "upvotes": 2, "downvotes": 1, "createdAt": "20/4/2024 14:47:41", "updatedAt": "23/4/2024 21:00:55", "userId": 11, "totalComments": 0}, {"id": 3, "title": "Minigames", "content": "More minigamesssss", "state": "Denied", "upvotes": 11, "downvotes": 2, "createdAt": "21/4/2024 17:11:12", "updatedAt": "13/6/2024 11:17:33", "userId": 12, "totalComments": 2}, {"id": 4, "title": "Make terraformer prestige work", "content": "<PERSON><PERSON>", "state": "Accepted", "upvotes": 2, "downvotes": 0, "createdAt": "21/4/2024 20:51:24", "updatedAt": "5/6/2024 12:12:47", "userId": 11, "totalComments": 1}, {"id": 5, "title": "Add \"Most Stamina-est\" to leaderboard", "content": "there's a rank for every other stat may as well!", "state": "Completed", "upvotes": 1, "downvotes": 0, "createdAt": "22/4/2024 18:38:06", "updatedAt": "22/4/2024 18:38:06", "userId": 11, "totalComments": 0}, {"id": 6, "title": "Auction House", "content": "Thinking of adding this during the Alpha - will be for crafting items only initially. Upvote/downvote to show me if it's something you want me to work on ", "state": "Completed", "upvotes": 9, "downvotes": 0, "createdAt": "22/4/2024 23:47:19", "updatedAt": "7/6/2024 06:25:09", "userId": 3, "totalComments": 1}, {"id": 7, "title": "Different home button", "content": "The home button looks a lot like the one used for the shops, not like a house at all", "state": "Denied", "upvotes": 3, "downvotes": 0, "createdAt": "23/4/2024 08:44:56", "updatedAt": "2/5/2024 09:31:06", "userId": 12, "totalComments": 1}, {"id": 8, "title": "Anti-Bullying Mechanic", "content": "Will add this in if it's wanted - It will prevent attacking players more than 3x in a day. Upvote/Downvote if you want it added or not", "state": "Completed", "upvotes": 3, "downvotes": 1, "createdAt": "23/4/2024 21:00:28", "updatedAt": "27/4/2024 18:53:53", "userId": 3, "totalComments": 5}, {"id": 9, "title": "Stats display", "content": "Maybe display your own stats (STR, DEX etc) somewhere on the main page, also consider including descriptions for the bars (energy, AP), since they are not mentioned anywhere other than Training or during actions right now - i.e. you are just expected to know which is which somehow.\n\nAdditionally, all the stats could be added to the inventory/equipment page.", "state": "Completed", "upvotes": 4, "downvotes": 2, "createdAt": "23/4/2024 21:07:05", "updatedAt": "20/5/2024 18:32:22", "userId": 15, "totalComments": 4}, {"id": 10, "title": "Bring More people into Alpha", "content": "Need more fresh meat", "state": "Completed", "upvotes": 4, "downvotes": 0, "createdAt": "24/4/2024 06:34:37", "updatedAt": "20/6/2024 00:32:24", "userId": 14, "totalComments": 1}, {"id": 11, "title": "Course Length", "content": "Display how long each of the courses will take before you take them.", "state": "Denied", "upvotes": 0, "downvotes": 1, "createdAt": "24/4/2024 18:35:26", "updatedAt": "24/4/2024 18:36:04", "userId": 15, "totalComments": 1}, {"id": 12, "title": "Light mode theme", "content": "Please refer to title, something lighter as an optional theme.", "state": "Denied", "upvotes": 2, "downvotes": 1, "createdAt": "25/4/2024 23:38:35", "updatedAt": "2/5/2024 11:37:28", "userId": 15, "totalComments": 1}, {"id": 13, "title": "Bank interest rate", "content": "What do you guys think about adding an interest rate to the money deposited in the bank? I think something like 1-2%/week would be a nice touch and add a bit to the utility of the bank.", "state": "Denied", "upvotes": 2, "downvotes": 3, "createdAt": "26/4/2024 13:37:23", "updatedAt": "15/5/2024 12:55:22", "userId": 19, "totalComments": 5}, {"id": 14, "title": "Push Notifications", "content": "Upvote/downvote to show me if this is something you want me to work on.\n\nWill be opt-in only and will allow you to get mobile/browser notifications when you're max AP/Energy again for now.", "state": "Completed", "upvotes": 6, "downvotes": 0, "createdAt": "27/4/2024 14:03:21", "updatedAt": "29/4/2024 17:44:00", "userId": 3, "totalComments": 1}, {"id": 15, "title": "Ability to Cancel a Workshop project", "content": "The ability to stop a project while its under construction. Alternatives to this could be.\n\n-Perk that allows you to recover all costs when cancelling\n-Perk that allows you to Shelf a cancelled project and automatically continue it once a slot has appeared.\n\n-Cancel project and regain half materials (Rounded up)\n-Cancel and lose all resources\n\n", "state": "Completed", "upvotes": 6, "downvotes": 1, "createdAt": "29/4/2024 00:40:59", "updatedAt": "22/5/2024 01:07:18", "userId": 14, "totalComments": 2}, {"id": 16, "title": "<PERSON>an <PERSON>", "content": "Option for players to loan money at an absurd interest rate (25-50%ish) with a max loan ammount tied to the character level (something like lv*1000?)\n\nThe player can choose to pay up immediatelly or stay with a \"debuff\" where 50-75%ish of all of his money gains adventuring/working/etc goes to reduce the loan bill\n\nThe player may start getting bounties in his head if he takes too long to pay up, and may find random encounters with loan shark goons while adventuring (resulting in long hospital times)2\n\nI think that'd be a good money sink in the long run, depending on how balanced/tweaked it is ofcourse, I dunno.. just a funny idea xD", "state": "Denied", "upvotes": 2, "downvotes": 3, "createdAt": "29/4/2024 20:43:42", "updatedAt": "3/6/2024 15:22:33", "userId": 48, "totalComments": 3}, {"id": 17, "title": "Pvp window and online status", "content": "Was talking with another player and we discussed a little about pvp. One thing i would like to see is possibly an online status. In other games with similar combat this was key to getting a good mug in and keeps people on their toes.\n\nAnother idea would be the ability to jump into the same combat screen if being attacked while online. Would help be able to use skills and items to make pvp more dynamic. As is now it just seems like your at the whim of the rng gods.\n\nBoth would be crucial features when gangs and gang warfare is implemented.", "state": "Denied", "upvotes": 3, "downvotes": 2, "createdAt": "30/4/2024 02:26:41", "updatedAt": "3/6/2024 15:21:18", "userId": 51, "totalComments": 3}, {"id": 18, "title": "Music or Sound Effects", "content": "It could be an optional checkbox but it would be cool to hear some sound effects or music during battle.", "state": "Denied", "upvotes": 1, "downvotes": 0, "createdAt": "30/4/2024 20:51:16", "updatedAt": "2/5/2024 11:36:30", "userId": 41, "totalComments": 1}, {"id": 19, "title": "Order", "content": "When I am in the inventory and the minute triggers, the items are shuffled. Can they mantain the same order?\n\nWhen I check the suggestions, can they be in order of creation? I'd like to check the newest knowing that they are on the top or the bottom\n\nThank you\nPs: good game", "state": "Completed", "upvotes": 6, "downvotes": 0, "createdAt": "30/4/2024 21:51:50", "updatedAt": "13/5/2024 13:41:31", "userId": 57, "totalComments": 2}, {"id": 20, "title": "Mission Board Reset Timer", "content": "A timer which tells us when the mission board resets.", "state": "Completed", "upvotes": 1, "downvotes": 0, "createdAt": "1/5/2024 11:54:44", "updatedAt": "2/5/2024 18:00:56", "userId": 49, "totalComments": 1}, {"id": 21, "title": "<PERSON><PERSON><PERSON> to get out of prison/hospital", "content": "I think it would be neat to be able to bribe the prison guards or doctors to release you early. This could have consequences such as lowered health or higher chance of going back to prison for longer next time.", "state": "Denied", "upvotes": 2, "downvotes": 4, "createdAt": "2/5/2024 02:13:41", "updatedAt": "25/5/2024 05:10:09", "userId": 41, "totalComments": 1}, {"id": 22, "title": "Daily Tasks", "content": "Upvote/downvote to show me if this is something you want me to work on.\n\nRandomised daily tasks that reset each day and give good rewards for completion", "state": "Completed", "upvotes": 7, "downvotes": 0, "createdAt": "3/5/2024 17:29:32", "updatedAt": "13/5/2024 13:25:39", "userId": 3, "totalComments": 1}, {"id": 23, "title": "Biggest Thief", "content": "Add to the leaderboard a top mugger. Or most stolen. That is all lol", "state": "Completed", "upvotes": 3, "downvotes": 0, "createdAt": "6/5/2024 20:34:25", "updatedAt": "20/6/2024 00:32:03", "userId": 51, "totalComments": 0}, {"id": 24, "title": "Prestige System", "content": "As people have leveled to cap faster than expected i'm trying to find a way to extend the time before 'endgame'.\nMy current thoughts are to add a one-time 'prestige' system, it would work as follows:\nA final task appears once you hit level 30 and have completed all other tasks.\nAfter completing the task you get the option to 'prestige' which does the following:\n\nResets:\nResets all your stats to 1\nResets your level to 1 and health to 200\nResets your talents\nResets your trader rep\nResets your roguelike level\nResets all quest progress (can redo quests)\nResets all course progress (can redo courses)\nUnequips all items (not lost on reset)\nUnequips all skills\n\nDoesn’t Reset:\nMoney\nItems\nMission Tier\nJob level\nTerraformer quest\nLeaderboard stats\n\nBenefits:\nYou now gain 2 stat points per training instead of 1\nEXP gain is increased by 1.5x\nNew level cap of 40\nUnlocks access to 10 more talent points\nPrestiged chat/profile icon\nNew tasks above level 30\n\nThis would make the new 'endgame' at level 40.\n\nListening to any opinions on this or any alternative suggestions. Please vote and comment!", "state": "Denied", "upvotes": 4, "downvotes": 5, "createdAt": "8/5/2024 22:40:53", "updatedAt": "13/5/2024 23:05:10", "userId": 3, "totalComments": 6}, {"id": 25, "title": "Button Progress States", "content": "When pressing buttons, it'd nice to have some sort of loading state to them, eg.  spinner, animated ellipsis, etc. so that there's a visual indicator that you've done it.\n\nSometimes actions take about a second to load, so it's nice to have some indication that something is happening.", "state": "Accepted", "upvotes": 5, "downvotes": 2, "createdAt": "9/5/2024 15:13:51", "updatedAt": "5/6/2024 12:12:59", "userId": 62, "totalComments": 1}, {"id": 26, "title": "Easier shop browsing", "content": "It's a little bit annoying when you are buying lots of stuff from different shops that you have to go back to explore -> shops everytime you have to switch vendors. I'd like to suggest a \"back\" button to the shops, or maybe a tab with all the vendors so you can quickly browse through all the wares.", "state": "Completed", "upvotes": 5, "downvotes": 0, "createdAt": "11/5/2024 16:52:36", "updatedAt": "13/5/2024 13:24:40", "userId": 19, "totalComments": 1}, {"id": 27, "title": "New player PvP protection", "content": "See title, preferrably based on time rather than level, so PvP protection for 2-3 days seem reasonble.", "state": "Completed", "upvotes": 9, "downvotes": 3, "createdAt": "11/5/2024 23:00:37", "updatedAt": "21/6/2024 12:14:00", "userId": 15, "totalComments": 9}, {"id": 28, "title": "more stimulants", "content": "make it so where there is more stimulants ", "state": "Completed", "upvotes": 3, "downvotes": 2, "createdAt": "12/5/2024 14:44:04", "updatedAt": "13/5/2024 21:49:40", "userId": 17, "totalComments": 1}, {"id": 29, "title": "Use of keyboard shortcuts in combat", "content": "Spam clicking can be annoying. Could be nice to be able to use keyboard shortcuts for combats.\n\nIf we could Press A to choose attack then M or R to choose [M]elee or [R]anged. Another example it pressing S for [S]kills and then choose the skill with [1], [2], [3] or [4].", "state": "Completed", "upvotes": 5, "downvotes": 0, "createdAt": "13/5/2024 13:22:40", "updatedAt": "21/5/2024 21:17:08", "userId": 77, "totalComments": 2}, {"id": 30, "title": "Indication when Task ready to be completed", "content": "Maybe add an icon on Tasks when a task requirement are met and can be completed. I'm often unsure if I'm done with the tasks and would like to have an indicator without having to open tasks.\n\nIt would also be satisfying seeing the indicator popup.", "state": "Accepted", "upvotes": 6, "downvotes": 0, "createdAt": "13/5/2024 15:11:04", "updatedAt": "1/7/2024 01:23:14", "userId": 77, "totalComments": 1}, {"id": 31, "title": "Exclude equipped items from shop sell tab ", "content": "As title suggests. Excluding equipped items from the shop sell tab allows for clarity when selling items.", "state": "Accepted", "upvotes": 8, "downvotes": 0, "createdAt": "13/5/2024 17:37:32", "updatedAt": "27/5/2024 06:07:29", "userId": 87, "totalComments": 4}, {"id": 32, "title": "Revive Support", "content": "The most common reasons for revives are being hospitalized as a result of being defeated by an NPC, other players, or bad luck.  And being sent to the hospital, your only option is to hope someone is willing to use their 2 daily revive skills, or wait out the timer. These hospital timer are dependent whether the attacker decides to mug, leave, or hospitalize you longer (further extended by the attackers' perk).\n\nThis opens up an avenue of alternative playstyle for students to provide their own services  or enjoy being a helping hand (this can include offering to heal at a price, healing fellow students in their friend/club, or playing a role of a healer within the academy). \n\nRevives can have a cost of 50 energy - whether successful or not - to attempt a chance at 1-100% success rate. 50 energy limiting to 2 heals every 1:40 hrs as the minimum energy cap is 100.\nSuccess rate can be factored by student gears (specialized gear set?), skills /perks in intelligence, or status of the patient can have a multitude of effects on the success rate at reviving the student. \n\ntl;dr certain students would enjoy a non-combat playstyle by being a supporter type.\n", "state": "New", "upvotes": 10, "downvotes": 3, "createdAt": "13/5/2024 18:48:06", "updatedAt": "6/7/2024 00:10:25", "userId": 89, "totalComments": 4}, {"id": 33, "title": "Display common/uncommon drops in each area/zone", "content": "Since it's a frequent question in chat, what would you guys think of having a list of possible common/uncommon drops before you start a streets run? I think having an idea of where common items for crafting drop and the chance to get them is going to help a lot of people (me included).", "state": "Completed", "upvotes": 6, "downvotes": 0, "createdAt": "14/5/2024 15:42:13", "updatedAt": "20/5/2024 18:29:06", "userId": 19, "totalComments": 6}, {"id": 34, "title": "Reduce cost of learning skills.", "content": "I think the cost of learning Talents/Skills should be unified so we can always get a new toy upon level up - it's a bit disheartening to have to level up multiple times just for one skill.", "state": "Denied", "upvotes": 1, "downvotes": 2, "createdAt": "14/5/2024 23:15:39", "updatedAt": "4/6/2024 08:34:30", "userId": 69, "totalComments": 4}, {"id": 35, "title": "Active buff skills", "content": "After thinking about the combat system a bit more, I feel that the comabt experienece feels more intuitive if the active buff skills such as <PERSON>, <PERSON><PERSON><PERSON>, High Guard, Recovery, Blood Frenzy does not take an 'attack' turn to cast.\n\nSo that you could still deal damage that turn/does not sacrifice a damage turn.", "state": "Denied", "upvotes": 7, "downvotes": 1, "createdAt": "15/5/2024 17:41:02", "updatedAt": "4/6/2024 08:34:19", "userId": 15, "totalComments": 8}, {"id": 36, "title": "Adjust XP won from battles", "content": "Winning a battle seems to five always 100XP. Maybe adjust it with the difficulty of the opponent ?", "state": "Completed", "upvotes": 5, "downvotes": 0, "createdAt": "15/5/2024 19:18:57", "updatedAt": "21/5/2024 21:17:34", "userId": 103, "totalComments": 3}, {"id": 37, "title": "Attack Round Counter", "content": "Add an attack round counter so it's easier to tell when the damage is about to start increasing ", "state": "Completed", "upvotes": 6, "downvotes": 2, "createdAt": "16/5/2024 22:26:19", "updatedAt": "20/5/2024 19:50:56", "userId": 88, "totalComments": 4}, {"id": 39, "title": "New Task ideas", "content": "Put em here. The wackier the better", "state": "Completed", "upvotes": 13, "downvotes": 1, "createdAt": "16/5/2024 23:40:05", "updatedAt": "5/7/2024 23:15:00", "userId": 3, "totalComments": 3}, {"id": 40, "title": "New Talent/Skill ideas", "content": "Put em here. The wackier the better", "state": "Completed", "upvotes": 12, "downvotes": 1, "createdAt": "16/5/2024 23:40:29", "updatedAt": "25/6/2024 09:18:49", "userId": 3, "totalComments": 4}, {"id": 41, "title": "Auto Battle", "content": "A Auto battle feature, eg.  watch an ad and get 30min of Auto battle. ", "state": "Denied", "upvotes": 2, "downvotes": 13, "createdAt": "18/5/2024 21:29:59", "updatedAt": "25/6/2024 06:44:51", "userId": 91, "totalComments": 7}, {"id": 42, "title": "Collect and Craft Again button when collecting a finished craft", "content": "A button next to the \"Collect\" button that would be something like \"Collect and craft another\".   Some items players will be crafting multiples of either for use or to craft another item.  I feel this will be a solid QoL to the crafting portion of the game.  ", "state": "Completed", "upvotes": 10, "downvotes": 0, "createdAt": "19/5/2024 20:13:04", "updatedAt": "4/7/2024 18:51:47", "userId": 87, "totalComments": 1}, {"id": 43, "title": "Hover over equipped item should display it's stat", "content": "Pretty annoying to have to go in inventory to check out the item  the stats of an item you have equipped. Should be able to see it directly in equipment.", "state": "Completed", "upvotes": 3, "downvotes": 0, "createdAt": "20/5/2024 00:22:56", "updatedAt": "20/5/2024 18:27:45", "userId": 77, "totalComments": 1}, {"id": 44, "title": "Show Shrine effect and timer in navigation bar", "content": "Somewhere on the site, when a shrine buff is active there should be an icon with a timer . On hover, the shrine effect should be detailed.", "state": "Completed", "upvotes": 3, "downvotes": 0, "createdAt": "20/5/2024 15:49:56", "updatedAt": "20/6/2024 00:32:16", "userId": 77, "totalComments": 1}, {"id": 45, "title": "Info on adventure locations ", "content": "A little info button on each location to see what it drops.", "state": "Completed", "upvotes": 2, "downvotes": 0, "createdAt": "20/5/2024 18:13:01", "updatedAt": "18/6/2024 03:26:56", "userId": 91, "totalComments": 1}, {"id": 46, "title": "PVP Attack Should Cost 1 AP", "content": "A single action should only cost 1 ACTION Point.", "state": "Denied", "upvotes": 4, "downvotes": 3, "createdAt": "21/5/2024 01:33:35", "updatedAt": "3/6/2024 15:25:21", "userId": 88, "totalComments": 1}, {"id": 47, "title": "Make chat messages longer", "content": "See title.", "state": "Completed", "upvotes": 4, "downvotes": 0, "createdAt": "21/5/2024 21:40:19", "updatedAt": "22/5/2024 01:04:27", "userId": 15, "totalComments": 1}, {"id": 48, "title": "Roleplay starts with /me", "content": "For those who enjoy creating and playing a role.\nOptional: selectable font colors for varied roleplayers.\n\nInput: /me stands sluggishly at the school's gate.\nOutput: 𝘚𝘤𝘩𝘶𝘭𝘵𝘻 𝘴𝘵𝘢𝘯𝘥𝘴 𝘴𝘭𝘶𝘨𝘨𝘪𝘴𝘩𝘭𝘺 𝘢𝘵 𝘵𝘩𝘦 𝘴𝘤𝘩𝘰𝘰𝘭'𝘴 𝘨𝘢𝘵𝘦.\n\n\n\nAdditionally, below is a list of some ideas for RP enhancements.\n· Expand the about me section on player profile for those who like to write a longer biograph/flavor text. From descriptors about their characters, or other long text for their usage.\n· Dice roll system? For the classic D&D style roleplay for the more gamified way of roleplaying.\n· Chat rooms, these rooms have the potential to be used in text RP settings. Users can set the name, room description, and theme them for public or private use. \n", "state": "Accepted", "upvotes": 4, "downvotes": 0, "createdAt": "21/5/2024 22:02:07", "updatedAt": "25/5/2024 05:08:04", "userId": 89, "totalComments": 2}, {"id": 50, "title": "Craft 2 Button", "content": "When you have unlocked the 2. crafting slot there should be a button to put the recipe in both slots. 🙏", "state": "Completed", "upvotes": 4, "downvotes": 1, "createdAt": "22/5/2024 17:02:29", "updatedAt": "4/7/2024 18:51:29", "userId": 91, "totalComments": 1}, {"id": 51, "title": "Change the daily reset text", "content": "From:\n\nThe Shrine goal has reset! Today's donation goal is ¥[]\n\nTo:\n\nThe Shrine goal and Daily Tasks have reset! Today's shrine donation goal is ¥[]", "state": "Accepted", "upvotes": 8, "downvotes": 0, "createdAt": "23/5/2024 00:25:51", "updatedAt": "28/5/2024 01:53:43", "userId": 69, "totalComments": 0}, {"id": 52, "title": "Afk Training Talent", "content": "I think alot of people like me can't check the phone alot though work hours or when youre asleep.\n\nI'm not saying full Afk Training but maybe have a talent that trains 20% of your energy when youre offline.", "state": "New", "upvotes": 9, "downvotes": 4, "createdAt": "23/5/2024 20:45:52", "updatedAt": "24/6/2024 00:45:35", "userId": 140, "totalComments": 2}, {"id": 53, "title": "AP Bank", "content": "Be able to store a small amount of AP for later use.\n\nMaybe a talent?", "state": "Completed", "upvotes": 5, "downvotes": 0, "createdAt": "23/5/2024 21:16:18", "updatedAt": "20/6/2024 00:31:57", "userId": 140, "totalComments": 1}, {"id": 54, "title": "Ability to use money in your bank to bail yourself out", "content": "I think you should be able to access the money in your bank to bail yourself out when you're in jail.", "state": "New", "upvotes": 15, "downvotes": 1, "createdAt": "24/5/2024 20:24:32", "updatedAt": "11/7/2024 06:00:30", "userId": 12, "totalComments": 3}, {"id": 55, "title": "get rid of suggestion quest", "content": "i suggest that the suggestion quest gets removed because i dont have any suggestions (except this one) and i want it to go away\nalso this is (not) a throwaway suggestion so it will leave me alone", "state": "Denied", "upvotes": 3, "downvotes": 2, "createdAt": "25/5/2024 15:42:18", "updatedAt": "29/5/2024 04:30:44", "userId": 118, "totalComments": 2}, {"id": 56, "title": "Indication of Crafting Complete", "content": "I suggest adding a little indicator on the top corner of the workshop button when an item is completed. I find it easy to forget that I was crafting something", "state": "Completed", "upvotes": 3, "downvotes": 0, "createdAt": "25/5/2024 16:20:04", "updatedAt": "28/5/2024 15:58:48", "userId": 143, "totalComments": 1}, {"id": 57, "title": "Better notifications", "content": "Better notifications for when bars are full. Aka health is filled or stamina.", "state": "Denied", "upvotes": 8, "downvotes": 2, "createdAt": "26/5/2024 05:11:20", "updatedAt": "12/6/2024 00:06:30", "userId": 99, "totalComments": 1}, {"id": 58, "title": "Faculty List Sorting", "content": "It is honestly a no brainer, specially if we want more players later on (and even more on full release).\n\nFirst off, a quick way of sorting by clicking on the itens on the top of the list. If you ckick on \"name\" it'll sorty aphabetically, if  you click on \"lvl\" it sort by level, if you click \"type\", it'll sort by thesse categories and so go on (you get the gist).\n\nSecond: Paging on the same area, maybe showing 50, or 100 itens in the same page (or even better, let the player decide the ammount per page)\n\nThird (that can be added later on): An actual search function.", "state": "Completed", "upvotes": 4, "downvotes": 0, "createdAt": "26/5/2024 12:18:45", "updatedAt": "28/5/2024 01:53:14", "userId": 48, "totalComments": 1}, {"id": 59, "title": "Selling Multiple Items", "content": "Able to select multiple items to sell instead of one by one", "state": "Accepted", "upvotes": 6, "downvotes": 0, "createdAt": "26/5/2024 15:24:07", "updatedAt": "27/6/2024 01:59:46", "userId": 156, "totalComments": 0}, {"id": 60, "title": "Increase Max Energy and AP", "content": "Please increase the max energy and AP so  players can sleep at night or go to work without wasting energy or AP. ", "state": "Denied", "upvotes": 6, "downvotes": 2, "createdAt": "26/5/2024 22:51:50", "updatedAt": "1/7/2024 01:24:14", "userId": 156, "totalComments": 2}, {"id": 61, "title": "Items Location", "content": "Able to click on the item and it will show you where to grt it. For example, click on the eggs and it will tell you that it drops from \"Zone 5 at School, General Store\". This will help people farm materials for crafting or doing quests.", "state": "Accepted", "upvotes": 12, "downvotes": 1, "createdAt": "27/5/2024 13:14:54", "updatedAt": "31/5/2024 12:36:40", "userId": 156, "totalComments": 0}, {"id": 62, "title": "Skill timers", "content": "The fact that a 4 turn skill wastes one of its turns just being turned on is kinda pointless. 40 energy for 3 attacks when the skill read thats you will get 4 attacks for 40 energy. ", "state": "New", "upvotes": 14, "downvotes": 0, "createdAt": "27/5/2024 17:29:25", "updatedAt": "29/6/2024 07:38:58", "userId": 99, "totalComments": 1}, {"id": 63, "title": "Close toast notifications on click or click on a X", "content": "On mobile this is more annoying. You get multiple toast notifications that block content and you have to wait until they disappear to be able to continue. Maybe add a X top right or just close on click.", "state": "Completed", "upvotes": 3, "downvotes": 0, "createdAt": "28/5/2024 15:55:58", "updatedAt": "7/6/2024 06:25:03", "userId": 77, "totalComments": 1}, {"id": 64, "title": "Gang Content", "content": "Other than gang wars + gang hideout/chat box, what would you like to see in the way of gang Content?\n\n(Gangs are player guilds)\n", "state": "Completed", "upvotes": 13, "downvotes": 0, "createdAt": "28/5/2024 23:55:08", "updatedAt": "9/6/2024 05:34:35", "userId": 3, "totalComments": 5}, {"id": 65, "title": "All-in button in casino", "content": "A button at the casino that allows you to put all the money you currently have in your pocket in the slot machine!", "state": "Completed", "upvotes": 9, "downvotes": 2, "createdAt": "29/5/2024 20:19:14", "updatedAt": "4/7/2024 18:52:25", "userId": 12, "totalComments": 1}, {"id": 66, "title": "Notification when you win the lottery", "content": "I didn't even know I won until I checked the global chat backlog, lol.", "state": "Accepted", "upvotes": 4, "downvotes": 0, "createdAt": "30/5/2024 23:24:54", "updatedAt": "3/7/2024 15:15:32", "userId": 69, "totalComments": 0}, {"id": 68, "title": "Background music", "content": "Small background music especially when on phone", "state": "Denied", "upvotes": 3, "downvotes": 5, "createdAt": "31/5/2024 04:53:56", "updatedAt": "3/6/2024 14:47:20", "userId": 161, "totalComments": 1}, {"id": 69, "title": "Chat reply feature", "content": "A feature that lets you reply to messages in the chat so you don't need to quote them in your message", "state": "Completed", "upvotes": 6, "downvotes": 0, "createdAt": "1/6/2024 01:50:31", "updatedAt": "20/6/2024 23:06:51", "userId": 49, "totalComments": 2}, {"id": 70, "title": "Adventure zone", "content": "After I complete a zone, I'd like to redo the same zone again, but the number updates automatically to the next one. Is it possible to have a choice to redo the same zone without having to change that number over and over?", "state": "Completed", "upvotes": 12, "downvotes": 0, "createdAt": "2/6/2024 09:37:25", "updatedAt": "4/7/2024 15:38:49", "userId": 57, "totalComments": 6}, {"id": 71, "title": "Refresh button in hospital", "content": "When I have daily task to attack student #9 several. I wait for hospital timer to go down. Adding refresh button would make it easier.", "state": "Completed", "upvotes": 3, "downvotes": 1, "createdAt": "3/6/2024 01:14:21", "updatedAt": "27/6/2024 01:58:57", "userId": 105, "totalComments": 1}, {"id": 72, "title": "Small change to UI", "content": "When fighting, the text in each box, like \"attack\" or \"skills\" or \"run\" is highlightable text, on the phone when spamming attack in a fight highlights the text for it to be copied or whatever happens when you highlight the text on the phone. \n\nSmall annoyance! ", "state": "Completed", "upvotes": 4, "downvotes": 0, "createdAt": "6/6/2024 06:00:53", "updatedAt": "7/6/2024 16:33:45", "userId": 172, "totalComments": 2}, {"id": 73, "title": "Gang members list", "content": "Please make it so the list is linked to their profiles.", "state": "Completed", "upvotes": 3, "downvotes": 0, "createdAt": "6/6/2024 21:17:15", "updatedAt": "27/6/2024 02:07:27", "userId": 15, "totalComments": 0}, {"id": 74, "title": "More detailed information for player lists", "content": "More details such as level, gang while in hospital/jail.\n\nAlso maybe an elapsed time for certain cases, for those who are too lazy to work out (backwards).", "state": "Completed", "upvotes": 4, "downvotes": 1, "createdAt": "6/6/2024 21:21:21", "updatedAt": "9/6/2024 00:03:19", "userId": 15, "totalComments": 1}, {"id": 75, "title": "Further gang display improvements", "content": "Highly likely that this is already being worked on.\n\nWhen we access gang list -> gang info, could all the members of that gang be displayed?", "state": "Accepted", "upvotes": 11, "downvotes": 0, "createdAt": "7/6/2024 20:34:58", "updatedAt": "24/6/2024 00:36:37", "userId": 15, "totalComments": 0}, {"id": 76, "title": "Confirmation for Log Out Button", "content": "I keep accidentally pressing it and am getting logged out, so can you add a confirmation to it?", "state": "Completed", "upvotes": 6, "downvotes": 0, "createdAt": "8/6/2024 13:09:31", "updatedAt": "5/7/2024 02:02:25", "userId": 179, "totalComments": 1}, {"id": 77, "title": "Weekend Buff or Events", "content": "Most people are off on the weekend.  Give a world buff like the shrine for free on weekend to encourage players to be more active. ", "state": "New", "upvotes": 11, "downvotes": 0, "createdAt": "9/6/2024 17:12:26", "updatedAt": "11/7/2024 19:21:20", "userId": 156, "totalComments": 0}, {"id": 78, "title": "Removing Sunday Shop Items Limit", "content": "Since you already have the limit buy for each player, I don't see the need to set a limit on the quantity.  By setting the limit on the quantity, you're giving an advantage to certain players and not others. ", "state": "New", "upvotes": 13, "downvotes": 0, "createdAt": "9/6/2024 17:16:23", "updatedAt": "11/7/2024 06:00:15", "userId": 156, "totalComments": 2}, {"id": 79, "title": "Rewards for Alpha Testers", "content": "Looking for some suggestions for rewards that players want  for alpha testing that carry over to release. Other than profile badge etc\n", "state": "Completed", "upvotes": 11, "downvotes": 1, "createdAt": "11/6/2024 00:31:39", "updatedAt": "3/7/2024 11:28:12", "userId": 3, "totalComments": 3}, {"id": 80, "title": "Show how many in inventory ", "content": "When looking at items in shop it would be helpful if it showed how many you already have in your inventory", "state": "Accepted", "upvotes": 8, "downvotes": 0, "createdAt": "12/6/2024 14:02:06", "updatedAt": "19/6/2024 02:08:55", "userId": 17, "totalComments": 0}, {"id": 81, "title": "Make level requirements clearer in shops", "content": "Some sort of indicator if you're not a high enough level to equip an item before purchase ('requires level x' in red perhaps, changing to green when level is reached).\n\n- On starting I spent all my money on gear I couldn't equip for a long time, which annoyed me as I could have spent it elsewhere.", "state": "Accepted", "upvotes": 7, "downvotes": 0, "createdAt": "12/6/2024 14:06:26", "updatedAt": "4/7/2024 18:57:19", "userId": 181, "totalComments": 0}, {"id": 82, "title": "An all/max button when depositing into bank", "content": "I feel like this might have been said before but an all/max button when you are depositing into your bank so you don't have to type the exact amount that is in your pockets every time.", "state": "Completed", "upvotes": 8, "downvotes": 2, "createdAt": "14/6/2024 11:15:35", "updatedAt": "27/6/2024 02:07:18", "userId": 12, "totalComments": 0}, {"id": 83, "title": "Indicator for completed tasks", "content": "And say 'completed' rather than 'in progress'.", "state": "New", "upvotes": 12, "downvotes": 0, "createdAt": "14/6/2024 14:35:53", "updatedAt": "11/7/2024 06:00:21", "userId": 181, "totalComments": 3}, {"id": 84, "title": "Compare currently equipped gear to shop gear", "content": "Show the difference between item you're viewing in the shop and your equipped item of the same type", "state": "Completed", "upvotes": 11, "downvotes": 0, "createdAt": "16/6/2024 12:35:54", "updatedAt": "3/7/2024 11:28:03", "userId": 181, "totalComments": 1}, {"id": 85, "title": "Allow Leaving Gangs immediately", "content": "Suggestion to allow leaving gangs immediately after joining, but having a 48h timer after you leave so you still cannot jump like a toad. Currently description says 'You cannot leave a gang until waiting 48 hours after joining.'", "state": "Accepted", "upvotes": 7, "downvotes": 0, "createdAt": "17/6/2024 15:20:52", "updatedAt": "6/7/2024 00:10:46", "userId": 195, "totalComments": 2}, {"id": 86, "title": "Atk timer after res", "content": "Should the person who gets ressed be subjected to the same cooldown, aka unable to atk for 10mins after getting ressed?\n\nShouldn't it only work defensively for the recipient of the revive?", "state": "Denied", "upvotes": 3, "downvotes": 1, "createdAt": "18/6/2024 20:06:45", "updatedAt": "12/7/2024 02:57:38", "userId": 15, "totalComments": 2}, {"id": 87, "title": "Alpha End Date", "content": "I've noticed there have been a few players getting burned out and quitting lately, so I thought it would be best to create a discussion on ending the Alpha in the next month or so.\n\nDoesn't feel like a good idea to invite more new players at this stage of the Alpha and would benefit from returning with a fresh Beta build in a few months.\n\nAlso thinking about holding an end of Alpha event with rewards that can carry over to <PERSON>, as well as giving out rewards based on leaderboard rankings.\n\nAny thoughts?", "state": "Completed", "upvotes": 10, "downvotes": 0, "createdAt": "19/6/2024 20:54:13", "updatedAt": "27/6/2024 02:05:38", "userId": 3, "totalComments": 4}, {"id": 88, "title": "Heal count", "content": "Make the heal count button actively go down without refresh in the infirmary injury page (for healing others).\n\nAt the moment it does not update unless page is refreshed.", "state": "Completed", "upvotes": 3, "downvotes": 0, "createdAt": "20/6/2024 18:13:34", "updatedAt": "5/7/2024 02:02:09", "userId": 15, "totalComments": 1}, {"id": 89, "title": "equipment order", "content": "reorder the list of equipment  increasing or decreasing based on each value, like level armor, dmg...", "state": "Completed", "upvotes": 4, "downvotes": 0, "createdAt": "20/6/2024 21:40:14", "updatedAt": "11/7/2024 01:39:06", "userId": 196, "totalComments": 1}, {"id": 90, "title": "Skill turn count issue", "content": "I wanted to state this but I was pretty sure it was already mentioned but here it goes anyway.\n\nSkills with turn duration aways count the round where you used it as one of them and I dont think it should happen, take for example the 2 skills that have the \"stun for 2 turns\" effect.    \n\n>You use it\n>Get hit anyway\n<end turn 1>\n>you act\n>enemy actually stunned\n<end turn 2 (and effect)>\n>Get hit\n\nIts effectivelly just a single round of actual effect.  \n\nI just want to suggest that these skils do not count the round you activated them in their timer (unless the effect actually happen in said turn), everything else can stay the same, thats all xD", "state": "Denied", "upvotes": 8, "downvotes": 0, "createdAt": "22/6/2024 19:58:44", "updatedAt": "3/7/2024 15:14:43", "userId": 48, "totalComments": 3}, {"id": 91, "title": "Thoughts on Injuries?", "content": "Forgot to include it in the Poll but wanted to get an idea of what people think of injuries.\n\nIs it better than the previous hospitalisation system?\n\nIs it too complicated? \n\nShould the treatments be less complicated? (items that treat injury tier e.g. Minor Injuries as opposed to Injury type e.g. Concussion)", "state": "New", "upvotes": 4, "downvotes": 1, "createdAt": "10/7/2024 13:48:48", "updatedAt": "12/7/2024 02:57:20", "userId": 3, "totalComments": 2}]