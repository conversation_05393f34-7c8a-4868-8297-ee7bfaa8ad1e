import { api } from "@/helpers/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";

// Query hook for getting current map
export const useGetCurrentMap = (options = {}) => {
    return useQuery(
        api.roguelike.currentMap.queryOptions({
            staleTime: 30000, // 30 seconds
            ...options,
        })
    );
};

// Mutation hook for beginning a new run
export const useBeginRun = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.roguelike.begin.mutationOptions({
            onSuccess: (data) => {
                // Update the current map query with the new map data
                queryClient.setQueryData(api.roguelike.currentMap.key(), data);

                // Invalidate user info to update roguelike level
                queryClient.invalidateQueries({
                    queryKey: api.user.currentUserInfo.key(),
                });
            },
            onError: (error) => {
                console.error("Begin run error:", error);
                toast.error(error.message || "Failed to begin run");
            },
        })
    );
};

// Mutation hook for advancing to next node
export const useAdvance = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.roguelike.advance.mutationOptions({
            onSuccess: (data) => {
                // Invalidate current map to get updated state
                queryClient.invalidateQueries({
                    queryKey: api.roguelike.currentMap.key(),
                });

                // Invalidate user info to update action points
                queryClient.invalidateQueries({
                    queryKey: api.user.currentUserInfo.key(),
                });
            },
            onError: (error) => {
                console.error("Advance error:", error);
                toast.error(error.message || "Failed to advance");
            },
        })
    );
};

// Mutation hook for activating current node
export const useActivateNode = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    return useMutation(
        api.roguelike.activateNode.mutationOptions({
            onSuccess: (data) => {
                // Invalidate current map to get updated state
                queryClient.invalidateQueries({
                    queryKey: api.roguelike.currentMap.key(),
                });

                // Invalidate user info to update any changes
                queryClient.invalidateQueries({
                    queryKey: api.user.currentUserInfo.key(),
                });

                // Handle navigation for battles
                if (data?.encounterType === "battle" || data?.encounterType === "boss") {
                    navigate("/fight");
                }
            },
            onError: (error) => {
                console.error("Activate node error:", error);
                toast.error(error.message || "Failed to activate node");
            },
        })
    );
};

// Mutation hook for choosing scavenge option
export const useChooseScavengeOption = (setScavengeResult?: (data: any) => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.roguelike.scavengeOption.mutationOptions({
            onSuccess: (data) => {
                // Update scavenge result if callback provided
                if (setScavengeResult) {
                    setScavengeResult(data);
                }

                // Invalidate current map to get updated state
                queryClient.invalidateQueries({
                    queryKey: api.roguelike.currentMap.key(),
                });

                // Invalidate user info to update inventory/stats
                queryClient.invalidateQueries({
                    queryKey: api.user.currentUserInfo.key(),
                });
            },
            onError: (error) => {
                console.error("Scavenge option error:", error);
                toast.error(error.message || "Failed to choose scavenge option");
            },
        })
    );
};
