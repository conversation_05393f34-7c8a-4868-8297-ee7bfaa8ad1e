import { db } from "../lib/db.js"; // prisma
import { logger } from "../utils/log.js";
import { Prisma } from "@prisma/client";

const UserAchievements = db.user_achievements;
type UserAchievementType = Prisma.user_achievementsGetPayload<object>;

/**
 * Increments a specified achievement type for a user by a given amount.
 * If the user does not have an achievement record, it creates one.
 */
export const UpdateUserAchievement = async (
    userId: number,
    achievementType: keyof UserAchievementType,
    amount = 1
): Promise<UserAchievementType> => {
    try {
        const userAchievements = await UserAchievements.upsert({
            where: { userId },
            create: { userId },
            update: {},
        });

        if (!userAchievements) {
            throw new Error(`No achievement record found or created`);
        }

        // Validate that the achievement type exists
        if (!(achievementType in userAchievements)) {
            throw new Error(`Invalid achievement type: ${achievementType}`);
        }

        // Increment the achievement
        const updatedAchievements = await UserAchievements.update({
            where: { userId },
            data: {
                [achievementType]: {
                    increment: amount,
                },
            },
        });

        return updatedAchievements;
    } catch (error) {
        logger.error(`Failed to increment achievement ${achievementType} for user ${userId}: ` + error);
        throw error;
    }
};

/**
 * Retrieves the specified achievement count for a given user.
 *
 * @param userId - The ID of the user whose achievement is being queried.
 * @param achievementType - The type of achievement to retrieve, must be a valid key of UserAchievementType.
 * @returns A promise that resolves to the count of the specified achievement, or 0 if no achievements are found.
 * @throws Error if the achievement type is invalid or if there is an issue retrieving the user's achievements.
 */
export const GetUserAchievement = async (userId: number, achievementType: keyof UserAchievementType) => {
    try {
        const userAchievements = await UserAchievements.findUnique({
            where: { userId },
        });

        if (!userAchievements) {
            return 0;
        }

        // Validate that the achievement type exists
        if (!(achievementType in userAchievements)) {
            throw new Error(`Invalid achievement type: ${achievementType}`);
        }

        return userAchievements[achievementType];
    } catch (error) {
        logger.error(`Failed to get achievement ${achievementType} for user ${userId}: ` + error);
        throw error;
    }
};

export default { UpdateUserAchievement, GetUserAchievement };
