const crafting_inputs = [
    {
        itemId: 185,
        count: 1,
        itemType: "input",
        craftingRecipeId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 197,
        count: 6,
        itemType: "input",
        craftingRecipeId: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 2,
        itemType: "input",
        craftingRecipeId: 3,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 2,
        itemType: "input",
        craftingRecipeId: 4,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 3,
        itemType: "input",
        craftingRecipeId: 5,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 3,
        itemType: "input",
        craftingRecipeId: 6,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 204,
        count: 2,
        itemType: "input",
        craftingRecipeId: 7,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        count: 3,
        itemType: "input",
        craftingRecipeId: 8,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 191,
        count: 4,
        itemType: "input",
        craftingRecipeId: 9,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 3,
        itemType: "input",
        craftingRecipeId: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 194,
        count: 1,
        itemType: "input",
        craftingRecipeId: 11,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 196,
        count: 4,
        itemType: "input",
        craftingRecipeId: 12,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 196,
        count: 2,
        itemType: "input",
        craftingRecipeId: 13,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 209,
        count: 1,
        itemType: "input",
        craftingRecipeId: 14,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 188,
        count: 1,
        itemType: "input",
        craftingRecipeId: 15,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 188,
        count: 1,
        itemType: "input",
        craftingRecipeId: 16,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 210,
        count: 2,
        itemType: "input",
        craftingRecipeId: 17,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 207,
        count: 1,
        itemType: "input",
        craftingRecipeId: 18,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 197,
        count: 3,
        itemType: "input",
        craftingRecipeId: 19,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 1,
        itemType: "input",
        craftingRecipeId: 20,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        count: 1,
        itemType: "input",
        craftingRecipeId: 21,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 2,
        itemType: "input",
        craftingRecipeId: 22,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 203,
        count: 1,
        itemType: "input",
        craftingRecipeId: 23,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 197,
        count: 5,
        itemType: "input",
        craftingRecipeId: 24,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 4,
        itemType: "input",
        craftingRecipeId: 25,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        count: 2,
        itemType: "input",
        craftingRecipeId: 26,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 2,
        itemType: "input",
        craftingRecipeId: 27,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 207,
        count: 1,
        itemType: "input",
        craftingRecipeId: 28,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 1,
        itemType: "input",
        craftingRecipeId: 29,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 203,
        count: 1,
        itemType: "input",
        craftingRecipeId: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 169,
        count: 1,
        itemType: "input",
        craftingRecipeId: 31,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 170,
        count: 1,
        itemType: "input",
        craftingRecipeId: 32,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 1,
        itemType: "input",
        craftingRecipeId: 35,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 3,
        itemType: "input",
        craftingRecipeId: 36,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 2,
        itemType: "input",
        craftingRecipeId: 37,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 3,
        itemType: "input",
        craftingRecipeId: 38,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 2,
        itemType: "input",
        craftingRecipeId: 39,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 197,
        count: 6,
        itemType: "input",
        craftingRecipeId: 40,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 2,
        itemType: "input",
        craftingRecipeId: 41,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 197,
        count: 8,
        itemType: "input",
        craftingRecipeId: 42,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 2,
        itemType: "input",
        craftingRecipeId: 43,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 197,
        count: 8,
        itemType: "input",
        craftingRecipeId: 44,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 2,
        itemType: "input",
        craftingRecipeId: 45,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        count: 2,
        itemType: "input",
        craftingRecipeId: 46,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 3,
        itemType: "input",
        craftingRecipeId: 47,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        count: 1,
        itemType: "input",
        craftingRecipeId: 48,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 3,
        itemType: "input",
        craftingRecipeId: 49,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 200,
        count: 1,
        itemType: "input",
        craftingRecipeId: 50,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 4,
        itemType: "input",
        craftingRecipeId: 51,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 3,
        itemType: "input",
        craftingRecipeId: 52,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 3,
        itemType: "input",
        craftingRecipeId: 53,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 3,
        itemType: "input",
        craftingRecipeId: 54,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 205,
        count: 2,
        itemType: "input",
        craftingRecipeId: 55,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 2,
        itemType: "input",
        craftingRecipeId: 56,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 1,
        itemType: "input",
        craftingRecipeId: 57,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 1,
        itemType: "input",
        craftingRecipeId: 58,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 1,
        itemType: "input",
        craftingRecipeId: 59,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 211,
        count: 1,
        itemType: "input",
        craftingRecipeId: 60,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 212,
        count: 1,
        itemType: "input",
        craftingRecipeId: 61,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 1,
        itemType: "input",
        craftingRecipeId: 62,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 207,
        count: 1,
        itemType: "input",
        craftingRecipeId: 63,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 182,
        count: 1,
        itemType: "input",
        craftingRecipeId: 66,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 186,
        count: 1,
        itemType: "input",
        craftingRecipeId: 67,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 126,
        count: 1,
        itemType: "input",
        craftingRecipeId: 68,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 183,
        count: 1,
        itemType: "input",
        craftingRecipeId: 69,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 230,
        count: 1,
        itemType: "input",
        craftingRecipeId: 70,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 195,
        count: 4,
        itemType: "input",
        craftingRecipeId: 73,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 164,
        count: 5,
        itemType: "input",
        craftingRecipeId: 74,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 215,
        count: 1,
        itemType: "input",
        craftingRecipeId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 217,
        count: 1,
        itemType: "input",
        craftingRecipeId: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 2,
        itemType: "input",
        craftingRecipeId: 3,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 201,
        count: 1,
        itemType: "input",
        craftingRecipeId: 4,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 220,
        count: 1,
        itemType: "input",
        craftingRecipeId: 5,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 223,
        count: 1,
        itemType: "input",
        craftingRecipeId: 6,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 221,
        count: 1,
        itemType: "input",
        craftingRecipeId: 7,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 221,
        count: 1,
        itemType: "input",
        craftingRecipeId: 9,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 221,
        count: 1,
        itemType: "input",
        craftingRecipeId: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 225,
        count: 1,
        itemType: "input",
        craftingRecipeId: 11,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 201,
        count: 1,
        itemType: "input",
        craftingRecipeId: 12,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        count: 2,
        itemType: "input",
        craftingRecipeId: 13,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 205,
        count: 1,
        itemType: "input",
        craftingRecipeId: 14,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 213,
        count: 1,
        itemType: "input",
        craftingRecipeId: 15,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 206,
        count: 1,
        itemType: "input",
        craftingRecipeId: 16,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 225,
        count: 1,
        itemType: "input",
        craftingRecipeId: 17,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 209,
        count: 2,
        itemType: "input",
        craftingRecipeId: 18,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 216,
        count: 1,
        itemType: "input",
        craftingRecipeId: 19,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 217,
        count: 1,
        itemType: "input",
        craftingRecipeId: 20,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 219,
        count: 1,
        itemType: "input",
        craftingRecipeId: 21,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 200,
        count: 1,
        itemType: "input",
        craftingRecipeId: 22,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 205,
        count: 2,
        itemType: "input",
        craftingRecipeId: 23,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 215,
        count: 1,
        itemType: "input",
        craftingRecipeId: 24,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 216,
        count: 2,
        itemType: "input",
        craftingRecipeId: 25,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 206,
        count: 1,
        itemType: "input",
        craftingRecipeId: 26,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 200,
        count: 2,
        itemType: "input",
        craftingRecipeId: 27,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 2,
        itemType: "input",
        craftingRecipeId: 28,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 193,
        count: 2,
        itemType: "input",
        craftingRecipeId: 29,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 116,
        count: 1,
        itemType: "input",
        craftingRecipeId: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 1,
        itemType: "input",
        craftingRecipeId: 35,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 2,
        itemType: "input",
        craftingRecipeId: 36,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 1,
        itemType: "input",
        craftingRecipeId: 37,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 1,
        itemType: "input",
        craftingRecipeId: 38,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 1,
        itemType: "input",
        craftingRecipeId: 39,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 2,
        itemType: "input",
        craftingRecipeId: 40,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 2,
        itemType: "input",
        craftingRecipeId: 41,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 2,
        itemType: "input",
        craftingRecipeId: 42,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 2,
        itemType: "input",
        craftingRecipeId: 43,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 228,
        count: 2,
        itemType: "input",
        craftingRecipeId: 44,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 1,
        itemType: "input",
        craftingRecipeId: 45,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 1,
        itemType: "input",
        craftingRecipeId: 46,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 1,
        itemType: "input",
        craftingRecipeId: 47,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 3,
        itemType: "input",
        craftingRecipeId: 48,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        count: 1,
        itemType: "input",
        craftingRecipeId: 49,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 1,
        itemType: "input",
        craftingRecipeId: 50,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 2,
        itemType: "input",
        craftingRecipeId: 51,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 1,
        itemType: "input",
        craftingRecipeId: 52,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 2,
        itemType: "input",
        craftingRecipeId: 53,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 1,
        itemType: "input",
        craftingRecipeId: 54,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 3,
        itemType: "input",
        craftingRecipeId: 55,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 205,
        count: 1,
        itemType: "input",
        craftingRecipeId: 56,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 205,
        count: 1,
        itemType: "input",
        craftingRecipeId: 57,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 205,
        count: 1,
        itemType: "input",
        craftingRecipeId: 58,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 205,
        count: 1,
        itemType: "input",
        craftingRecipeId: 59,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 1,
        itemType: "input",
        craftingRecipeId: 60,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 204,
        count: 1,
        itemType: "input",
        craftingRecipeId: 61,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        count: 1,
        itemType: "input",
        craftingRecipeId: 62,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 121,
        count: 1,
        itemType: "input",
        craftingRecipeId: 63,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 187,
        count: 1,
        itemType: "input",
        craftingRecipeId: 66,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 227,
        count: 1,
        itemType: "input",
        craftingRecipeId: 67,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 128,
        count: 1,
        itemType: "input",
        craftingRecipeId: 68,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 184,
        count: 1,
        itemType: "input",
        craftingRecipeId: 69,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 182,
        count: 3,
        itemType: "input",
        craftingRecipeId: 70,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 131,
        count: 2,
        itemType: "input",
        craftingRecipeId: 73,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 134,
        count: 3,
        itemType: "input",
        craftingRecipeId: 74,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 217,
        count: 1,
        itemType: "input",
        craftingRecipeId: 4,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 221,
        count: 1,
        itemType: "input",
        craftingRecipeId: 5,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 218,
        count: 1,
        itemType: "input",
        craftingRecipeId: 6,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 217,
        count: 1,
        itemType: "input",
        craftingRecipeId: 7,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 229,
        count: 1,
        itemType: "input",
        craftingRecipeId: 12,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 229,
        count: 1,
        itemType: "input",
        craftingRecipeId: 13,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 223,
        count: 1,
        itemType: "input",
        craftingRecipeId: 14,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 216,
        count: 2,
        itemType: "input",
        craftingRecipeId: 15,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 216,
        count: 2,
        itemType: "input",
        craftingRecipeId: 16,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 229,
        count: 1,
        itemType: "input",
        craftingRecipeId: 18,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 215,
        count: 1,
        itemType: "input",
        craftingRecipeId: 19,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 218,
        count: 1,
        itemType: "input",
        craftingRecipeId: 22,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 224,
        count: 1,
        itemType: "input",
        craftingRecipeId: 23,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 215,
        count: 1,
        itemType: "input",
        craftingRecipeId: 25,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 219,
        count: 1,
        itemType: "input",
        craftingRecipeId: 26,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 222,
        count: 1,
        itemType: "input",
        craftingRecipeId: 27,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 221,
        count: 1,
        itemType: "input",
        craftingRecipeId: 28,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 217,
        count: 1,
        itemType: "input",
        craftingRecipeId: 29,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 1,
        itemType: "input",
        craftingRecipeId: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 45,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 46,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 47,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 48,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 49,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 50,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 51,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 52,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 53,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 214,
        count: 1,
        itemType: "input",
        craftingRecipeId: 54,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 224,
        count: 1,
        itemType: "input",
        craftingRecipeId: 55,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 2,
        itemType: "input",
        craftingRecipeId: 56,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 2,
        itemType: "input",
        craftingRecipeId: 57,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 4,
        itemType: "input",
        craftingRecipeId: 58,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        count: 2,
        itemType: "input",
        craftingRecipeId: 59,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 217,
        count: 1,
        itemType: "input",
        craftingRecipeId: 60,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 217,
        count: 1,
        itemType: "input",
        craftingRecipeId: 61,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 217,
        count: 1,
        itemType: "input",
        craftingRecipeId: 62,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 1,
        itemType: "input",
        craftingRecipeId: 63,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 195,
        count: 1,
        itemType: "input",
        craftingRecipeId: 68,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 226,
        count: 1,
        itemType: "input",
        craftingRecipeId: 69,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 187,
        count: 2,
        itemType: "input",
        craftingRecipeId: 70,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 227,
        count: 1,
        itemType: "input",
        craftingRecipeId: 73,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 227,
        count: 1,
        itemType: "input",
        craftingRecipeId: 74,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 224,
        count: 1,
        itemType: "input",
        craftingRecipeId: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 224,
        count: 1,
        itemType: "input",
        craftingRecipeId: 56,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 224,
        count: 1,
        itemType: "input",
        craftingRecipeId: 57,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 224,
        count: 1,
        itemType: "input",
        craftingRecipeId: 58,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 224,
        count: 1,
        itemType: "input",
        craftingRecipeId: 59,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 224,
        count: 1,
        itemType: "input",
        craftingRecipeId: 63,
        createdAt: new Date(),
        updatedAt: new Date(),
    },

    {
        itemId: 162,
        count: 1,
        itemType: "input",
        craftingRecipeId: 72,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 197,
        count: 2,
        itemType: "input",
        craftingRecipeId: 72,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 215,
        count: 1,
        itemType: "input",
        craftingRecipeId: 72,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
];

export default crafting_inputs;
