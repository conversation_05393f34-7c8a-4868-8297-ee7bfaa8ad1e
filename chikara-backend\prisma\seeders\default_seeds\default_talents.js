const default_talents = [
    {
        // strength talents
        name: "melee_damage_increase",
        displayName: "Brute Force",
        talentType: "passive",
        tree: "strength",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.1,
        description: "Increases your melee damage by 10%",
    },
    {
        name: "berserker",
        displayName: "Executioner",
        talentType: "passive",
        tree: "strength",
        skillLevelRequired: 20,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 3,
        tier1Modifier: 15,
        tier2Modifier: 25,
        tier3Modifier: 35,
        secondaryModifier: 1.5,
        description: "Deal 1.5x damage when your target is below a certain % HP",
    },
    {
        name: "bully",
        displayName: "Bully",
        talentType: "passive",
        tree: "strength",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 3,
        tier1Modifier: 1.5,
        tier2Modifier: 1.75,
        tier3Modifier: 2.0,
        description: "Inflict more injuries on your defeated targets",
    },
    {
        name: "offensive_offhands",
        displayName: "Offhand Mastery",
        talentType: "passive",
        tree: "strength",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        description: "Gives the ability to equip damage dealing offhands",
    },
    {
        name: "combat_regeneration",
        displayName: "Bloodlust",
        talentType: "passive",
        tree: "strength",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.05,
        description: "Regenerates 5% max HP per turn whilst in combat",
    },
    {
        name: "rage",
        displayName: "Rage",
        talentType: "ability",
        tree: "strength",
        skillLevelRequired: 10,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 40,
        tier1Modifier: 0.2,
        description: "Gives a 20% melee damage bonus",
    },
    {
        name: "cripple",
        displayName: "Rend",
        talentType: "ability",
        tree: "strength",
        skillLevelRequired: 20,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 50,
        tier1Modifier: 0.35,
        description: "Reduces targets defence by 35%",
    },
    {
        name: "stun",
        displayName: "Stun",
        talentType: "ability",
        tree: "strength",
        skillLevelRequired: 40,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 70,
        description: "Stuns enemy target",
    },
    {
        name: "headbutt",
        displayName: "Headbutt",
        talentType: "ability",
        tree: "strength",
        skillLevelRequired: 20,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 70,
        tier1Modifier: 0.3,
        description: "Deals 30% of the targets max HP as damage",
    },
    // defence talents
    {
        name: "active_defence",
        displayName: "Rigidity",
        talentType: "passive",
        tree: "defence",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.1,
        description: "Increases your defence by 10%",
    },
    {
        name: "strong_bones",
        displayName: "Strong Bones",
        talentType: "passive",
        tree: "defence",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 2,
        tier1Modifier: 0.75,
        tier2Modifier: 0.9,
        description: "Increased resistance to fractures",
    },
    {
        name: "good_stomach",
        displayName: "Metabolism",
        talentType: "passive",
        tree: "defence",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 1.2,
        description: "Increases item healing by 20%",
    },
    {
        name: "shieldbearer",
        displayName: "Shieldbearer",
        talentType: "passive",
        tree: "defence",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        description: "Gives the ability to equip shield offhands",
    },
    {
        name: "deflect_damage",
        displayName: "Deflect Damage",
        talentType: "passive",
        tree: "defence",
        skillLevelRequired: 20,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 70,
        tier1Modifier: 0.4,
        description: "Gives a 40% chance to block ranged attacks",
    },
    {
        name: "shockwave",
        displayName: "Shockwave",
        talentType: "ability",
        tree: "defence",
        skillLevelRequired: 40,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 70,
        description: "Stuns the target",
    },
    {
        name: "high_guard",
        displayName: "High Guard",
        talentType: "ability",
        tree: "defence",
        skillLevelRequired: 20,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 70,
        tier1Modifier: 0.75,
        secondaryModifier: 0.4,
        description: "Reduces incoming damage by 75% whilst reducing your own damage output by 40%",
    },
    {
        name: "shield_bash",
        displayName: "Shield Bash",
        talentType: "ability",
        tree: "defence",
        skillLevelRequired: 10,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 60,
        tier1Modifier: 0.25,
        secondaryModifier: 50,
        description: "Deals damage equal to 50 + 25% of your Defence stat",
    },
    {
        name: "mitigation",
        displayName: "Mitigation",
        talentType: "passive",
        tree: "defence",
        skillLevelRequired: 20,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.85,
        secondaryModifier: 0.01,
        description: "Reduces all damage received by 1% per combat turn, up to a max of 15%",
    },
    // intelligence talents
    {
        name: "healthy_caster",
        displayName: "Focused Caster",
        talentType: "passive",
        tree: "intelligence",
        skillLevelRequired: 20,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 3,
        tier1Modifier: 90,
        tier2Modifier: 80,
        tier3Modifier: 70,
        description: "Deal 1.5x ability damage when your enemy is above a certain % HP",
    },
    {
        name: "cunning_rat",
        displayName: "Cunning",
        talentType: "passive",
        tree: "intelligence",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 1.25,
        description: "Enemies that cripple you have a 25% higher chance of going to jail",
    },
    {
        name: "speed_crafter",
        displayName: "Quick Hands",
        talentType: "passive",
        tree: "intelligence",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.75,
        description: "Increases item crafting speed by 25%",
    },
    {
        name: "multitasker",
        displayName: "Multitasker",
        talentType: "passive",
        tree: "intelligence",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 1,
        description: "Adds an extra crafting slot. Can be used to craft more than one item at a time",
    },
    {
        name: "investor",
        displayName: "Investor",
        talentType: "passive",
        tree: "intelligence",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 2,
        tier1Modifier: 1.1,
        tier2Modifier: 1.25,
        description: "Increases part-time job earnings",
    },
    {
        name: "learner",
        displayName: "Quick Learner",
        talentType: "passive",
        tree: "intelligence",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 1.15,
        description: "Increases mission board EXP + Cash rewards by 15% (multiplicative)",
    },
    {
        name: "heal",
        displayName: "Buffer",
        talentType: "ability",
        tree: "intelligence",
        skillLevelRequired: 10,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 40,
        tier1Modifier: 0.25,
        description: "Temporarily heals you for 25% of your max health while in combat",
    },
    {
        name: "heal_over_time",
        displayName: "Recovery",
        talentType: "ability",
        tree: "intelligence",
        skillLevelRequired: 20,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 60,
        tier1Modifier: 0.15,
        description: "Heals 15% of your max health each turn",
    },
    {
        name: "sleep",
        displayName: "Sleep",
        talentType: "ability",
        tree: "intelligence",
        skillLevelRequired: 40,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 70,
        description: "Sends the target to sleep. Any damage to the target will wake them up",
    },
    {
        name: "ability_efficiency",
        displayName: "Chaining",
        talentType: "passive",
        tree: "intelligence",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.5,
        description: "Casting 2 of the same ability makes the 3rd cast cost half",
    },
    {
        name: "revive",
        displayName: "First Aid",
        talentType: "passive",
        tree: "intelligence",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 2,
        description: "Allows you to heal 2 injured players each day.",
    },
    // stamina talents
    {
        name: "energetic",
        displayName: "Energetic",
        talentType: "passive",
        tree: "endurance",
        skillLevelRequired: 20,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 3,
        description: "Increases maximum action points",
    },
    {
        name: "outside_combat_regeneration",
        displayName: "Body Regen",
        talentType: "passive",
        tree: "endurance",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 1.3,
        description: "Increases out of combat health regen",
    },
    {
        name: "recovery",
        displayName: "Cardio King",
        talentType: "passive",
        tree: "endurance",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 2,
        description: "Faster stamina regen in combat",
    },
    {
        name: "mugger",
        displayName: "Greedy",
        talentType: "passive",
        tree: "endurance",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 1.3,
        description: "Gain 30% extra cash when mugging someone",
    },
    {
        name: "coward",
        displayName: "Coward",
        talentType: "passive",
        tree: "endurance",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.3,
        description: "Increases the chance of fleeing a battle by 30% (additive)",
    },
    {
        name: "free_movement",
        displayName: "Free Movement",
        talentType: "passive",
        tree: "endurance",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.3,
        description: "Gives a 30% chance of no AP cost when advancing a roguelike node",
    },
    {
        name: "built",
        displayName: "Built",
        talentType: "passive",
        tree: "endurance",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 200,
        description: "Gain 200 extra max health points",
    },
    {
        name: "rejuvenation",
        displayName: "Rejuvenation",
        talentType: "passive",
        tree: "endurance",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 2,
        tier1Modifier: 0.1,
        tier2Modifier: 0.2,
        description: "You heal 10% of your max HP after winning a battle",
    },
    {
        name: "max_hp_heal",
        displayName: "Second Wind",
        talentType: "ability",
        tree: "endurance",
        skillLevelRequired: 40,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 80,
        tier1Modifier: 0.75,
        description: "Heals you for 75% of your max health",
    },
    {
        name: "disarm",
        displayName: "Disarm",
        talentType: "ability",
        tree: "endurance",
        skillLevelRequired: 20,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 65,
        description: "Temporarily disarms the target's equipped weapons",
    },
    {
        name: "self_harm",
        displayName: "Blood Frenzy",
        talentType: "ability",
        tree: "endurance",
        skillLevelRequired: 10,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 30,
        tier1Modifier: 1.4,
        secondaryModifier: 0.1,
        description: "Sacrifices 10% of your current HP for a 40% damage increase",
    },

    // dexterity talents
    {
        name: "ranger",
        displayName: "Ranger",
        talentType: "passive",
        tree: "dexterity",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.1,
        description: "Increases ranged damage by 10%",
    },
    {
        name: "escape_artist",
        displayName: "Escape Artist",
        talentType: "passive",
        tree: "dexterity",
        skillLevelRequired: 5,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.7,
        description: "Reduces chance of being jailed by 30%",
    },
    {
        name: "quiver",
        displayName: "Deep Pockets",
        talentType: "passive",
        tree: "dexterity",
        skillLevelRequired: 10,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 3,
        tier1Modifier: 1,
        tier2Modifier: 2,
        tier3Modifier: 3,
        description: "Bigger ammo capacity in battle",
    },
    {
        name: "quick_turn_taker",
        displayName: "Speedster",
        talentType: "passive",
        tree: "dexterity",
        skillLevelRequired: 20,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        description: "Higher chance of first turn order",
    },
    {
        name: "exhaust",
        displayName: "Exhaust",
        talentType: "ability",
        tree: "dexterity",
        skillLevelRequired: 20,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 50,
        tier1Modifier: 0.35,
        description: "Reduce the targets strength by 35%",
    },
    {
        name: "shadow_step",
        displayName: "Ultra Instinct",
        talentType: "passive",
        tree: "dexterity",
        skillLevelRequired: 20,
        pointsCost: 1,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        tier1Modifier: 0.3,
        secondaryModifier: 0.03,
        description: "Gives a 30% chance to dodge enemy attacks",
    },
    {
        name: "reload",
        displayName: "Reload",
        talentType: "ability",
        tree: "dexterity",
        skillLevelRequired: 10,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 70,
        description: "Refills all of your ranged ammo",
    },
    {
        name: "spray",
        displayName: "Spray",
        talentType: "ability",
        tree: "dexterity",
        skillLevelRequired: 10,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 50,
        tier1Modifier: 0.25,
        description: "Uses up all of your ammo at once to deal bonus damage",
    },
    {
        name: "toxic_dart",
        displayName: "Toxic Dart",
        talentType: "ability",
        tree: "dexterity",
        skillLevelRequired: 20,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 65,
        tier1Modifier: 0.1,
        secondaryModifier: 30,
        description: "Poisons the target enemy, dealing 30 + 10% of their current health per turn",
    },
    {
        name: "giant_killing_slingshot",
        displayName: "Giant Slingshot",
        talentType: "ability",
        tree: "dexterity",
        skillLevelRequired: 40,
        pointsCost: 2,
        pointsInTreeRequired: 0,
        maxPoints: 1,
        staminaCost: 60,
        tier1Modifier: 0.4,
        description: "Deals 40% of the targets current HP as damage",
    },
];

export default default_talents;
