import type { EvolvePetParams } from "@/features/pets/types/pets";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface UseEvolvePetOptions {
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
}

const useEvolvePet = (options: UseEvolvePetOptions = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.pets.evolve.mutationOptions({
            onSuccess: (data) => {
                // Invalidate pets list to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.pets.list.key(),
                });
                options.onSuccess?.(data);
            },
            onError: (error) => {
                console.error("Evolve pet error:", error);
                options.onError?.(error);
            },
        })
    );
};

export default useEvolvePet;
