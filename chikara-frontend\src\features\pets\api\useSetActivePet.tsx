import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface SetActivePetParams {
    userPetId: number;
}

interface UseSetActivePetOptions {
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
}

const useSetActivePet = (options: UseSetActivePetOptions = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.pets.setActive.mutationOptions({
            onSuccess: (data) => {
                // Invalidate pets list to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.pets.list.key(),
                });
                options.onSuccess?.(data);
            },
            onError: (error) => {
                console.error("Set active pet error:", error);
                options.onError?.(error);
            },
        })
    );
};

export default useSetActivePet;
