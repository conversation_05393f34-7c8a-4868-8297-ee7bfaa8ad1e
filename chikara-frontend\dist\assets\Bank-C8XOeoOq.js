import{r as c,j as e,h as v,x as Y,I as D,u as R,e as k,g as N,c as y,y as f,b as J,o as H,v as G,t as M,S as V,f as S,B as z,C,l as B}from"./index--cEnoMkg.js";import{C as X}from"./circle-alert-CQS1v2Kc.js";import{C as Z}from"./circle-x-B-7GxCnr.js";import{C as ee}from"./circle-check-big-DkHAMB3v.js";import{C as te}from"./Callout-B-FXvRY0.js";const ae=Y("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 dark:bg-green-900/20 dark:border-green-500 dark:text-green-300 [&>svg]:text-green-600 dark:[&>svg]:text-green-400",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-500 dark:text-yellow-300 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400",info:"border-blue-500/50 text-blue-700 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-500 dark:text-blue-300 [&>svg]:text-blue-600 dark:[&>svg]:text-blue-400"}},defaultVariants:{variant:"default"}}),U=c.forwardRef(({className:t,variant:s,...a},n)=>e.jsx("div",{ref:n,role:"alert",className:v(ae({variant:s}),t),...a}));U.displayName="Alert";const _=c.forwardRef(({className:t,...s},a)=>e.jsx("h5",{ref:a,className:v("mb-1 font-medium leading-none tracking-tight",t),...s}));_.displayName="AlertTitle";const K=c.forwardRef(({className:t,...s},a)=>e.jsx("div",{ref:a,className:v("text-sm [&_p]:leading-relaxed",t),...s}));K.displayName="AlertDescription";const re=({variant:t})=>{switch(t){case"success":return e.jsx(ee,{className:"h-4 w-4"});case"destructive":return e.jsx(Z,{className:"h-4 w-4"});case"warning":return e.jsx(X,{className:"h-4 w-4"});case"info":return e.jsx(D,{className:"h-4 w-4"});default:return e.jsx(D,{className:"h-4 w-4"})}},h=(t,s={})=>{const{minimumFractionDigits:a=0,maximumFractionDigits:n=0,showSymbol:d=!0}=s;return d?new Intl.NumberFormat("ja-JP",{style:"currency",currency:"JPY",minimumFractionDigits:a,maximumFractionDigits:n}).format(t).replace("JP¥","¥"):new Intl.NumberFormat("ja-JP",{minimumFractionDigits:a,maximumFractionDigits:n}).format(t)},se=(t,s)=>Math.round(t/100*s),ne=({transactionType:t,amount:s,transferID:a,TRANSACTION_FEE:n=0,MINIMUM_WITHDRAWAL:d=0,MINIMUM_DEPOSIT:o=0,MINIMUM_TRANSFER:m=0,className:p})=>{const u=a?Number.parseInt(String(a)):void 0,{data:x}=R(u,{enabled:!!u&&t==="Transfer"}),r=c.useMemo(()=>{const l=h(s),b=se(s,n),g=h(b);switch(t){case"Deposit":return{variant:"success",title:`Successfully deposited ${l}!`,description:n>0?`A ${n}% deposit fee of ${g} was taken by the bank.`:void 0};case"Withdraw":return{variant:"success",title:`Successfully withdrew ${l}!`};case"Transfer":return{variant:"success",title:`Successfully transferred ${l} to ${x?.username||"recipient"}!`,description:n>0?`A ${n}% transfer fee of ${g} was taken by the bank.`:void 0};case"DepositAmountTooLow":return{variant:"destructive",title:`Minimum deposit amount is ${h(o)}`};case"WithdrawAmountTooLow":return{variant:"destructive",title:`Minimum withdrawal amount is ${h(d)}`};case"TransferAmountTooLow":return{variant:"destructive",title:`Minimum transfer amount is ${h(m)}`};case"InvalidDepositAmount":return{variant:"destructive",title:"Insufficient funds for deposit",description:"You don't have enough cash to make this deposit."};case"InvalidWithdrawAmount":return{variant:"destructive",title:"Insufficient bank balance",description:"You don't have enough money in your bank account for this withdrawal."};default:return{variant:"info",title:"Transaction processed"}}},[t,s,x?.username,n,o,d,m]);return e.jsxs(U,{variant:r.variant,className:p,children:[e.jsx(re,{variant:r.variant}),e.jsx(_,{children:r.title}),r.description&&e.jsx(K,{children:r.description})]})},ie=()=>{const t=k();return N(y.bank.deposit.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:f.USER.CURRENTUSERINFO}),t.invalidateQueries({queryKey:f.BANK.BANKTRANSACTIONS})}}))},oe=()=>{const t=k();return N(y.bank.transfer.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:f.USER.CURRENTUSERINFO}),t.invalidateQueries({queryKey:f.BANK.BANKTRANSACTIONS})}}))},le=()=>{const t=k();return N(y.bank.withdraw.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:f.USER.CURRENTUSERINFO}),t.invalidateQueries({queryKey:f.BANK.BANKTRANSACTIONS})}}))},de=()=>J(y.bank.bankTransactions.queryOptions());function ce({transaction:t,currentUser:s}){let a;t.initiatorId===s?a=t.secondPartyId:a=t.initiatorId;const{data:n}=R(a,{enabled:!!a});return e.jsx("td",{className:"whitespace-nowrap p-2 font-medium text-gray-900 text-sm dark:text-gray-300",children:e.jsxs("span",{children:["Bank transfer ",t.initiatorId===s?"to":"from"," ",e.jsxs(H,{className:"inline text-blue-600",to:`/profile/${a}`,children:[" ",n?.username]})]})})}const me=({historyLimit:t=10})=>{const s=G(),{isLoading:a,error:n,data:d}=de(),{data:o}=M(),m=d?d.slice(0,t):[];if(a)return"Loading...";if(n)return"An error has occurred: "+n.message;const p=r=>{let l;return s?l=S(r,"dd/MM kk:mm"):l=S(r,"dd/MM/y kk:mm"),l},u=r=>r.transaction_type==="bank_withdrawl"?"Bank Withdrawal":r.transaction_type==="bank_deposit"?"Bank Deposit":r.transaction_type,x=r=>r.transaction_type==="bank_transfer"?r.playerId===o.id?e.jsxs("td",{className:"whitespace-nowrap p-2 text-green-500 text-sm",children:["+¥",r.cash]}):e.jsxs("td",{className:"whitespace-nowrap p-2 text-red-500 text-sm",children:["-¥",r.cash]}):e.jsxs("td",{className:"whitespace-nowrap p-2 text-gray-900 text-sm dark:text-gray-300",children:["¥",r.cash]});return e.jsxs("div",{className:"mb-5 px-4 sm:px-6 lg:px-8",children:[e.jsx("div",{className:"sm:flex sm:items-center",children:e.jsx("div",{className:"sm:flex-auto",children:e.jsxs("h1",{className:"font-semibold text-gray-900 md:text-xl dark:font-normal dark:text-gray-200 dark:text-stroke-s-sm",children:["Transaction History (Last ",t," Transactions)"]})})}),e.jsx("div",{className:"mt-4 flex flex-col md:mt-8",children:e.jsx("div",{className:"-mx-4 -my-2 sm:-mx-6 lg:-mx-8 overflow-x-auto",children:e.jsx("div",{className:"inline-block min-w-full py-2 align-middle md:px-6 lg:px-8",children:e.jsx("div",{className:"overflow-hidden shadow-sm ring-1 ring-black/5 md:rounded-lg",children:a?e.jsx(V,{}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-300",children:[e.jsx("thead",{className:"bg-gray-50 text-gray-900 text-sm dark:bg-gray-800 dark:text-gray-200 dark:text-stroke-sm",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"whitespace-nowrap py-3.5 pr-3 pl-4 text-left font-semibold sm:pl-6 dark:font-normal",children:"Date"}),e.jsx("th",{scope:"col",className:"whitespace-nowrap px-2 py-3.5 text-left font-semibold dark:font-normal",children:"Type"}),e.jsx("th",{scope:"col",className:"whitespace-nowrap px-2 py-3.5 text-left font-semibold dark:font-normal",children:"Amount"}),e.jsx("th",{scope:"col",className:"hidden whitespace-nowrap px-2 py-3.5 text-left font-semibold md:table-cell dark:font-normal",children:"Bank Balance"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white dark:divide-gray-600 dark:bg-gray-700",children:m.map((r,l)=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap py-2 pr-3 pl-4 text-gray-500 text-sm sm:pl-6 md:pr-1 dark:text-gray-300",children:p(r.createdAt)}),r.transaction_type==="bank_transfer"?e.jsx(ce,{transaction:r,currentUser:o.id}):e.jsx("td",{className:"whitespace-nowrap p-2 font-medium text-gray-900 text-sm dark:text-gray-300",children:u(r)}),x(r),e.jsxs("td",{className:"hidden whitespace-nowrap p-2 text-gray-900 text-sm md:table-cell dark:text-gray-300",children:["¥",r.bankBalance]})]},l))})]})})})})})]})};function ge(){const[t,s]=c.useState(0),[a,n]=c.useState("Deposit"),[d,o]=c.useState(void 0),[m,p]=c.useState(0),[u,x]=c.useState(0),{data:r}=M(),l=ie(),b=le(),g=oe(),{MINIMUM_WITHDRAWAL:j,MINIMUM_DEPOSIT:A,MINIMUM_TRANSFER:T,TRANSACTION_HISTORY_LIMIT:W,TRANSACTION_FEE:E,BANK_DISABLED:L,DEPOSIT_DISABLED:w}=z(),I=E*100;if(L)return e.jsx("div",{className:"mt-10 flex flex-col dark:text-slate-200",children:e.jsxs("div",{className:"mx-auto text-center",children:[e.jsx("h2",{className:"text-xl",children:"Bank currently Disabled"}),e.jsx("p",{children:"Please return later."})]})});w&&a==="Deposit"&&n("Withdraw");const O=i=>{s(0),n(i.target.value)},$=i=>{s(Number.parseInt(i.target.value))},F=i=>{if(i.preventDefault(),a==="Deposit"){s(r?.cash??0);return}s(r?.bank_balance??0)},q=i=>{p(Number.parseInt(i.target.value))},P=i=>{i.preventDefault(),Q()};async function Q(){if(a==="Deposit"&&t<A){o("DepositAmountTooLow");return}if(a==="Withdraw"&&t<j){o("WithdrawAmountTooLow");return}if(a==="Transfer"){if(t<T){o("TransferAmountTooLow");return}if(r?.id===m){B.error("You can't bank transfer to yourself!");return}}try{a==="Deposit"?await l.mutateAsync({amount:t}):a==="Withdraw"?await b.mutateAsync({amount:t}):a==="Transfer"&&await g.mutateAsync({recipientId:m,transferAmount:t}),o(a),x(t),s(0)}catch(i){console.error(i),i instanceof Error&&i.message.includes("400")&&(a==="Deposit"?o("InvalidDepositAmount"):a==="Withdraw"&&o("InvalidWithdrawAmount")),B.error(i instanceof Error?i.message:"An unknown error occurred")}}return e.jsxs("div",{className:"mb-6 flex flex-col justify-center md:mx-auto md:mb-0 md:max-w-6xl",children:[w&&e.jsx(te,{title:"Due to a recent security breach, all bank deposits are temporarily closed.",className:"mx-auto! w-fit! pr-4!"}),e.jsxs("div",{className:"mx-auto my-4 flex w-full flex-col border-y bg-white p-4 md:w-auto md:rounded-md md:border dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300",children:[e.jsxs("div",{className:"mx-auto",children:[e.jsxs("p",{children:["Current Bank Balance: ¥",r?.bank_balance]}),e.jsxs("p",{children:["Current Cash Balance: ¥",r?.cash]})]}),e.jsxs("div",{className:"text-center sm:flex-auto",children:[e.jsxs("p",{className:"mt-4 text-sm ",children:["You will be charged a ",I,"% transaction fee for all deposits and transfers."]}),e.jsx("p",{className:"mt-2 text-sm ",children:"Money in your bank cannot be mugged from you."})]}),e.jsxs("form",{className:"mx-auto mt-5 flex flex-col",onSubmit:P,children:[e.jsx("label",{htmlFor:"location",className:"block font-medium text-sm ",children:"Transaction Type"}),e.jsxs("select",{id:"location",name:"location",className:"mt-1 block w-full rounded-md border-gray-600 bg-gray-900 py-2 pr-10 pl-3 font-body font-semibold text-base focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm dark:text-gray-300",value:a,onChange:O,children:[w?null:e.jsx("option",{className:"font-semibold",value:"Deposit",children:"Deposit"}),e.jsx("option",{className:"font-semibold",value:"Withdraw",children:"Withdraw"}),e.jsx("option",{className:"font-semibold",value:"Transfer",children:"Bank Transfer"})]}),e.jsxs("div",{className:"mt-3 mb-5",children:[e.jsx("label",{htmlFor:"amount",className:"block font-medium text-sm ",children:"Amount"}),e.jsxs("div",{className:"mt-1 flex gap-3 rounded-md shadow-xs",children:[e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-600 border-r-0 bg-gray-900 px-3 text-gray-300 text-shadow sm:text-sm",children:"¥"}),e.jsx("input",{type:"number",name:"amount",id:"amount",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-600 px-3 py-2 placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300",placeholder:"0",min:"0",value:t,onChange:$})]}),e.jsx(C,{variant:"flat",size:"md",onClick:F,children:"Max"})]})]}),a==="Transfer"&&e.jsxs("div",{className:"mt-3 mb-5",children:[e.jsx("label",{htmlFor:"amount",className:"block font-medium text-sm ",children:"Recipient Student ID"}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-600 border-r-0 bg-gray-900 px-3 text-gray-300 text-shadow sm:text-sm",children:"#"}),e.jsx("input",{type:"number",name:"amount",id:"amount",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-600 px-3 py-2 placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300",placeholder:"0",onChange:q})]})]}),e.jsx(C,{fullWidth:!0,className:"mx-auto max-w-36 text-stroke-sm dark:text-slate-100",children:"Confirm"})]})]}),e.jsx("div",{className:"mb-2 h-16",children:d&&e.jsx(ne,{transactionType:d,amount:u,transferID:m,TRANSACTION_FEE:I,MINIMUM_WITHDRAWAL:j,MINIMUM_DEPOSIT:A,MINIMUM_TRANSFER:T})}),e.jsx(me,{historyLimit:W})]})}export{ge as default};
