const e=JSON.parse(`[{"id":1,"date":"21/05/2023","version":"Alpha 1 Launch","heading":"Initial Alpha Release","patchNotes":[{"heading":"Current Content:","content":["Limited endless roguelite mode","Simple battle system","Inventory System","Basic Shops","Basic Leveling System","Part-Time Job","Gym/Library Stat Training","Item Crafting","Global Chat","Profile Pages","Jail / Hospital","Banking System","Event Notifications","Daily Login Reward","Alpha Leaderboard","Settings Page"]}]},{"id":2,"date":"20/04/2024","version":"Alpha 2 Launch","heading":"Alpha Test 2","patchNotes":[{"heading":"New Content:","content":["Quest System","Talent Trees","Stats Overhaul","Combat Overhaul + Rebalancing","Combat Skills","Courses","Visual Overhaul","Trader Reputation","Unique Items","Player Bounties","Suggestions Page","Basic Casino"]},{"heading":"Minor Changes:","content2":["Dark mode","Profile comments","Level cap set to 30","Streets locations","New leaderboards","Custom chat emotes","Many bug fixes"]}]},{"id":3,"date":"23/04/2024","version":"v0.2.1","heading":"Alpha 0.2.1","patchNotes":[{"content":["Attacking another player now costs 2 AP (Up from 1)","Faceminer added to the arcade (desktop only)","Slightly increased EXP needed for each level","Slightly reduced boss difficulty","Reduced stats gain from courses","Increased droprates for some crafting items","Added a new level-up visual popup","Enabled suggestion comments","Enabled tips banner","Multiple bugfixes and improvements"]}]},{"id":4,"date":"24/04/2024","version":"v0.2.2","heading":"Alpha 0.2.2","patchNotes":[{"content":["Added new consumable items","Added new quest rewards","Fixed all missing/broken NPC images","Improved profile banner uploads","Safari + iOS visual fixes"]}]},{"id":5,"date":"25/04/2024","version":"v0.2.3","heading":"Alpha 0.2.3","patchNotes":[{"content":["Buffed NPC enemy scaling at higher levels","Reduced the cap on Roguelike stat buffs, now limited to a maximum of 50% increase per stat.","Increased damage output progressively in prolonged combat encounters.","Fixed issue where chat failed to establish connection on initial login.","Fixed issue with avatar uploads on iOS devices","Multiple visual fixes"]}]},{"id":6,"date":"26/04/2024","version":"v0.2.4","heading":"Alpha 0.2.4","patchNotes":[{"content":["Reduced global item droprates across the board","Increased the droprate of most crafting items","Reduced the hospital duration of the 'hospitalised' PvP option to 45 minutes (down from 60)","Changed the 'Deflect Damage' talent to a passive. Now has a 25% chance to block ranged attacks (down from 40%)","Reduced the 'Bully' talent effect to 40% (down from 50%)","Increased past chat history limit","Increased suggestion comment text limit","Multiple visual fixes"]}]},{"id":7,"date":"27/04/2024","version":"v0.2.5","heading":"Alpha 0.2.5","patchNotes":[{"content":["Anti-Bullying Mechanic - Players can no longer attack others they've defeated three times within a day, resetting midnight daily.","Base ammo is now a unique amount for each ranged weapon.","The stamina cost to use the 'Reload' skill has been increased to 100 (was 70).","Slightly increased droprate of all quest items"]}]},{"id":8,"date":"28/04/2024","version":"v0.2.6","heading":"Alpha 0.2.6","patchNotes":[{"heading":"New Feature: Mission Board","content":["Choose from a list of available missions between 2, 4 and 8 hours long.","Once a mission is started, you cannot complete any gameplay activities during the mission’s duration. (Ideal for when you need to log off for the day!)","Missions and mission rewards are randomised each day.","Rewards scale with the mission length and the selected mission tier!","Level up and complete more missions in order to progress to the next tier.","Unlocked from Level 3"]},{"heading":"Other Changes:","content":["NPC damage changes (further NPC scaling changes coming soon)","The rage strength skill now lasts 5 turns (up from 3) and costs 40 stamina.","Improvements to AP regen ticks","Fixed bug with the Free Movement talent"]}]},{"id":9,"date":"29/04/2024","version":"v0.2.7","heading":"Alpha 0.2.7","patchNotes":[{"content":["Disabled the 'Give Up' button on the Streets mode","Slightly reduced boss difficulty.","Reduced difficulty of creatures pre Level 10","Fixed issue causing some buttons to break when using Firefox.","Mission Board visual fixes"]}]},{"id":10,"date":"30/04/2024","version":"v0.2.8","heading":"Patch Notes: Alpha Version 0.2.8 (30 April 2024)","patchNotes":[{"heading":"Update to INT Stat Passive Effects:","content":["The overall cap on base intelligence has been removed."],"content2":["The maximum reduction in training energy costs due to intelligence is now limited to 25% (-5)."]},{"heading":"INT Heal Skill Changed","content":["The Heal skill is now named 'Buffer'."],"content2":["Combat Effect: Temporarily heals you for 25% of your max health in combat. The heal is removed when combat ends."]},{"heading":"INT Quick Learner Passive Talent Changed","content":["Now increases mission board EXP + Cash rewards by 15% (multiplicative)"]},{"heading":"Minor Changes:","content":["Added additional functionality to Prefect users","Slightly reduced boss difficulty for some zones."]}],"subheading2":"Contact staff if you wish to remove any of the affected talents for a free talent point refund"},{"id":11,"date":"04/05/2024","version":"v0.2.9","heading":"Patch Notes: Alpha Version 0.2.9","patchNotes":[{"heading":"Shrine now unlocked:","content":["The Shrine is now collecting donations from players!","Work together as a community to reach the Shrine donation goal and earn global buffs for all players!","The Shrine goal and any buffs will reset at midnight UTC on the next day.","Available buffs are randomly generated each day."],"content2":["The Shrine can be found from the 'Explore' page."]},{"heading":"Other Changes","content":["Multiple bugfixes and improvements","QoL improvements for various pages","Improvements to the new player experience and onboarding"]}]},{"id":12,"date":"07/05/2024","version":"v0.2.10","heading":"Patch Notes: Alpha Version 0.2.10","patchNotes":[{"heading":"New INT Talent: Revive","content":["Allows you to revive 2 players from the hospital each day.","Unlocked from the 250 INT talent tier.","Accessed from a button on the 'Hospital' page.","You cannot attack a player you have recently revived."]},{"heading":"Minor Changes:","content":["Rebalanced NPC stats across all zone levels","Re-arranged some INT talents into different tiers.","Added new leaderboards for Casino and mugging win/lose amounts.","Added chat message announcement when a player gets a rare item drop.","Reduced droprates for some rare items."]},{"heading":"Bugfixes:","content":["Fixed issue where task items were not dropping when intended for some players.","Fixed issue where task items were still dropping after quest completion for some players.","Fixed issue where weapon offhands were not working correctly.","Fixed issue with some shrine buffs being applied incorrectly.","Fixed issue where Balaclavas were not being properly equipped.","Improved the visibility/unlocking of the Sunday shop"]}]},{"id":13,"date":"11/05/2024","version":"v0.2.11","heading":"Patch Notes: Alpha Version 0.2.11","patchNotes":[{"heading":"Hospitalisation Changes","subheading":"Hospitalisation times have been reduced across the board, but now only heals you to 25% of your max health upon release.","content":["'Mug' - Reduced to 20 minutes","'Hospitalise' - Reduced to 40 minutes","'Leave' - Reduced to 10 minutes","PVP Loss - Reduced to 20 minutes","NPC Loss - Reduced to 10 minutes","Death Book - Reduced to 30 minutes"]},{"heading":"Refer A Friend System","content":["Added a refer a friend system for players to refer their friends to the alpha.","Rewards scale based on the level that the referred player reaches.","Accessed from the Home Page after reaching level 4."]},{"heading":"Balance Changes:","content":["Reduced difficulty of all NPCs in lower level zones","INT 'Recovery' talent now regenerates 15% of your max HP per turn (up from 10%)"]},{"heading":"New Leaderboards:","content":["Total Stat Amount","Total Bounty Amount Claimed","Total Mission Hours"]},{"heading":"Minor Changes:","content":["Added Back button to the 'Shopkeeper' page on desktop & mobile."]},{"heading":"Bugfixes:","content":["Fixed issue where the shopkeeper images would sometimes change size when scrolling on mobile.","Fixed issue with server time being incorrect due to timezone differences. (visual only)","Multiple minor visual fixes"]}]},{"id":14,"date":"15/05/2024","version":"v0.2.12","heading":"Patch Notes: Alpha Version 0.2.12","patchNotes":[{"heading":"Minor Changes:","content":["Raised level cap to 35","Increased the maximum length of suggestion comments"]},{"heading":"Bugfixes:","content":["Resolved an issue causing enemy information to sometimes fail to load correctly at the start and end of battles.","Fixed an issue where health bars would intermittently fail to display during battles.","Fixed issue causing rare crashes during battles","Fixed issue causing rare crashes if a patch is applied during battles","Fixed issue causing mission EXP values to show incorrectly in the event log","Fixed multiple visual issues in the battle screen","Fixed an issue where the author of suggestion comments would not display on mobile."]}]},{"id":15,"date":"20/05/2024","version":"v0.2.13","heading":"Patch Notes: Alpha Version 0.2.13","patchNotes":[{"heading":"New Feature: Daily Tasks:","content":["Daily tasks are now available from the 'Home' page after reaching level 3.","Daily tasks reset each day at midnight UTC.","Completing all daily tasks for the day will reward you with a 'Daily Chest' item. - This won't be usable until patch 0.2.14, coming shortly."]},{"heading":"Minor Changes:","content":["Hovering/Clicking on an equipped item will now display the item tooltip in the Equipped screen","Active shrine buff is now displayed on the UI","Added an indicator during battles to display the current turn number","Removed the requirement to complete the 'Upskilling' task before unlocking the Workshop","Displayed some of the common drop types on each location in Location select","Added poll results to the 'Polls' page"]},{"heading":"Talents"},{"subheading":"New Stamina talent: Coward (Passive)","content":["Increases the chance of fleeing a battle by 20% (additive)","Requires 50 Stamina points to unlock"]},{"subheading":"Rage (Strength Skill)","content":["Raised the melee damage bonus to 60% (up from 20%)","Now lasts 4 turns (down from 5)"]},{"subheading":"Blood Frenzy (Stamina Skill)","content":["Raised the damage buff to 40% (up from 30%)","Lowered the current HP health cost to 10% (down from 15%)","Reduced the Stamina cost to 20 (down from 30)","Now lasts 3 turns (up from 2)"]},{"subheading":"High Guard (Defence Skill)","content":["Now lasts 3 turns (up from 2)"]},{"subheading":"Executioner (Strength Passive)","content":["Fixed an issue with the passive being applied incorrectly in combat"]},{"heading":"Bugfixes:","content":["Fixed an issue where popup values were showing the incorrect value when using a consumable","Fixed an issue causing crashing during battles","Fixed some issues related to the Shrine buffs"]}]},{"id":16,"date":"21/05/2024","version":"v0.2.14","heading":"Patch Notes: Alpha Version 0.2.14","patchNotes":[{"heading":"Daily Chest Lootbox:","content":["Daily chest lootbox is now usable.","Drops a random item, with a chance of super rare item drops!."]},{"heading":"NPC EXP Scaling:","content":["NPC EXP rewards now scale relative to your own level","Attacking lower level NPCs will now cause a decrease in the EXP reward gained.","There is also a (smaller) EXP bonus for defeating higher level NPCs"]},{"heading":"Combat Keyboard Shortcuts:","content":["Desktop users can now use keyboard shortcuts while in battle.","A letter next to each combat button will show the associated keyboard shortcut.","This can be disabled in the Settings > Interface page."]},{"heading":"Minor Changes:","content":["You can now cancel any on-going crafts, all crafting items will be returned to your inventory","Faceminer has been removed from the Arcade - will be replaced with a new game in a future update","New suggestion comments now send a notification to other commenters in the same thread"]},{"heading":"Bugfixes:","content":["Fixed bug which showed incorrect EXP rewards from defeating other players","Fixed rare bug which allowed some users to change course","Multiple visual fixes"]}]},{"id":17,"date":"28/05/2024","version":"v0.3.0","heading":"Patch Notes: Alpha Version 0.3.0","subheading":"Streamlined patch versioning for more consistency","patchNotes":[{"heading":"Player Market:","subheading":"The Market is currently restricted to Weapons, Armor  + Consumables listings. This will be extended to more items soon.","content":["A new player market has been added which is accessible from level 12","Items can be listed on the market for a length of 12, 24 or 48 hours.","The market charges a 3/6/9% fee on listing an item stack, scaling with listing time"]},{"heading":"Rooftop Battles:","content":["Starting from level 10 you will be able to fight a variety of increasingly difficult NPC bosses on the academy rooftop","Each boss has a unique reward and can only be defeated once","Each battle has a cost of 2 AP, losing a battle will send you to the hospital as normal"]},{"heading":"New Recipe Item Type:","content":["A new 'Recipe' item type has been added to the game","Using these items unlocks the crafting recipe for the specific item"]},{"heading":"Multiple New Items + Recipes"},{"heading":"Daily Lottery:","content":["A daily lottery draw is created each day at 6:30PM UTC from the Casino page.","Each player can buy one lottery ticket each for 2500 Yen","The lottery prize scales with the amount of entries to the draw","An extra 10,000 Yen is added for every 5 players that enter the lottery draw"]},{"heading":"Table/List Rework","subheading":"New tables are now used for the new auction house + rooftop battles. The new tables will replace the existing faculty list/hospital/jail/bounty tables in the next patch","content":["Filtering","Searching","Sorting","Pagination","Resizing"]},{"heading":"Minor Changes:","content":["New Shrine buffs and improvements","You no longer get jailed when defeating a player with a bounty"]},{"heading":"Bugfixes:","content":["Craft complete notification icon now shows correctly on the bottom nav bar in mobile view","Fixed an issue that could potentially cause infinite loops during battles","Fixed an issue that could sometimes require a reload to display updated crafting task progress","Fixed an issue that could cause item icons to get squished on some resolutions","Fixed an issue causing the Recovery skill to not work correctly","Multiple visual fixes and improvements"]}]},{"id":18,"date":"28/05/2024","version":"v0.3.1","heading":"Patch Notes: Alpha Version 0.3.1","patchNotes":[{"heading":"Chikara Wiki:","subheading":"The Wiki is now live at ","subheadinglink":"https://chikaraacademymmo.wiki.gg","content":["Currently needs content added, feel free to contribute!"]},{"heading":"Minor Changes:","content":["Buffed the base stats for most of the rooftop bosses","The market now allows 'Special' and 'Junk' items to be listed","Blacklisted certain items from being listed on the market","Notification popup messages can now be clicked to hide. They were also reduced in size.","Moved the rooftop battles link from the Campus to the Adventure page","Added leaderboards for 'Most Market items sold' and 'Most money made from the market'","Market table page size now persists across page changes"]},{"heading":"Bugfixes:","content":["Fixed bug causing a crash when re-speccing with equipped skills","Minor visual bugfixes"]}]},{"id":19,"date":"28/05/2024","version":"v0.3.2","heading":"Patch Notes: Alpha Version 0.3.2","subheading":"This is the first of a few patches that are aimed towards improving the economy and market activity. Will be adding more crafts and high level items soon.","patchNotes":[{"heading":"Crafting Changes:","subheading":"The following item crafts are no longer unlocked by default, and now require the associated recipe item to unlock:","content":["Physical Bitcoin","Death Book","Life Book","Refined Katana","Masterwork Short Sword","Ice Bow","Farmers Double-Barrel","Shotgun","Shield Glove Implant","ArmBlade Implant (Offhand)","Samurai Helm","Samurai Chestpiece","Samurai Gauntlets","Samurai Trousers","Samurai Boots","Shadow Helm","Shadow Chest","Shadow Gloves","Shadow Trousers","Shadow Boots","Dominator Helm","Dominator Chest","Dominator Gloves","Dominator Legs","Dominator Boots","Gemstone Ring","Amethyst Ring"]},{"heading":"Item Changes:","content":["Farmers Double-Barrel now does 425 damage (up from 385) and has 2 base ammo (down from 3)","Pistol now does 400 damage (down from 425) and has 4 base ammo (up from 3)","Golden Pistol now does 450 damage (down from 480) and has 4 base ammo (up from 3)"]},{"heading":"Minor Changes:","content":["Recipe items are now listable on the market.","Items gained from the Rooftop Battles are now blacklisted from being listed on the market.","Haruto Watanabe and Ayumi Fujimoto Roof Battles are now unlocked"]}]},{"id":20,"date":"01/06/2024","version":"v0.4.0","heading":"Patch Notes: Alpha Version 0.4.0","subheading":"A very minor patch in terms of content, but multiple improvements have been made towards the stability of the game. The client should now stay up to date regularly without manual refreshing and deal with crashes better. A test server has also been created to enable better testing of new features before they are released.","patchNotes":[{"heading":"Minor Changes:","content":["Market fees were reduced to 3/6/9% depending on listing time","NPCs will now randomly list items on the market multiple times per day","NPCs will now randomly place small bounties on inactive players multiple times per day (to help with daily tasks)","Reduced frequency of item drop chat messages.","Roof Boss balance updates."]},{"heading":"Push Notifications:","content":["Push Notifications are now possible as part of this update, they are currently disabled but will be enabled slowly over the next few days for testing","In order to enable access to push notifications you will need to 'allow notifications' on the browser popup that appears","Push Notification customisation will be adjustable in the Settings page soon"]},{"heading":"Next Steps:","subheading":"A full roadmap will be coming soon, but for now here are some of the main things that will be focused on in the next few patches:","content":["Gangs + Group content (Turf wars, Gang hideouts w/ chatroom, Gang leaderboards)","Item upgrading","More variety to lategame items and gear","Temporary buff consumables","More tasks focused around early and lategame","Leaderboards/Ranking incentives","Wiki participation incentives","QoL improvements"]}]},{"id":21,"date":"03/06/2024","version":"v0.4.1","heading":"Patch Notes: Alpha Version 0.4.1","patchNotes":[{"heading":"Tradeable/Untradeable Items:","subheading":"Items will now be classified as Tradeable or Untradeable depending on how they were obtained. The following item sources now mark the item as Untradeable:","content":["Shop Purchased Items","Market Purchased Items","Daily Reward Chests","Quest Reward Items"],"footer":"All current user Inventory items will be marked as tradeable."},{"heading":"Item Upgrading Bench:","subheading":"In preparation for item upgrading next patch, a new bench is available to construct from the 'Workshop' page. This bench will allow you to upgrade items to a higher level on release."},{"heading":"Push Notifications","content":["Global notification Opt-In/Out is now accessible from the Settings page","More notifications and notification settings coming soon."]},{"subheading":"The following push notifications are now enabled:","content":["Action Points regenerated back to maximum","Health regenerated back to maximum"]},{"heading":"Minor Changes:","content":["Hospitalisation time is now halved for players under level 15","Enabled random NPC market listing/bounties","Slightly reduced shrine Yen requirements","Lottery prize is increased by 15,000 Yen if 5 players enter the draw and by 25,000 Yen if 10 players enter the draw","Gishnib Bishlab changed to a crafting item","New items/crafts/drops"]}]},{"id":22,"date":"06/06/2024","version":"v0.5.0","summary":"Gangs Pt.1","heading":"Patch Notes: Alpha Version 0.5.0","patchNotes":[{"heading":"Gang System:","subheading":"Version 1 of the Gang system is now live. Players can create a gang from level 20 after purchasing a Gang Sigil from Otake's shop.","content":["Each gang can set a custom Name, Description and Avatar. (Custom Banner coming soon)","Gangs have a max member limit of 5 which is increased by 3 after every hideout upgrade.","Gangs currently have 6 possible ranks: Leader, Lieutenant, Officer, Thug, Member, Rookie.","Ranks and Rank powers will be customisable in the near future.","Players can be invited to the gang through their profile or by inputting their student ID in the gang page.","You cannot leave a gang until waiting 48 hours after joining."]},{"heading":"Gang Resources:","subheading":"Gangs can earn specific resources through the following activities:","content":["Raw Materials - Random item drops in PvE battles.","Life Essence - PvP battles, Reviving Hospitalised players.","Tools - Crafting tool crates."],"footer":"NOTE: Life Essence has an individual daily cap of 50 per player."},{"heading":"Gang Hideouts:","content":["Gang Hideouts are constructed and upgraded using the Gangs Resources.","Each Hideout level increases the max player capacity by 3.","Level 1 - Unlocks Private Gang Chatroom + Custom Gang Icon","Level 2 - Unlocks Gang Bank + Gang MOTD","Level 3 - Unlocks Gang Store + Custom Ranks (coming soon)","Level 4 - Unlocks Gang Talent Tree + Gang Buffs (coming soon)","Level 5 - Unlocks Gang Weekly Tasks (coming soon)"]},{"heading":"Respect","content":["Each gang starts with 1500 Respect","Respect is gained and lost through individual PvP battles against members of other gangs","Gang Respect is Reset each week.","Gang Creds will be distributed between all gang member based on the total respect and their weekly gang contribution","Bonuses will be given to the gangs with the highest respect each week (Leaderboard coming soon)"]},{"heading":"Gang Creds","content":["Gang Creds are earned weekly through the gang PvP system or converting gang resources (coming soon)","Gang Creds will be spendable on unique and rare items, as well as certain buffs for all gang members."]},{"heading":"Job Changes","content":["You can now change your job salary payout time","Requesting job promotions now sets you to your highest possible job level"]}],"subheading2":"The gang system is in testing and is subject to change. Please feel free to bring up any feedback or suggestions."},{"id":23,"date":"10/06/2024","version":"v0.6.0","summary":"PvP/Gang Balancing Update","heading":"Patch Notes: Alpha Version 0.6.0","patchNotes":[{"heading":"PvP Changes:","content":["PvP Battle rewards scaling is now based off the difference in power between the two players instead of level.","Life Essence daily limit is now gang-wide instead of player-specific.","Revived players can no longer be attacked by any player during the recently revived cooldown instead of just the reviver."]},{"heading":"Hospitalisation Changes:","content":["'Hospitalise' hospitalisation time reduced to 30 minutes (down from 40)","'Mug' hospitalisation time reduced to 15 minutes (down from 20)","Attacker PvP Loss hospitalisation time reduced to 15 minutes (down from 20)"]},{"heading":"Gang Updates:","content":["Gangs are now temporarily restricted to a maximum of 7 members.","Gang member ranks can now be changed by the gang leader/officers","Gang members can now be kicked from the gang by the gang leader/officers","Added Gang Logs to display Gang Member activity and respect gain/loss","Gang Leaders/Officers can now change the gang MOTD"]},{"heading":"Faculty List:","content":["The Faculty list has been updated to use the new Table design"]},{"heading":"Minor Changes:","content":["Added confirmation before logout"]}]},{"id":24,"date":"18/06/2024","version":"v0.7.0","summary":"Hospitalisation System Rework","heading":"Patch Notes: Alpha Version 0.7.0","patchNotes":[{"heading":"Injuries:","content":["You will now no longer be hospitalised on battle loss, your HP will go to 1 and you will get an injury","There are 3 injury types: Minor, Moderate and Severe.","Injuries gained are random but the post battle options will decide the severity of the injury.","'Crippling' (Formerly hospitalise) a user will have a higher chance of giving a 'Severe' injury, else a guaranteed 'Moderate' injury.","'Mugging' a user will have a higher chance of giving a 'Moderate' injury, else a guaranteed 'Minor' injury.","'Leaving' a user will only have a small chance of giving a 'Minor' injury."]},{"heading":"Hospital Changes:","content":["Players can now 'Check In' to the hospital to receive treatment for their injuries and health.","Getting treated is the same as the old hospital system, you will be hospitalised for 10 minutes and then you will be able to play again.","Treatment has a cost which scales with the amount of injuries you have, and the amount of missing HP you have. This also scales with your level."]},{"heading":"Future Hospitalisation Changes:","content":["Currently Injuries expire after 12 hours, this may change in the future","Multiple items will be added to treat different injury types.","The Death Note/Life Note currently works as before - this may change in the future, if not then it will be made harder to craft"]},{"heading":"Future Talent Changes:","content":["The current system makes a few of the talents obsolete, these will be changed in the next day or two to accomodate the new system","More options for a 'Support' playstyle will be explored, with possibly First Aid talents"]},{"heading":"Other Changes:","content":["Base health regen has been halved from 5% to 2.5%","The Hospital page now uses the new table design","Adjustments to the Respect power difference penalty","Multiple bugfixes and improvements"]}]},{"id":25,"date":"19/06/2024","version":"v0.8.0","summary":"Injury System Updates","heading":"Patch Notes: Alpha Version 0.8.0","patchNotes":[{"heading":"Injury Changes:","content":["Added a choice to only heal injuries at the hospital","Reduced the debuff amount on some injuries","Reduced the injury chances for all injury tiers","Reduced hospital fees for certain injuries","Reduced the injury chance for players under level 15","Reduced injury duration to 6 hours (down from 12)","Gaining a new injury stack will now only add half the amount of hours to the injury duration"]},{"heading":"Hospital Injured List:","content":["The Hospital now has a tab for 'Patients' and 'Injured' players","Patients are players that have checked in to the hospital and are undergoing treatment","Injured players are players that currently have active injuries and are not in the hospital","The main purpose of this list is to help players with the new First Aid talent find other players to heal","Players on this injured list are anonymous unless you are in the same gang"]},{"heading":"Talent Changes"},{"h2Heading":"INT Talents"},{"subheading":"Revive (Passive) renamed to First Aid","content":["Allows you to heal 3 players of their injuries each day.","Accessed from a button on the 'Injured List' in the Hospital.","Rewards Gang Essence for each healed player."]},{"h2Heading":"STR Talents"},{"subheading":"Bully (Passive) Changed","content":["Level 1 - Chance to cause Minor injuries to targets increased by 35%","Level 2 - Chance to cause Moderate injuries to targets increased by 35%","Level 3 - Chance to cause Severe injuries to targets increased by 35%"]},{"subheading":"Bloodlust (Passive)","content":["Regenerates 4% max HP per turn while in combat (down from 5%)"]},{"subheading":"Cripple (Skill) renamed to Rend"},{"h2Heading":"DEF Talents"},{"subheading":"Strong Bones (Passive) Changed","content":["Level 1 - 50% chance to block fracture injuries from being applied","Level 2 - 100% chance to block fracture injuries from being applied"]},{"h2Heading":"STA Talents"},{"subheading":"Rejuvenation (Passive) Changed","content":["Now has 2 unlock levels.","Level 1 - Heals 5% of your max HP after winning a battle","Level 2 - Heals 10% of your max HP after winning a battle"]},{"heading":"Bug Fixes:","content":["Fixed bug causing incorrect debuff values for stacking injuries","Fixed incorrect hospital check in fees","Fixed multiple issues related to the roguelike + battles for poor connections and spam clicks","Fixed visual issue with daily chest claiming","Added injury information to relevant logs in the events page","Cleaned up texts referring to the old hospital system"]}]},{"id":26,"date":"20/06/2024","version":"v0.9.0","summary":"Private Messaging and Life/Death Book Changes","heading":"Patch Notes: Alpha Version 0.9.0","patchNotes":[{"heading":"Private Messages:","content":["Private Messages are now working as intended and have been enabled"]},{"heading":"Death Book Changes:","content":["Targeting a player with a Death Book will now allow you to give them a 'Severe' injury with a debuff of your choice","The name of this injury can be customised by the player","Can't be used on players that are in combat or in the hospital"]},{"heading":"Life Book Changes:","content":["Targeting a player with a Life Book will now heal them to full and remove all injuries","Can't be used on players that are in combat or in the hospital"]},{"heading":"Chat Replies:","content":["There is now a 'Reply' option for Global/Gang Chat messages accessed through the menu dropdown on each message"]},{"heading":"Minor Changes:","content":["Multiple visual improvements for smaller desktop screen sizes","More visual bugfixes for the Hospital","Various bugfixes and visual improvements"]}]},{"id":27,"date":"24/06/2024","version":"v0.10.0","summary":"QoL Improvements and Bug Fixes","heading":"Patch Notes: Alpha Version 0.10.0","patchNotes":[{"heading":"Inventory Table:","content":["The Inventory page now uses the new table design with more features and better layout"]},{"heading":"Equipped Items:","content":["Clicking an equipped item on the equipment screen will now bring up a new tooltip for easier item equipping and comparison"]},{"heading":"Lottery Disabled:","content":["The lottery hasn't been getting much love recently, so it has been disabled for now"]},{"heading":"Minor Changes:","content":["Weapon lifesteal is now working as intended","Shop icons now display a comparison icon against your currently equipped item in that slot","Shop items now display the base ammo for ranged weapons","Multiple bugfixes and visual improvements"]}]}]`);export{e as p};
