import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useRevealCell = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.scavenging.revealCell.mutationOptions({
            onSuccess: (data) => {
                queryClient.invalidateQueries({ queryKey: api.scavenging.activeSession.key() });
                return data;
            },
        })
    );
};

export default useRevealCell;
