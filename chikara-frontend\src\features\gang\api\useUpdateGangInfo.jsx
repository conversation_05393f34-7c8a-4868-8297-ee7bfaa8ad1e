import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useUpdateGangInfo = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.updateGangInfo.mutationOptions({
            onSuccess: () => {
                toast.success("Gang info updated successfully!");
                queryClient.invalidateQueries({
                    queryKey: api.gang.getCurrentGang.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    const updateGangInfo = ({ about, motd, gang_avatar }) => {
        const input = {};

        if (about !== undefined) {
            input.about = about;
        }

        if (motd !== undefined) {
            input.motd = motd;
        }

        if (gang_avatar) {
            input.gang_avatar = gang_avatar;
        }

        mutation.mutate(input);
    };

    return {
        updateGangInfo,
        isLoading: mutation.isPending,
    };
};

export default useUpdateGangInfo;
