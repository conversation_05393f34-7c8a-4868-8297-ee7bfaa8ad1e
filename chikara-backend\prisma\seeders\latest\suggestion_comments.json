[{"id": 1, "message": "Maybe cap at 5 times?", "createdAt": "23/4/2024 21:08:07", "updatedAt": "23/4/2024 21:08:07", "userId": 15, "suggestionId": 8}, {"id": 2, "message": "I agree with 5\n", "createdAt": "23/4/2024 21:20:19", "updatedAt": "23/4/2024 21:20:19", "userId": 14, "suggestionId": 8}, {"id": 3, "message": "I disapprove, Strategies in games like this can be paramount\n", "createdAt": "23/4/2024 21:24:24", "updatedAt": "23/4/2024 21:24:24", "userId": 14, "suggestionId": 9}, {"id": 4, "message": "Not sure if there is a misunderstanding here, this is not for other people to see, just to see your own.", "createdAt": "23/4/2024 22:01:55", "updatedAt": "23/4/2024 22:01:55", "userId": 15, "suggestionId": 9}, {"id": 5, "message": "oh yeah, seeing your own stats would be nice\n", "createdAt": "23/4/2024 23:16:30", "updatedAt": "23/4/2024 23:16:30", "userId": 14, "suggestionId": 9}, {"id": 6, "message": "Main issue is that i don't have any Home icons in the same style - this was the closest i could get", "createdAt": "24/4/2024 00:01:27", "updatedAt": "24/4/2024 00:01:27", "userId": 3, "suggestionId": 7}, {"id": 7, "message": "Never mind it already does, I am pepega, please reject this.", "createdAt": "24/4/2024 18:36:04", "updatedAt": "24/4/2024 18:36:04", "userId": 15, "suggestionId": 11}, {"id": 8, "message": "I think there should be unlimited times you can attack others, but limited times you can attack the same player. 3 times per player per day", "createdAt": "25/4/2024 11:27:38", "updatedAt": "25/4/2024 11:27:38", "userId": 12, "suggestionId": 8}, {"id": 9, "message": "Attacking the same person 5 times a day is crazy, that can lead up to 7.5 hours in the hospital a day", "createdAt": "25/4/2024 11:28:55", "updatedAt": "25/4/2024 11:28:55", "userId": 12, "suggestionId": 8}, {"id": 10, "message": "strong approve\n", "createdAt": "25/4/2024 13:30:35", "updatedAt": "25/4/2024 13:30:35", "userId": 14, "suggestionId": 6}, {"id": 11, "message": "The utility of the bank is to protect your money, If your making it into a generator, there will be no point in keeping money in hand\n", "createdAt": "26/4/2024 13:38:11", "updatedAt": "26/4/2024 13:38:11", "userId": 14, "suggestionId": 13}, {"id": 12, "message": "I try not to incentivize hoarding money in the bank - an 'investment' bank is planned for the future where you can lock up your ..", "createdAt": "26/4/2024 19:48:27", "updatedAt": "26/4/2024 19:48:27", "userId": 3, "suggestionId": 13}, {"id": 13, "message": "money for a period in order to earn interest on it\n", "createdAt": "26/4/2024 19:48:39", "updatedAt": "26/4/2024 19:48:39", "userId": 3, "suggestionId": 13}, {"id": 14, "message": "Great, investment seems nice", "createdAt": "26/4/2024 19:50:04", "updatedAt": "26/4/2024 19:50:04", "userId": 19, "suggestionId": 13}, {"id": 15, "message": "Faceminer mini game text display size is too small on computer.\nsize//pixel makes it  too hard to read\nUsing common size screen", "createdAt": "26/4/2024 20:08:32", "updatedAt": "26/4/2024 20:08:32", "userId": 42, "suggestionId": 3}, {"id": 16, "message": "I didn't create <PERSON><PERSON><PERSON> so I have no control over the screen size unfortunately - it's an external game", "createdAt": "26/4/2024 23:22:11", "updatedAt": "26/4/2024 23:22:11", "userId": 3, "suggestionId": 3}, {"id": 17, "message": "Hell yeah", "createdAt": "27/4/2024 14:24:00", "updatedAt": "27/4/2024 14:24:00", "userId": 19, "suggestionId": 14}, {"id": 18, "message": "Added in 0.2.5 - Cannot attack a user you've defeated already 3 times that day", "createdAt": "27/4/2024 18:53:53", "updatedAt": "27/4/2024 18:53:53", "userId": 3, "suggestionId": 8}, {"id": 19, "message": "Not priority but will be done at some point", "createdAt": "29/4/2024 14:36:59", "updatedAt": "29/4/2024 14:36:59", "userId": 3, "suggestionId": 4}, {"id": 20, "message": "Definitely not a suggestion made just for the \"make a suggestion task\", I'd never do such a thing u.u", "createdAt": "29/4/2024 20:45:31", "updatedAt": "29/4/2024 20:45:31", "userId": 48, "suggestionId": 16}, {"id": 21, "message": "Too realistic, could induce some people's PTSD", "createdAt": "29/4/2024 20:49:03", "updatedAt": "29/4/2024 20:49:03", "userId": 15, "suggestionId": 16}, {"id": 22, "message": "Moving to denied for now as the Bank rework will include a form of this", "createdAt": "29/4/2024 21:05:54", "updatedAt": "29/4/2024 21:05:54", "userId": 3, "suggestionId": 13}, {"id": 23, "message": "Moving to completed for now. <PERSON> send out further invites after revamping the new player experience a bit", "createdAt": "29/4/2024 21:09:12", "updatedAt": "29/4/2024 21:09:12", "userId": 3, "suggestionId": 10}, {"id": 24, "message": "I disagree with the Online status, but being able to enter the same combat as being attacked in pvp should be mandatory.", "createdAt": "30/4/2024 07:15:56", "updatedAt": "30/4/2024 07:15:56", "userId": 14, "suggestionId": 17}, {"id": 25, "message": "I am also strongly against the online status, but would love the ability to participate in fights when you're attacked.", "createdAt": "30/4/2024 11:13:56", "updatedAt": "30/4/2024 11:13:56", "userId": 39, "suggestionId": 17}, {"id": 26, "message": "A sorting tool for inventory would be fantastic, Like it is in the shop\n", "createdAt": "30/4/2024 22:05:30", "updatedAt": "30/4/2024 22:05:30", "userId": 14, "suggestionId": 19}, {"id": 27, "message": "You can already bail yourself out of jail or get bailed out by someone else. I don't think there should be a similar thing for the hospital.", "createdAt": "2/5/2024 06:43:52", "updatedAt": "2/5/2024 06:43:52", "userId": 12, "suggestionId": 21}, {"id": 28, "message": "Will be added eventually but not for alpha, moved to denied for now", "createdAt": "2/5/2024 11:36:30", "updatedAt": "2/5/2024 11:36:30", "userId": 3, "suggestionId": 18}, {"id": 29, "message": "Will be added eventually but not for alpha, moved to denied for now", "createdAt": "2/5/2024 11:37:28", "updatedAt": "2/5/2024 11:37:28", "userId": 3, "suggestionId": 12}, {"id": 30, "message": "Fixed in v0.2.9", "createdAt": "2/5/2024 17:40:29", "updatedAt": "2/5/2024 17:40:29", "userId": 3, "suggestionId": 19}, {"id": 31, "message": "Added in v0.2.9", "createdAt": "2/5/2024 18:00:56", "updatedAt": "2/5/2024 18:00:56", "userId": 3, "suggestionId": 20}, {"id": 32, "message": "Alternative suggestion: putting in something ridiculous for the current level, like a super strong NPC to keep end game entertaining for now", "createdAt": "8/5/2024 22:44:00", "updatedAt": "8/5/2024 22:44:00", "userId": 15, "suggestionId": 24}, {"id": 33, "message": "I like the prestige idea for now, maybe later add something like a prestige raid or dungeon for endgame characters", "createdAt": "8/5/2024 22:47:10", "updatedAt": "8/5/2024 22:47:10", "userId": 19, "suggestionId": 24}, {"id": 34, "message": "I don't really like prestiges ngl but to extend the endgame you could add a really strong equipment set that needs lots of material grinding", "createdAt": "8/5/2024 22:59:54", "updatedAt": "8/5/2024 22:59:54", "userId": 49, "suggestionId": 24}, {"id": 35, "message": "And then add really strong npcs like sereneace suggested", "createdAt": "8/5/2024 23:00:33", "updatedAt": "8/5/2024 23:00:33", "userId": 49, "suggestionId": 24}, {"id": 36, "message": "For those who are far from prestige, one perspective is that their current progress seems fake given they will eventually have to prestige.", "createdAt": "8/5/2024 23:00:45", "updatedAt": "8/5/2024 23:00:45", "userId": 15, "suggestionId": 24}, {"id": 37, "message": "Yeah definitely something i'll add, there's a couple improvements that need to be made for high ping users", "createdAt": "9/5/2024 15:20:50", "updatedAt": "9/5/2024 15:20:50", "userId": 3, "suggestionId": 25}, {"id": 38, "message": "Would make them incapable of doing the PvP tasks at 5 that's the only issue. Couldn't have them able to atk others but not be attked really", "createdAt": "11/5/2024 23:06:15", "updatedAt": "11/5/2024 23:06:15", "userId": 3, "suggestionId": 27}, {"id": 39, "message": "You could give them an option to disable the protection if they wish to do so, like have it disabled the moment they attack someone", "createdAt": "11/5/2024 23:14:18", "updatedAt": "11/5/2024 23:14:18", "userId": 49, "suggestionId": 27}, {"id": 40, "message": "This way they can choose to do the level 5 pvp tasks or keep their protection if they want to get stronger first", "createdAt": "11/5/2024 23:14:51", "updatedAt": "11/5/2024 23:14:51", "userId": 49, "suggestionId": 27}, {"id": 41, "message": "The toggle mechanic would make it too advantageous, I guess whichever one comes first, lv5 or 2 days?", "createdAt": "11/5/2024 23:24:02", "updatedAt": "11/5/2024 23:24:02", "userId": 15, "suggestionId": 27}, {"id": 42, "message": "Raises the question of should there be an option to opt out of PvP entirely, some players have asked", "createdAt": "11/5/2024 23:24:38", "updatedAt": "11/5/2024 23:24:38", "userId": 3, "suggestionId": 27}, {"id": 43, "message": "There's already new player protection till level 5 atm", "createdAt": "11/5/2024 23:25:36", "updatedAt": "11/5/2024 23:25:36", "userId": 3, "suggestionId": 27}, {"id": 44, "message": "i kinda hate it... thats liteally what a lot of bad idler games do to make ppl keep playing once they reach the \"peak\" xD", "createdAt": "12/5/2024 00:16:21", "updatedAt": "12/5/2024 00:16:21", "userId": 48, "suggestionId": 24}, {"id": 45, "message": "Comment test", "createdAt": "12/5/2024 16:07:13", "updatedAt": "12/5/2024 16:07:13", "userId": 1, "suggestionId": 22}, {"id": 46, "message": "ok goku i will", "createdAt": "12/5/2024 17:37:21", "updatedAt": "12/5/2024 17:37:21", "userId": 3, "suggestionId": 28}, {"id": 47, "message": "I like the current system. You are safe until level 5.", "createdAt": "12/5/2024 19:07:34", "updatedAt": "12/5/2024 19:07:34", "userId": 77, "suggestionId": 27}, {"id": 48, "message": "Good idea, also if you have a defensive build your mouse is definitely age a year per boss battle.     ", "createdAt": "13/5/2024 13:24:21", "updatedAt": "13/5/2024 13:24:21", "userId": 48, "suggestionId": 29}, {"id": 49, "message": "Added a back button to shops for now. Will add more QoL updates in a future update for better shop navigation", "createdAt": "13/5/2024 13:24:40", "updatedAt": "13/5/2024 13:24:40", "userId": 3, "suggestionId": 26}, {"id": 50, "message": "Yeah definitely planned - will probably work on this sometime this week once daily tasks are in", "createdAt": "13/5/2024 15:12:46", "updatedAt": "13/5/2024 15:12:46", "userId": 3, "suggestionId": 30}, {"id": 51, "message": "While I would want to be able to sell the multiples of what I have equipped. I get what you mean and it's nice to not have to worry about it", "createdAt": "13/5/2024 17:39:27", "updatedAt": "13/5/2024 17:39:27", "userId": 15, "suggestionId": 31}, {"id": 52, "message": "Maybe instead of removing just add a thing to indicate it is equipped. And maybe a warning if you sell something you have equip.", "createdAt": "13/5/2024 17:45:00", "updatedAt": "13/5/2024 17:45:00", "userId": 77, "suggestionId": 31}, {"id": 53, "message": "For clarity this would still have the item in the sell tab if you had more than one instance of it. Just deducting the equipped one.", "createdAt": "13/5/2024 17:54:44", "updatedAt": "13/5/2024 17:54:44", "userId": 87, "suggestionId": 31}, {"id": 54, "message": "I think it should cost 2AP instead and give some xp. Plus a sub menu on the hospital where you put a $ reward for anyone that revives you", "createdAt": "13/5/2024 18:53:27", "updatedAt": "13/5/2024 18:53:27", "userId": 48, "suggestionId": 32}, {"id": 55, "message": "@Pandora: An excellent suggestion, AP is a great alternative; though 2 AP seems too little as it takes the same amount as attacking.", "createdAt": "13/5/2024 18:58:30", "updatedAt": "13/5/2024 18:58:30", "userId": 89, "suggestionId": 32}, {"id": 56, "message": "Tipping for revives will be added at some point in the future - will also look into potentially being able to increase the revive limit", "createdAt": "14/5/2024 13:03:27", "updatedAt": "14/5/2024 13:03:27", "userId": 3, "suggestionId": 32}, {"id": 57, "message": "I do like the idea of supporting non combat playstyles, would also be beneficial when gang/group content releases", "createdAt": "14/5/2024 13:03:32", "updatedAt": "14/5/2024 13:03:32", "userId": 3, "suggestionId": 32}, {"id": 58, "message": "Further suggestion: I like filling out information, so maybe you could start without Knowing drops (so they appear as ???s) but (cont.)", "createdAt": "14/5/2024 18:24:49", "updatedAt": "14/5/2024 18:24:49", "userId": 69, "suggestionId": 33}, {"id": 59, "message": "as you get the drops, your information on them fills up. It would help motivate exploration in-between tasks/etc. ", "createdAt": "14/5/2024 18:25:01", "updatedAt": "14/5/2024 18:25:01", "userId": 69, "suggestionId": 33}, {"id": 60, "message": "I like the progressive information gathering too.", "createdAt": "14/5/2024 18:26:18", "updatedAt": "14/5/2024 18:26:18", "userId": 19, "suggestionId": 33}, {"id": 61, "message": "It's mainly temporary as not all the talents/skills are available. If i made them cheaper now it would make it so you can unlock everything", "createdAt": "14/5/2024 23:26:46", "updatedAt": "14/5/2024 23:26:46", "userId": 3, "suggestionId": 34}, {"id": 62, "message": "Which would make builds pointless\n", "createdAt": "14/5/2024 23:26:55", "updatedAt": "14/5/2024 23:26:55", "userId": 3, "suggestionId": 34}, {"id": 63, "message": "Disagree on that. BUT having more levels for individual skills/passives like a few of them already have would be nice.", "createdAt": "14/5/2024 23:27:30", "updatedAt": "14/5/2024 23:27:30", "userId": 48, "suggestionId": 34}, {"id": 64, "message": "Currently it won't let you sell the last of an item if it's equipped, but can add some visual indicators too", "createdAt": "15/5/2024 00:22:25", "updatedAt": "15/5/2024 00:22:25", "userId": 3, "suggestionId": 31}, {"id": 65, "message": "I like the idea of getting the drop and unlocking the info. Or possibly unlock that info as you complete runs gaining knowledge of the zone.", "createdAt": "15/5/2024 03:52:57", "updatedAt": "15/5/2024 03:52:57", "userId": 87, "suggestionId": 33}, {"id": 66, "message": "This could also make room for stronger active skills -> more engaging combat.", "createdAt": "15/5/2024 17:41:55", "updatedAt": "15/5/2024 17:41:55", "userId": 15, "suggestionId": 35}, {"id": 67, "message": "So that buff stacking doesn't happen to a ridiculous extent, it still takes 1 'buff turn', given per turn.", "createdAt": "15/5/2024 17:43:31", "updatedAt": "15/5/2024 17:43:31", "userId": 15, "suggestionId": 35}, {"id": 68, "message": "I have to agree that <PERSON> feels bad to use.", "createdAt": "15/5/2024 17:45:02", "updatedAt": "15/5/2024 17:45:02", "userId": 77, "suggestionId": 35}, {"id": 69, "message": "most skills fell terrible to use, specially short buffs like High guard", "createdAt": "15/5/2024 18:31:47", "updatedAt": "15/5/2024 18:31:47", "userId": 48, "suggestionId": 35}, {"id": 70, "message": "I would like it as a personal list that start blank and you start filling it as you find each possible drop.  would be awesome", "createdAt": "15/5/2024 18:33:18", "updatedAt": "15/5/2024 18:33:18", "userId": 48, "suggestionId": 33}, {"id": 71, "message": "The current system should stay, but adding a time window so we can hit trully innactive people would be nice.", "createdAt": "15/5/2024 18:35:34", "updatedAt": "15/5/2024 18:35:34", "userId": 48, "suggestionId": 27}, {"id": 72, "message": "Yeah will do, it adjusts in PvP currently but need to extend it to PvE battles as well", "createdAt": "15/5/2024 19:25:29", "updatedAt": "15/5/2024 19:25:29", "userId": 3, "suggestionId": 36}, {"id": 73, "message": "It should be a sliding scale based on difference of levels. It also seems like the higher level you get XP should also go up.", "createdAt": "15/5/2024 19:25:58", "updatedAt": "15/5/2024 19:25:58", "userId": 88, "suggestionId": 36}, {"id": 74, "message": "Hard agree - it doesn't feel super worth it to take a turn of damage in exchange for minor buffs.", "createdAt": "15/5/2024 23:00:02", "updatedAt": "15/5/2024 23:00:02", "userId": 69, "suggestionId": 35}, {"id": 75, "message": "Moving this to denied for now until more talents are added, then will have another look at the costs", "createdAt": "16/5/2024 10:03:42", "updatedAt": "16/5/2024 10:03:42", "userId": 3, "suggestionId": 34}, {"id": 76, "message": "Rage skill seems to be useless.\nIt takes 1 turn to be active. It gives 20% more damage during 5 turns.\nImagine your base damage is 100.\nThe calculation for 6 turns :\nWithout the skill : 100 x 6 = 600 damage\nWith the skill : 0 + (100+20%) x 5 = 0+120x5 = 600 damage", "createdAt": "16/5/2024 13:18:58", "updatedAt": "16/5/2024 13:18:58", "userId": 103, "suggestionId": 35}, {"id": 77, "message": "Maybe something to indicate the damage increase. Like round 10 damage 1.5x. Something like that. Right now it goes up and I don't understand what's happening", "createdAt": "16/5/2024 22:29:41", "updatedAt": "16/5/2024 22:29:41", "userId": 77, "suggestionId": 37}, {"id": 78, "message": "At least for alpha/beta I think that's a good idea. Personally, I find it very confusing trying to figure out how long buffs, debuffs and healing are active for.", "createdAt": "16/5/2024 22:31:21", "updatedAt": "16/5/2024 22:31:21", "userId": 19, "suggestionId": 37}, {"id": 79, "message": "Both good additions ", "createdAt": "16/5/2024 22:54:19", "updatedAt": "16/5/2024 22:54:19", "userId": 88, "suggestionId": 37}, {"id": 80, "message": "Passive in strength tree. Low chance to intimidate enemy when attacking, they skip a turn.\nPassive in dexterity tree. Low chance to double ranged attack for the cost of 1 ammo.", "createdAt": "17/5/2024 00:51:34", "updatedAt": "17/5/2024 00:51:34", "userId": 77, "suggestionId": 40}, {"id": 81, "message": "\nProfessional Mugger: Mug a player right after he received his part-time-job payment\nVery Nice: Donate 6969 gold to the Shrine.  ", "createdAt": "17/5/2024 01:15:18", "updatedAt": "17/5/2024 01:15:18", "userId": 48, "suggestionId": 39}, {"id": 82, "message": "\nProfessional Mugger: Mug a player right after he received his part-time-job payment\nVery Nice: Donate 6969 gold to the Shrine.  ", "createdAt": "17/5/2024 01:15:19", "updatedAt": "17/5/2024 01:15:19", "userId": 48, "suggestionId": 39}, {"id": 83, "message": "Rage skill has been adjusted since my last comment,n now it's +60% damage during 4 turns, so it's OK.", "createdAt": "17/5/2024 06:34:48", "updatedAt": "17/5/2024 06:34:48", "userId": 103, "suggestionId": 35}, {"id": 84, "message": "Please, no. No ads, and nothing that encourages not actually playing the game.", "createdAt": "18/5/2024 21:30:44", "updatedAt": "18/5/2024 21:30:44", "userId": 69, "suggestionId": 41}, {"id": 85, "message": "i dont like either the idea of ads or auto battle personally", "createdAt": "18/5/2024 21:31:30", "updatedAt": "18/5/2024 21:31:30", "userId": 118, "suggestionId": 41}, {"id": 86, "message": "the game should me monetized in some way to give the developers something back reward ads are an easy way . It's just an addition for people to use there is no need to use it just convince ", "createdAt": "18/5/2024 21:33:28", "updatedAt": "18/5/2024 21:33:28", "userId": 91, "suggestionId": 41}, {"id": 87, "message": "someone in the chat said a talent that reduces penalty when getting attacked ", "createdAt": "18/5/2024 21:33:41", "updatedAt": "18/5/2024 21:33:41", "userId": 118, "suggestionId": 40}, {"id": 88, "message": "For a more passive play style ", "createdAt": "18/5/2024 21:33:51", "updatedAt": "18/5/2024 21:33:51", "userId": 91, "suggestionId": 41}, {"id": 89, "message": "id personally rather have dedicated places for ads on my screen like on the side or top than have that ", "createdAt": "18/5/2024 21:38:01", "updatedAt": "18/5/2024 21:38:01", "userId": 118, "suggestionId": 41}, {"id": 90, "message": "It shouldn't be monetized yet because it's in closed alpha and all progress will be wiped upon the end of testing - but when it does get monetized, I'd prefer buying a premium subscription that say, decrease AP regen time to having to watch ads. And I don't want ads getting in the way of the beautiful UI in general, even on the side or top, but I guess that could also be solved by buying a premium subscription ... But it would make the first impression that much worse.", "createdAt": "18/5/2024 22:05:32", "updatedAt": "18/5/2024 22:05:32", "userId": 69, "suggestionId": 41}, {"id": 91, "message": "Maybe a more idle way of playing the game could be good but combat should be active and fun imo.", "createdAt": "18/5/2024 22:58:48", "updatedAt": "18/5/2024 22:58:48", "userId": 77, "suggestionId": 41}, {"id": 92, "message": "Another idea is that the longer that you have been crafting, the less resources you get back.", "createdAt": "19/5/2024 19:25:24", "updatedAt": "19/5/2024 19:25:24", "userId": 118, "suggestionId": 15}, {"id": 93, "message": "Added in v0.2.13", "createdAt": "20/5/2024 18:26:53", "updatedAt": "20/5/2024 18:26:53", "userId": 3, "suggestionId": 44}, {"id": 94, "message": "Added in v0.2.13", "createdAt": "20/5/2024 18:27:45", "updatedAt": "20/5/2024 18:27:45", "userId": 3, "suggestionId": 43}, {"id": 95, "message": "Added a brief description for item drops per location in v0.2.13. Upcoming wiki will help with this as well as an item encyclopedia at some point", "createdAt": "20/5/2024 18:29:06", "updatedAt": "20/5/2024 18:29:06", "userId": 3, "suggestionId": 33}, {"id": 96, "message": "Added a brief description for item drops per location in v0.2.13. Upcoming wiki will help with this as well as an item encyclopedia at some point.  In an upcoming patch i'll include more details of drops in quest descriptions too", "createdAt": "20/5/2024 18:30:36", "updatedAt": "20/5/2024 18:30:36", "userId": 3, "suggestionId": 45}, {"id": 97, "message": "Added in v0.2.13", "createdAt": "20/5/2024 18:31:38", "updatedAt": "20/5/2024 18:31:38", "userId": 3, "suggestionId": 37}, {"id": 98, "message": "Added in v0.2.13", "createdAt": "20/5/2024 18:32:22", "updatedAt": "20/5/2024 18:32:22", "userId": 3, "suggestionId": 9}, {"id": 99, "message": "<PERSON>, High Guard + <PERSON> Frenzy were all buffed in v0.2.13. <PERSON> continue to look at talent balancing but I think they should still be taking a turn to cast", "createdAt": "20/5/2024 18:34:20", "updatedAt": "20/5/2024 18:34:20", "userId": 3, "suggestionId": 35}, {"id": 100, "message": "I don't have a name for this but:\nClick every button in the game", "createdAt": "20/5/2024 21:05:52", "updatedAt": "20/5/2024 21:05:52", "userId": 49, "suggestionId": 39}, {"id": 101, "message": "Added in patch v0.2.14", "createdAt": "21/5/2024 21:17:08", "updatedAt": "21/5/2024 21:17:08", "userId": 3, "suggestionId": 29}, {"id": 102, "message": "Added in patch v0.2.14", "createdAt": "21/5/2024 21:17:34", "updatedAt": "21/5/2024 21:17:34", "userId": 3, "suggestionId": 36}, {"id": 103, "message": "What do you mean by dice roll system? In what situations? \nAlso additional chat rooms will be added at some point - gangs will be next so they'll be accessible there first", "createdAt": "22/5/2024 01:03:40", "updatedAt": "22/5/2024 01:03:40", "userId": 3, "suggestionId": 48}, {"id": 104, "message": "ok", "createdAt": "22/5/2024 01:04:27", "updatedAt": "22/5/2024 01:04:27", "userId": 3, "suggestionId": 47}, {"id": 105, "message": "Added in v0.2.14", "createdAt": "22/5/2024 01:07:18", "updatedAt": "22/5/2024 01:07:18", "userId": 3, "suggestionId": 15}, {"id": 106, "message": "Passive. Crit chance. x% chance to critical strike and deal 2x damage.\nPassive strength tree. Life steal.  % of damage dealt restore as health.\nIntellect tree. Start gaining interest from your money in the bank. % per day.", "createdAt": "22/5/2024 15:47:17", "updatedAt": "22/5/2024 15:47:17", "userId": 77, "suggestionId": 40}, {"id": 107, "message": "A dice roll system is just as described, depending on what type of dice you want . It outputs it into the chat. See below as I’m basing this off of Roll20.\nhttps://wiki.roll20.net/Dice_Reference\n\nFor example: /me swings their fist at the direction of the robber.  (Roll to see if I succeed)\n\nInput: /roll 1d20+5\nOutput: 13 + 5 = 18 (I succeed in landing a punch)\n1 being how many dices, d followed by a number of what type of # roll up to to that number, + or - for roll modifier (buffs or debuffs)", "createdAt": "22/5/2024 18:44:19", "updatedAt": "22/5/2024 18:44:19", "userId": 89, "suggestionId": 48}, {"id": 108, "message": "Hm i think bailing yourself out defeats the point a bit though", "createdAt": "24/5/2024 20:56:00", "updatedAt": "24/5/2024 20:56:00", "userId": 3, "suggestionId": 54}, {"id": 109, "message": "The point is punishment right? Whether it's monetary or time-based doesn't really matter imo. If you're able to bail yourself out with pocketmoney it should be possible with bankfunds as well 🤷🏼‍♀️", "createdAt": "24/5/2024 21:32:36", "updatedAt": "24/5/2024 21:32:36", "userId": 12, "suggestionId": 54}, {"id": 110, "message": "I think bail is based on level? \nIt's actually a good money drain which all games need .", "createdAt": "24/5/2024 22:01:21", "updatedAt": "24/5/2024 22:01:21", "userId": 140, "suggestionId": 54}, {"id": 111, "message": "At some point it might have to be removed to prevent spam when the game gets fairly complete.", "createdAt": "25/5/2024 22:23:48", "updatedAt": "25/5/2024 22:23:48", "userId": 77, "suggestionId": 55}, {"id": 112, "message": "All tables in the game are getting a rework next patch/patch after with sorting/filtering/searching/pagination", "createdAt": "26/5/2024 12:28:15", "updatedAt": "26/5/2024 12:28:15", "userId": 3, "suggestionId": 58}, {"id": 113, "message": "By this do you mean mobile push notifications? Or some other notification types as well?", "createdAt": "26/5/2024 13:45:22", "updatedAt": "26/5/2024 13:45:22", "userId": 3, "suggestionId": 57}, {"id": 114, "message": "Will most likely have something like this through unlockable items rather than talents", "createdAt": "26/5/2024 22:12:02", "updatedAt": "26/5/2024 22:12:02", "userId": 3, "suggestionId": 52}, {"id": 115, "message": "Mission board is there for that reason, so that you still get something while offline.", "createdAt": "26/5/2024 22:52:31", "updatedAt": "26/5/2024 22:52:31", "userId": 15, "suggestionId": 60}, {"id": 116, "message": "I also don't like that skill but unsure what a good alternatrive could be. Higher stamina cost but it doesn't take a turn to use ?", "createdAt": "27/5/2024 17:32:52", "updatedAt": "27/5/2024 17:32:52", "userId": 77, "suggestionId": 62}, {"id": 117, "message": "One day", "createdAt": "28/5/2024 15:57:49", "updatedAt": "28/5/2024 15:57:49", "userId": 3, "suggestionId": 55}, {"id": 118, "message": "Fixed in 0.3.0", "createdAt": "28/5/2024 15:58:48", "updatedAt": "28/5/2024 15:58:48", "userId": 3, "suggestionId": 56}, {"id": 119, "message": "Added in 0.3.1 - Notification messages can now be clicked to hide. They were also reduced in size.", "createdAt": "28/5/2024 17:35:40", "updatedAt": "28/5/2024 17:35:40", "userId": 3, "suggestionId": 63}, {"id": 120, "message": "Gang quests that all gang members can contribute to and receive rewards from.", "createdAt": "29/5/2024 00:23:16", "updatedAt": "29/5/2024 00:23:16", "userId": 69, "suggestionId": 64}, {"id": 121, "message": "I would like to see gangs control particular parts of the map can be contested, these areas provides daily resources.", "createdAt": "29/5/2024 04:00:23", "updatedAt": "29/5/2024 04:00:23", "userId": 89, "suggestionId": 64}, {"id": 122, "message": "Drama, roles, alliances, gang treasury, gang upgrades/boosts\n\n(I also like the contested territory idea below this)", "createdAt": "29/5/2024 09:42:58", "updatedAt": "29/5/2024 09:42:58", "userId": 49, "suggestionId": 64}, {"id": 123, "message": "Gang Wars = Territory Wars FYI. Probably needs a rename", "createdAt": "29/5/2024 12:05:48", "updatedAt": "29/5/2024 12:05:48", "userId": 3, "suggestionId": 64}, {"id": 124, "message": "Turf Wars", "createdAt": "29/5/2024 12:12:19", "updatedAt": "29/5/2024 12:12:19", "userId": 49, "suggestionId": 64}, {"id": 125, "message": "Maybe missions could be used for that? Like missions that give stat points", "createdAt": "31/5/2024 02:52:13", "updatedAt": "31/5/2024 02:52:13", "userId": 77, "suggestionId": 52}, {"id": 126, "message": "Will be added oneday but not priority currently", "createdAt": "3/6/2024 14:47:20", "updatedAt": "3/6/2024 14:47:20", "userId": 3, "suggestionId": 68}, {"id": 127, "message": "It should do this automatically really, will look into it", "createdAt": "3/6/2024 15:17:18", "updatedAt": "3/6/2024 15:17:18", "userId": 3, "suggestionId": 71}, {"id": 128, "message": "Won't show online status (except for gangs/friends lists) but will probably implement a system where you can purchase a 'Spy' to have a chance to find out their online status/stats.\nThe second suggestion will be implemented more towards beta/release when live battles are added as the current combat system can't support this", "createdAt": "3/6/2024 15:21:18", "updatedAt": "3/6/2024 15:21:18", "userId": 3, "suggestionId": 17}, {"id": 129, "message": "Unfortunately I think this is too abusable without too much of a benefit", "createdAt": "3/6/2024 15:22:33", "updatedAt": "3/6/2024 15:22:33", "userId": 3, "suggestionId": 16}, {"id": 130, "message": "This will probably change when there's more mugging protection but not for this Alpha", "createdAt": "3/6/2024 15:25:21", "updatedAt": "3/6/2024 15:25:21", "userId": 3, "suggestionId": 46}, {"id": 131, "message": "Moving to denied but there will be items/talents that help with this.", "createdAt": "3/6/2024 15:29:01", "updatedAt": "3/6/2024 15:29:01", "userId": 3, "suggestionId": 60}, {"id": 132, "message": "Already a talent to increase max AP and a new 'AP Storage' item to help with this also", "createdAt": "3/6/2024 15:29:48", "updatedAt": "3/6/2024 15:29:48", "userId": 3, "suggestionId": 53}, {"id": 133, "message": "Would it be better to have a 'Redo zone' button or a checkbox to stay on the same zone until unchecked?", "createdAt": "3/6/2024 15:32:30", "updatedAt": "3/6/2024 15:32:30", "userId": 3, "suggestionId": 70}, {"id": 134, "message": "Accepted, will come sometime in the next few patches", "createdAt": "3/6/2024 15:33:02", "updatedAt": "3/6/2024 15:33:02", "userId": 3, "suggestionId": 69}, {"id": 135, "message": "I like the checkbox idea\n", "createdAt": "3/6/2024 15:48:54", "updatedAt": "3/6/2024 15:48:54", "userId": 156, "suggestionId": 70}, {"id": 136, "message": "A checkbox in the settings? Yes it could work", "createdAt": "3/6/2024 17:46:33", "updatedAt": "3/6/2024 17:46:33", "userId": 57, "suggestionId": 70}, {"id": 137, "message": "Strength tree: x% damage bonus with melee attacks if you have no ranged weapons equipped.\nDex tree: x% damage bonus with ranged attack if you have no melee weapons equipped.\n", "createdAt": "3/6/2024 18:36:49", "updatedAt": "3/6/2024 18:36:49", "userId": 77, "suggestionId": 40}, {"id": 138, "message": "Forgot to add: it might be able to be change to a picture with the text, instead of actual text ", "createdAt": "6/6/2024 06:02:08", "updatedAt": "6/6/2024 06:02:08", "userId": 172, "suggestionId": 72}, {"id": 139, "message": "Will be added when the new tables are ported over", "createdAt": "7/6/2024 16:33:29", "updatedAt": "7/6/2024 16:33:29", "userId": 3, "suggestionId": 74}, {"id": 140, "message": "Fixed", "createdAt": "7/6/2024 16:33:45", "updatedAt": "7/6/2024 16:33:45", "userId": 3, "suggestionId": 72}, {"id": 141, "message": "More or less neutral on this one, could be made into an incentive to play on the weekend too, like budget supermarket sales in Japan iykwim.", "createdAt": "9/6/2024 17:32:43", "updatedAt": "9/6/2024 17:32:43", "userId": 15, "suggestionId": 78}, {"id": 142, "message": "Added in 0.6.0", "createdAt": "10/6/2024 22:12:14", "updatedAt": "10/6/2024 22:12:14", "userId": 3, "suggestionId": 76}, {"id": 143, "message": "All I want is to keep my coveted member number. I wear it like a badge of honor.", "createdAt": "11/6/2024 00:48:23", "updatedAt": "11/6/2024 00:48:23", "userId": 69, "suggestionId": 79}, {"id": 144, "message": "Colored name fonts to show off your Alpha bling.", "createdAt": "11/6/2024 01:02:27", "updatedAt": "11/6/2024 01:02:27", "userId": 89, "suggestionId": 79}, {"id": 145, "message": "Maybe  a title rather than colored names? & The title could be colored!", "createdAt": "11/6/2024 02:46:05", "updatedAt": "11/6/2024 02:46:05", "userId": 69, "suggestionId": 79}, {"id": 146, "message": "Is this for tasks that havn't been handed in?\n", "createdAt": "14/6/2024 18:26:52", "updatedAt": "14/6/2024 18:26:52", "userId": 3, "suggestionId": 83}, {"id": 147, "message": "I can just raise the limits or have restocks for now until the Sunday shop stock is reworked. It's mainly to prevent too much access to things like the Balaclava", "createdAt": "14/6/2024 18:31:21", "updatedAt": "14/6/2024 18:31:21", "userId": 3, "suggestionId": 78}, {"id": 148, "message": "Will implement this soon. Would it be preferable to have the zone choice persist indefinitely or just for your current session? ", "createdAt": "14/6/2024 18:33:21", "updatedAt": "14/6/2024 18:33:21", "userId": 3, "suggestionId": 70}, {"id": 149, "message": "It can persist. To change the zone is something rare. I need to spend enough points before I feel confident to go the next zone level", "createdAt": "14/6/2024 18:39:23", "updatedAt": "14/6/2024 18:39:23", "userId": 57, "suggestionId": 70}, {"id": 150, "message": "Yeah exactly - I was thinking some sort of indicator on the tasks button to show there's completed tasks to hand in, and then when you go to tasks it could perhaps have an indicator on the person who you can hand it in to (and say completed rather than in progress).", "createdAt": "16/6/2024 14:28:31", "updatedAt": "16/6/2024 14:28:31", "userId": 181, "suggestionId": 83}, {"id": 151, "message": "Yeah I can do that. Currently you can just leave at any time during Alpha though", "createdAt": "17/6/2024 15:27:51", "updatedAt": "17/6/2024 15:27:51", "userId": 1, "suggestionId": 85}, {"id": 152, "message": "Noted. Thanks ^_^\n", "createdAt": "17/6/2024 15:30:19", "updatedAt": "17/6/2024 15:30:19", "userId": 195, "suggestionId": 85}, {"id": 153, "message": "I agree that it doesn't make sense for the resurrected person to be blocked from attacking. It kinda makes the fact that you get healed back to full health less useful as well. Imo there should only be a cooldown on attacks on the resurrected person, not on attacks of the resurrected person.", "createdAt": "18/6/2024 20:14:44", "updatedAt": "18/6/2024 20:14:44", "userId": 12, "suggestionId": 86}, {"id": 155, "message": "Like the game alot and will be returning but yah im losing my drive lately mistly because alpha wipe. That being said, its really about if you feel you have acomplished what you intended to and want to move on.", "createdAt": "19/6/2024 22:18:59", "updatedAt": "19/6/2024 22:18:59", "userId": 51, "suggestionId": 87}, {"id": 156, "message": "Sounds good and try to keep the existing community involved over on discord, keep the discussion going on CBT.\n\nAnother topic which would be nice to address is the story/story mode/getting a writer involved/onboard. Could help build out the lore and provide an extra dimension to the game overall.\n\nWill say it again but thank you for all the hard work running the game by yourself, was a much higher quality Alpha Test than I had expected.", "createdAt": "19/6/2024 22:35:45", "updatedAt": "19/6/2024 22:35:45", "userId": 15, "suggestionId": 87}, {"id": 157, "message": "I agree with serene. I had fun playing the game and watching it grow. Even though it was an alpha test I encountered very few issues and most of them were solved immediately. Well done!\n\nAlso out of curiosity, what did you plan to accomplish during this alpha test and are you happy with the results?", "createdAt": "19/6/2024 23:44:55", "updatedAt": "19/6/2024 23:44:55", "userId": 49, "suggestionId": 87}, {"id": 158, "message": "Isnt the point of Alpha is to create something that's exciting and ready to test for beta/release? If progress is going to be wiped at the end of Alpha, there's no stopping people from losing their drive. Why worry about losing progress anyways when the point is to test out the game?", "createdAt": "19/6/2024 23:45:27", "updatedAt": "19/6/2024 23:45:27", "userId": 170, "suggestionId": 87}, {"id": 159, "message": "New inventory table will be coming this week", "createdAt": "20/6/2024 23:05:35", "updatedAt": "20/6/2024 23:05:35", "userId": 3, "suggestionId": 89}, {"id": 160, "message": "Fixed in 0.9.0, was a bug", "createdAt": "20/6/2024 23:05:57", "updatedAt": "20/6/2024 23:05:57", "userId": 3, "suggestionId": 88}, {"id": 161, "message": "Added in 0.9.0", "createdAt": "20/6/2024 23:06:51", "updatedAt": "20/6/2024 23:06:51", "userId": 3, "suggestionId": 69}, {"id": 162, "message": "Think the new player PvP experience is better now with the new injury changes, moving to completed but will continue to monitor", "createdAt": "21/6/2024 12:14:00", "updatedAt": "21/6/2024 12:14:00", "userId": 3, "suggestionId": 27}, {"id": 163, "message": "Hm i guess this would require another popup with your currently equipped item to the side, will see how easy that is to implement", "createdAt": "21/6/2024 12:15:21", "updatedAt": "21/6/2024 12:15:21", "userId": 3, "suggestionId": 84}, {"id": 164, "message": "This is definitely in the works but less priority considering the Task system will be getting a mini overhaul soon. For now I can add a completed indicator for combat tasks but one for item collection tasks is a little harder to implement", "createdAt": "21/6/2024 12:18:23", "updatedAt": "21/6/2024 12:18:23", "userId": 3, "suggestionId": 83}, {"id": 165, "message": "Weird, stun should be happening immediately on the turn you use it I thought", "createdAt": "22/6/2024 19:59:59", "updatedAt": "22/6/2024 19:59:59", "userId": 3, "suggestionId": 90}, {"id": 166, "message": "@below that is indeed not the case, however the stun does last '3 turns' in this case, as in 2 effective turns, just not on the turn you cast it.", "createdAt": "22/6/2024 20:03:06", "updatedAt": "22/6/2024 20:03:06", "userId": 15, "suggestionId": 90}, {"id": 167, "message": "I want also to suggest the ability to edit an post because I did a gross mistake over there xD (but overrall the fact it dows not activate immediatelly is weird but still, it actually still works for 2 turns, my b)", "createdAt": "22/6/2024 20:14:14", "updatedAt": "22/6/2024 20:14:14", "userId": 48, "suggestionId": 90}, {"id": 168, "message": "Added in v0.12.4", "createdAt": "4/7/2024 15:38:49", "updatedAt": "4/7/2024 15:38:49", "userId": 3, "suggestionId": 70}, {"id": 169, "message": "Added in v0.12.6", "createdAt": "4/7/2024 18:51:29", "updatedAt": "4/7/2024 18:51:29", "userId": 3, "suggestionId": 50}, {"id": 170, "message": "Added in v0.12.6", "createdAt": "4/7/2024 18:51:47", "updatedAt": "4/7/2024 18:51:47", "userId": 3, "suggestionId": 42}, {"id": 171, "message": "Added in v0.12.5", "createdAt": "4/7/2024 18:52:25", "updatedAt": "4/7/2024 18:52:25", "userId": 3, "suggestionId": 65}, {"id": 172, "message": "Treatments less complicated, the variety of injuries is fun to play around with and has potential.\n\nSome severe injuries should basically enforce hospitalisation, but the chances of receiving such injuries should be minimal, while overall injury rate can also see some reduction I feel.", "createdAt": "10/7/2024 19:12:09", "updatedAt": "10/7/2024 19:12:09", "userId": 15, "suggestionId": 91}, {"id": 173, "message": "- I feel like the injuries system is better than the hospitalisation system because now we can choose whether we want to wait out the hospital timer or power through while injured.\n- It doesn't feel complicated.\n- Items that treat by injury tier rather than type sounds like a good idea since it would reduce the number of healing items that would need to exist while also making it easier to heal injuries in general.\n\nI do feel like the injury timers are a bit long though (and expensive to heal if you have multiple injuries), but it could probably be mitigated by reducing the injury rate as serene suggested.", "createdAt": "11/7/2024 03:02:44", "updatedAt": "11/7/2024 03:02:44", "userId": 49, "suggestionId": 91}]