import type { Prisma } from "@prisma/client";

type JsonValue = Prisma.JsonValue;

export interface Session {
    userId: string;
    ipAddress?: string | null;
    userAgent?: string | null;
}

export interface AuditEntryParams {
    action: string;
    userId?: string | number; // Optional - will be inferred from context if not provided
    info?: object;
    session?: Session; // Available for manual override of ipAddress/userAgent
    logType?: "admin" | "player";
}

export interface AuditEntryMetadata {
    ipAddress?: string | null;
    userAgent?: string | null;
    correlationId?: string | null;
}

export interface AuditEntry {
    id: string;
    userId: number;
    action: string;
    info: JsonValue;
    createdAt: Date;
    logType: "admin" | "player";
    metadata: AuditEntryMetadata;
}
