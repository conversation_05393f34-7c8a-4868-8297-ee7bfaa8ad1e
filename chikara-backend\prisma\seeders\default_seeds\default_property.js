export const defaultProperty = [
    {
        id: 1,
        name: "Capsule Hotel Pod",
        propertyType: "housing",
        cost: 15000,
        upkeep: 200,
        slots: 1,
        buffs: {
            energyRegen: 1.02,
            healthRegen: 1.02,
        },
        description: "A minimal sleeping pod in a capsule hotel. Better than nothing!",
        image: "https://i.imgur.com/IiVmNPF.png",
    },
    {
        id: 2,
        name: "Manga Cafe Premium Room",
        propertyType: "housing",
        cost: 25000,
        upkeep: 300,
        slots: 1,
        buffs: {
            energyRegen: 1.02,
            healthRegen: 1.02,
            gamingBonus: 1.05,
        },
        description: "A private room in a manga cafe with unlimited entertainment access.",
    },
    {
        id: 3,
        name: "Share House Room",
        propertyType: "housing",
        cost: 35000,
        upkeep: 400,
        slots: 2,
        buffs: {
            energyRegen: 1.03,
            healthRegen: 1.03,
            socialXP: 1.05,
        },
        description: "A private room in a shared student house. Common areas provide social bonuses.",
    },
    {
        id: 4,
        name: "Converted Office Space",
        propertyType: "housing",
        cost: 45000,
        upkeep: 500,
        slots: 2,
        buffs: {
            energyRegen: 1.03,
            healthRegen: 1.03,
            studySpeed: 1.03,
            nightworkBonus: 1.05,
        },
        description: "A repurposed office space in a business district. Great for late-night studying.",
    },
    {
        id: 9,
        name: "Student Apartment",
        propertyType: "housing",
        cost: 75000,
        upkeep: 600,
        slots: 3,
        buffs: {
            energyRegen: 1.04,
            healthRegen: 1.04,
            studySpeed: 1.05,
        },
        description: "A compact 1K apartment near the school. Perfect for studying.",
    },
    {
        id: 10,
        name: "Urban Apartment",
        propertyType: "housing",
        cost: 150000,
        upkeep: 1000,
        slots: 4,
        buffs: {
            energyRegen: 1.05,
            healthRegen: 1.05,
            craftSpeed: 1.03,
            apRegen: 1.02,
        },
        description: "A modern 1LDK apartment in a convenient location.",
    },
    {
        id: 5,
        name: "Rooftop Apartment",
        propertyType: "housing",
        cost: 200000,
        upkeep: 1200,
        slots: 4,
        buffs: {
            energyRegen: 1.06,
            healthRegen: 1.06,
            meditation: 1.05,
            weatherBonus: 1.1,
        },
        description: "A cozy apartment with a private rooftop space. Perfect for stargazing.",
    },
    {
        id: 11,
        name: "Family Condominium",
        propertyType: "housing",
        cost: 300000,
        upkeep: 2000,
        slots: 5,
        buffs: {
            energyRegen: 1.07,
            healthRegen: 1.07,
            craftSpeed: 1.05,
            apRegen: 1.03,
            storage: 1.2,
        },
        description: "A spacious 2LDK condo with extra storage space.",
    },
    {
        id: 6,
        name: "Artist Loft",
        propertyType: "housing",
        cost: 400000,
        upkeep: 2500,
        slots: 5,
        buffs: {
            energyRegen: 1.07,
            healthRegen: 1.07,
            craftSpeed: 1.08,
            creativeBonus: 1.15,
        },
        description: "A spacious loft with great natural light and inspiration bonuses.",
    },
    {
        id: 12,
        name: "Garden House",
        propertyType: "housing",
        cost: 500000,
        upkeep: 3000,
        slots: 6,
        buffs: {
            energyRegen: 1.08,
            healthRegen: 1.08,
            craftSpeed: 1.07,
            meditation: 1.1,
            gardenYield: 1.2,
        },
        description: "A traditional house with a peaceful garden for meditation.",
    },
    {
        id: 7,
        name: "Eco Smart House",
        propertyType: "housing",
        cost: 600000,
        upkeep: 1500,
        slots: 6,
        buffs: {
            energyRegen: 1.08,
            healthRegen: 1.08,
            sustainableBonus: 1.2,
            gardenYield: 1.15,
            upkeepReduction: 1.2,
        },
        description: "A sustainable smart home with solar panels and advanced recycling systems.",
    },
    {
        id: 13,
        name: "Smart House",
        propertyType: "housing",
        cost: 750000,
        upkeep: 4000,
        slots: 7,
        buffs: {
            energyRegen: 1.09,
            healthRegen: 1.09,
            craftSpeed: 1.08,
            techBonus: 1.1,
            apRegen: 1.04,
        },
        description: "A high-tech house with automated systems and a home lab.",
    },
    {
        id: 8,
        name: "Modern Dojo Residence",
        propertyType: "housing",
        cost: 850000,
        upkeep: 4500,
        slots: 7,
        buffs: {
            energyRegen: 1.09,
            healthRegen: 1.09,
            martialArtsXP: 1.15,
            disciplineBonus: 1.1,
            meditation: 1.15,
        },
        description: "A traditional dojo with living quarters. Perfect for martial arts training.",
    },
    {
        id: 14,
        name: "Luxury Apartment",
        propertyType: "housing",
        cost: 1000000,
        upkeep: 5000,
        slots: 8,
        buffs: {
            energyRegen: 1.1,
            healthRegen: 1.1,
            craftSpeed: 1.1,
            apRegen: 1.05,
            socialStatus: 1.1,
        },
        description: "A prestigious apartment in an upscale district.",
    },
    {
        id: 19,
        name: "Tokyo Bay Floating Apartment",
        propertyType: "housing",
        cost: 1500000,
        upkeep: 6000,
        slots: 8,
        buffs: {
            energyRegen: 1.11,
            healthRegen: 1.11,
            uniqueLocation: 1.15,
            waterBonus: 1.2,
            stressReduction: 1.1,
        },
        description: "A unique floating apartment in Tokyo Bay with calming water views.",
    },
    {
        id: 15,
        name: "Skyscraper Suite",
        propertyType: "housing",
        cost: 2000000,
        upkeep: 7500,
        slots: 9,
        buffs: {
            energyRegen: 1.12,
            healthRegen: 1.12,
            craftSpeed: 1.12,
            apRegen: 1.06,
            xpGain: 1.05,
            viewBonus: 1.1,
        },
        description: "A luxurious suite high above Tokyo with stunning views.",
    },
    {
        id: 16,
        name: "Penthouse",
        propertyType: "housing",
        cost: 3500000,
        upkeep: 10000,
        slots: 10,
        buffs: {
            energyRegen: 1.15,
            healthRegen: 1.15,
            craftSpeed: 1.15,
            apRegen: 1.07,
            xpGain: 1.07,
            partyBonus: 1.2,
        },
        description: "A spectacular penthouse perfect for hosting exclusive parties.",
    },
    {
        id: 20,
        name: "Quantum Research House",
        propertyType: "housing",
        cost: 4000000,
        upkeep: 12000,
        slots: 11,
        buffs: {
            energyRegen: 1.18,
            healthRegen: 1.18,
            techBonus: 1.15,
            researchSpeed: 1.2,
            timeManipulation: 1.1,
            apRegen: 1.08,
        },
        description: "A cutting-edge research facility and home with experimental quantum technology.",
    },
    {
        id: 17,
        name: "Private Tower",
        propertyType: "housing",
        cost: 5000000,
        upkeep: 15000,
        slots: 12,
        buffs: {
            energyRegen: 1.2,
            healthRegen: 1.2,
            craftSpeed: 1.2,
            apRegen: 1.1,
            xpGain: 1.1,
            allStats: 1.05,
        },
        description: "Your own tower in Tokyo. The ultimate status symbol.",
    },
    {
        id: 18,
        name: "Secret Underground Base",
        propertyType: "housing",
        cost: 10000000,
        upkeep: 25000,
        slots: 15,
        buffs: {
            energyRegen: 1.25,
            healthRegen: 1.25,
            craftSpeed: 1.25,
            apRegen: 1.15,
            xpGain: 1.15,
            allStats: 1.1,
            secretBonus: 1.2,
        },
        description: "A hidden high-tech facility beneath the city. For those who truly run Tokyo.",
    },
];

export default defaultProperty;
