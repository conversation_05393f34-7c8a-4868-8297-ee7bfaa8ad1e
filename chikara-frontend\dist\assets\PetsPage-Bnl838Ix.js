import{F as j,b as T,c as f,e as w,g as z,ag as S,j as e,N as h,B as R,r as x,db as I,X as C,dc as F,Z as H,h as v,bX as O,a as X,L as B,O as G,T as Q,J as E,M as D,dd as K,P as Z,G as V}from"./index--cEnoMkg.js";import{i as Y}from"./intervalToDuration-f8TGlnXJ.js";import{D as L}from"./dumbbell-CX2fW577.js";import"./differenceInHours-BsBlWOxD.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J=[["path",{d:"M12 2C8 2 4 8 4 14a8 8 0 0 0 16 0c0-6-4-12-8-12",key:"1le142"}]],U=j("egg",J);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]],P=j("gamepad-2",W);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]],se=j("smile",ee);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]],q=j("utensils",te);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=[["path",{d:"M12.8 19.6A2 2 0 1 0 14 16H2",key:"148xed"}],["path",{d:"M17.5 8a2.5 2.5 0 1 1 2 4H2",key:"1u4tom"}],["path",{d:"M9.8 4.4A2 2 0 1 1 11 8H2",key:"75valh"}]],le=j("wind",re),ae=(s={})=>T(f.pets.list.queryOptions({staleTime:6e4,...s})),ne=(s={})=>{const r=w();return z(f.pets.customize.mutationOptions({onSuccess:t=>{r.invalidateQueries({queryKey:f.pets.list.key()}),s.onSuccess?.(t)},onError:t=>{console.error("Customize pet error:",t),s.onError?.(t)}}))},_=(s={})=>{const r=w();return z(f.pets.evolve.mutationOptions({onSuccess:t=>{r.invalidateQueries({queryKey:f.pets.list.key()}),s.onSuccess?.(t)},onError:t=>{console.error("Evolve pet error:",t),s.onError?.(t)}}))},ie=(s={})=>{const r=w();return z(f.pets.setActive.mutationOptions({onSuccess:t=>{r.invalidateQueries({queryKey:f.pets.list.key()}),s.onSuccess?.(t)},onError:t=>{console.error("Set active pet error:",t),s.onError?.(t)}}))},g=(s,r="current")=>{const t=s.evolution[r],n=s.pet?.evolution_stages.find(l=>l.stage===t);return S(n?.image)||S(!0)};function oe({selectedPet:s}){const{mutate:r,isPending:t}=_();if(!s)return null;const n=s.level>=(s.evolution.requiredLevel||0),l=s.happiness>=100,d=n&&l&&s.evolution.next,u=()=>{r({petId:s.id})};return e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-blue-900/30",children:[e.jsx("h3",{className:"text-white font-semibold mb-2",children:"Evolution Progress"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-10 lg:size-18 rounded-lg bg-gray-700/50 flex items-center justify-center",children:e.jsx("img",{src:g(s),alt:s.evolution.current,className:"size-8 lg:size-16 opacity-70"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex justify-between text-xs lg:text-base mb-1",children:[e.jsx("span",{className:"text-gray-400",children:h(s.evolution.current)}),e.jsx("span",{className:"text-blue-300",children:h(s.evolution.next)})]}),e.jsx("div",{className:"w-full h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-linear-to-r from-blue-600 to-cyan-500 rounded-full",style:{width:`${s.evolution.progress}%`}})})]}),e.jsx("div",{className:"size-10 lg:size-18 rounded-lg bg-gray-700/50 flex items-center justify-center",children:e.jsx("img",{src:g(s,"next"),alt:s.evolution.next,className:"size-8 lg:size-16 opacity-30 grayscale"})})]}),e.jsx("div",{className:"mt-2 text-xs text-gray-400",children:e.jsxs("span",{children:["Required: Level ",s.evolution.requiredLevel," and 100% Happiness"]})}),d&&e.jsxs("button",{className:"mt-4 w-full py-2 bg-linear-to-r from-blue-600 to-cyan-500 hover:from-blue-500 hover:to-cyan-400 text-white font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed",disabled:t,onClick:u,children:["Evolve ",h(s.name)]})]})}function ce({selectedPet:s}){const{mutate:r,isPending:t}=_(),{EGG_TIME_PER_PROGRESS_POINT:n}=R();if(!s)return null;const l=s.evolution.progress,d=s.evolution.requiredEggProgress??100,m=(d-l)*n/(1e3*60*60),i=Math.min(Math.round(l/d*100),100),b=y=>{if(y<=0)return"Ready to hatch!";const c=Y({start:0,end:y*60*60*1e3});return c.days&&c.days>0?`${c.days}d ${c.hours||0}h remaining`:c.hours&&c.hours>0?`${c.hours}h ${c.minutes||0}m remaining`:`${c.minutes}m remaining`};return e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-blue-900/30 flex flex-col items-center",children:[e.jsx("h3",{className:"text-white font-semibold mb-2",children:"Egg Incubation"}),e.jsx("div",{className:"relative my-4 mx-auto",children:e.jsxs("div",{className:"size-44 rounded-lg bg-linear-to-br from-blue-500/20 to-purple-700/30 flex items-center justify-center overflow-hidden border border-blue-500/30",children:[g(s)?e.jsx("img",{src:g(s),alt:"Pet Egg",style:{opacity:`${i+40}%`},className:"size-40 object-contain"}):e.jsx(U,{className:"size-36 text-yellow-200/80"}),i>=90&&i<95&&e.jsx("div",{className:"absolute inset-0 bg-yellow-400/10 animate-pulse rounded-lg"}),i>=95&&e.jsx("div",{className:"absolute inset-0 bg-yellow-400/30 animate-pulse rounded-lg"})]})}),e.jsx("div",{className:"flex items-center gap-4 w-3/4",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"text-white font-medium",children:s.name}),e.jsx("span",{className:"text-blue-300",children:h(s.evolution.next)})]}),e.jsx("div",{className:"w-full h-2.5 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-linear-to-r from-blue-600 to-cyan-400 rounded-full transition-all duration-1000",style:{width:`${i}%`}})}),e.jsxs("div",{className:"mt-2 flex justify-between text-xs",children:[e.jsxs("span",{className:"text-yellow-300/90",children:[i,"% incubated"]}),e.jsx("span",{className:"text-gray-300",children:b(m)})]}),e.jsx("div",{className:" text-xs text-gray-400 text-center",children:i>=100?e.jsx("p",{className:"mt-1 text-green-400",children:"This egg is ready to hatch! Interact with it to welcome your new pet."}):i>=90?e.jsx("p",{className:"mt-1 text-yellow-300",children:"Your egg is nearly ready to hatch! The egg is showing signs of movement."}):i>=50?e.jsx("p",{className:"mt-1",children:"The egg is warm to the touch and occasionally makes small movements."}):e.jsx("p",{className:"mt-1",children:"The egg needs more time to develop. Be patient!"})})]})}),i>=100&&e.jsx("button",{className:"mt-4 w-1/2 mb-2 py-2 bg-linear-to-r from-yellow-500 to-amber-600 hover:from-yellow-400 hover:to-amber-500 text-white font-medium rounded-md transition-colors",disabled:t,onClick:()=>r({petId:s.id}),children:"Hatch Egg"})]})}function de({isOpen:s,onClose:r,pet:t,onSuccess:n}){const[l,d]=x.useState(t.name),[u,m]=x.useState(!1),[i,b]=x.useState(null),{mutate:y}=ne({onSuccess:()=>{m(!1),n?.(),r()},onError:o=>{m(!1),b(o.message||"Failed to rename your pet. Please try again.")}}),c=o=>{if(o.preventDefault(),!l.trim()){b("Pet name cannot be empty");return}if(l.trim()===t.name){r();return}b(null),m(!0),y({petId:t.id,customization:{name:l.trim()}})};return s?e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/70",children:e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-blue-900/50 p-4 w-80 max-w-full",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-white font-semibold flex items-center gap-2",children:[e.jsx(I,{className:"size-4 text-blue-400"}),"Rename Pet"]}),e.jsx("button",{className:"text-gray-400 hover:text-white",onClick:r,children:e.jsx(C,{className:"size-5"})})]}),e.jsx("div",{className:"flex items-center justify-center my-4",children:e.jsx("div",{className:"relative size-20 rounded-lg bg-linear-to-br from-blue-500 to-blue-700 flex items-center justify-center overflow-hidden border border-blue-500",children:e.jsx("img",{src:g(t),alt:t.name,className:"size-full object-cover"})})}),e.jsxs("form",{onSubmit:c,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"pet-name",className:"block text-sm font-medium text-gray-300 mb-1",children:"Pet Name"}),e.jsx(F,{autoFocus:!0,id:"pet-name",value:l,className:"bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500",placeholder:"Enter new name",maxLength:20,disabled:u,onChange:o=>d(o.target.value)}),i&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:i})]}),e.jsxs("div",{className:"flex gap-2 mt-4",children:[e.jsx("button",{type:"button",className:"flex-1 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors",disabled:u,onClick:r,children:"Cancel"}),e.jsx("button",{type:"submit",className:"flex-1 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:bg-blue-800 disabled:opacity-70",disabled:u,children:"Save Name"})]})]})]})}):null}function me({selectedPet:s,handleInteraction:r}){const{mutate:t,isPending:n}=ie(),[l,d]=x.useState(!1);return s?e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-blue-900/30",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"size-16 rounded-lg bg-linear-to-br from-blue-500 to-blue-700 flex items-center justify-center overflow-hidden border border-blue-500",children:e.jsx("img",{src:g(s),alt:s?.name,className:"size-full object-cover"})}),e.jsx("div",{className:"absolute -bottom-1 -right-1 size-5 rounded-full bg-gray-900 flex items-center justify-center text-xs font-bold text-white border border-blue-500",children:s.level})]}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx("h3",{className:"text-white font-semibold",children:s.name}),e.jsx("button",{className:"text-gray-400 hover:text-blue-400 transition-colors",title:"Rename pet",onClick:()=>d(!0),children:e.jsx(I,{className:"size-3.5"})})]}),e.jsxs("p",{className:"text-gray-400 text-xs",children:[h(s.pet?.species)," •"," ",h(s.evolution.current)]})]}),s.isActive?e.jsx("span",{className:"px-2 py-0.5 bg-blue-900/30 text-blue-300 text-xs rounded-full border border-blue-900/50 lg:py-[0.2rem] lg:px-2.5 lg:text-base",children:"Active"}):e.jsx("button",{className:"px-2 py-0.5 bg-gray-700/50 hover:bg-blue-900/30 text-gray-400 hover:text-blue-300 text-xs lg:py-[0.2rem] lg:px-2.5 lg:text-base rounded-full border border-gray-700 hover:border-blue-900/50 transition-colors",onClick:()=>t({userPetId:s.id}),children:"Set Active"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 mt-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(se,{className:"size-3 text-red-400"}),e.jsx("span",{className:"text-xs text-gray-300",children:"Happiness"})]}),e.jsxs("span",{className:"text-xs text-gray-400",children:[s.happiness,"%"]})]}),e.jsx("div",{className:"w-full h-1.5 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-linear-to-r from-red-600 to-red-400 rounded-full",style:{width:`${s.happiness}%`}})})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(H,{className:"size-3 text-yellow-400"}),e.jsx("span",{className:"text-xs text-gray-300",children:"Energy"})]}),e.jsxs("span",{className:"text-xs text-gray-400",children:[s.energy,"%"]})]}),e.jsx("div",{className:"w-full h-1.5 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-linear-to-r from-yellow-600 to-yellow-400 rounded-full",style:{width:`${s.energy}%`}})})]})]})]})]}),e.jsxs("div",{className:"flex justify-between mt-3 gap-2",children:[e.jsxs("button",{className:"flex-1 flex items-center justify-center gap-1 py-1.5 bg-gray-700/50 hover:bg-blue-900/30 text-gray-300 hover:text-blue-300 rounded-md border border-gray-700 hover:border-blue-900/50 transition-colors",onClick:()=>r("feed"),children:[e.jsx(q,{className:"size-4"}),e.jsx("span",{className:"text-xs",children:"Feed"})]}),e.jsxs("button",{className:"flex-1 flex items-center justify-center gap-1 py-1.5 bg-gray-700/50 hover:bg-blue-900/30 text-gray-300 hover:text-blue-300 rounded-md border border-gray-700 hover:border-blue-900/50 transition-colors",onClick:()=>r("play"),children:[e.jsx(P,{className:"size-4"}),e.jsx("span",{className:"text-xs",children:"Play"})]}),e.jsxs("button",{className:"flex-1 flex items-center justify-center gap-1 py-1.5 bg-gray-700/50 hover:bg-blue-900/30 text-gray-300 hover:text-blue-300 rounded-md border border-gray-700 hover:border-blue-900/50 transition-colors",onClick:()=>r("train"),children:[e.jsx(L,{className:"size-4"}),e.jsx("span",{className:"text-xs",children:"Train"})]})]}),l&&e.jsx(de,{isOpen:l,pet:s,onClose:()=>d(!1)})]}):null}function ue({selectedPet:s,getBuffIcon:r}){const t=s.pet?.evolution_stages.find(n=>n.stage===s.evolution.current)?.buffs||[];return e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-blue-900/30",children:[e.jsx("h3",{className:"text-white font-semibold mb-2",children:"Active Buffs"}),e.jsx("div",{className:"space-y-2",children:t.map((n,l)=>e.jsxs("div",{className:"flex items-center gap-2 p-2 bg-gray-700/50 rounded-lg",children:[e.jsx("div",{className:"size-8 rounded-full bg-blue-900/30 border text-white border-blue-900/50 flex items-center justify-center",children:r(n)}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white text-sm",children:n.name}),e.jsx("p",{className:"text-custom-yellow text-xs",children:n?.description})]})]},l))})]})}function xe({selectedPet:s}){return e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-blue-900/30",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("h3",{className:"text-white font-semibold",children:"Experience"}),e.jsxs("div",{className:"text-xs text-blue-300",children:["Level ",s.level,"/",s.pet?.maxLevel]})]}),e.jsx("div",{className:"w-full h-2 bg-gray-700 rounded-full overflow-hidden mb-1",children:e.jsx("div",{className:"h-full bg-linear-to-r from-blue-600 to-cyan-500 rounded-full",style:{width:`${s.xp/s.nextLevelXp*100}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400",children:[e.jsxs("span",{children:[s.xp," XP"]}),e.jsxs("span",{children:[s.nextLevelXp," XP"]})]})]})}function ge({showModal:s,closeModal:r,interactionResult:t,interactionProgress:n,selectedPet:l}){return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/70",children:e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-blue-900/50 p-4 w-64 max-w-full",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-white font-semibold",children:[s==="feed"&&"Feeding",s==="play"&&"Playing",s==="train"&&"Training"]}),e.jsx("button",{className:"text-gray-400 hover:text-white",onClick:r,children:e.jsx(C,{className:"size-5"})})]}),e.jsx("div",{className:"flex items-center justify-center my-4",children:e.jsxs("div",{className:"relative size-20 rounded-lg bg-linear-to-br from-blue-500 to-blue-700 flex items-center justify-center overflow-hidden border border-blue-500",children:[e.jsx("img",{src:g(l),alt:l.name,className:"size-full object-cover"}),s==="feed"&&e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsx(q,{className:v("size-8 text-white",t?"opacity-0":"animate-pulse")})}),s==="play"&&e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsx(P,{className:v("size-8 text-white",t?"opacity-0":"animate-pulse")})}),s==="train"&&e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsx(L,{className:v("size-8 text-white",t?"opacity-0":"animate-pulse")})})]})}),t?e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"inline-block rounded-full bg-blue-900/30 p-2 mb-2",children:e.jsx(O,{className:"size-6 text-blue-400"})}),e.jsx("p",{className:"text-white font-medium mb-1",children:"Success!"}),e.jsx("p",{className:"text-blue-300 text-sm",children:t}),e.jsx("button",{className:"mt-4 w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors",onClick:r,children:"Continue"})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full h-2 bg-gray-700 rounded-full overflow-hidden mb-2",children:e.jsx("div",{className:"h-full bg-blue-600 rounded-full transition-all duration-100",style:{width:`${n}%`}})}),e.jsxs("p",{className:"text-center text-gray-400 text-sm",children:[s==="feed"&&"Feeding your pet...",s==="play"&&"Playing with your pet...",s==="train"&&"Training your pet..."]})]})]})})}const be=s=>{switch(s.name){case"shield":return e.jsx(E,{className:"size-3"});case"swords":return e.jsx(V,{className:"size-3"});case"fire":return e.jsx(Z,{className:"size-3"});case"eye":return e.jsx(K,{className:"size-3"});case"heart":return e.jsx(D,{className:"size-3"});case"wind":return e.jsx(le,{className:"size-3"});default:return e.jsx(E,{className:"size-3"})}};function ve(){const{data:s,isLoading:r}=ae(),[t,n]=x.useState(null),[l,d]=x.useState(0),[u,m]=x.useState(null),[i,b]=x.useState(null),y=X();if(r)return e.jsx(B,{className:"mt-10",isLoading:r});const c=s?.sort((a,N)=>(a.isActive?-1:1)-(N.isActive?-1:1)),o=i?c?.find(a=>a.id===i):c?.[0],$=a=>{b(a)},A=a=>{n(a),d(0),m(null);const N=setInterval(()=>{d(k=>k>=100?(clearInterval(N),a==="feed"?m("Energy +20, Happiness +5"):a==="play"?m("Happiness +15, Player XP +10"):a==="train"&&m("Pet XP +50, Buff Strength +2%"),100):k+5)},100)},M=()=>{n(null),m(null)},p=o?.evolution.current==="egg";return e.jsxs("div",{className:"space-y-4 w-full md:mx-auto md:max-w-4xl",children:[o&&e.jsx(e.Fragment,{children:p?e.jsx(ce,{selectedPet:o}):e.jsxs(e.Fragment,{children:[e.jsx(me,{selectedPet:o,handleInteraction:A}),e.jsx(xe,{selectedPet:o}),e.jsx(oe,{selectedPet:o}),e.jsx(ue,{selectedPet:o,getBuffIcon:be})]})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-white font-semibold",children:"Your Pets"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[s?.map(a=>e.jsxs("button",{className:v("flex flex-col items-center p-2 rounded-lg border transition-colors",o?.id===a.id?"bg-blue-900/30 border-blue-500/50":"bg-gray-800/50 border-gray-700/50 hover:border-gray-600"),onClick:()=>$(a.id),children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"size-14 rounded-lg bg-linear-to-br from-gray-700 to-gray-800 flex items-center justify-center overflow-hidden",children:e.jsx("img",{src:g(a),alt:a.name,className:"size-full object-cover"})}),e.jsx("div",{className:"absolute -bottom-1 -right-1 size-5 rounded-full bg-gray-900 flex items-center justify-center text-xs font-bold text-white border border-blue-500",children:a.level}),a?.isActive&&e.jsx("div",{className:"absolute -top-1 -right-1 size-4 rounded-full bg-blue-500 flex items-center justify-center",children:e.jsx(G,{className:"size-2.5 text-white"})})]}),e.jsx("span",{className:"text-white text-xs mt-1",children:p?"???":a.name}),e.jsx("span",{className:"text-gray-400 text-[10px]",children:p?"Egg":h(a.pet?.species)})]},a.id)),e.jsxs("button",{className:"flex flex-col items-center justify-center p-2 rounded-lg border border-dashed border-gray-700/50 bg-gray-800/30 hover:bg-gray-700/30 hover:border-blue-900/30 transition-colors",onClick:()=>y("/inventory"),children:[e.jsx("div",{className:"size-14 rounded-lg bg-gray-800/50 flex items-center justify-center",children:e.jsx(Q,{className:"size-6 text-gray-500"})}),e.jsx("span",{className:"text-gray-500 text-xs mt-1",children:"Add Pet"})]})]})]}),t&&o&&e.jsx(ge,{selectedPet:o,showModal:t,closeModal:M,interactionResult:u,interactionProgress:l})]})}export{ve as default};
