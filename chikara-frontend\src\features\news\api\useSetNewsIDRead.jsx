import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useSetNewsIDRead = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.user.setLastNewsIdRead.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
            },
            onError: (error) => {
                console.log(error?.message || "An error occurred");
                //   toast.error(`Error: ${error.message}`);
            },
        })
    );

    return {
        setNewsID: mutation,
    };
};

export default useSetNewsIDRead;
