import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery } from "@tanstack/react-query";

/**
 * Custom hook to fetch daily chest items
 */
const useGetDailyChestItems = (options: QueryOptions = {}) => {
    return useQuery(
        api.specialItems.dailyChestItems.queryOptions({
            staleTime: 300000, // 5 minutes
            ...options,
        })
    );
};

export default useGetDailyChestItems;
