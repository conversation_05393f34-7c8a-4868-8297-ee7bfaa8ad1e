import { useQuery } from "@tanstack/react-query";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { api } from "@/helpers/api";

const useMiningSession = (options: QueryOptions = {}) => {
    return useQuery(
        api.mining.session.queryOptions({
            staleTime: 30000, // 30 seconds
            ...options,
        })
    );
};

export default useMiningSession;
