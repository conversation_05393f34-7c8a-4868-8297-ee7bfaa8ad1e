import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery } from "@tanstack/react-query";

const useActiveSession = (options: QueryOptions = {}) => {
    return useQuery(
        api.scavenging.activeSession.queryOptions({
            staleTime: 30000, // 30 seconds
            ...options,
        })
    );
};

export default useActiveSession;
