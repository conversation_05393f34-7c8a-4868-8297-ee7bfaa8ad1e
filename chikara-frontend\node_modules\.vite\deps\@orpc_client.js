import {
  COMMON_ORPC_ERROR_DEFS,
  ORPCError,
  createORPC<PERSON>rror<PERSON><PERSON><PERSON><PERSON>,
  fallbackORPCErrorMessage,
  fallbackORPCErrorStatus,
  isDefinedError,
  isORPCError<PERSON>son,
  isORPCError<PERSON>tatus,
  mapEventIterator,
  toORPCError
} from "./chunk-DRWM7KGB.js";
import {
  ErrorEvent
} from "./chunk-KMQCVE4M.js";
import {
  EventPublisher,
  onError,
  onFinish,
  onStart,
  onSuccess
} from "./chunk-C2MRSQUB.js";
import "./chunk-G3PMV62Z.js";

// ../node_modules/@orpc/client/dist/index.mjs
async function safe(promise) {
  try {
    const output = await promise;
    return Object.assign(
      [null, output, false, true],
      { error: null, data: output, isDefined: false, isSuccess: true }
    );
  } catch (e) {
    const error = e;
    if (isDefinedError(error)) {
      return Object.assign(
        [error, void 0, true, false],
        { error, data: void 0, isDefined: true, isSuccess: false }
      );
    }
    return Object.assign(
      [error, void 0, false, false],
      { error, data: void 0, isDefined: false, isSuccess: false }
    );
  }
}
function resolveFriendlyClientOptions(options) {
  return {
    ...options,
    context: options.context ?? {}
    // Context only optional if all fields are optional
  };
}
function createORPCClient(link, options) {
  const path = options?.path ?? [];
  const procedureClient = async (...[input, options2 = {}]) => {
    return await link.call(path, input, resolveFriendlyClientOptions(options2));
  };
  const recursive = new Proxy(procedureClient, {
    get(target, key) {
      if (typeof key !== "string") {
        return Reflect.get(target, key);
      }
      return createORPCClient(link, {
        ...options,
        path: [...path, key]
      });
    }
  });
  return recursive;
}
var DynamicLink = class {
  constructor(linkResolver) {
    this.linkResolver = linkResolver;
  }
  async call(path, input, options) {
    const resolvedLink = await this.linkResolver(options, path, input);
    const output = await resolvedLink.call(path, input, options);
    return output;
  }
};
export {
  COMMON_ORPC_ERROR_DEFS,
  DynamicLink,
  ErrorEvent,
  EventPublisher,
  ORPCError,
  createORPCClient,
  createORPCErrorFromJson,
  fallbackORPCErrorMessage,
  fallbackORPCErrorStatus,
  isDefinedError,
  isORPCErrorJson,
  isORPCErrorStatus,
  mapEventIterator,
  onError,
  onFinish,
  onStart,
  onSuccess,
  resolveFriendlyClientOptions,
  safe,
  toORPCError
};
//# sourceMappingURL=@orpc_client.js.map
