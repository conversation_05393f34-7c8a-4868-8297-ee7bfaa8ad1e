import Button from "@/components/Buttons/Button";
import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";

const CombatPageSection = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    // Mutation hooks for dev combat operations
    const fullHealMutation = useMutation(
        orpc.dev.fullHeal.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                toast.success("Full heal completed!");
            },
            onError: (error) => {
                console.error("Error performing full heal:", error);
                toast.error(error.message || "Failed to perform full heal");
            },
        })
    );

    const fullHealAllMutation = useMutation(
        orpc.dev.fullHealAll.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                toast.success("All users healed!");
            },
            onError: (error) => {
                console.error("Error healing all users:", error);
                toast.error(error.message || "Failed to heal all users");
            },
        })
    );

    const startPvpBattleMutation = useMutation(
        orpc.dev.startPvpBattle.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                queryClient.invalidateQueries({ queryKey: APIROUTES.BATTLE.STATUS });
                navigate("/fight");
                toast.success("PvP battle started!");
            },
            onError: (error) => {
                console.error("Error initiating PvP battle:", error);
                toast.error(error.message || "Failed to start PvP battle");
            },
        })
    );

    const resetBattlesMutation = useMutation(
        orpc.dev.resetBattles.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                navigate("/home");
                toast.success("All battles reset!");
            },
            onError: (error) => {
                console.error("Error resetting battles:", error);
                toast.error(error.message || "Failed to reset battles");
            },
        })
    );

    const addRandomEffectsMutation = useMutation(
        orpc.dev.addRandomEffects.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.STATUSEFFECTS,
                });
                toast.success("Random effects added!");
            },
            onError: (error) => {
                console.error("Error adding random effects:", error);
                toast.error(error.message || "Failed to add random effects");
            },
        })
    );

    const removeAllEffectsMutation = useMutation(
        orpc.dev.removeAllEffects.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.STATUSEFFECTS,
                });
                toast.success("All effects removed!");
            },
            onError: (error) => {
                console.error("Error removing all effects:", error);
                toast.error(error.message || "Failed to remove all effects");
            },
        })
    );

    // Handler functions
    const fullHeal = () => {
        fullHealMutation.mutate();
    };

    const healAllUsers = () => {
        fullHealAllMutation.mutate();
    };

    const randomPVPBattle = () => {
        startPvpBattleMutation.mutate();
    };

    const resetAllBattles = () => {
        resetBattlesMutation.mutate();
    };

    const addRandomEffects = () => {
        addRandomEffectsMutation.mutate({});
    };

    const removeAllEffects = () => {
        removeAllEffectsMutation.mutate({});
    };

    return (
        <div className="grid grid-cols-2 gap-3 p-2">
            <Button className="text-sm!" type="primary" onClick={randomPVPBattle}>
                Random PvP Battle
            </Button>
            <Button type="primary" onClick={fullHeal}>
                Full Heal
            </Button>
            <Button className="text-sm!" type="primary" onClick={healAllUsers}>
                Heal all Users
            </Button>
            <Button className="text-sm!" type="primary" onClick={resetAllBattles}>
                Reset All Battles
            </Button>
            <Button className="text-sm!" type="primary" onClick={addRandomEffects}>
                Random Status Effects
            </Button>
            <Button className="text-sm!" type="primary" onClick={removeAllEffects}>
                Remove Status Effects
            </Button>
        </div>
    );
};

export default CombatPageSection;
