{"version": 3, "sources": ["../../../../node_modules/@orpc/client/dist/shared/client.DHOfWE0c.mjs"], "sourcesContent": ["import { isObject, AsyncIteratorClass, isTypescriptObject } from '@orpc/shared';\nimport { getEventMeta, withEventMeta } from '@orpc/standard-server';\n\nconst COMMON_ORPC_ERROR_DEFS = {\n  BAD_REQUEST: {\n    status: 400,\n    message: \"Bad Request\"\n  },\n  UNAUTHORIZED: {\n    status: 401,\n    message: \"Unauthorized\"\n  },\n  FORBIDDEN: {\n    status: 403,\n    message: \"Forbidden\"\n  },\n  NOT_FOUND: {\n    status: 404,\n    message: \"Not Found\"\n  },\n  METHOD_NOT_SUPPORTED: {\n    status: 405,\n    message: \"Method Not Supported\"\n  },\n  NOT_ACCEPTABLE: {\n    status: 406,\n    message: \"Not Acceptable\"\n  },\n  TIMEOUT: {\n    status: 408,\n    message: \"Request Timeout\"\n  },\n  CONFLICT: {\n    status: 409,\n    message: \"Conflict\"\n  },\n  PRECONDITION_FAILED: {\n    status: 412,\n    message: \"Precondition Failed\"\n  },\n  PAYLOAD_TOO_LARGE: {\n    status: 413,\n    message: \"Payload Too Large\"\n  },\n  UNSUPPORTED_MEDIA_TYPE: {\n    status: 415,\n    message: \"Unsupported Media Type\"\n  },\n  UNPROCESSABLE_CONTENT: {\n    status: 422,\n    message: \"Unprocessable Content\"\n  },\n  TOO_MANY_REQUESTS: {\n    status: 429,\n    message: \"Too Many Requests\"\n  },\n  CLIENT_CLOSED_REQUEST: {\n    status: 499,\n    message: \"Client Closed Request\"\n  },\n  INTERNAL_SERVER_ERROR: {\n    status: 500,\n    message: \"Internal Server Error\"\n  },\n  NOT_IMPLEMENTED: {\n    status: 501,\n    message: \"Not Implemented\"\n  },\n  BAD_GATEWAY: {\n    status: 502,\n    message: \"Bad Gateway\"\n  },\n  SERVICE_UNAVAILABLE: {\n    status: 503,\n    message: \"Service Unavailable\"\n  },\n  GATEWAY_TIMEOUT: {\n    status: 504,\n    message: \"Gateway Timeout\"\n  }\n};\nfunction fallbackORPCErrorStatus(code, status) {\n  return status ?? COMMON_ORPC_ERROR_DEFS[code]?.status ?? 500;\n}\nfunction fallbackORPCErrorMessage(code, message) {\n  return message || COMMON_ORPC_ERROR_DEFS[code]?.message || code;\n}\nclass ORPCError extends Error {\n  defined;\n  code;\n  status;\n  data;\n  constructor(code, ...[options]) {\n    if (options?.status && !isORPCErrorStatus(options.status)) {\n      throw new Error(\"[ORPCError] Invalid error status code.\");\n    }\n    const message = fallbackORPCErrorMessage(code, options?.message);\n    super(message, options);\n    this.code = code;\n    this.status = fallbackORPCErrorStatus(code, options?.status);\n    this.defined = options?.defined ?? false;\n    this.data = options?.data;\n  }\n  toJSON() {\n    return {\n      defined: this.defined,\n      code: this.code,\n      status: this.status,\n      message: this.message,\n      data: this.data\n    };\n  }\n}\nfunction isDefinedError(error) {\n  return error instanceof ORPCError && error.defined;\n}\nfunction toORPCError(error) {\n  return error instanceof ORPCError ? error : new ORPCError(\"INTERNAL_SERVER_ERROR\", {\n    message: \"Internal server error\",\n    cause: error\n  });\n}\nfunction isORPCErrorStatus(status) {\n  return status < 200 || status >= 400;\n}\nfunction isORPCErrorJson(json) {\n  if (!isObject(json)) {\n    return false;\n  }\n  const validKeys = [\"defined\", \"code\", \"status\", \"message\", \"data\"];\n  if (Object.keys(json).some((k) => !validKeys.includes(k))) {\n    return false;\n  }\n  return \"defined\" in json && typeof json.defined === \"boolean\" && \"code\" in json && typeof json.code === \"string\" && \"status\" in json && typeof json.status === \"number\" && isORPCErrorStatus(json.status) && \"message\" in json && typeof json.message === \"string\";\n}\nfunction createORPCErrorFromJson(json, options = {}) {\n  return new ORPCError(json.code, {\n    ...options,\n    ...json\n  });\n}\n\nfunction mapEventIterator(iterator, maps) {\n  return new AsyncIteratorClass(async () => {\n    const { done, value } = await (async () => {\n      try {\n        return await iterator.next();\n      } catch (error) {\n        let mappedError = await maps.error(error);\n        if (mappedError !== error) {\n          const meta = getEventMeta(error);\n          if (meta && isTypescriptObject(mappedError)) {\n            mappedError = withEventMeta(mappedError, meta);\n          }\n        }\n        throw mappedError;\n      }\n    })();\n    let mappedValue = await maps.value(value, done);\n    if (mappedValue !== value) {\n      const meta = getEventMeta(value);\n      if (meta && isTypescriptObject(mappedValue)) {\n        mappedValue = withEventMeta(mappedValue, meta);\n      }\n    }\n    return { done, value: mappedValue };\n  }, async () => {\n    await iterator.return?.();\n  });\n}\n\nexport { COMMON_ORPC_ERROR_DEFS as C, ORPCError as O, fallbackORPCErrorMessage as a, isORPCErrorStatus as b, isORPCErrorJson as c, createORPCErrorFromJson as d, fallbackORPCErrorStatus as f, isDefinedError as i, mapEventIterator as m, toORPCError as t };\n"], "mappings": ";;;;;;;;;;;AAGA,IAAM,yBAAyB;AAAA,EAC7B,aAAa;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,sBAAsB;AAAA,IACpB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,qBAAqB;AAAA,IACnB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,IACjB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,wBAAwB;AAAA,IACtB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,uBAAuB;AAAA,IACrB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,IACjB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,uBAAuB;AAAA,IACrB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,uBAAuB;AAAA,IACrB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,iBAAiB;AAAA,IACf,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,qBAAqB;AAAA,IACnB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,iBAAiB;AAAA,IACf,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,SAAS,wBAAwB,MAAM,QAAQ;AAC7C,SAAO,UAAU,uBAAuB,IAAI,GAAG,UAAU;AAC3D;AACA,SAAS,yBAAyB,MAAM,SAAS;AAC/C,SAAO,WAAW,uBAAuB,IAAI,GAAG,WAAW;AAC7D;AACA,IAAM,YAAN,cAAwB,MAAM;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,SAAS,CAAC,OAAO,GAAG;AAC9B,QAAI,SAAS,UAAU,CAAC,kBAAkB,QAAQ,MAAM,GAAG;AACzD,YAAM,IAAI,MAAM,wCAAwC;AAAA,IAC1D;AACA,UAAM,UAAU,yBAAyB,MAAM,SAAS,OAAO;AAC/D,UAAM,SAAS,OAAO;AACtB,SAAK,OAAO;AACZ,SAAK,SAAS,wBAAwB,MAAM,SAAS,MAAM;AAC3D,SAAK,UAAU,SAAS,WAAW;AACnC,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,MAAM,KAAK;AAAA,MACX,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,iBAAiB,aAAa,MAAM;AAC7C;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,iBAAiB,YAAY,QAAQ,IAAI,UAAU,yBAAyB;AAAA,IACjF,SAAS;AAAA,IACT,OAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,SAAS,OAAO,UAAU;AACnC;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,CAAC,SAAS,IAAI,GAAG;AACnB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,CAAC,WAAW,QAAQ,UAAU,WAAW,MAAM;AACjE,MAAI,OAAO,KAAK,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,UAAU,SAAS,CAAC,CAAC,GAAG;AACzD,WAAO;AAAA,EACT;AACA,SAAO,aAAa,QAAQ,OAAO,KAAK,YAAY,aAAa,UAAU,QAAQ,OAAO,KAAK,SAAS,YAAY,YAAY,QAAQ,OAAO,KAAK,WAAW,YAAY,kBAAkB,KAAK,MAAM,KAAK,aAAa,QAAQ,OAAO,KAAK,YAAY;AAC5P;AACA,SAAS,wBAAwB,MAAM,UAAU,CAAC,GAAG;AACnD,SAAO,IAAI,UAAU,KAAK,MAAM;AAAA,IAC9B,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACH;AAEA,SAAS,iBAAiB,UAAU,MAAM;AACxC,SAAO,IAAI,mBAAmB,YAAY;AACxC,UAAM,EAAE,MAAM,MAAM,IAAI,OAAO,YAAY;AACzC,UAAI;AACF,eAAO,MAAM,SAAS,KAAK;AAAA,MAC7B,SAAS,OAAO;AACd,YAAI,cAAc,MAAM,KAAK,MAAM,KAAK;AACxC,YAAI,gBAAgB,OAAO;AACzB,gBAAM,OAAO,aAAa,KAAK;AAC/B,cAAI,QAAQ,mBAAmB,WAAW,GAAG;AAC3C,0BAAc,cAAc,aAAa,IAAI;AAAA,UAC/C;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,GAAG;AACH,QAAI,cAAc,MAAM,KAAK,MAAM,OAAO,IAAI;AAC9C,QAAI,gBAAgB,OAAO;AACzB,YAAM,OAAO,aAAa,KAAK;AAC/B,UAAI,QAAQ,mBAAmB,WAAW,GAAG;AAC3C,sBAAc,cAAc,aAAa,IAAI;AAAA,MAC/C;AAAA,IACF;AACA,WAAO,EAAE,MAAM,OAAO,YAAY;AAAA,EACpC,GAAG,YAAY;AACb,UAAM,SAAS,SAAS;AAAA,EAC1B,CAAC;AACH;", "names": []}