[{"id": 1, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Improve existing features\\\",\\\"worst_feature\\\":\\\"Crafting\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "14/5/2024 22:39:16", "updatedAt": "14/5/2024 22:39:16", "userId": 62, "pollId": 1}, {"id": 2, "answer": "\"{\\\"stats_removed\\\":\\\"Yes\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "14/5/2024 22:51:25", "updatedAt": "14/5/2024 22:51:25", "userId": 19, "pollId": 1}, {"id": 3, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Auction House\\\",\\\"worst_feature\\\":\\\"Skills + Talents\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "14/5/2024 23:06:37", "updatedAt": "14/5/2024 23:06:37", "userId": 69, "pollId": 1}, {"id": 4, "answer": "\"{\\\"stats_removed\\\":\\\"Yes\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Improve existing features\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Combat\\\"}\"", "createdAt": "14/5/2024 23:13:22", "updatedAt": "14/5/2024 23:13:22", "userId": 60, "pollId": 1}, {"id": 5, "answer": "\"{\\\"stats_removed\\\":\\\"Yes\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Improve existing features\\\",\\\"worst_feature\\\":\\\"Skills + Talents\\\",\\\"best_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\"}\"", "createdAt": "14/5/2024 23:14:21", "updatedAt": "14/5/2024 23:14:21", "userId": 89, "pollId": 1}, {"id": 6, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"Missions\\\",\\\"best_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\"}\"", "createdAt": "14/5/2024 23:21:22", "updatedAt": "14/5/2024 23:21:22", "userId": 48, "pollId": 1}, {"id": 7, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Improve existing features\\\",\\\"worst_feature\\\":\\\"Combat\\\",\\\"best_feature\\\":\\\"Skills + Talents\\\"}\"", "createdAt": "15/5/2024 00:06:17", "updatedAt": "15/5/2024 00:06:17", "userId": 49, "pollId": 1}, {"id": 8, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"More Casino/Arcade games\\\",\\\"worst_feature\\\":\\\"Combat\\\",\\\"best_feature\\\":\\\"other\\\",\\\"best_feature-Comment\\\":\\\"Idk\\\"}\"", "createdAt": "15/5/2024 00:20:32", "updatedAt": "15/5/2024 00:20:32", "userId": 105, "pollId": 1}, {"id": 9, "answer": "\"{\\\"pvp_opt_out\\\":\\\"No\\\",\\\"stats_removed\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"Crafting\\\",\\\"best_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\"}\"", "createdAt": "15/5/2024 00:57:37", "updatedAt": "15/5/2024 00:57:37", "userId": 51, "pollId": 1}, {"id": 10, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"Skills + Talents\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "15/5/2024 01:03:30", "updatedAt": "15/5/2024 01:03:30", "userId": 80, "pollId": 1}, {"id": 11, "answer": "\"{\\\"stats_removed\\\":\\\"Yes\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Improve existing features\\\",\\\"worst_feature\\\":\\\"Missions\\\",\\\"best_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\"}\"", "createdAt": "15/5/2024 01:25:25", "updatedAt": "15/5/2024 01:25:25", "userId": 77, "pollId": 1}, {"id": 12, "answer": "\"{\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Auction House\\\",\\\"stats_removed\\\":\\\"Yes\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "15/5/2024 03:50:09", "updatedAt": "15/5/2024 03:50:09", "userId": 87, "pollId": 1}, {"id": 13, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Improve existing features\\\",\\\"worst_feature\\\":\\\"other\\\",\\\"worst_feature-Comment\\\":\\\"None atm\\\",\\\"best_feature\\\":\\\"Skills + Talents\\\"}\"", "createdAt": "15/5/2024 06:36:25", "updatedAt": "15/5/2024 06:36:25", "userId": 56, "pollId": 1}, {"id": 14, "answer": "\"{\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"stats_removed\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"More Casino/Arcade games\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Tasks\\\"}\"", "createdAt": "15/5/2024 07:19:22", "updatedAt": "15/5/2024 07:19:22", "userId": 12, "pollId": 1}, {"id": 15, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"More Social features \\\",\\\"worst_feature\\\":\\\"Adventure Mode\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "15/5/2024 12:01:23", "updatedAt": "15/5/2024 12:01:23", "userId": 96, "pollId": 1}, {"id": 16, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"other\\\",\\\"next_feature-Comment\\\":\\\"Auction house and more casino games because who doesnt like gambling lol, maybe a lottery with the results each of the week.\\\",\\\"worst_feature\\\":\\\"Missions\\\",\\\"best_feature\\\":\\\"Missions\\\"}\"", "createdAt": "15/5/2024 12:55:12", "updatedAt": "15/5/2024 12:55:12", "userId": 22, "pollId": 1}, {"id": 17, "answer": "\"{\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"stats_removed\\\":\\\"No\\\",\\\"next_feature\\\":\\\"other\\\",\\\"next_feature-Comment\\\":\\\"either a communal boss system, or some form of daily / weekly combat encounter with multiple tiers that rewards brtter as you progress\\\",\\\"best_feature\\\":\\\"Skills + Talents\\\"}\"", "createdAt": "15/5/2024 13:38:57", "updatedAt": "15/5/2024 13:38:57", "userId": 92, "pollId": 1}, {"id": 18, "answer": "\"{\\\"stats_removed\\\":\\\"Yes\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Auction House\\\",\\\"worst_feature\\\":\\\"Missions\\\",\\\"best_feature\\\":\\\"Tasks\\\"}\"", "createdAt": "15/5/2024 14:39:50", "updatedAt": "15/5/2024 14:39:50", "userId": 88, "pollId": 1}, {"id": 19, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"More Social features \\\",\\\"worst_feature\\\":\\\"Combat\\\",\\\"best_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\"}\"", "createdAt": "15/5/2024 15:23:35", "updatedAt": "15/5/2024 15:23:35", "userId": 15, "pollId": 1}, {"id": 20, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "15/5/2024 15:31:48", "updatedAt": "15/5/2024 15:31:48", "userId": 73, "pollId": 1}, {"id": 21, "answer": "\"{\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"worst_feature\\\":\\\"Adventure Mode\\\",\\\"best_feature\\\":\\\"Skills + Talents\\\"}\"", "createdAt": "15/5/2024 17:34:36", "updatedAt": "15/5/2024 17:34:36", "userId": 91, "pollId": 1}, {"id": 22, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Combat\\\"}\"", "createdAt": "15/5/2024 22:12:26", "updatedAt": "15/5/2024 22:12:26", "userId": 118, "pollId": 1}, {"id": 23, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"More Casino/Arcade games\\\",\\\"best_feature\\\":\\\"Tasks\\\",\\\"worst_feature\\\":\\\"Tasks\\\"}\"", "createdAt": "16/5/2024 10:59:28", "updatedAt": "16/5/2024 10:59:28", "userId": 11, "pollId": 1}, {"id": 25, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"worst_feature\\\":\\\"other\\\",\\\"worst_feature-Comment\\\":\\\"dev\\\",\\\"next_feature\\\":\\\"other\\\",\\\"best_feature\\\":\\\"Tasks\\\",\\\"next_feature-Comment\\\":\\\"tism screening. turing test on signup should do it\\\",\\\"pvp_opt_out\\\":\\\"No\\\"}\"", "createdAt": "16/5/2024 14:12:01", "updatedAt": "16/5/2024 14:12:01", "userId": 17, "pollId": 1}, {"id": 26, "answer": "\"{\\\"stats_removed\\\":\\\"Yes\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Auction House\\\",\\\"worst_feature\\\":\\\"other\\\",\\\"best_feature\\\":\\\"other\\\",\\\"worst_feature-Comment\\\":\\\"e\\\",\\\"best_feature-Comment\\\":\\\"e\\\"}\"", "createdAt": "16/5/2024 17:36:40", "updatedAt": "16/5/2024 17:36:40", "userId": 3, "pollId": 1}, {"id": 27, "answer": "\"{\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"stats_removed\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "16/5/2024 19:28:45", "updatedAt": "16/5/2024 19:28:45", "userId": 103, "pollId": 1}, {"id": 28, "answer": "\"{\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Auction House\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\",\\\"stats_removed\\\":\\\"No\\\"}\"", "createdAt": "16/5/2024 22:45:12", "updatedAt": "16/5/2024 22:45:12", "userId": 38, "pollId": 1}, {"id": 29, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"Adventure Mode\\\",\\\"best_feature\\\":\\\"Skills + Talents\\\"}\"", "createdAt": "17/5/2024 16:53:50", "updatedAt": "17/5/2024 16:53:50", "userId": 119, "pollId": 1}, {"id": 30, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"More Social features \\\",\\\"worst_feature\\\":\\\"Missions\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "18/5/2024 17:06:43", "updatedAt": "18/5/2024 17:06:43", "userId": 57, "pollId": 1}, {"id": 31, "answer": "\"{\\\"device_performance\\\":\\\"Excellent\\\",\\\"top_three\\\":[\\\"More PvE content\\\",\\\"Social Improvements\\\",\\\"More adventure Mode content/improvements\\\"],\\\"patch_size\\\":\\\"Bigger Patches\\\",\\\"game_satisfaction\\\":\\\"Extremely satisfied\\\"}\"", "createdAt": "23/5/2024 22:56:16", "updatedAt": "23/5/2024 22:56:16", "userId": 3, "pollId": 2}, {"id": 32, "answer": "\"{\\\"top_three\\\":[\\\"PvP fairness/balancing improvements\\\",\\\"Combat Improvements\\\",\\\"Bug fixes and technical improvements\\\"],\\\"device_performance\\\":\\\"Average\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Moderately satisfied\\\",\\\"additional_info\\\":\\\"When I try to do a PVP battle the site sometimes reloads rapidly then gives me an error also this textbox resets when a stat gets update and its super annoying\\\"}\"", "createdAt": "23/5/2024 22:59:32", "updatedAt": "23/5/2024 22:59:32", "userId": 118, "pollId": 2}, {"id": 33, "answer": "\"{\\\"top_three\\\":[\\\"More Stats / Talents / Skills\\\",\\\"More PvE content\\\",\\\"More PvP content\\\"],\\\"device_performance\\\":\\\"Average\\\",\\\"patch_size\\\":\\\"Bigger Patches\\\",\\\"game_satisfaction\\\":\\\"Moderately satisfied\\\"}\"", "createdAt": "23/5/2024 23:01:31", "updatedAt": "23/5/2024 23:01:31", "userId": 48, "pollId": 2}, {"id": 34, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "23/5/2024 23:03:31", "updatedAt": "23/5/2024 23:03:31", "userId": 38, "pollId": 1}, {"id": 35, "answer": "\"{\\\"top_three\\\":[\\\"More PvE content\\\",\\\"More adventure Mode content/improvements\\\",\\\"More Stats / Talents / Skills\\\"],\\\"device_performance\\\":\\\"Poor\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\"}\"", "createdAt": "23/5/2024 23:14:30", "updatedAt": "23/5/2024 23:14:30", "userId": 62, "pollId": 2}, {"id": 36, "answer": "\"{\\\"worst_feature\\\":\\\"Skills + Talents\\\",\\\"next_feature\\\":\\\"Auction House\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"stats_removed\\\":\\\"Yes\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "23/5/2024 23:28:28", "updatedAt": "23/5/2024 23:28:28", "userId": 140, "pollId": 1}, {"id": 37, "answer": "\"{\\\"game_satisfaction\\\":\\\"Moderately satisfied\\\",\\\"patch_size\\\":\\\"Bigger Patches\\\",\\\"device_performance\\\":\\\"Good\\\",\\\"top_three\\\":[\\\"Combat Improvements\\\",\\\"Crafting System improvements\\\",\\\"More adventure Mode content/improvements\\\"]}\"", "createdAt": "23/5/2024 23:28:30", "updatedAt": "23/5/2024 23:28:30", "userId": 60, "pollId": 2}, {"id": 38, "answer": "\"{\\\"device_performance\\\":\\\"Good\\\",\\\"top_three\\\":[\\\"More PvP content\\\",\\\"Combat Improvements\\\",\\\"Social Improvements\\\"],\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Extremely satisfied\\\"}\"", "createdAt": "23/5/2024 23:38:25", "updatedAt": "23/5/2024 23:38:25", "userId": 51, "pollId": 2}, {"id": 39, "answer": "\"{\\\"top_three\\\":[\\\"More adventure Mode content/improvements\\\",\\\"Social Improvements\\\",\\\"Combat Improvements\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"patch_size\\\":\\\"Bigger Patches\\\",\\\"game_satisfaction\\\":\\\"Moderately satisfied\\\"}\"", "createdAt": "23/5/2024 23:44:08", "updatedAt": "23/5/2024 23:44:08", "userId": 89, "pollId": 2}, {"id": 40, "answer": "\"{\\\"top_three\\\":[\\\"Bug fixes and technical improvements\\\",\\\"More Stats / Talents / Skills\\\",\\\"Balancing improvements\\\"],\\\"device_performance\\\":\\\"Average\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\",\\\"additional_info\\\":\\\"require reloads often that are annoying. Like you craft something but the task is not marked complete until you refresh. I would look into that\\\"}\"", "createdAt": "23/5/2024 23:45:54", "updatedAt": "23/5/2024 23:45:54", "userId": 77, "pollId": 2}, {"id": 41, "answer": "\"{\\\"top_three\\\":[\\\"More PvE content\\\",\\\"More adventure Mode content/improvements\\\",\\\"More Stats / Talents / Skills\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"patch_size\\\":\\\"Bigger Patches\\\",\\\"game_satisfaction\\\":\\\"Extremely satisfied\\\"}\"", "createdAt": "23/5/2024 23:59:45", "updatedAt": "23/5/2024 23:59:45", "userId": 96, "pollId": 2}, {"id": 42, "answer": "\"{\\\"stats_removed\\\":\\\"Yes\\\",\\\"pvp_opt_out\\\":\\\"No\\\",\\\"next_feature\\\":\\\"Gangs + Gang Wars\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Adventure Mode\\\"}\"", "createdAt": "23/5/2024 23:59:47", "updatedAt": "23/5/2024 23:59:47", "userId": 19, "pollId": 1}, {"id": 43, "answer": "\"{\\\"top_three\\\":[\\\"More PvE content\\\",\\\"More adventure Mode content/improvements\\\",\\\"Balancing improvements\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"patch_size\\\":\\\"Bigger Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\"}\"", "createdAt": "24/5/2024 00:10:16", "updatedAt": "24/5/2024 00:10:16", "userId": 69, "pollId": 2}, {"id": 44, "answer": "\"{\\\"top_three\\\":[\\\"More Stats / Talents / Skills\\\",\\\"More adventure Mode content/improvements\\\",\\\"PvP fairness/balancing improvements\\\"],\\\"device_performance\\\":\\\"Good\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Extremely satisfied\\\",\\\"additional_info\\\":\\\":^)\\\"}\"", "createdAt": "24/5/2024 02:46:24", "updatedAt": "24/5/2024 02:46:24", "userId": 17, "pollId": 2}, {"id": 45, "answer": "\"{\\\"top_three\\\":[\\\"Combat Improvements\\\",\\\"Social Improvements\\\",\\\"More adventure Mode content/improvements\\\"],\\\"device_performance\\\":\\\"Good\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\",\\\"additional_info\\\":\\\"Very cool game\\\"}\"", "createdAt": "24/5/2024 05:37:50", "updatedAt": "24/5/2024 05:37:50", "userId": 49, "pollId": 2}, {"id": 46, "answer": "\"{\\\"top_three\\\":[\\\"Balancing improvements\\\",\\\"More Tasks / Missions\\\",\\\"More PvE content\\\"],\\\"device_performance\\\":\\\"Good\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\"}\"", "createdAt": "24/5/2024 07:06:12", "updatedAt": "24/5/2024 07:06:12", "userId": 91, "pollId": 2}, {"id": 47, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"More Casino/Arcade games\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Tasks\\\"}\"", "createdAt": "24/5/2024 08:20:58", "updatedAt": "24/5/2024 08:20:58", "userId": 12, "pollId": 1}, {"id": 48, "answer": "\"{\\\"top_three\\\":[\\\"Bug fixes and technical improvements\\\",\\\"More Tasks / Missions\\\",\\\"Balancing improvements\\\"],\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\",\\\"device_performance\\\":\\\"Good\\\"}\"", "createdAt": "24/5/2024 11:10:17", "updatedAt": "24/5/2024 11:10:17", "userId": 56, "pollId": 2}, {"id": 49, "answer": "\"{\\\"top_three\\\":[\\\"Combat Improvements\\\",\\\"More Stats / Talents / Skills\\\",\\\"Balancing improvements\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\"}\"", "createdAt": "24/5/2024 15:07:19", "updatedAt": "24/5/2024 15:07:19", "userId": 19, "pollId": 2}, {"id": 50, "answer": "\"{\\\"stats_removed\\\":\\\"No\\\",\\\"pvp_opt_out\\\":\\\"Yes\\\",\\\"next_feature\\\":\\\"More Casino/Arcade games\\\",\\\"worst_feature\\\":\\\"PvP / Hospitalisation / Bounties\\\",\\\"best_feature\\\":\\\"Tasks\\\"}\"", "createdAt": "24/5/2024 16:04:01", "updatedAt": "24/5/2024 16:04:01", "userId": 12, "pollId": 1}, {"id": 51, "answer": "\"{\\\"top_three\\\":[\\\"More PvE content\\\",\\\"More adventure Mode content/improvements\\\",\\\"More Stats / Talents / Skills\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Extremely satisfied\\\"}\"", "createdAt": "24/5/2024 18:03:57", "updatedAt": "24/5/2024 18:03:57", "userId": 88, "pollId": 2}, {"id": 52, "answer": "\"{\\\"top_three\\\":[\\\"More Tasks / Missions\\\",\\\"More adventure Mode content/improvements\\\",\\\"Social Improvements\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\",\\\"additional_info\\\":\\\"I would recommend a roadmap as it allows potential players to see the future of the game!\\\"}\"", "createdAt": "25/5/2024 02:08:23", "updatedAt": "25/5/2024 02:08:23", "userId": 143, "pollId": 2}, {"id": 53, "answer": "\"{\\\"top_three\\\":[\\\"More PvE content\\\",\\\"More Tasks / Missions\\\",\\\"More Stats / Talents / Skills\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"patch_size\\\":\\\"Bigger Patches\\\",\\\"game_satisfaction\\\":\\\"Moderately satisfied\\\"}\"", "createdAt": "25/5/2024 03:40:24", "updatedAt": "25/5/2024 03:40:24", "userId": 99, "pollId": 2}, {"id": 54, "answer": "\"{\\\"top_three\\\":[\\\"More PvE content\\\",\\\"More adventure Mode content/improvements\\\",\\\"Balancing improvements\\\"],\\\"device_performance\\\":\\\"Good\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Moderately satisfied\\\"}\"", "createdAt": "25/5/2024 03:57:26", "updatedAt": "25/5/2024 03:57:26", "userId": 38, "pollId": 2}, {"id": 55, "answer": "\"{\\\"top_three\\\":[\\\"More PvE content\\\",\\\"More Stats / Talents / Skills\\\",\\\"More adventure Mode content/improvements\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\"}\"", "createdAt": "25/5/2024 04:23:28", "updatedAt": "25/5/2024 04:23:28", "userId": 87, "pollId": 2}, {"id": 56, "answer": "\"{\\\"top_three\\\":[\\\"More Stats / Talents / Skills\\\",\\\"Combat Improvements\\\",\\\"Crafting System improvements\\\"],\\\"device_performance\\\":\\\"Good\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\"}\"", "createdAt": "25/5/2024 07:36:09", "updatedAt": "25/5/2024 07:36:09", "userId": 156, "pollId": 2}, {"id": 57, "answer": "\"{\\\"top_three\\\":[\\\"More PvE content\\\",\\\"More adventure Mode content/improvements\\\",\\\"Social Improvements\\\"],\\\"device_performance\\\":\\\"Excellent\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\",\\\"patch_size\\\":\\\"Bigger Patches\\\"}\"", "createdAt": "26/5/2024 11:58:20", "updatedAt": "26/5/2024 11:58:20", "userId": 57, "pollId": 2}, {"id": 58, "answer": "\"{\\\"top_three\\\":[\\\"Bug fixes and technical improvements\\\",\\\"More Tasks / Missions\\\",\\\"Social Improvements\\\"],\\\"device_performance\\\":\\\"Good\\\",\\\"patch_size\\\":\\\"Bigger Patches\\\",\\\"game_satisfaction\\\":\\\"Very satisfied\\\"}\"", "createdAt": "27/5/2024 04:36:01", "updatedAt": "27/5/2024 04:36:01", "userId": 162, "pollId": 2}, {"id": 59, "answer": "\"{\\\"top_three\\\":[\\\"More PvP content\\\",\\\"More Stats / Talents / Skills\\\",\\\"Combat Improvements\\\"],\\\"device_performance\\\":\\\"Good\\\",\\\"patch_size\\\":\\\"Smaller Patches\\\",\\\"game_satisfaction\\\":\\\"Extremely satisfied\\\"}\"", "createdAt": "27/5/2024 14:10:37", "updatedAt": "27/5/2024 14:10:37", "userId": 39, "pollId": 2}, {"id": 60, "answer": "\"{\\\"game_play_length\\\":\\\"5+ hours a day\\\",\\\"daily_logins\\\":\\\"Frequent small sessions\\\",\\\"energy_training\\\":\\\"No - It's good as it is\\\",\\\"beta_improved_feature\\\":\\\"Adventure Mode\\\",\\\"beta_new_feature\\\":\\\"Housing + Furniture\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\"}\"", "createdAt": "9/7/2024 14:23:09", "updatedAt": "9/7/2024 14:23:09", "userId": 56, "pollId": 3}, {"id": 61, "answer": "\"{\\\"game_play_length\\\":\\\"1 - 2 hours a day\\\",\\\"daily_logins\\\":\\\"Frequent small sessions\\\",\\\"energy_training\\\":\\\"No - It's good as it is\\\",\\\"beta_improved_feature\\\":\\\"Stats + Talents\\\",\\\"beta_new_feature\\\":\\\"Gang Territory System\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\",\\\"additional_info\\\":\\\"The amount of features is starting to feel overwhelming. Maybe keep a focus on the fundamentals. The adventure mode is a great selling point but the combat system with talents got stale around level 30-35.\\\",\\\"disliked_features\\\":\\\"Crafting feels more like a chore \\\"}\"", "createdAt": "9/7/2024 14:25:38", "updatedAt": "9/7/2024 14:25:38", "userId": 77, "pollId": 3}, {"id": 62, "answer": "\"{\\\"game_play_length\\\":\\\"1 - 2 hours a day\\\",\\\"daily_logins\\\":\\\"Once or twice a day\\\",\\\"energy_training\\\":\\\"Yes - Reduce energy regen\\\",\\\"beta_improved_feature\\\":\\\"Tasks/Daily Tasks\\\",\\\"beta_new_feature\\\":\\\"Story Mode\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\",\\\"disliked_features\\\":\\\"Injuries from the streets, they're expensive to heal and take way longer to disappear on their own than the loss of hp. I'd prefer to just have hp loss from the streets and keep the injuries as they are but make them a PvP-only thing.\\\",\\\"additional_info\\\":\\\"Great job on the alpha x\\\"}\"", "createdAt": "9/7/2024 14:32:55", "updatedAt": "9/7/2024 14:32:55", "userId": 12, "pollId": 3}, {"id": 63, "answer": "\"{\\\"game_play_length\\\":\\\"< 1 hour a day\\\",\\\"daily_logins\\\":\\\"Once or twice a day\\\",\\\"energy_training\\\":\\\"No - It's good as it is\\\",\\\"beta_improved_feature\\\":\\\"Tasks/Daily Tasks\\\",\\\"beta_new_feature\\\":\\\"Gathering System\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\",\\\"disliked_features\\\":\\\"Crafting was a nightmare, barely got through any crafting tasks due to lack of drops\\\",\\\"additional_info\\\":\\\"Launch it sooner rather than later, build as you go\\\"}\"", "createdAt": "9/7/2024 14:34:21", "updatedAt": "9/7/2024 14:34:21", "userId": 181, "pollId": 3}, {"id": 64, "answer": "\"{\\\"game_play_length\\\":\\\"< 1 hour a day\\\",\\\"daily_logins\\\":\\\"Once or twice a day\\\",\\\"energy_training\\\":\\\"Yes - Full rework into a new system\\\",\\\"beta_improved_feature\\\":\\\"Stats + Talents\\\",\\\"beta_new_feature\\\":\\\"New Stats / Talents / Skills\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\",\\\"additional_info\\\":\\\"More items, more mistery for people wondering how the heck a player got a certain item or how to craft.   but it really need to add A LOT of stuff, specially gear. What I hate the most is the \\\\\\\"late game feel\\\\\\\" where everyone is literally using the same gear, or just 2 variations of late game gear\\\"}\"", "createdAt": "9/7/2024 15:22:09", "updatedAt": "9/7/2024 15:22:09", "userId": 48, "pollId": 3}, {"id": 65, "answer": "\"{\\\"game_play_length\\\":\\\"5+ hours a day\\\",\\\"daily_logins\\\":\\\"Play all day\\\",\\\"energy_training\\\":\\\"Yes - Full rework into a new system\\\",\\\"beta_improved_feature\\\":\\\"Stats + Talents\\\",\\\"beta_new_feature\\\":\\\"Story Mode\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\"}\"", "createdAt": "9/7/2024 16:37:12", "updatedAt": "9/7/2024 16:37:12", "userId": 38, "pollId": 3}, {"id": 66, "answer": "\"{\\\"game_play_length\\\":\\\"2 - 3 hours a day\\\",\\\"daily_logins\\\":\\\"Play all day\\\",\\\"energy_training\\\":\\\"No - It's good as it is\\\",\\\"beta_improved_feature\\\":\\\"Combat/Skills\\\",\\\"beta_new_feature\\\":\\\"Story Mode\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\",\\\"disliked_features\\\":\\\"How the injury system current interacts with the combat system, that you can be out and about despite carrying a million injuries and still fight as long as you have AP doesn't make sense to me.\\\\n\\\\nSimple resolution would be to make an AP regen debuff but I consider forced downtime would be good\\\",\\\"additional_info\\\":\\\"cont.d: \\\"}\"", "createdAt": "9/7/2024 18:57:15", "updatedAt": "9/7/2024 18:57:15", "userId": 15, "pollId": 3}, {"id": 67, "answer": "\"{\\\"game_play_length\\\":\\\"2 - 3 hours a day\\\",\\\"daily_logins\\\":\\\"Once or twice a day\\\",\\\"beta_improved_feature\\\":\\\"Crafting\\\",\\\"beta_new_feature\\\":\\\"Story Mode\\\",\\\"missions\\\":\\\"No - Remove/rework missions entirely\\\",\\\"energy_training\\\":\\\"Yes - Increase energy regen\\\"}\"", "createdAt": "9/7/2024 20:53:31", "updatedAt": "9/7/2024 20:53:31", "userId": 60, "pollId": 3}, {"id": 68, "answer": "\"{\\\"game_play_length\\\":\\\"1 - 2 hours a day\\\",\\\"daily_logins\\\":\\\"Frequent small sessions\\\",\\\"energy_training\\\":\\\"Yes - Reduce energy regen\\\",\\\"beta_improved_feature\\\":\\\"Combat/Skills\\\",\\\"beta_new_feature\\\":\\\"New PvP content\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\",\\\"disliked_features\\\":\\\"I dislike the new hospitalisation system because of how easy it currently is to kill someone 3 times in a row and because of how rare items to cure injuries are. Also not liking that you have to wait 10 minutes to heal after paying, at this point i preferred the previous system\\\",\\\"additional_info\\\":\\\"Right now powerful skills have the potential to be very unbalanced, but I think just putting lower % accuracy would keep strong skills in check, especially those with fix damage, making the result of certain matches more unpredictable.\\\\nAlso I suggest using the old skull icons on the adventure map.\\\"}\"", "createdAt": "9/7/2024 21:01:58", "updatedAt": "9/7/2024 21:01:58", "userId": 39, "pollId": 3}, {"id": 69, "answer": "\"{\\\"game_play_length\\\":\\\"5+ hours a day\\\",\\\"daily_logins\\\":\\\"Frequent small sessions\\\",\\\"energy_training\\\":\\\"No - It's good as it is\\\",\\\"beta_improved_feature\\\":\\\"Combat/Skills\\\",\\\"beta_new_feature\\\":\\\"Story Mode\\\",\\\"missions\\\":\\\"Yes - Keep missions as an AFK feature\\\",\\\"disliked_features\\\":\\\"I feel like the injury system could be improved, as right now the injuries take a long to heal by themselves, and they become expensive to heal in the hospital once you get multiple injuries. I also feel like fights in adventure mode are kinda repetitive (clicking same buttons to fight every enemy).\\\",\\\"additional_info\\\":\\\"I had a lot of fun with the alpha, testing the systems and watching them change as I played. For beta I would suggest having a draft version of the story mode and maybe the world boss system too (I feel like it would be fun to have us all fight together against an enemy). Other features where we could cooperate would be cool too. I feel like it would create a sense of fellowship amongst the players. I'd also like to say thanks for making the game and giving us the opportunity to play and test it\\\"}\"", "createdAt": "10/7/2024 00:45:14", "updatedAt": "10/7/2024 00:45:14", "userId": 49, "pollId": 3}, {"id": 70, "answer": "\"{\\\"game_play_length\\\":\\\"1 - 2 hours a day\\\",\\\"daily_logins\\\":\\\"Frequent small sessions\\\",\\\"energy_training\\\":\\\"Yes - Full rework into a new system\\\",\\\"beta_improved_feature\\\":\\\"Combat/Skills\\\",\\\"beta_new_feature\\\":\\\"New PvE content\\\",\\\"disliked_features\\\":\\\"The Injury system. Though with the talents and new items it has become much better and will likely be a fine system in future updates, but I could see people with less time have some issues, but I think between all the items and future tweaks it will become better.\\\",\\\"missions\\\":\\\"No - Have it as a feature that occurs in the background\\\"}\"", "createdAt": "10/7/2024 06:10:29", "updatedAt": "10/7/2024 06:10:29", "userId": 87, "pollId": 3}]