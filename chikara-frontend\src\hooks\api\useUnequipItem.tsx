import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

type EquipSlot = "head" | "chest" | "hands" | "legs" | "feet" | "finger" | "offhand" | "shield" | "weapon" | "ranged";

export interface UnequipItemParams {
    slot: EquipSlot;
}

/**
 * Custom hook to unequip an item from a slot
 */
export const useUnequipItem = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.user.unequipItem.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.user.getEquippedItems.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
                toast.success("Item unequipped");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error: Error) => {
                toast.error(error.message || "Failed to unequip item");
            },
        })
    );
};

export default useUnequipItem;
