import * as StatusEffectService from "../statuseffect.service.js";
import * as TalentHelper from "../../features/talents/talents.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";
import { StatusEffectModifierType, StatusEffectTier, StatusEffectType } from "@prisma/client";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { db } from "../../lib/db.js";

vi.mock("../../features/talents/talents.helpers", () => ({
    UserHasStrongBonesTalent: vi.fn(),
}));

describe("Status Effect Service", () => {
    const mockUser = { id: 1, username: "testUser" };
    const mockDate = 1625097600000; // July 1, 2021

    beforeEach(() => {
        vi.resetAllMocks();
        vi.spyOn(globalThis.Date, "now").mockReturnValue(mockDate);
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe("GetUserStatusEffects", () => {
        test("should return user status effects", async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    endsAt: BigInt(mockDate + 3600000),
                    stacks: 1,
                    effect: { id: 1, name: "Effect 1" },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);

            const result = await StatusEffectService.GetUserStatusEffects(mockUser);

            expect(db.user_status_effect.findMany).toHaveBeenCalledWith({
                where: { userId: mockUser.id, endsAt: { gt: mockDate } },
                include: { effect: true },
            });
            expect(result).toEqual(mockEffects);
        });

        test("should return null when no effects found", async () => {
            vi.mocked(db.user_status_effect.findMany).mockResolvedValue([]);

            const result = await StatusEffectService.GetUserStatusEffects(mockUser);

            expect(result).toEqual([]);
        });

        test("should handle errors properly", async () => {
            const mockError = new Error("Database error");
            vi.mocked(db.user_status_effect.findMany).mockRejectedValue(mockError);

            await expect(StatusEffectService.GetUserStatusEffects(mockUser)).rejects.toThrow();
            expect(LogErrorStack).toHaveBeenCalled();
        });
    });

    describe("removeUserStatusEffects", () => {
        test("should delete all status effects for a user", async () => {
            await StatusEffectService.removeUserStatusEffects(mockUser.id);

            expect(db.user_status_effect.deleteMany).toHaveBeenCalledWith({
                where: { userId: mockUser.id },
            });
        });
    });

    describe("GetRandomInjury", () => {
        test("should get a random injury with default tier", async () => {
            const mockInjuries = [
                { id: 1, name: "Bruise", tier: "Minor" },
                { id: 2, name: "Cut", tier: "Minor" },
            ];

            vi.mocked(db.status_effect.findMany).mockResolvedValue(mockInjuries);

            const result = await StatusEffectService.GetRandomInjury();

            expect(db.status_effect.findMany).toHaveBeenCalledWith({
                where: { tier: "Minor", disabled: false },
            });
            expect(mockInjuries).toContain(result);
        });

        test("should get a random injury with specified tier and source", async () => {
            const mockInjuries = [{ id: 1, name: "Broken Bone", tier: "Moderate", source: "combat_injury" }];

            vi.mocked(db.status_effect.findMany).mockResolvedValue(mockInjuries);

            const result = await StatusEffectService.GetRandomInjury("Moderate", "combat_injury");

            expect(db.status_effect.findMany).toHaveBeenCalledWith({
                where: { tier: "Moderate", disabled: false, source: "combat_injury" },
            });
            expect(result).toEqual(mockInjuries[0]);
        });

        test("should return null when no injuries found", async () => {
            vi.mocked(db.status_effect.findMany).mockResolvedValue([]);

            const result = await StatusEffectService.GetRandomInjury();

            expect(result).toBeNull();
        });

        test("should handle errors and return null", async () => {
            const mockError = new Error("Database error");
            vi.mocked(db.status_effect.findMany).mockRejectedValue(mockError);

            const result = await StatusEffectService.GetRandomInjury();

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toBe(null);
        });
    });

    describe("GetInjuryOfType", () => {
        test("should get a random injury of specific type and tier", async () => {
            const mockInjuries = [
                { id: 1, name: "Bruise", category: "physical", tier: "Minor" },
                { id: 2, name: "Cut", category: "physical", tier: "Minor" },
            ];

            vi.mocked(db.status_effect.findMany).mockResolvedValue(mockInjuries);
            // Mock Math.random to return a predictable value
            const mockRandom = vi.spyOn(Math, "random").mockReturnValue(0.5);

            const result = await StatusEffectService.GetInjuryOfType("physical", "Minor");

            expect(db.status_effect.findMany).toHaveBeenCalledWith({
                where: {
                    tier: "Minor",
                    disabled: false,
                    category: "physical",
                },
            });
            // With Math.random mocked to return 0.5, we expect the second item (index 1)
            expect(result).toEqual(mockInjuries[1]);
            mockRandom.mockRestore();
        });

        test("should return null when no injury found", async () => {
            vi.mocked(db.status_effect.findMany).mockResolvedValue([]);

            const result = await StatusEffectService.GetInjuryOfType("physical");

            expect(result).toBeNull();
        });

        test("should handle errors and return false", async () => {
            const mockError = new Error("Database error");
            vi.mocked(db.status_effect.findMany).mockRejectedValue(mockError);

            const result = await StatusEffectService.GetInjuryOfType("physical");

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toBe(null);
        });
    });

    describe("GetBattleStatusEffects", () => {
        test("should calculate battle effects correctly", async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    endsAt: BigInt(mockDate + 3600000),
                    stacks: 2,
                    effect: {
                        id: 1,
                        category: "damage",
                        effectType: "BUFF",
                        modifier: 0.5,
                        modifierType: "add",
                    },
                },
                {
                    id: 2,
                    userId: 1,
                    effectId: 2,
                    endsAt: BigInt(mockDate + 3600000),
                    stacks: 1,
                    effect: {
                        id: 2,
                        category: "defense",
                        effectType: "BUFF",
                        modifier: 2,
                        modifierType: "multiply",
                    },
                },
                {
                    id: 3,
                    userId: 1,
                    effectId: 3,
                    endsAt: BigInt(mockDate + 3600000),
                    stacks: 1,
                    effect: {
                        id: 3,
                        category: "health",
                        effectType: "DEBUFF",
                        modifier: 2,
                        modifierType: "divide",
                    },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);

            const result = await StatusEffectService.GetBattleStatusEffects(mockUser);

            expect(result).toEqual({
                damage_buff: { value: 2 }, // 1 + (0.5 * 2)
                defense_buff: { value: 2 },
                health_debuff: { value: 0.5 }, // 1 / 2
            });
        });

        test("should handle empty effects", async () => {
            vi.mocked(db.user_status_effect.findMany).mockResolvedValue([]);

            const result = await StatusEffectService.GetBattleStatusEffects(mockUser);

            expect(result).toEqual({});
        });

        test("should handle null effects", async () => {
            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(null);

            const result = await StatusEffectService.GetBattleStatusEffects(mockUser);

            expect(result).toEqual({});
        });

        test("should handle errors", async () => {
            const mockError = new Error("Database error");
            vi.mocked(db.user_status_effect.findMany).mockRejectedValue(mockError);

            const result = await StatusEffectService.GetBattleStatusEffects(mockUser);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({});
        });

        test("should handle division by zero", async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    endsAt: BigInt(mockDate + 3600000),
                    stacks: 1,
                    effect: {
                        id: 1,
                        category: "damage",
                        effectType: "DEBUFF",
                        modifier: 0,
                        modifierType: "divide",
                    },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);

            const result = await StatusEffectService.GetBattleStatusEffects(mockUser);

            expect(result).toEqual({
                damage_debuff: { value: 1 }, // Division by 0 is handled
            });
        });

        test("should handle null modifier", async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    endsAt: BigInt(mockDate + 3600000),
                    stacks: 1,
                    effect: {
                        id: 1,
                        category: "damage",
                        effectType: "BUFF",
                        modifier: null,
                        modifierType: "add",
                    },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);

            const result = await StatusEffectService.GetBattleStatusEffects(mockUser);

            expect(result).toEqual({
                damage_buff: { value: 1 }, // modifier is null, treated as 0
            });
        });
    });

    describe("ApplyStatusEffectToUser", () => {
        const mockEffect = {
            id: 1,
            name: "Burning",
            effectType: "DEBUFF" as StatusEffectType,
            category: "damage",
            duration: 3600000,
            stackable: true,
            maxStacks: 3,
            modifier: 0.1,
            modifierType: "add" as StatusEffectModifierType,
        };

        test("should create a new status effect for user", async () => {
            vi.mocked(db.user_status_effect.findFirst).mockResolvedValue(null);
            vi.mocked(db.user_status_effect.create).mockResolvedValue({
                id: 1,
                userId: mockUser.id,
                effectId: mockEffect.id,
                endsAt: BigInt(mockDate + mockEffect.duration),
                stacks: 1,
                customName: null,
            });

            const result = await StatusEffectService.ApplyStatusEffectToUser(mockUser, mockEffect);

            expect(db.user_status_effect.create).toHaveBeenCalledWith({
                data: {
                    customName: null,
                    userId: mockUser.id,
                    effectId: mockEffect.id,
                    endsAt: BigInt(mockDate + mockEffect.duration),
                    stacks: 1,
                },
            });
            expect(result).toBeTruthy();
        });

        test("should update existing effect duration if not stackable", async () => {
            const existingEffect = {
                id: 1,
                userId: mockUser.id,
                effectId: mockEffect.id,
                endsAt: BigInt(mockDate),
                stacks: 1,
                customName: null,
            };

            const nonStackableEffect = { ...mockEffect, stackable: false };

            vi.mocked(db.user_status_effect.findFirst).mockResolvedValue(existingEffect);
            vi.mocked(db.user_status_effect.update).mockResolvedValue({
                ...existingEffect,
                endsAt: BigInt(mockDate + nonStackableEffect.duration),
            });

            const result = await StatusEffectService.ApplyStatusEffectToUser(mockUser, nonStackableEffect);

            expect(db.user_status_effect.update).toHaveBeenCalledWith({
                where: { id: existingEffect.id },
                data: expect.objectContaining({
                    endsAt: BigInt(mockDate + nonStackableEffect.duration),
                }),
            });
            expect(result).toBeTruthy();
        });

        test("should increase stacks for stackable effect", async () => {
            const existingEffect = {
                id: 1,
                userId: mockUser.id,
                effectId: mockEffect.id,
                endsAt: BigInt(mockDate),
                stacks: 1,
                customName: null,
            };

            vi.mocked(db.user_status_effect.findFirst).mockResolvedValue(existingEffect);
            vi.mocked(db.user_status_effect.update).mockResolvedValue({
                ...existingEffect,
                endsAt: BigInt(mockDate + mockEffect.duration / 2),
                stacks: 2,
            });

            const result = await StatusEffectService.ApplyStatusEffectToUser(mockUser, mockEffect);

            expect(db.user_status_effect.update).toHaveBeenCalledWith({
                where: { id: existingEffect.id },
                data: expect.objectContaining({
                    endsAt: BigInt(mockDate + mockEffect.duration / 2),
                    stacks: 2,
                }),
            });
            expect(result).toBeTruthy();
        });

        test("should only extend duration when at max stacks", async () => {
            const existingEffect = {
                id: 1,
                userId: mockUser.id,
                effectId: mockEffect.id,
                endsAt: BigInt(mockDate),
                stacks: 3, // Already at max stacks
                customName: null,
            };

            vi.mocked(db.user_status_effect.findFirst).mockResolvedValue(existingEffect);
            vi.mocked(db.user_status_effect.update).mockResolvedValue({
                ...existingEffect,
                endsAt: BigInt(mockDate + mockEffect.duration / 2),
            });

            const result = await StatusEffectService.ApplyStatusEffectToUser(mockUser, mockEffect);

            expect(db.user_status_effect.update).toHaveBeenCalledWith({
                where: { id: existingEffect.id },
                data: expect.objectContaining({
                    endsAt: BigInt(mockDate + mockEffect.duration / 2),
                    stacks: 3, // Stacks remain the same
                }),
            });
            expect(result).toBeTruthy();
        });

        test("should resist damage debuff with Strong Bones talent", async () => {
            const damageDebuff = {
                ...mockEffect,
                effectType: "DEBUFF" as StatusEffectType,
                category: "damage",
            };

            vi.mocked(TalentHelper.UserHasStrongBonesTalent).mockResolvedValue({
                modifier: 1.0, // 100% chance to resist
            });

            const result = await StatusEffectService.ApplyStatusEffectToUser(mockUser, damageDebuff);

            expect(TalentHelper.UserHasStrongBonesTalent).toHaveBeenCalledWith(mockUser.id);
            expect(result).toBeNull();
        });

        test("should handle errors", async () => {
            const mockError = new Error("Database error");
            vi.mocked(db.user_status_effect.findFirst).mockRejectedValue(mockError);

            const result = await StatusEffectService.ApplyStatusEffectToUser(mockUser, mockEffect);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toBe(null);
        });
    });

    describe("removeStatusEffect", () => {
        test("should remove a status effect", async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    stacks: 2,
                    endsAt: BigInt(mockDate + 3600000),
                    effect: {
                        id: 1,
                        name: "Bruise",
                        tier: "Minor" as StatusEffectTier,
                        source: "physical_injury",
                    },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);
            vi.mocked(db.user_status_effect.update).mockResolvedValue({ ...mockEffects[0], stacks: 1 });

            const result = await StatusEffectService.removeStatusEffect(1, "physical", "Minor");

            expect(logAction).toHaveBeenCalledWith(
                expect.objectContaining({
                    action: "STATUS_EFFECT_REMOVED",
                    userId: 1,
                    info: expect.objectContaining({
                        effectName: "Bruise",
                        amount: 1,
                    }),
                })
            );

            expect(db.user_status_effect.update).toHaveBeenCalledWith({
                where: { id: 1 },
                data: { stacks: 1 },
            });

            expect(result).toBe(true);
        });

        test("should completely remove effect when stacks is reduced to zero", async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    stacks: 1,
                    endsAt: BigInt(mockDate + 3600000),
                    effect: {
                        id: 1,
                        name: "Bruise",
                        tier: "Minor" as StatusEffectTier,
                        source: "physical_injury",
                    },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);
            vi.mocked(db.user_status_effect.delete).mockResolvedValue(mockEffects[0]);

            const result = await StatusEffectService.removeStatusEffect(1, "physical", "Minor");

            expect(db.user_status_effect.delete).toHaveBeenCalledWith({
                where: { id: 1 },
            });

            expect(result).toBe(true);
        });

        test("should handle removal of multiple stacks", async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    stacks: 3,
                    endsAt: BigInt(mockDate + 3600000),
                    effect: {
                        id: 1,
                        name: "Bruise",
                        tier: "Minor" as StatusEffectTier,
                        source: "physical_injury",
                    },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);
            vi.mocked(db.user_status_effect.update).mockResolvedValue({ ...mockEffects[0], stacks: 1 });

            const result = await StatusEffectService.removeStatusEffect(1, "physical", "Minor", 2);

            expect(db.user_status_effect.update).toHaveBeenCalledWith({
                where: { id: 1 },
                data: { stacks: 1 },
            });

            expect(result).toBe(true);
        });

        test("should handle when no effects are found", async () => {
            vi.mocked(db.user_status_effect.findMany).mockResolvedValue([]);

            const result = await StatusEffectService.removeStatusEffect(1, "physical", "Minor");

            expect(result).toBe(false);
        });

        test('should handle "All" tier parameter', async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    stacks: 1,
                    endsAt: BigInt(mockDate + 3600000),
                    effect: {
                        id: 1,
                        name: "Bruise",
                        tier: "Minor" as StatusEffectTier,
                        source: "physical_injury",
                    },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);
            vi.mocked(db.user_status_effect.delete).mockResolvedValue(mockEffects[0]);

            const result = await StatusEffectService.removeStatusEffect(1, "physical", "All");

            expect(db.user_status_effect.findMany).toHaveBeenCalledWith(
                expect.objectContaining({
                    where: expect.objectContaining({
                        userId: 1,
                        endsAt: { gt: mockDate },
                        effect: {
                            source: "physical_injury",
                        },
                    }),
                })
            );

            expect(result).toBe(true);
        });

        test("should return false when missing required parameters", async () => {
            const result = await StatusEffectService.removeStatusEffect(1, "", "Minor");
            expect(result).toBe(false);

            const result2 = await StatusEffectService.removeStatusEffect(1, "physical", "");
            expect(result2).toBe(false);
        });

        test("should return false when effect has no stacks", async () => {
            const mockEffects = [
                {
                    id: 1,
                    userId: 1,
                    effectId: 1,
                    stacks: null,
                    endsAt: BigInt(mockDate + 3600000),
                    effect: {
                        id: 1,
                        name: "Bruise",
                        tier: "Minor" as StatusEffectTier,
                        source: "physical_injury",
                    },
                },
            ];

            vi.mocked(db.user_status_effect.findMany).mockResolvedValue(mockEffects);

            const result = await StatusEffectService.removeStatusEffect(1, "physical", "Minor");

            expect(result).toBe(false);
        });
    });
});
