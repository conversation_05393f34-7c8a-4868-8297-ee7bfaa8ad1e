import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";

function useIsQuestCompleted(questName: string): boolean {
    const { data: questProgress } = useQuery(
        api.quests.getQuestProgress.queryOptions({
            input: { activeOnly: false },
            staleTime: Number.POSITIVE_INFINITY,
        })
    );

    const questCompleted = useMemo(() => {
        const completedQuests = questProgress?.filter((quest) => quest.questStatus === "complete");
        if (!completedQuests) return false;

        const isQuestComplete = completedQuests?.find((quest) => quest.quest.name === questName);

        if (isQuestComplete) return true;
        return false;
    }, [questProgress, questName]);

    if (!questProgress) return false;

    return questCompleted;
}

export default useIsQuestCompleted;
