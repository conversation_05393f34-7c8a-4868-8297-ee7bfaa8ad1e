{"version": 3, "sources": ["../../../../node_modules/@date-fns/utc/date/mini.js", "../../../../node_modules/@date-fns/utc/date/index.js", "../../../../node_modules/@date-fns/utc/utc/index.js"], "sourcesContent": ["export class UTCDateMini extends Date {\n  constructor() {\n    super();\n    this.setTime(arguments.length === 0 ?\n    // Enables Sinon's fake timers that override the constructor\n    Date.now() : arguments.length === 1 ? typeof arguments[0] === \"string\" ? +new Date(arguments[0]) : arguments[0] : Date.UTC(...arguments));\n  }\n  getTimezoneOffset() {\n    return 0;\n  }\n}\n\n// Replace getter and setter functions with UTC counterparts\nconst re = /^(get|set)(?!UTC)/;\nObject.getOwnPropertyNames(Date.prototype).forEach(method => {\n  if (re.test(method)) {\n    const utcMethod = Date.prototype[method.replace(re, \"$1UTC\")];\n    if (utcMethod) UTCDateMini.prototype[method] = utcMethod;\n  }\n});", "import { UTCDateMini } from \"./mini.js\";\n\n/**\n * UTC date class. It maps getters and setters to corresponding UTC methods,\n * forcing all calculations in the UTC time zone.\n *\n * Combined with date-fns, it allows using the class the same way as\n * the original date class.\n *\n * This complete version provides not only getters, setters,\n * and `getTimezoneOffset`, but also the formatter functions, mirroring\n * all original `Date` functionality. Use this version when you need to format\n * a string or in an environment you don't fully control (a library).\n * For a minimal version, see `UTCDateMini`.\n */\nexport class UTCDate extends UTCDateMini {\n  toString() {\n    const date = this.toDateString();\n    const time = this.toTimeString();\n    return `${date} ${time}`;\n  }\n  toDateString() {\n    const weekday = weekdayFormat.format(this);\n    const date = dateFormat.format(this);\n    const year = this.getFullYear();\n    return `${weekday} ${date} ${year}`;\n  }\n  toTimeString() {\n    const time = timeFormat.format(this);\n    return `${time} GMT+0000 (Coordinated Universal Time)`;\n  }\n  toLocaleString(locales, options) {\n    return Date.prototype.toLocaleString.call(this, locales, {\n      timeZone: \"UTC\",\n      ...options\n    });\n  }\n  toLocaleDateString(locales, options) {\n    return Date.prototype.toLocaleDateString.call(this, locales, {\n      timeZone: \"UTC\",\n      ...options\n    });\n  }\n  toLocaleTimeString(locales, options) {\n    return Date.prototype.toLocaleTimeString.call(this, locales, {\n      timeZone: \"UTC\",\n      ...options\n    });\n  }\n}\nvar weekdayFormat = new Intl.DateTimeFormat(\"en-US\", {\n  weekday: \"short\",\n  timeZone: \"UTC\"\n});\nvar dateFormat = new Intl.DateTimeFormat(\"en-US\", {\n  month: \"short\",\n  day: \"numeric\",\n  timeZone: \"UTC\"\n});\nvar timeFormat = new Intl.DateTimeFormat(\"en-GB\", {\n  hour12: false,\n  hour: \"numeric\",\n  minute: \"numeric\",\n  second: \"numeric\",\n  timeZone: \"UTC\"\n});", "import { UTCDate } from \"../date/index.js\";\n\n/**\n * The function creates a new `UTCDate` instance from the provided value. Use it\n * to provide the context for the date-fns functions, via the `in` option.\n *\n * @param value - Date value, timestamp, string or `Date` instance\n *\n * @returns UTCDate instance created from the provided value\n */\nexport const utc = value => new UTCDate(+new Date(value));"], "mappings": ";;;AAAO,IAAM,cAAN,cAA0B,KAAK;AAAA,EACpC,cAAc;AACZ,UAAM;AACN,SAAK,QAAQ,UAAU,WAAW;AAAA;AAAA,MAElC,KAAK,IAAI;AAAA,QAAI,UAAU,WAAW,IAAI,OAAO,UAAU,CAAC,MAAM,WAAW,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC;AAAA,EAC1I;AAAA,EACA,oBAAoB;AAClB,WAAO;AAAA,EACT;AACF;AAGA,IAAM,KAAK;AACX,OAAO,oBAAoB,KAAK,SAAS,EAAE,QAAQ,YAAU;AAC3D,MAAI,GAAG,KAAK,MAAM,GAAG;AACnB,UAAM,YAAY,KAAK,UAAU,OAAO,QAAQ,IAAI,OAAO,CAAC;AAC5D,QAAI,UAAW,aAAY,UAAU,MAAM,IAAI;AAAA,EACjD;AACF,CAAC;;;ACJM,IAAM,UAAN,cAAsB,YAAY;AAAA,EACvC,WAAW;AACT,UAAM,OAAO,KAAK,aAAa;AAC/B,UAAM,OAAO,KAAK,aAAa;AAC/B,WAAO,GAAG,IAAI,IAAI,IAAI;AAAA,EACxB;AAAA,EACA,eAAe;AACb,UAAM,UAAU,cAAc,OAAO,IAAI;AACzC,UAAM,OAAO,WAAW,OAAO,IAAI;AACnC,UAAM,OAAO,KAAK,YAAY;AAC9B,WAAO,GAAG,OAAO,IAAI,IAAI,IAAI,IAAI;AAAA,EACnC;AAAA,EACA,eAAe;AACb,UAAM,OAAO,WAAW,OAAO,IAAI;AACnC,WAAO,GAAG,IAAI;AAAA,EAChB;AAAA,EACA,eAAe,SAAS,SAAS;AAC/B,WAAO,KAAK,UAAU,eAAe,KAAK,MAAM,SAAS;AAAA,MACvD,UAAU;AAAA,MACV,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,SAAS,SAAS;AACnC,WAAO,KAAK,UAAU,mBAAmB,KAAK,MAAM,SAAS;AAAA,MAC3D,UAAU;AAAA,MACV,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,SAAS,SAAS;AACnC,WAAO,KAAK,UAAU,mBAAmB,KAAK,MAAM,SAAS;AAAA,MAC3D,UAAU;AAAA,MACV,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF;AACA,IAAI,gBAAgB,IAAI,KAAK,eAAe,SAAS;AAAA,EACnD,SAAS;AAAA,EACT,UAAU;AACZ,CAAC;AACD,IAAI,aAAa,IAAI,KAAK,eAAe,SAAS;AAAA,EAChD,OAAO;AAAA,EACP,KAAK;AAAA,EACL,UAAU;AACZ,CAAC;AACD,IAAI,aAAa,IAAI,KAAK,eAAe,SAAS;AAAA,EAChD,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC;;;ACvDM,IAAM,MAAM,WAAS,IAAI,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC;", "names": []}