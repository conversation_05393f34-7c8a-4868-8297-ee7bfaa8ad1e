import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBeginBattle = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.battle.battleBegin.mutationOptions({
            onSuccess: () => {
                // Fetch latest battle state and refresh user info
                queryClient.invalidateQueries({ queryKey: api.battle.status.key() });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
        })
    );
};

export default useBeginBattle;
