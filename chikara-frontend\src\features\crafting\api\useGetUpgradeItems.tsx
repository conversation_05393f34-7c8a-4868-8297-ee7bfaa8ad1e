import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery } from "@tanstack/react-query";

/**
 * Custom hook to fetch upgrade items
 */
const useGetUpgradeItems = (options: QueryOptions = {}) => {
    return useQuery(
        api.items.getUpgradeItems.queryOptions({
            staleTime: 60000, // 1 minute
            ...options,
        })
    );
};

export default useGetUpgradeItems;
