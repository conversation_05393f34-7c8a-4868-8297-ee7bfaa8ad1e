import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as AuctionController from "./auction.controller.js";
import auctionSchema from "./auction.validation.js";

export const auctionRouter = {
    /**
     * Get all auction listings
     */
    getList: isLoggedInAuth.handler(async () => {
        const response = await AuctionController.getAuctionList();
        return handleResponse(response);
    }),

    /**
     * Create a new auction listing
     */
    createListing: canMakeStateChangesAuth
        .input(auctionSchema.createAuctionListing)
        .handler(async ({ input, context }) => {
            const { itemId, quantity, buyoutPrice, auctionLength, bankFunds } = input;
            const response = await AuctionController.createAuctionListing(
                context.user.id,
                itemId,
                quantity,
                buyoutPrice,
                auctionLength,
                bankFunds
            );
            return handleResponse(response);
        }),

    /**
     * Buyout an auction listing
     */
    buyoutListing: canMakeStateChangesAuth
        .input(auctionSchema.buyoutAuctionListing)
        .handler(async ({ input, context }) => {
            const { auctionItemId, quantity } = input;
            const response = await AuctionController.buyoutAuctionListing(context.user.id, auctionItemId, quantity);
            return handleResponse(response);
        }),

    /**
     * Cancel an auction listing
     */
    cancelListing: canMakeStateChangesAuth
        .input(auctionSchema.cancelAuctionListing)
        .handler(async ({ input, context }) => {
            const response = await AuctionController.cancelAuctionListing(context.user.id, input.auctionItemId);
            return handleResponse(response);
        }),
};

export default auctionRouter;
