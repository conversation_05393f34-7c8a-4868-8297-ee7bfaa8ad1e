import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-hot-toast";

const useSetNotificationSettings = (initialSettings) => {
    const [pushNotificationsEnabled, setPushNotificationsEnabled] = useState(initialSettings?.pushNotificationsEnabled);
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.notifications.updatePushNotificationSettings.mutationOptions({
            onSuccess: () => {
                toast.success(`Saved Successfully`);
                queryClient.invalidateQueries({
                    queryKey: api.user.currentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(`Error: ${error.message}`);
            },
        })
    );

    return {
        pushNotificationsEnabled,
        setPushNotificationsEnabled,
        saveNotificationSettings: () => mutation.mutate({ pushEnabled: pushNotificationsEnabled }),
    };
};

export default useSetNotificationSettings;
