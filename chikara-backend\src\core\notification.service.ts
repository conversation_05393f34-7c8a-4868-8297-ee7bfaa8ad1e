import { sendPushNotifications } from "../config/firebase.js";
import * as sockets from "../config/socket.js";
import { db } from "../lib/db.js";
import { NotificationTypes } from "../types/notification.js";
import { LogErrorStack, logger } from "../utils/log.js";

const READ_BY_DEFAULT_NOTIFICATION_TYPES = new Set<NotificationTypes>([
    NotificationTypes.hospitalised,
    NotificationTypes.jail,
    NotificationTypes.transfer_sent,
    NotificationTypes.fight_win,
    NotificationTypes.banned,
]);

export const createNotification = async (
    userId: number,
    notificationType: NotificationTypes,
    details: string,
    read: boolean
) => {
    return await db.notification.create({
        data: {
            notificationType,
            details,
            read,
            userId,
        },
    });
};

export const fetchUserPushTokens = async (userId: number) => {
    return await db.push_token.findMany({
        where: { userId },
        select: {
            userId: true,
            token: true,
        },
    });
};

export async function NotifyUser(
    userId: number,
    notificationType: NotificationTypes,
    details: Record<string, unknown>,
    read?: boolean
): Promise<void> {
    try {
        const normalisedDetails = JSON.stringify(details);
        logger.debug("Notification details: " + normalisedDetails);
        if (notificationType !== NotificationTypes.message) {
            await createNotification(
                userId,
                notificationType,
                normalisedDetails,
                read ?? READ_BY_DEFAULT_NOTIFICATION_TYPES.has(notificationType)
            );
        }
        sockets.SendNotification(userId, {
            type: notificationType,
            details: normalisedDetails,
        });
    } catch (error) {
        LogErrorStack({ message: "Failed to send notification:", error });
    }
}

export function NotifyMessageRemoved(): void {
    // TODO: support other chat rooms
    sockets.NotifyMessageRemoved();
}

export async function sendSinglePushNotification(userId: number, message: string) {
    try {
        // Fetch the user's push tokens from the database
        const pushTokens = await fetchUserPushTokens(userId);

        if (pushTokens.length === 0) {
            logger.error("No push tokens found for user: " + userId);
            return false;
        }

        await sendPushNotifications(pushTokens, message);
        return true;
    } catch (error) {
        LogErrorStack({ message: "Error sending push notification:", error });
        return false;
    }
}

export { sendPushNotifications } from "../config/firebase.js";

export default {
    NotifyUser,
    NotifyMessageRemoved,
    fetchUserPushTokens,
    sendPushNotifications,
    sendSinglePushNotification,
};
