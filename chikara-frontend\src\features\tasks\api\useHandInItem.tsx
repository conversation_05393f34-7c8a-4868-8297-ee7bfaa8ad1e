import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

/**
 * Hook for handling quest item hand-in operations
 * @returns A mutation function and related state for handling item hand-ins
 */
export const useHandInItem = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.quests.handInItem.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.quests.getActive.key(),
                });
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
            },
        })
    );
};
