import{cC as j,cB as g,cM as h,cH as D,e as w,g as I,c as N,l as x,j as a,ad as C,c_ as T,o as v,D as k,a3 as U,f as q}from"./index--cEnoMkg.js";function M(n,e){const t=()=>j(e?.in,NaN),r=Y(n);let o;if(r.date){const i=O(r.date,2);o=F(i.restDateString,i.year)}if(!o||isNaN(+o))return t();const d=+o;let c=0,u;if(r.time&&(c=$(r.time),isNaN(c)))return t();if(r.timezone){if(u=B(r.timezone),isNaN(u))return t()}else{const i=new Date(d+c),l=g(0,e?.in);return l.setFullYear(i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()),l.setHours(i.getUTCHours(),i.getUTCMinutes(),i.getUTCSeconds(),i.getUTCMilliseconds()),l}return g(d+c+u,e?.in)}const f={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},S=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,R=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,z=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Y(n){const e={},t=n.split(f.dateTimeDelimiter);let s;if(t.length>2)return e;if(/:/.test(t[0])?s=t[0]:(e.date=t[0],s=t[1],f.timeZoneDelimiter.test(e.date)&&(e.date=n.split(f.timeZoneDelimiter)[0],s=n.substr(e.date.length,n.length))),s){const r=f.timezone.exec(s);r?(e.time=s.replace(r[1],""),e.timezone=r[1]):e.time=s}return e}function O(n,e){const t=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),s=n.match(t);if(!s)return{year:NaN,restDateString:""};const r=s[1]?parseInt(s[1]):null,o=s[2]?parseInt(s[2]):null;return{year:o===null?r:o*100,restDateString:n.slice((s[1]||s[2]).length)}}function F(n,e){if(e===null)return new Date(NaN);const t=n.match(S);if(!t)return new Date(NaN);const s=!!t[4],r=m(t[1]),o=m(t[2])-1,d=m(t[3]),c=m(t[4]),u=m(t[5])-1;if(s)return A(e,c,u)?W(e,c,u):new Date(NaN);{const i=new Date(0);return!H(e,o,d)||!L(e,r)?new Date(NaN):(i.setUTCFullYear(e,o,Math.max(r,d)),i)}}function m(n){return n?parseInt(n):1}function $(n){const e=n.match(R);if(!e)return NaN;const t=p(e[1]),s=p(e[2]),r=p(e[3]);return E(t,s,r)?t*h+s*D+r*1e3:NaN}function p(n){return n&&parseFloat(n.replace(",","."))||0}function B(n){if(n==="Z")return 0;const e=n.match(z);if(!e)return 0;const t=e[1]==="+"?-1:1,s=parseInt(e[2]),r=e[3]&&parseInt(e[3])||0;return V(s,r)?t*(s*h+r*D):NaN}function W(n,e,t){const s=new Date(0);s.setUTCFullYear(n,0,4);const r=s.getUTCDay()||7,o=(e-1)*7+t+1-r;return s.setUTCDate(s.getUTCDate()+o),s}const Z=[31,null,31,30,31,30,31,31,30,31,30,31];function y(n){return n%400===0||n%4===0&&n%100!==0}function H(n,e,t){return e>=0&&e<=11&&t>=1&&t<=(Z[e]||(y(n)?29:28))}function L(n,e){return e>=1&&e<=(y(n)?366:365)}function A(n,e,t){return e>=1&&e<=53&&t>=0&&t<=6}function E(n,e,t){return n===24?e===0&&t===0:t>=0&&t<60&&e>=0&&e<60&&n>=0&&n<25}function V(n,e){return e>=0&&e<=59}const _=()=>{const n=w(),e=I(N.gang.requestInvite.mutationOptions({onSuccess:()=>{x.success("Request sent!"),n.invalidateQueries({queryKey:N.gang.getGangList.key()})},onError:s=>{x.error(s?.message||"An error occurred")}}));return{requestGangInvite:s=>{if(!s){x.error("Gang ID is required");return}e.mutate({gangId:parseInt(s)})}}};function J({open:n,setOpen:e,selectedGang:t,setSelectedGang:s,currentUser:r,hideInviteButton:o}){const{requestGangInvite:d}=_(),c=()=>{d(t?.id)},u=l=>{if(!l)return null;const b=M(l);return q(b,"dd/MM/y")};if(!t)return null;const i=l=>{l||s(null),e(l)};return a.jsx(C,{showClose:!0,open:n,title:"Gang Info",iconBackground:"shadow-lg",modalMaxWidth:"max-w-2xl!",contentPadding:"px-0 md:px-6 mb-6",Icon:()=>a.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/Su197a0.png",alt:"",className:"mt-0.5 h-10 w-auto"}),onOpenChange:i,children:a.jsxs("div",{className:"mx-4 flex flex-col md:mx-12",children:[a.jsx(T,{gang:t,className:"-mx-4 h-24 rounded-t-none"}),a.jsxs("div",{className:"mx-auto my-2 flex w-fit flex-col rounded-lg border border-gray-600 bg-slate-800 p-2 px-4 text-left text-stroke-s-sm text-white",children:[a.jsxs("div",{className:"flex gap-8",children:[a.jsxs(v,{to:`/profile/${t.owner.id}`,className:"flex cursor-pointer flex-col",children:[a.jsx("p",{className:"font-body font-semibold text-gray-300 text-xs uppercase",children:"Gang Leader"}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(k,{className:"size-7 rounded-lg",src:t.owner}),a.jsx("p",{className:"text-custom-yellow",children:t.owner.username})]})]}),a.jsxs("div",{className:"flex flex-col gap-1",children:[a.jsx("p",{className:"font-body font-semibold text-gray-300 text-xs uppercase",children:"Created"}),a.jsx("div",{className:"flex items-center gap-2",children:a.jsx("p",{className:"font-body font-semibold text-blue-400 text-sm tracking-wide",children:u(t.createdAt)})})]})]}),a.jsx("p",{className:"mt-2 font-body font-semibold text-gray-300 text-xs uppercase",children:"Description"}),a.jsx("p",{children:t.about})]}),r?.gangId!==t?.id&&t?.id!==1&&!o?a.jsx(a.Fragment,{children:t?.requestSent?a.jsxs("p",{className:"mx-auto mt-2 w-fit rounded-lg border border-black bg-gray-950 px-4 py-1 text-center text-2xl text-green-500",children:["Request Sent ",a.jsx(U,{className:"mb-0.5 ml-1 inline size-5 text-green-500"})]}):a.jsx("button",{type:"button",disabled:t?.requestSent,className:"darkBlueButtonBGSVG mx-auto mt-2 flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:grayscale md:mt-3 dark:text-slate-200",onClick:()=>c(),children:"Request Invite"})}):null]})})}export{J as V};
