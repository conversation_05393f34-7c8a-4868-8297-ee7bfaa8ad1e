import { useQuery } from "@tanstack/react-query";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { api } from "@/helpers/api";

/**
 * Hook for fetching active bounties with more control
 */
export const useActiveBounties = (options?: QueryOptions) => {
    return useQuery(
        api.bounties.activeBountyList.queryOptions({
            staleTime: options?.staleTime || 30000,
            enabled: options?.enabled !== false,
        })
    );
};
