{"version": 3, "sources": ["../../../../node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.esm.js"], "sourcesContent": ["/*!\n * chartjs-plugin-datalabels v2.2.0\n * https://chartjs-plugin-datalabels.netlify.app\n * (c) 2017-2022 chartjs-plugin-datalabels contributors\n * Released under the MIT license\n */\nimport { isNullOrUndef, merge, toFont, resolve, toPadding, valueOrDefault, callback, isObject, each } from 'chart.js/helpers';\nimport { defaults as defaults$1, ArcElement, PointElement, BarElement } from 'chart.js';\n\nvar devicePixelRatio = (function() {\n  if (typeof window !== 'undefined') {\n    if (window.devicePixelRatio) {\n      return window.devicePixelRatio;\n    }\n\n    // devicePixelRatio is undefined on IE10\n    // https://stackoverflow.com/a/20204180/8837887\n    // https://github.com/chartjs/chartjs-plugin-datalabels/issues/85\n    var screen = window.screen;\n    if (screen) {\n      return (screen.deviceXDPI || 1) / (screen.logicalXDPI || 1);\n    }\n  }\n\n  return 1;\n}());\n\nvar utils = {\n  // @todo move this in Chart.helpers.toTextLines\n  toTextLines: function(inputs) {\n    var lines = [];\n    var input;\n\n    inputs = [].concat(inputs);\n    while (inputs.length) {\n      input = inputs.pop();\n      if (typeof input === 'string') {\n        lines.unshift.apply(lines, input.split('\\n'));\n      } else if (Array.isArray(input)) {\n        inputs.push.apply(inputs, input);\n      } else if (!isNullOrUndef(inputs)) {\n        lines.unshift('' + input);\n      }\n    }\n\n    return lines;\n  },\n\n  // @todo move this in Chart.helpers.canvas.textSize\n  // @todo cache calls of measureText if font doesn't change?!\n  textSize: function(ctx, lines, font) {\n    var items = [].concat(lines);\n    var ilen = items.length;\n    var prev = ctx.font;\n    var width = 0;\n    var i;\n\n    ctx.font = font.string;\n\n    for (i = 0; i < ilen; ++i) {\n      width = Math.max(ctx.measureText(items[i]).width, width);\n    }\n\n    ctx.font = prev;\n\n    return {\n      height: ilen * font.lineHeight,\n      width: width\n    };\n  },\n\n  /**\n   * Returns value bounded by min and max. This is equivalent to max(min, min(value, max)).\n   * @todo move this method in Chart.helpers.bound\n   * https://doc.qt.io/qt-5/qtglobal.html#qBound\n   */\n  bound: function(min, value, max) {\n    return Math.max(min, Math.min(value, max));\n  },\n\n  /**\n   * Returns an array of pair [value, state] where state is:\n   * * -1: value is only in a0 (removed)\n   * *  1: value is only in a1 (added)\n   */\n  arrayDiff: function(a0, a1) {\n    var prev = a0.slice();\n    var updates = [];\n    var i, j, ilen, v;\n\n    for (i = 0, ilen = a1.length; i < ilen; ++i) {\n      v = a1[i];\n      j = prev.indexOf(v);\n\n      if (j === -1) {\n        updates.push([v, 1]);\n      } else {\n        prev.splice(j, 1);\n      }\n    }\n\n    for (i = 0, ilen = prev.length; i < ilen; ++i) {\n      updates.push([prev[i], -1]);\n    }\n\n    return updates;\n  },\n\n  /**\n   * https://github.com/chartjs/chartjs-plugin-datalabels/issues/70\n   */\n  rasterize: function(v) {\n    return Math.round(v * devicePixelRatio) / devicePixelRatio;\n  }\n};\n\nfunction orient(point, origin) {\n  var x0 = origin.x;\n  var y0 = origin.y;\n\n  if (x0 === null) {\n    return {x: 0, y: -1};\n  }\n  if (y0 === null) {\n    return {x: 1, y: 0};\n  }\n\n  var dx = point.x - x0;\n  var dy = point.y - y0;\n  var ln = Math.sqrt(dx * dx + dy * dy);\n\n  return {\n    x: ln ? dx / ln : 0,\n    y: ln ? dy / ln : -1\n  };\n}\n\nfunction aligned(x, y, vx, vy, align) {\n  switch (align) {\n  case 'center':\n    vx = vy = 0;\n    break;\n  case 'bottom':\n    vx = 0;\n    vy = 1;\n    break;\n  case 'right':\n    vx = 1;\n    vy = 0;\n    break;\n  case 'left':\n    vx = -1;\n    vy = 0;\n    break;\n  case 'top':\n    vx = 0;\n    vy = -1;\n    break;\n  case 'start':\n    vx = -vx;\n    vy = -vy;\n    break;\n  case 'end':\n    // keep natural orientation\n    break;\n  default:\n    // clockwise rotation (in degree)\n    align *= (Math.PI / 180);\n    vx = Math.cos(align);\n    vy = Math.sin(align);\n    break;\n  }\n\n  return {\n    x: x,\n    y: y,\n    vx: vx,\n    vy: vy\n  };\n}\n\n// Line clipping (Cohen–Sutherland algorithm)\n// https://en.wikipedia.org/wiki/Cohen–Sutherland_algorithm\n\nvar R_INSIDE = 0;\nvar R_LEFT = 1;\nvar R_RIGHT = 2;\nvar R_BOTTOM = 4;\nvar R_TOP = 8;\n\nfunction region(x, y, rect) {\n  var res = R_INSIDE;\n\n  if (x < rect.left) {\n    res |= R_LEFT;\n  } else if (x > rect.right) {\n    res |= R_RIGHT;\n  }\n  if (y < rect.top) {\n    res |= R_TOP;\n  } else if (y > rect.bottom) {\n    res |= R_BOTTOM;\n  }\n\n  return res;\n}\n\nfunction clipped(segment, area) {\n  var x0 = segment.x0;\n  var y0 = segment.y0;\n  var x1 = segment.x1;\n  var y1 = segment.y1;\n  var r0 = region(x0, y0, area);\n  var r1 = region(x1, y1, area);\n  var r, x, y;\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (!(r0 | r1) || (r0 & r1)) {\n      // both points inside or on the same side: no clipping\n      break;\n    }\n\n    // at least one point is outside\n    r = r0 || r1;\n\n    if (r & R_TOP) {\n      x = x0 + (x1 - x0) * (area.top - y0) / (y1 - y0);\n      y = area.top;\n    } else if (r & R_BOTTOM) {\n      x = x0 + (x1 - x0) * (area.bottom - y0) / (y1 - y0);\n      y = area.bottom;\n    } else if (r & R_RIGHT) {\n      y = y0 + (y1 - y0) * (area.right - x0) / (x1 - x0);\n      x = area.right;\n    } else if (r & R_LEFT) {\n      y = y0 + (y1 - y0) * (area.left - x0) / (x1 - x0);\n      x = area.left;\n    }\n\n    if (r === r0) {\n      x0 = x;\n      y0 = y;\n      r0 = region(x0, y0, area);\n    } else {\n      x1 = x;\n      y1 = y;\n      r1 = region(x1, y1, area);\n    }\n  }\n\n  return {\n    x0: x0,\n    x1: x1,\n    y0: y0,\n    y1: y1\n  };\n}\n\nfunction compute$1(range, config) {\n  var anchor = config.anchor;\n  var segment = range;\n  var x, y;\n\n  if (config.clamp) {\n    segment = clipped(segment, config.area);\n  }\n\n  if (anchor === 'start') {\n    x = segment.x0;\n    y = segment.y0;\n  } else if (anchor === 'end') {\n    x = segment.x1;\n    y = segment.y1;\n  } else {\n    x = (segment.x0 + segment.x1) / 2;\n    y = (segment.y0 + segment.y1) / 2;\n  }\n\n  return aligned(x, y, range.vx, range.vy, config.align);\n}\n\nvar positioners = {\n  arc: function(el, config) {\n    var angle = (el.startAngle + el.endAngle) / 2;\n    var vx = Math.cos(angle);\n    var vy = Math.sin(angle);\n    var r0 = el.innerRadius;\n    var r1 = el.outerRadius;\n\n    return compute$1({\n      x0: el.x + vx * r0,\n      y0: el.y + vy * r0,\n      x1: el.x + vx * r1,\n      y1: el.y + vy * r1,\n      vx: vx,\n      vy: vy\n    }, config);\n  },\n\n  point: function(el, config) {\n    var v = orient(el, config.origin);\n    var rx = v.x * el.options.radius;\n    var ry = v.y * el.options.radius;\n\n    return compute$1({\n      x0: el.x - rx,\n      y0: el.y - ry,\n      x1: el.x + rx,\n      y1: el.y + ry,\n      vx: v.x,\n      vy: v.y\n    }, config);\n  },\n\n  bar: function(el, config) {\n    var v = orient(el, config.origin);\n    var x = el.x;\n    var y = el.y;\n    var sx = 0;\n    var sy = 0;\n\n    if (el.horizontal) {\n      x = Math.min(el.x, el.base);\n      sx = Math.abs(el.base - el.x);\n    } else {\n      y = Math.min(el.y, el.base);\n      sy = Math.abs(el.base - el.y);\n    }\n\n    return compute$1({\n      x0: x,\n      y0: y + sy,\n      x1: x + sx,\n      y1: y,\n      vx: v.x,\n      vy: v.y\n    }, config);\n  },\n\n  fallback: function(el, config) {\n    var v = orient(el, config.origin);\n\n    return compute$1({\n      x0: el.x,\n      y0: el.y,\n      x1: el.x + (el.width || 0),\n      y1: el.y + (el.height || 0),\n      vx: v.x,\n      vy: v.y\n    }, config);\n  }\n};\n\nvar rasterize = utils.rasterize;\n\nfunction boundingRects(model) {\n  var borderWidth = model.borderWidth || 0;\n  var padding = model.padding;\n  var th = model.size.height;\n  var tw = model.size.width;\n  var tx = -tw / 2;\n  var ty = -th / 2;\n\n  return {\n    frame: {\n      x: tx - padding.left - borderWidth,\n      y: ty - padding.top - borderWidth,\n      w: tw + padding.width + borderWidth * 2,\n      h: th + padding.height + borderWidth * 2\n    },\n    text: {\n      x: tx,\n      y: ty,\n      w: tw,\n      h: th\n    }\n  };\n}\n\nfunction getScaleOrigin(el, context) {\n  var scale = context.chart.getDatasetMeta(context.datasetIndex).vScale;\n\n  if (!scale) {\n    return null;\n  }\n\n  if (scale.xCenter !== undefined && scale.yCenter !== undefined) {\n    return {x: scale.xCenter, y: scale.yCenter};\n  }\n\n  var pixel = scale.getBasePixel();\n  return el.horizontal ?\n    {x: pixel, y: null} :\n    {x: null, y: pixel};\n}\n\nfunction getPositioner(el) {\n  if (el instanceof ArcElement) {\n    return positioners.arc;\n  }\n  if (el instanceof PointElement) {\n    return positioners.point;\n  }\n  if (el instanceof BarElement) {\n    return positioners.bar;\n  }\n  return positioners.fallback;\n}\n\nfunction drawRoundedRect(ctx, x, y, w, h, radius) {\n  var HALF_PI = Math.PI / 2;\n\n  if (radius) {\n    var r = Math.min(radius, h / 2, w / 2);\n    var left = x + r;\n    var top = y + r;\n    var right = x + w - r;\n    var bottom = y + h - r;\n\n    ctx.moveTo(x, top);\n    if (left < right && top < bottom) {\n      ctx.arc(left, top, r, -Math.PI, -HALF_PI);\n      ctx.arc(right, top, r, -HALF_PI, 0);\n      ctx.arc(right, bottom, r, 0, HALF_PI);\n      ctx.arc(left, bottom, r, HALF_PI, Math.PI);\n    } else if (left < right) {\n      ctx.moveTo(left, y);\n      ctx.arc(right, top, r, -HALF_PI, HALF_PI);\n      ctx.arc(left, top, r, HALF_PI, Math.PI + HALF_PI);\n    } else if (top < bottom) {\n      ctx.arc(left, top, r, -Math.PI, 0);\n      ctx.arc(left, bottom, r, 0, Math.PI);\n    } else {\n      ctx.arc(left, top, r, -Math.PI, Math.PI);\n    }\n    ctx.closePath();\n    ctx.moveTo(x, y);\n  } else {\n    ctx.rect(x, y, w, h);\n  }\n}\n\nfunction drawFrame(ctx, rect, model) {\n  var bgColor = model.backgroundColor;\n  var borderColor = model.borderColor;\n  var borderWidth = model.borderWidth;\n\n  if (!bgColor && (!borderColor || !borderWidth)) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  drawRoundedRect(\n    ctx,\n    rasterize(rect.x) + borderWidth / 2,\n    rasterize(rect.y) + borderWidth / 2,\n    rasterize(rect.w) - borderWidth,\n    rasterize(rect.h) - borderWidth,\n    model.borderRadius);\n\n  ctx.closePath();\n\n  if (bgColor) {\n    ctx.fillStyle = bgColor;\n    ctx.fill();\n  }\n\n  if (borderColor && borderWidth) {\n    ctx.strokeStyle = borderColor;\n    ctx.lineWidth = borderWidth;\n    ctx.lineJoin = 'miter';\n    ctx.stroke();\n  }\n}\n\nfunction textGeometry(rect, align, font) {\n  var h = font.lineHeight;\n  var w = rect.w;\n  var x = rect.x;\n  var y = rect.y + h / 2;\n\n  if (align === 'center') {\n    x += w / 2;\n  } else if (align === 'end' || align === 'right') {\n    x += w;\n  }\n\n  return {\n    h: h,\n    w: w,\n    x: x,\n    y: y\n  };\n}\n\nfunction drawTextLine(ctx, text, cfg) {\n  var shadow = ctx.shadowBlur;\n  var stroked = cfg.stroked;\n  var x = rasterize(cfg.x);\n  var y = rasterize(cfg.y);\n  var w = rasterize(cfg.w);\n\n  if (stroked) {\n    ctx.strokeText(text, x, y, w);\n  }\n\n  if (cfg.filled) {\n    if (shadow && stroked) {\n      // Prevent drawing shadow on both the text stroke and fill, so\n      // if the text is stroked, remove the shadow for the text fill.\n      ctx.shadowBlur = 0;\n    }\n\n    ctx.fillText(text, x, y, w);\n\n    if (shadow && stroked) {\n      ctx.shadowBlur = shadow;\n    }\n  }\n}\n\nfunction drawText(ctx, lines, rect, model) {\n  var align = model.textAlign;\n  var color = model.color;\n  var filled = !!color;\n  var font = model.font;\n  var ilen = lines.length;\n  var strokeColor = model.textStrokeColor;\n  var strokeWidth = model.textStrokeWidth;\n  var stroked = strokeColor && strokeWidth;\n  var i;\n\n  if (!ilen || (!filled && !stroked)) {\n    return;\n  }\n\n  // Adjust coordinates based on text alignment and line height\n  rect = textGeometry(rect, align, font);\n\n  ctx.font = font.string;\n  ctx.textAlign = align;\n  ctx.textBaseline = 'middle';\n  ctx.shadowBlur = model.textShadowBlur;\n  ctx.shadowColor = model.textShadowColor;\n\n  if (filled) {\n    ctx.fillStyle = color;\n  }\n  if (stroked) {\n    ctx.lineJoin = 'round';\n    ctx.lineWidth = strokeWidth;\n    ctx.strokeStyle = strokeColor;\n  }\n\n  for (i = 0, ilen = lines.length; i < ilen; ++i) {\n    drawTextLine(ctx, lines[i], {\n      stroked: stroked,\n      filled: filled,\n      w: rect.w,\n      x: rect.x,\n      y: rect.y + rect.h * i\n    });\n  }\n}\n\nvar Label = function(config, ctx, el, index) {\n  var me = this;\n\n  me._config = config;\n  me._index = index;\n  me._model = null;\n  me._rects = null;\n  me._ctx = ctx;\n  me._el = el;\n};\n\nmerge(Label.prototype, {\n  /**\n   * @private\n   */\n  _modelize: function(display, lines, config, context) {\n    var me = this;\n    var index = me._index;\n    var font = toFont(resolve([config.font, {}], context, index));\n    var color = resolve([config.color, defaults$1.color], context, index);\n\n    return {\n      align: resolve([config.align, 'center'], context, index),\n      anchor: resolve([config.anchor, 'center'], context, index),\n      area: context.chart.chartArea,\n      backgroundColor: resolve([config.backgroundColor, null], context, index),\n      borderColor: resolve([config.borderColor, null], context, index),\n      borderRadius: resolve([config.borderRadius, 0], context, index),\n      borderWidth: resolve([config.borderWidth, 0], context, index),\n      clamp: resolve([config.clamp, false], context, index),\n      clip: resolve([config.clip, false], context, index),\n      color: color,\n      display: display,\n      font: font,\n      lines: lines,\n      offset: resolve([config.offset, 4], context, index),\n      opacity: resolve([config.opacity, 1], context, index),\n      origin: getScaleOrigin(me._el, context),\n      padding: toPadding(resolve([config.padding, 4], context, index)),\n      positioner: getPositioner(me._el),\n      rotation: resolve([config.rotation, 0], context, index) * (Math.PI / 180),\n      size: utils.textSize(me._ctx, lines, font),\n      textAlign: resolve([config.textAlign, 'start'], context, index),\n      textShadowBlur: resolve([config.textShadowBlur, 0], context, index),\n      textShadowColor: resolve([config.textShadowColor, color], context, index),\n      textStrokeColor: resolve([config.textStrokeColor, color], context, index),\n      textStrokeWidth: resolve([config.textStrokeWidth, 0], context, index)\n    };\n  },\n\n  update: function(context) {\n    var me = this;\n    var model = null;\n    var rects = null;\n    var index = me._index;\n    var config = me._config;\n    var value, label, lines;\n\n    // We first resolve the display option (separately) to avoid computing\n    // other options in case the label is hidden (i.e. display: false).\n    var display = resolve([config.display, true], context, index);\n\n    if (display) {\n      value = context.dataset.data[index];\n      label = valueOrDefault(callback(config.formatter, [value, context]), value);\n      lines = isNullOrUndef(label) ? [] : utils.toTextLines(label);\n\n      if (lines.length) {\n        model = me._modelize(display, lines, config, context);\n        rects = boundingRects(model);\n      }\n    }\n\n    me._model = model;\n    me._rects = rects;\n  },\n\n  geometry: function() {\n    return this._rects ? this._rects.frame : {};\n  },\n\n  rotation: function() {\n    return this._model ? this._model.rotation : 0;\n  },\n\n  visible: function() {\n    return this._model && this._model.opacity;\n  },\n\n  model: function() {\n    return this._model;\n  },\n\n  draw: function(chart, center) {\n    var me = this;\n    var ctx = chart.ctx;\n    var model = me._model;\n    var rects = me._rects;\n    var area;\n\n    if (!this.visible()) {\n      return;\n    }\n\n    ctx.save();\n\n    if (model.clip) {\n      area = model.area;\n      ctx.beginPath();\n      ctx.rect(\n        area.left,\n        area.top,\n        area.right - area.left,\n        area.bottom - area.top);\n      ctx.clip();\n    }\n\n    ctx.globalAlpha = utils.bound(0, model.opacity, 1);\n    ctx.translate(rasterize(center.x), rasterize(center.y));\n    ctx.rotate(model.rotation);\n\n    drawFrame(ctx, rects.frame, model);\n    drawText(ctx, model.lines, rects.text, model);\n\n    ctx.restore();\n  }\n});\n\nvar MIN_INTEGER = Number.MIN_SAFE_INTEGER || -9007199254740991; // eslint-disable-line es/no-number-minsafeinteger\nvar MAX_INTEGER = Number.MAX_SAFE_INTEGER || 9007199254740991;  // eslint-disable-line es/no-number-maxsafeinteger\n\nfunction rotated(point, center, angle) {\n  var cos = Math.cos(angle);\n  var sin = Math.sin(angle);\n  var cx = center.x;\n  var cy = center.y;\n\n  return {\n    x: cx + cos * (point.x - cx) - sin * (point.y - cy),\n    y: cy + sin * (point.x - cx) + cos * (point.y - cy)\n  };\n}\n\nfunction projected(points, axis) {\n  var min = MAX_INTEGER;\n  var max = MIN_INTEGER;\n  var origin = axis.origin;\n  var i, pt, vx, vy, dp;\n\n  for (i = 0; i < points.length; ++i) {\n    pt = points[i];\n    vx = pt.x - origin.x;\n    vy = pt.y - origin.y;\n    dp = axis.vx * vx + axis.vy * vy;\n    min = Math.min(min, dp);\n    max = Math.max(max, dp);\n  }\n\n  return {\n    min: min,\n    max: max\n  };\n}\n\nfunction toAxis(p0, p1) {\n  var vx = p1.x - p0.x;\n  var vy = p1.y - p0.y;\n  var ln = Math.sqrt(vx * vx + vy * vy);\n\n  return {\n    vx: (p1.x - p0.x) / ln,\n    vy: (p1.y - p0.y) / ln,\n    origin: p0,\n    ln: ln\n  };\n}\n\nvar HitBox = function() {\n  this._rotation = 0;\n  this._rect = {\n    x: 0,\n    y: 0,\n    w: 0,\n    h: 0\n  };\n};\n\nmerge(HitBox.prototype, {\n  center: function() {\n    var r = this._rect;\n    return {\n      x: r.x + r.w / 2,\n      y: r.y + r.h / 2\n    };\n  },\n\n  update: function(center, rect, rotation) {\n    this._rotation = rotation;\n    this._rect = {\n      x: rect.x + center.x,\n      y: rect.y + center.y,\n      w: rect.w,\n      h: rect.h\n    };\n  },\n\n  contains: function(point) {\n    var me = this;\n    var margin = 1;\n    var rect = me._rect;\n\n    point = rotated(point, me.center(), -me._rotation);\n\n    return !(point.x < rect.x - margin\n      || point.y < rect.y - margin\n      || point.x > rect.x + rect.w + margin * 2\n      || point.y > rect.y + rect.h + margin * 2);\n  },\n\n  // Separating Axis Theorem\n  // https://gamedevelopment.tutsplus.com/tutorials/collision-detection-using-the-separating-axis-theorem--gamedev-169\n  intersects: function(other) {\n    var r0 = this._points();\n    var r1 = other._points();\n    var axes = [\n      toAxis(r0[0], r0[1]),\n      toAxis(r0[0], r0[3])\n    ];\n    var i, pr0, pr1;\n\n    if (this._rotation !== other._rotation) {\n      // Only separate with r1 axis if the rotation is different,\n      // else it's enough to separate r0 and r1 with r0 axis only!\n      axes.push(\n        toAxis(r1[0], r1[1]),\n        toAxis(r1[0], r1[3])\n      );\n    }\n\n    for (i = 0; i < axes.length; ++i) {\n      pr0 = projected(r0, axes[i]);\n      pr1 = projected(r1, axes[i]);\n\n      if (pr0.max < pr1.min || pr1.max < pr0.min) {\n        return false;\n      }\n    }\n\n    return true;\n  },\n\n  /**\n   * @private\n   */\n  _points: function() {\n    var me = this;\n    var rect = me._rect;\n    var angle = me._rotation;\n    var center = me.center();\n\n    return [\n      rotated({x: rect.x, y: rect.y}, center, angle),\n      rotated({x: rect.x + rect.w, y: rect.y}, center, angle),\n      rotated({x: rect.x + rect.w, y: rect.y + rect.h}, center, angle),\n      rotated({x: rect.x, y: rect.y + rect.h}, center, angle)\n    ];\n  }\n});\n\nfunction coordinates(el, model, geometry) {\n  var point = model.positioner(el, model);\n  var vx = point.vx;\n  var vy = point.vy;\n\n  if (!vx && !vy) {\n    // if aligned center, we don't want to offset the center point\n    return {x: point.x, y: point.y};\n  }\n\n  var w = geometry.w;\n  var h = geometry.h;\n\n  // take in account the label rotation\n  var rotation = model.rotation;\n  var dx = Math.abs(w / 2 * Math.cos(rotation)) + Math.abs(h / 2 * Math.sin(rotation));\n  var dy = Math.abs(w / 2 * Math.sin(rotation)) + Math.abs(h / 2 * Math.cos(rotation));\n\n  // scale the unit vector (vx, vy) to get at least dx or dy equal to\n  // w or h respectively (else we would calculate the distance to the\n  // ellipse inscribed in the bounding rect)\n  var vs = 1 / Math.max(Math.abs(vx), Math.abs(vy));\n  dx *= vx * vs;\n  dy *= vy * vs;\n\n  // finally, include the explicit offset\n  dx += model.offset * vx;\n  dy += model.offset * vy;\n\n  return {\n    x: point.x + dx,\n    y: point.y + dy\n  };\n}\n\nfunction collide(labels, collider) {\n  var i, j, s0, s1;\n\n  // IMPORTANT Iterate in the reverse order since items at the end of the\n  // list have an higher weight/priority and thus should be less impacted\n  // by the overlapping strategy.\n\n  for (i = labels.length - 1; i >= 0; --i) {\n    s0 = labels[i].$layout;\n\n    for (j = i - 1; j >= 0 && s0._visible; --j) {\n      s1 = labels[j].$layout;\n\n      if (s1._visible && s0._box.intersects(s1._box)) {\n        collider(s0, s1);\n      }\n    }\n  }\n\n  return labels;\n}\n\nfunction compute(labels) {\n  var i, ilen, label, state, geometry, center, proxy;\n\n  // Initialize labels for overlap detection\n  for (i = 0, ilen = labels.length; i < ilen; ++i) {\n    label = labels[i];\n    state = label.$layout;\n\n    if (state._visible) {\n      // Chart.js 3 removed el._model in favor of getProps(), making harder to\n      // abstract reading values in positioners. Also, using string arrays to\n      // read values (i.e. var {a,b,c} = el.getProps([\"a\",\"b\",\"c\"])) would make\n      // positioners inefficient in the normal case (i.e. not the final values)\n      // and the code a bit ugly, so let's use a Proxy instead.\n      proxy = new Proxy(label._el, {get: (el, p) => el.getProps([p], true)[p]});\n\n      geometry = label.geometry();\n      center = coordinates(proxy, label.model(), geometry);\n      state._box.update(center, geometry, label.rotation());\n    }\n  }\n\n  // Auto hide overlapping labels\n  return collide(labels, function(s0, s1) {\n    var h0 = s0._hidable;\n    var h1 = s1._hidable;\n\n    if ((h0 && h1) || h1) {\n      s1._visible = false;\n    } else if (h0) {\n      s0._visible = false;\n    }\n  });\n}\n\nvar layout = {\n  prepare: function(datasets) {\n    var labels = [];\n    var i, j, ilen, jlen, label;\n\n    for (i = 0, ilen = datasets.length; i < ilen; ++i) {\n      for (j = 0, jlen = datasets[i].length; j < jlen; ++j) {\n        label = datasets[i][j];\n        labels.push(label);\n        label.$layout = {\n          _box: new HitBox(),\n          _hidable: false,\n          _visible: true,\n          _set: i,\n          _idx: label._index\n        };\n      }\n    }\n\n    // TODO New `z` option: labels with a higher z-index are drawn\n    // of top of the ones with a lower index. Lowest z-index labels\n    // are also discarded first when hiding overlapping labels.\n    labels.sort(function(a, b) {\n      var sa = a.$layout;\n      var sb = b.$layout;\n\n      return sa._idx === sb._idx\n        ? sb._set - sa._set\n        : sb._idx - sa._idx;\n    });\n\n    this.update(labels);\n\n    return labels;\n  },\n\n  update: function(labels) {\n    var dirty = false;\n    var i, ilen, label, model, state;\n\n    for (i = 0, ilen = labels.length; i < ilen; ++i) {\n      label = labels[i];\n      model = label.model();\n      state = label.$layout;\n      state._hidable = model && model.display === 'auto';\n      state._visible = label.visible();\n      dirty |= state._hidable;\n    }\n\n    if (dirty) {\n      compute(labels);\n    }\n  },\n\n  lookup: function(labels, point) {\n    var i, state;\n\n    // IMPORTANT Iterate in the reverse order since items at the end of\n    // the list have an higher z-index, thus should be picked first.\n\n    for (i = labels.length - 1; i >= 0; --i) {\n      state = labels[i].$layout;\n\n      if (state && state._visible && state._box.contains(point)) {\n        return labels[i];\n      }\n    }\n\n    return null;\n  },\n\n  draw: function(chart, labels) {\n    var i, ilen, label, state, geometry, center;\n\n    for (i = 0, ilen = labels.length; i < ilen; ++i) {\n      label = labels[i];\n      state = label.$layout;\n\n      if (state._visible) {\n        geometry = label.geometry();\n        center = coordinates(label._el, label.model(), geometry);\n        state._box.update(center, geometry, label.rotation());\n        label.draw(chart, center);\n      }\n    }\n  }\n};\n\nvar formatter = function(value) {\n  if (isNullOrUndef(value)) {\n    return null;\n  }\n\n  var label = value;\n  var keys, klen, k;\n  if (isObject(value)) {\n    if (!isNullOrUndef(value.label)) {\n      label = value.label;\n    } else if (!isNullOrUndef(value.r)) {\n      label = value.r;\n    } else {\n      label = '';\n      keys = Object.keys(value);\n      for (k = 0, klen = keys.length; k < klen; ++k) {\n        label += (k !== 0 ? ', ' : '') + keys[k] + ': ' + value[keys[k]];\n      }\n    }\n  }\n\n  return '' + label;\n};\n\n/**\n * IMPORTANT: make sure to also update tests and TypeScript definition\n * files (`/test/specs/defaults.spec.js` and `/types/options.d.ts`)\n */\n\nvar defaults = {\n  align: 'center',\n  anchor: 'center',\n  backgroundColor: null,\n  borderColor: null,\n  borderRadius: 0,\n  borderWidth: 0,\n  clamp: false,\n  clip: false,\n  color: undefined,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: 1.2,\n    size: undefined,\n    style: undefined,\n    weight: null\n  },\n  formatter: formatter,\n  labels: undefined,\n  listeners: {},\n  offset: 4,\n  opacity: 1,\n  padding: {\n    top: 4,\n    right: 4,\n    bottom: 4,\n    left: 4\n  },\n  rotation: 0,\n  textAlign: 'start',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  textShadowBlur: 0,\n  textShadowColor: undefined\n};\n\n/**\n * @see https://github.com/chartjs/Chart.js/issues/4176\n */\n\nvar EXPANDO_KEY = '$datalabels';\nvar DEFAULT_KEY = '$default';\n\nfunction configure(dataset, options) {\n  var override = dataset.datalabels;\n  var listeners = {};\n  var configs = [];\n  var labels, keys;\n\n  if (override === false) {\n    return null;\n  }\n  if (override === true) {\n    override = {};\n  }\n\n  options = merge({}, [options, override]);\n  labels = options.labels || {};\n  keys = Object.keys(labels);\n  delete options.labels;\n\n  if (keys.length) {\n    keys.forEach(function(key) {\n      if (labels[key]) {\n        configs.push(merge({}, [\n          options,\n          labels[key],\n          {_key: key}\n        ]));\n      }\n    });\n  } else {\n    // Default label if no \"named\" label defined.\n    configs.push(options);\n  }\n\n  // listeners: {<event-type>: {<label-key>: <fn>}}\n  listeners = configs.reduce(function(target, config) {\n    each(config.listeners || {}, function(fn, event) {\n      target[event] = target[event] || {};\n      target[event][config._key || DEFAULT_KEY] = fn;\n    });\n\n    delete config.listeners;\n    return target;\n  }, {});\n\n  return {\n    labels: configs,\n    listeners: listeners\n  };\n}\n\nfunction dispatchEvent(chart, listeners, label, event) {\n  if (!listeners) {\n    return;\n  }\n\n  var context = label.$context;\n  var groups = label.$groups;\n  var callback$1;\n\n  if (!listeners[groups._set]) {\n    return;\n  }\n\n  callback$1 = listeners[groups._set][groups._key];\n  if (!callback$1) {\n    return;\n  }\n\n  if (callback(callback$1, [context, event]) === true) {\n    // Users are allowed to tweak the given context by injecting values that can be\n    // used in scriptable options to display labels differently based on the current\n    // event (e.g. highlight an hovered label). That's why we update the label with\n    // the output context and schedule a new chart render by setting it dirty.\n    chart[EXPANDO_KEY]._dirty = true;\n    label.update(context);\n  }\n}\n\nfunction dispatchMoveEvents(chart, listeners, previous, label, event) {\n  var enter, leave;\n\n  if (!previous && !label) {\n    return;\n  }\n\n  if (!previous) {\n    enter = true;\n  } else if (!label) {\n    leave = true;\n  } else if (previous !== label) {\n    leave = enter = true;\n  }\n\n  if (leave) {\n    dispatchEvent(chart, listeners.leave, previous, event);\n  }\n  if (enter) {\n    dispatchEvent(chart, listeners.enter, label, event);\n  }\n}\n\nfunction handleMoveEvents(chart, event) {\n  var expando = chart[EXPANDO_KEY];\n  var listeners = expando._listeners;\n  var previous, label;\n\n  if (!listeners.enter && !listeners.leave) {\n    return;\n  }\n\n  if (event.type === 'mousemove') {\n    label = layout.lookup(expando._labels, event);\n  } else if (event.type !== 'mouseout') {\n    return;\n  }\n\n  previous = expando._hovered;\n  expando._hovered = label;\n  dispatchMoveEvents(chart, listeners, previous, label, event);\n}\n\nfunction handleClickEvents(chart, event) {\n  var expando = chart[EXPANDO_KEY];\n  var handlers = expando._listeners.click;\n  var label = handlers && layout.lookup(expando._labels, event);\n  if (label) {\n    dispatchEvent(chart, handlers, label, event);\n  }\n}\n\nvar plugin = {\n  id: 'datalabels',\n\n  defaults: defaults,\n\n  beforeInit: function(chart) {\n    chart[EXPANDO_KEY] = {\n      _actives: []\n    };\n  },\n\n  beforeUpdate: function(chart) {\n    var expando = chart[EXPANDO_KEY];\n    expando._listened = false;\n    expando._listeners = {};     // {<event-type>: {<dataset-index>: {<label-key>: <fn>}}}\n    expando._datasets = [];      // per dataset labels: [Label[]]\n    expando._labels = [];        // layouted labels: Label[]\n  },\n\n  afterDatasetUpdate: function(chart, args, options) {\n    var datasetIndex = args.index;\n    var expando = chart[EXPANDO_KEY];\n    var labels = expando._datasets[datasetIndex] = [];\n    var visible = chart.isDatasetVisible(datasetIndex);\n    var dataset = chart.data.datasets[datasetIndex];\n    var config = configure(dataset, options);\n    var elements = args.meta.data || [];\n    var ctx = chart.ctx;\n    var i, j, ilen, jlen, cfg, key, el, label;\n\n    ctx.save();\n\n    for (i = 0, ilen = elements.length; i < ilen; ++i) {\n      el = elements[i];\n      el[EXPANDO_KEY] = [];\n\n      if (visible && el && chart.getDataVisibility(i) && !el.skip) {\n        for (j = 0, jlen = config.labels.length; j < jlen; ++j) {\n          cfg = config.labels[j];\n          key = cfg._key;\n\n          label = new Label(cfg, ctx, el, i);\n          label.$groups = {\n            _set: datasetIndex,\n            _key: key || DEFAULT_KEY\n          };\n          label.$context = {\n            active: false,\n            chart: chart,\n            dataIndex: i,\n            dataset: dataset,\n            datasetIndex: datasetIndex\n          };\n\n          label.update(label.$context);\n          el[EXPANDO_KEY].push(label);\n          labels.push(label);\n        }\n      }\n    }\n\n    ctx.restore();\n\n    // Store listeners at the chart level and per event type to optimize\n    // cases where no listeners are registered for a specific event.\n    merge(expando._listeners, config.listeners, {\n      merger: function(event, target, source) {\n        target[event] = target[event] || {};\n        target[event][args.index] = source[event];\n        expando._listened = true;\n      }\n    });\n  },\n\n  afterUpdate: function(chart) {\n    chart[EXPANDO_KEY]._labels = layout.prepare(chart[EXPANDO_KEY]._datasets);\n  },\n\n  // Draw labels on top of all dataset elements\n  // https://github.com/chartjs/chartjs-plugin-datalabels/issues/29\n  // https://github.com/chartjs/chartjs-plugin-datalabels/issues/32\n  afterDatasetsDraw: function(chart) {\n    layout.draw(chart, chart[EXPANDO_KEY]._labels);\n  },\n\n  beforeEvent: function(chart, args) {\n    // If there is no listener registered for this chart, `listened` will be false,\n    // meaning we can immediately ignore the incoming event and avoid useless extra\n    // computation for users who don't implement label interactions.\n    if (chart[EXPANDO_KEY]._listened) {\n      var event = args.event;\n      switch (event.type) {\n      case 'mousemove':\n      case 'mouseout':\n        handleMoveEvents(chart, event);\n        break;\n      case 'click':\n        handleClickEvents(chart, event);\n        break;\n      }\n    }\n  },\n\n  afterEvent: function(chart) {\n    var expando = chart[EXPANDO_KEY];\n    var previous = expando._actives;\n    var actives = expando._actives = chart.getActiveElements();\n    var updates = utils.arrayDiff(previous, actives);\n    var i, ilen, j, jlen, update, label, labels;\n\n    for (i = 0, ilen = updates.length; i < ilen; ++i) {\n      update = updates[i];\n      if (update[1]) {\n        labels = update[0].element[EXPANDO_KEY] || [];\n        for (j = 0, jlen = labels.length; j < jlen; ++j) {\n          label = labels[j];\n          label.$context.active = (update[1] === 1);\n          label.update(label.$context);\n        }\n      }\n    }\n\n    if (expando._dirty || updates.length) {\n      layout.update(expando._labels);\n      chart.render();\n    }\n\n    delete expando._dirty;\n  }\n};\n\nexport { plugin as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AASA,IAAI,mBAAoB,WAAW;AACjC,MAAI,OAAO,WAAW,aAAa;AACjC,QAAI,OAAO,kBAAkB;AAC3B,aAAO,OAAO;AAAA,IAChB;AAKA,QAAI,SAAS,OAAO;AACpB,QAAI,QAAQ;AACV,cAAQ,OAAO,cAAc,MAAM,OAAO,eAAe;AAAA,IAC3D;AAAA,EACF;AAEA,SAAO;AACT,EAAE;AAEF,IAAI,QAAQ;AAAA;AAAA,EAEV,aAAa,SAAS,QAAQ;AAC5B,QAAI,QAAQ,CAAC;AACb,QAAI;AAEJ,aAAS,CAAC,EAAE,OAAO,MAAM;AACzB,WAAO,OAAO,QAAQ;AACpB,cAAQ,OAAO,IAAI;AACnB,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,QAAQ,MAAM,OAAO,MAAM,MAAM,IAAI,CAAC;AAAA,MAC9C,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,eAAO,KAAK,MAAM,QAAQ,KAAK;AAAA,MACjC,WAAW,CAAC,cAAc,MAAM,GAAG;AACjC,cAAM,QAAQ,KAAK,KAAK;AAAA,MAC1B;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAIA,UAAU,SAAS,KAAK,OAAO,MAAM;AACnC,QAAI,QAAQ,CAAC,EAAE,OAAO,KAAK;AAC3B,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,IAAI;AACf,QAAI,QAAQ;AACZ,QAAI;AAEJ,QAAI,OAAO,KAAK;AAEhB,SAAK,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AACzB,cAAQ,KAAK,IAAI,IAAI,YAAY,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK;AAAA,IACzD;AAEA,QAAI,OAAO;AAEX,WAAO;AAAA,MACL,QAAQ,OAAO,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAAS,KAAK,OAAO,KAAK;AAC/B,WAAO,KAAK,IAAI,KAAK,KAAK,IAAI,OAAO,GAAG,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,SAAS,IAAI,IAAI;AAC1B,QAAI,OAAO,GAAG,MAAM;AACpB,QAAI,UAAU,CAAC;AACf,QAAI,GAAG,GAAG,MAAM;AAEhB,SAAK,IAAI,GAAG,OAAO,GAAG,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC3C,UAAI,GAAG,CAAC;AACR,UAAI,KAAK,QAAQ,CAAC;AAElB,UAAI,MAAM,IAAI;AACZ,gBAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,MACrB,OAAO;AACL,aAAK,OAAO,GAAG,CAAC;AAAA,MAClB;AAAA,IACF;AAEA,SAAK,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC7C,cAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAAA,IAC5B;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,SAAS,GAAG;AACrB,WAAO,KAAK,MAAM,IAAI,gBAAgB,IAAI;AAAA,EAC5C;AACF;AAEA,SAAS,OAAO,OAAO,QAAQ;AAC7B,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,OAAO;AAEhB,MAAI,OAAO,MAAM;AACf,WAAO,EAAC,GAAG,GAAG,GAAG,GAAE;AAAA,EACrB;AACA,MAAI,OAAO,MAAM;AACf,WAAO,EAAC,GAAG,GAAG,GAAG,EAAC;AAAA,EACpB;AAEA,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEpC,SAAO;AAAA,IACL,GAAG,KAAK,KAAK,KAAK;AAAA,IAClB,GAAG,KAAK,KAAK,KAAK;AAAA,EACpB;AACF;AAEA,SAAS,QAAQ,GAAG,GAAG,IAAI,IAAI,OAAO;AACpC,UAAQ,OAAO;AAAA,IACf,KAAK;AACH,WAAK,KAAK;AACV;AAAA,IACF,KAAK;AACH,WAAK;AACL,WAAK;AACL;AAAA,IACF,KAAK;AACH,WAAK;AACL,WAAK;AACL;AAAA,IACF,KAAK;AACH,WAAK;AACL,WAAK;AACL;AAAA,IACF,KAAK;AACH,WAAK;AACL,WAAK;AACL;AAAA,IACF,KAAK;AACH,WAAK,CAAC;AACN,WAAK,CAAC;AACN;AAAA,IACF,KAAK;AAEH;AAAA,IACF;AAEE,eAAU,KAAK,KAAK;AACpB,WAAK,KAAK,IAAI,KAAK;AACnB,WAAK,KAAK,IAAI,KAAK;AACnB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,QAAQ;AAEZ,SAAS,OAAO,GAAG,GAAG,MAAM;AAC1B,MAAI,MAAM;AAEV,MAAI,IAAI,KAAK,MAAM;AACjB,WAAO;AAAA,EACT,WAAW,IAAI,KAAK,OAAO;AACzB,WAAO;AAAA,EACT;AACA,MAAI,IAAI,KAAK,KAAK;AAChB,WAAO;AAAA,EACT,WAAW,IAAI,KAAK,QAAQ;AAC1B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,SAAS,MAAM;AAC9B,MAAI,KAAK,QAAQ;AACjB,MAAI,KAAK,QAAQ;AACjB,MAAI,KAAK,QAAQ;AACjB,MAAI,KAAK,QAAQ;AACjB,MAAI,KAAK,OAAO,IAAI,IAAI,IAAI;AAC5B,MAAI,KAAK,OAAO,IAAI,IAAI,IAAI;AAC5B,MAAI,GAAG,GAAG;AAGV,SAAO,MAAM;AACX,QAAI,EAAE,KAAK,OAAQ,KAAK,IAAK;AAE3B;AAAA,IACF;AAGA,QAAI,MAAM;AAEV,QAAI,IAAI,OAAO;AACb,UAAI,MAAM,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK;AAC7C,UAAI,KAAK;AAAA,IACX,WAAW,IAAI,UAAU;AACvB,UAAI,MAAM,KAAK,OAAO,KAAK,SAAS,OAAO,KAAK;AAChD,UAAI,KAAK;AAAA,IACX,WAAW,IAAI,SAAS;AACtB,UAAI,MAAM,KAAK,OAAO,KAAK,QAAQ,OAAO,KAAK;AAC/C,UAAI,KAAK;AAAA,IACX,WAAW,IAAI,QAAQ;AACrB,UAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK;AAC9C,UAAI,KAAK;AAAA,IACX;AAEA,QAAI,MAAM,IAAI;AACZ,WAAK;AACL,WAAK;AACL,WAAK,OAAO,IAAI,IAAI,IAAI;AAAA,IAC1B,OAAO;AACL,WAAK;AACL,WAAK;AACL,WAAK,OAAO,IAAI,IAAI,IAAI;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,UAAU,OAAO,QAAQ;AAChC,MAAI,SAAS,OAAO;AACpB,MAAI,UAAU;AACd,MAAI,GAAG;AAEP,MAAI,OAAO,OAAO;AAChB,cAAU,QAAQ,SAAS,OAAO,IAAI;AAAA,EACxC;AAEA,MAAI,WAAW,SAAS;AACtB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAAA,EACd,WAAW,WAAW,OAAO;AAC3B,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAAA,EACd,OAAO;AACL,SAAK,QAAQ,KAAK,QAAQ,MAAM;AAChC,SAAK,QAAQ,KAAK,QAAQ,MAAM;AAAA,EAClC;AAEA,SAAO,QAAQ,GAAG,GAAG,MAAM,IAAI,MAAM,IAAI,OAAO,KAAK;AACvD;AAEA,IAAI,cAAc;AAAA,EAChB,KAAK,SAAS,IAAI,QAAQ;AACxB,QAAI,SAAS,GAAG,aAAa,GAAG,YAAY;AAC5C,QAAI,KAAK,KAAK,IAAI,KAAK;AACvB,QAAI,KAAK,KAAK,IAAI,KAAK;AACvB,QAAI,KAAK,GAAG;AACZ,QAAI,KAAK,GAAG;AAEZ,WAAO,UAAU;AAAA,MACf,IAAI,GAAG,IAAI,KAAK;AAAA,MAChB,IAAI,GAAG,IAAI,KAAK;AAAA,MAChB,IAAI,GAAG,IAAI,KAAK;AAAA,MAChB,IAAI,GAAG,IAAI,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,IACF,GAAG,MAAM;AAAA,EACX;AAAA,EAEA,OAAO,SAAS,IAAI,QAAQ;AAC1B,QAAI,IAAI,OAAO,IAAI,OAAO,MAAM;AAChC,QAAI,KAAK,EAAE,IAAI,GAAG,QAAQ;AAC1B,QAAI,KAAK,EAAE,IAAI,GAAG,QAAQ;AAE1B,WAAO,UAAU;AAAA,MACf,IAAI,GAAG,IAAI;AAAA,MACX,IAAI,GAAG,IAAI;AAAA,MACX,IAAI,GAAG,IAAI;AAAA,MACX,IAAI,GAAG,IAAI;AAAA,MACX,IAAI,EAAE;AAAA,MACN,IAAI,EAAE;AAAA,IACR,GAAG,MAAM;AAAA,EACX;AAAA,EAEA,KAAK,SAAS,IAAI,QAAQ;AACxB,QAAI,IAAI,OAAO,IAAI,OAAO,MAAM;AAChC,QAAI,IAAI,GAAG;AACX,QAAI,IAAI,GAAG;AACX,QAAI,KAAK;AACT,QAAI,KAAK;AAET,QAAI,GAAG,YAAY;AACjB,UAAI,KAAK,IAAI,GAAG,GAAG,GAAG,IAAI;AAC1B,WAAK,KAAK,IAAI,GAAG,OAAO,GAAG,CAAC;AAAA,IAC9B,OAAO;AACL,UAAI,KAAK,IAAI,GAAG,GAAG,GAAG,IAAI;AAC1B,WAAK,KAAK,IAAI,GAAG,OAAO,GAAG,CAAC;AAAA,IAC9B;AAEA,WAAO,UAAU;AAAA,MACf,IAAI;AAAA,MACJ,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,IAAI;AAAA,MACJ,IAAI,EAAE;AAAA,MACN,IAAI,EAAE;AAAA,IACR,GAAG,MAAM;AAAA,EACX;AAAA,EAEA,UAAU,SAAS,IAAI,QAAQ;AAC7B,QAAI,IAAI,OAAO,IAAI,OAAO,MAAM;AAEhC,WAAO,UAAU;AAAA,MACf,IAAI,GAAG;AAAA,MACP,IAAI,GAAG;AAAA,MACP,IAAI,GAAG,KAAK,GAAG,SAAS;AAAA,MACxB,IAAI,GAAG,KAAK,GAAG,UAAU;AAAA,MACzB,IAAI,EAAE;AAAA,MACN,IAAI,EAAE;AAAA,IACR,GAAG,MAAM;AAAA,EACX;AACF;AAEA,IAAI,YAAY,MAAM;AAEtB,SAAS,cAAc,OAAO;AAC5B,MAAI,cAAc,MAAM,eAAe;AACvC,MAAI,UAAU,MAAM;AACpB,MAAI,KAAK,MAAM,KAAK;AACpB,MAAI,KAAK,MAAM,KAAK;AACpB,MAAI,KAAK,CAAC,KAAK;AACf,MAAI,KAAK,CAAC,KAAK;AAEf,SAAO;AAAA,IACL,OAAO;AAAA,MACL,GAAG,KAAK,QAAQ,OAAO;AAAA,MACvB,GAAG,KAAK,QAAQ,MAAM;AAAA,MACtB,GAAG,KAAK,QAAQ,QAAQ,cAAc;AAAA,MACtC,GAAG,KAAK,QAAQ,SAAS,cAAc;AAAA,IACzC;AAAA,IACA,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACF;AAEA,SAAS,eAAe,IAAI,SAAS;AACnC,MAAI,QAAQ,QAAQ,MAAM,eAAe,QAAQ,YAAY,EAAE;AAE/D,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,YAAY,UAAa,MAAM,YAAY,QAAW;AAC9D,WAAO,EAAC,GAAG,MAAM,SAAS,GAAG,MAAM,QAAO;AAAA,EAC5C;AAEA,MAAI,QAAQ,MAAM,aAAa;AAC/B,SAAO,GAAG,aACR,EAAC,GAAG,OAAO,GAAG,KAAI,IAClB,EAAC,GAAG,MAAM,GAAG,MAAK;AACtB;AAEA,SAAS,cAAc,IAAI;AACzB,MAAI,cAAc,YAAY;AAC5B,WAAO,YAAY;AAAA,EACrB;AACA,MAAI,cAAc,cAAc;AAC9B,WAAO,YAAY;AAAA,EACrB;AACA,MAAI,cAAc,YAAY;AAC5B,WAAO,YAAY;AAAA,EACrB;AACA,SAAO,YAAY;AACrB;AAEA,SAAS,gBAAgB,KAAK,GAAG,GAAG,GAAG,GAAG,QAAQ;AAChD,MAAIA,WAAU,KAAK,KAAK;AAExB,MAAI,QAAQ;AACV,QAAI,IAAI,KAAK,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AACrC,QAAI,OAAO,IAAI;AACf,QAAI,MAAM,IAAI;AACd,QAAI,QAAQ,IAAI,IAAI;AACpB,QAAI,SAAS,IAAI,IAAI;AAErB,QAAI,OAAO,GAAG,GAAG;AACjB,QAAI,OAAO,SAAS,MAAM,QAAQ;AAChC,UAAI,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAACA,QAAO;AACxC,UAAI,IAAI,OAAO,KAAK,GAAG,CAACA,UAAS,CAAC;AAClC,UAAI,IAAI,OAAO,QAAQ,GAAG,GAAGA,QAAO;AACpC,UAAI,IAAI,MAAM,QAAQ,GAAGA,UAAS,KAAK,EAAE;AAAA,IAC3C,WAAW,OAAO,OAAO;AACvB,UAAI,OAAO,MAAM,CAAC;AAClB,UAAI,IAAI,OAAO,KAAK,GAAG,CAACA,UAASA,QAAO;AACxC,UAAI,IAAI,MAAM,KAAK,GAAGA,UAAS,KAAK,KAAKA,QAAO;AAAA,IAClD,WAAW,MAAM,QAAQ;AACvB,UAAI,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC;AACjC,UAAI,IAAI,MAAM,QAAQ,GAAG,GAAG,KAAK,EAAE;AAAA,IACrC,OAAO;AACL,UAAI,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,KAAK,EAAE;AAAA,IACzC;AACA,QAAI,UAAU;AACd,QAAI,OAAO,GAAG,CAAC;AAAA,EACjB,OAAO;AACL,QAAI,KAAK,GAAG,GAAG,GAAG,CAAC;AAAA,EACrB;AACF;AAEA,SAAS,UAAU,KAAK,MAAM,OAAO;AACnC,MAAI,UAAU,MAAM;AACpB,MAAI,cAAc,MAAM;AACxB,MAAI,cAAc,MAAM;AAExB,MAAI,CAAC,YAAY,CAAC,eAAe,CAAC,cAAc;AAC9C;AAAA,EACF;AAEA,MAAI,UAAU;AAEd;AAAA,IACE;AAAA,IACA,UAAU,KAAK,CAAC,IAAI,cAAc;AAAA,IAClC,UAAU,KAAK,CAAC,IAAI,cAAc;AAAA,IAClC,UAAU,KAAK,CAAC,IAAI;AAAA,IACpB,UAAU,KAAK,CAAC,IAAI;AAAA,IACpB,MAAM;AAAA,EAAY;AAEpB,MAAI,UAAU;AAEd,MAAI,SAAS;AACX,QAAI,YAAY;AAChB,QAAI,KAAK;AAAA,EACX;AAEA,MAAI,eAAe,aAAa;AAC9B,QAAI,cAAc;AAClB,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,QAAI,OAAO;AAAA,EACb;AACF;AAEA,SAAS,aAAa,MAAM,OAAO,MAAM;AACvC,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK,IAAI,IAAI;AAErB,MAAI,UAAU,UAAU;AACtB,SAAK,IAAI;AAAA,EACX,WAAW,UAAU,SAAS,UAAU,SAAS;AAC/C,SAAK;AAAA,EACP;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,aAAa,KAAK,MAAM,KAAK;AACpC,MAAI,SAAS,IAAI;AACjB,MAAI,UAAU,IAAI;AAClB,MAAI,IAAI,UAAU,IAAI,CAAC;AACvB,MAAI,IAAI,UAAU,IAAI,CAAC;AACvB,MAAI,IAAI,UAAU,IAAI,CAAC;AAEvB,MAAI,SAAS;AACX,QAAI,WAAW,MAAM,GAAG,GAAG,CAAC;AAAA,EAC9B;AAEA,MAAI,IAAI,QAAQ;AACd,QAAI,UAAU,SAAS;AAGrB,UAAI,aAAa;AAAA,IACnB;AAEA,QAAI,SAAS,MAAM,GAAG,GAAG,CAAC;AAE1B,QAAI,UAAU,SAAS;AACrB,UAAI,aAAa;AAAA,IACnB;AAAA,EACF;AACF;AAEA,SAAS,SAAS,KAAK,OAAO,MAAM,OAAO;AACzC,MAAI,QAAQ,MAAM;AAClB,MAAIC,SAAQ,MAAM;AAClB,MAAI,SAAS,CAAC,CAACA;AACf,MAAI,OAAO,MAAM;AACjB,MAAI,OAAO,MAAM;AACjB,MAAI,cAAc,MAAM;AACxB,MAAI,cAAc,MAAM;AACxB,MAAI,UAAU,eAAe;AAC7B,MAAI;AAEJ,MAAI,CAAC,QAAS,CAAC,UAAU,CAAC,SAAU;AAClC;AAAA,EACF;AAGA,SAAO,aAAa,MAAM,OAAO,IAAI;AAErC,MAAI,OAAO,KAAK;AAChB,MAAI,YAAY;AAChB,MAAI,eAAe;AACnB,MAAI,aAAa,MAAM;AACvB,MAAI,cAAc,MAAM;AAExB,MAAI,QAAQ;AACV,QAAI,YAAYA;AAAA,EAClB;AACA,MAAI,SAAS;AACX,QAAI,WAAW;AACf,QAAI,YAAY;AAChB,QAAI,cAAc;AAAA,EACpB;AAEA,OAAK,IAAI,GAAG,OAAO,MAAM,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC9C,iBAAa,KAAK,MAAM,CAAC,GAAG;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK,IAAI,KAAK,IAAI;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAEA,IAAI,QAAQ,SAAS,QAAQ,KAAK,IAAI,OAAO;AAC3C,MAAI,KAAK;AAET,KAAG,UAAU;AACb,KAAG,SAAS;AACZ,KAAG,SAAS;AACZ,KAAG,SAAS;AACZ,KAAG,OAAO;AACV,KAAG,MAAM;AACX;AAEA,MAAM,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA,EAIrB,WAAW,SAAS,SAAS,OAAO,QAAQ,SAAS;AACnD,QAAI,KAAK;AACT,QAAI,QAAQ,GAAG;AACf,QAAI,OAAO,OAAO,QAAQ,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,KAAK,CAAC;AAC5D,QAAIA,SAAQ,QAAQ,CAAC,OAAO,OAAO,SAAW,KAAK,GAAG,SAAS,KAAK;AAEpE,WAAO;AAAA,MACL,OAAO,QAAQ,CAAC,OAAO,OAAO,QAAQ,GAAG,SAAS,KAAK;AAAA,MACvD,QAAQ,QAAQ,CAAC,OAAO,QAAQ,QAAQ,GAAG,SAAS,KAAK;AAAA,MACzD,MAAM,QAAQ,MAAM;AAAA,MACpB,iBAAiB,QAAQ,CAAC,OAAO,iBAAiB,IAAI,GAAG,SAAS,KAAK;AAAA,MACvE,aAAa,QAAQ,CAAC,OAAO,aAAa,IAAI,GAAG,SAAS,KAAK;AAAA,MAC/D,cAAc,QAAQ,CAAC,OAAO,cAAc,CAAC,GAAG,SAAS,KAAK;AAAA,MAC9D,aAAa,QAAQ,CAAC,OAAO,aAAa,CAAC,GAAG,SAAS,KAAK;AAAA,MAC5D,OAAO,QAAQ,CAAC,OAAO,OAAO,KAAK,GAAG,SAAS,KAAK;AAAA,MACpD,MAAM,QAAQ,CAAC,OAAO,MAAM,KAAK,GAAG,SAAS,KAAK;AAAA,MAClD,OAAOA;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,QAAQ,CAAC,OAAO,QAAQ,CAAC,GAAG,SAAS,KAAK;AAAA,MAClD,SAAS,QAAQ,CAAC,OAAO,SAAS,CAAC,GAAG,SAAS,KAAK;AAAA,MACpD,QAAQ,eAAe,GAAG,KAAK,OAAO;AAAA,MACtC,SAAS,UAAU,QAAQ,CAAC,OAAO,SAAS,CAAC,GAAG,SAAS,KAAK,CAAC;AAAA,MAC/D,YAAY,cAAc,GAAG,GAAG;AAAA,MAChC,UAAU,QAAQ,CAAC,OAAO,UAAU,CAAC,GAAG,SAAS,KAAK,KAAK,KAAK,KAAK;AAAA,MACrE,MAAM,MAAM,SAAS,GAAG,MAAM,OAAO,IAAI;AAAA,MACzC,WAAW,QAAQ,CAAC,OAAO,WAAW,OAAO,GAAG,SAAS,KAAK;AAAA,MAC9D,gBAAgB,QAAQ,CAAC,OAAO,gBAAgB,CAAC,GAAG,SAAS,KAAK;AAAA,MAClE,iBAAiB,QAAQ,CAAC,OAAO,iBAAiBA,MAAK,GAAG,SAAS,KAAK;AAAA,MACxE,iBAAiB,QAAQ,CAAC,OAAO,iBAAiBA,MAAK,GAAG,SAAS,KAAK;AAAA,MACxE,iBAAiB,QAAQ,CAAC,OAAO,iBAAiB,CAAC,GAAG,SAAS,KAAK;AAAA,IACtE;AAAA,EACF;AAAA,EAEA,QAAQ,SAAS,SAAS;AACxB,QAAI,KAAK;AACT,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,QAAQ,GAAG;AACf,QAAI,SAAS,GAAG;AAChB,QAAI,OAAO,OAAO;AAIlB,QAAI,UAAU,QAAQ,CAAC,OAAO,SAAS,IAAI,GAAG,SAAS,KAAK;AAE5D,QAAI,SAAS;AACX,cAAQ,QAAQ,QAAQ,KAAK,KAAK;AAClC,cAAQ,eAAe,SAAS,OAAO,WAAW,CAAC,OAAO,OAAO,CAAC,GAAG,KAAK;AAC1E,cAAQ,cAAc,KAAK,IAAI,CAAC,IAAI,MAAM,YAAY,KAAK;AAE3D,UAAI,MAAM,QAAQ;AAChB,gBAAQ,GAAG,UAAU,SAAS,OAAO,QAAQ,OAAO;AACpD,gBAAQ,cAAc,KAAK;AAAA,MAC7B;AAAA,IACF;AAEA,OAAG,SAAS;AACZ,OAAG,SAAS;AAAA,EACd;AAAA,EAEA,UAAU,WAAW;AACnB,WAAO,KAAK,SAAS,KAAK,OAAO,QAAQ,CAAC;AAAA,EAC5C;AAAA,EAEA,UAAU,WAAW;AACnB,WAAO,KAAK,SAAS,KAAK,OAAO,WAAW;AAAA,EAC9C;AAAA,EAEA,SAAS,WAAW;AAClB,WAAO,KAAK,UAAU,KAAK,OAAO;AAAA,EACpC;AAAA,EAEA,OAAO,WAAW;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,QAAI,KAAK;AACT,QAAI,MAAM,MAAM;AAChB,QAAI,QAAQ,GAAG;AACf,QAAI,QAAQ,GAAG;AACf,QAAI;AAEJ,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB;AAAA,IACF;AAEA,QAAI,KAAK;AAET,QAAI,MAAM,MAAM;AACd,aAAO,MAAM;AACb,UAAI,UAAU;AACd,UAAI;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QAAQ,KAAK;AAAA,QAClB,KAAK,SAAS,KAAK;AAAA,MAAG;AACxB,UAAI,KAAK;AAAA,IACX;AAEA,QAAI,cAAc,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC;AACjD,QAAI,UAAU,UAAU,OAAO,CAAC,GAAG,UAAU,OAAO,CAAC,CAAC;AACtD,QAAI,OAAO,MAAM,QAAQ;AAEzB,cAAU,KAAK,MAAM,OAAO,KAAK;AACjC,aAAS,KAAK,MAAM,OAAO,MAAM,MAAM,KAAK;AAE5C,QAAI,QAAQ;AAAA,EACd;AACF,CAAC;AAED,IAAI,cAAc,OAAO,oBAAoB;AAC7C,IAAI,cAAc,OAAO,oBAAoB;AAE7C,SAAS,QAAQ,OAAO,QAAQ,OAAO;AACrC,MAAI,MAAM,KAAK,IAAI,KAAK;AACxB,MAAI,MAAM,KAAK,IAAI,KAAK;AACxB,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,OAAO;AAEhB,SAAO;AAAA,IACL,GAAG,KAAK,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,IAAI;AAAA,IAChD,GAAG,KAAK,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,IAAI;AAAA,EAClD;AACF;AAEA,SAAS,UAAU,QAAQ,MAAM;AAC/B,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,SAAS,KAAK;AAClB,MAAI,GAAG,IAAI,IAAI,IAAI;AAEnB,OAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AAClC,SAAK,OAAO,CAAC;AACb,SAAK,GAAG,IAAI,OAAO;AACnB,SAAK,GAAG,IAAI,OAAO;AACnB,SAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC9B,UAAM,KAAK,IAAI,KAAK,EAAE;AACtB,UAAM,KAAK,IAAI,KAAK,EAAE;AAAA,EACxB;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,OAAO,IAAI,IAAI;AACtB,MAAI,KAAK,GAAG,IAAI,GAAG;AACnB,MAAI,KAAK,GAAG,IAAI,GAAG;AACnB,MAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEpC,SAAO;AAAA,IACL,KAAK,GAAG,IAAI,GAAG,KAAK;AAAA,IACpB,KAAK,GAAG,IAAI,GAAG,KAAK;AAAA,IACpB,QAAQ;AAAA,IACR;AAAA,EACF;AACF;AAEA,IAAI,SAAS,WAAW;AACtB,OAAK,YAAY;AACjB,OAAK,QAAQ;AAAA,IACX,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,MAAM,OAAO,WAAW;AAAA,EACtB,QAAQ,WAAW;AACjB,QAAI,IAAI,KAAK;AACb,WAAO;AAAA,MACL,GAAG,EAAE,IAAI,EAAE,IAAI;AAAA,MACf,GAAG,EAAE,IAAI,EAAE,IAAI;AAAA,IACjB;AAAA,EACF;AAAA,EAEA,QAAQ,SAAS,QAAQ,MAAM,UAAU;AACvC,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,MACX,GAAG,KAAK,IAAI,OAAO;AAAA,MACnB,GAAG,KAAK,IAAI,OAAO;AAAA,MACnB,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EAEA,UAAU,SAAS,OAAO;AACxB,QAAI,KAAK;AACT,QAAI,SAAS;AACb,QAAI,OAAO,GAAG;AAEd,YAAQ,QAAQ,OAAO,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS;AAEjD,WAAO,EAAE,MAAM,IAAI,KAAK,IAAI,UACvB,MAAM,IAAI,KAAK,IAAI,UACnB,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,KACrC,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA,EAIA,YAAY,SAAS,OAAO;AAC1B,QAAI,KAAK,KAAK,QAAQ;AACtB,QAAI,KAAK,MAAM,QAAQ;AACvB,QAAI,OAAO;AAAA,MACT,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MACnB,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IACrB;AACA,QAAI,GAAG,KAAK;AAEZ,QAAI,KAAK,cAAc,MAAM,WAAW;AAGtC,WAAK;AAAA,QACH,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QACnB,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MACrB;AAAA,IACF;AAEA,SAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,YAAM,UAAU,IAAI,KAAK,CAAC,CAAC;AAC3B,YAAM,UAAU,IAAI,KAAK,CAAC,CAAC;AAE3B,UAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,KAAK;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,WAAW;AAClB,QAAI,KAAK;AACT,QAAI,OAAO,GAAG;AACd,QAAI,QAAQ,GAAG;AACf,QAAI,SAAS,GAAG,OAAO;AAEvB,WAAO;AAAA,MACL,QAAQ,EAAC,GAAG,KAAK,GAAG,GAAG,KAAK,EAAC,GAAG,QAAQ,KAAK;AAAA,MAC7C,QAAQ,EAAC,GAAG,KAAK,IAAI,KAAK,GAAG,GAAG,KAAK,EAAC,GAAG,QAAQ,KAAK;AAAA,MACtD,QAAQ,EAAC,GAAG,KAAK,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,KAAK,EAAC,GAAG,QAAQ,KAAK;AAAA,MAC/D,QAAQ,EAAC,GAAG,KAAK,GAAG,GAAG,KAAK,IAAI,KAAK,EAAC,GAAG,QAAQ,KAAK;AAAA,IACxD;AAAA,EACF;AACF,CAAC;AAED,SAAS,YAAY,IAAI,OAAO,UAAU;AACxC,MAAI,QAAQ,MAAM,WAAW,IAAI,KAAK;AACtC,MAAI,KAAK,MAAM;AACf,MAAI,KAAK,MAAM;AAEf,MAAI,CAAC,MAAM,CAAC,IAAI;AAEd,WAAO,EAAC,GAAG,MAAM,GAAG,GAAG,MAAM,EAAC;AAAA,EAChC;AAEA,MAAI,IAAI,SAAS;AACjB,MAAI,IAAI,SAAS;AAGjB,MAAI,WAAW,MAAM;AACrB,MAAI,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,QAAQ,CAAC;AACnF,MAAI,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,QAAQ,CAAC;AAKnF,MAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAChD,QAAM,KAAK;AACX,QAAM,KAAK;AAGX,QAAM,MAAM,SAAS;AACrB,QAAM,MAAM,SAAS;AAErB,SAAO;AAAA,IACL,GAAG,MAAM,IAAI;AAAA,IACb,GAAG,MAAM,IAAI;AAAA,EACf;AACF;AAEA,SAAS,QAAQ,QAAQ,UAAU;AACjC,MAAI,GAAG,GAAG,IAAI;AAMd,OAAK,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACvC,SAAK,OAAO,CAAC,EAAE;AAEf,SAAK,IAAI,IAAI,GAAG,KAAK,KAAK,GAAG,UAAU,EAAE,GAAG;AAC1C,WAAK,OAAO,CAAC,EAAE;AAEf,UAAI,GAAG,YAAY,GAAG,KAAK,WAAW,GAAG,IAAI,GAAG;AAC9C,iBAAS,IAAI,EAAE;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ;AACvB,MAAI,GAAG,MAAM,OAAO,OAAO,UAAU,QAAQ;AAG7C,OAAK,IAAI,GAAG,OAAO,OAAO,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC/C,YAAQ,OAAO,CAAC;AAChB,YAAQ,MAAM;AAEd,QAAI,MAAM,UAAU;AAMlB,cAAQ,IAAI,MAAM,MAAM,KAAK,EAAC,KAAK,CAAC,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,EAAC,CAAC;AAExE,iBAAW,MAAM,SAAS;AAC1B,eAAS,YAAY,OAAO,MAAM,MAAM,GAAG,QAAQ;AACnD,YAAM,KAAK,OAAO,QAAQ,UAAU,MAAM,SAAS,CAAC;AAAA,IACtD;AAAA,EACF;AAGA,SAAO,QAAQ,QAAQ,SAAS,IAAI,IAAI;AACtC,QAAI,KAAK,GAAG;AACZ,QAAI,KAAK,GAAG;AAEZ,QAAK,MAAM,MAAO,IAAI;AACpB,SAAG,WAAW;AAAA,IAChB,WAAW,IAAI;AACb,SAAG,WAAW;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAEA,IAAI,SAAS;AAAA,EACX,SAAS,SAAS,UAAU;AAC1B,QAAI,SAAS,CAAC;AACd,QAAI,GAAG,GAAG,MAAM,MAAM;AAEtB,SAAK,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,EAAE,GAAG;AACjD,WAAK,IAAI,GAAG,OAAO,SAAS,CAAC,EAAE,QAAQ,IAAI,MAAM,EAAE,GAAG;AACpD,gBAAQ,SAAS,CAAC,EAAE,CAAC;AACrB,eAAO,KAAK,KAAK;AACjB,cAAM,UAAU;AAAA,UACd,MAAM,IAAI,OAAO;AAAA,UACjB,UAAU;AAAA,UACV,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM,MAAM;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAKA,WAAO,KAAK,SAAS,GAAG,GAAG;AACzB,UAAI,KAAK,EAAE;AACX,UAAI,KAAK,EAAE;AAEX,aAAO,GAAG,SAAS,GAAG,OAClB,GAAG,OAAO,GAAG,OACb,GAAG,OAAO,GAAG;AAAA,IACnB,CAAC;AAED,SAAK,OAAO,MAAM;AAElB,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,QAAQ;AACvB,QAAI,QAAQ;AACZ,QAAI,GAAG,MAAM,OAAO,OAAO;AAE3B,SAAK,IAAI,GAAG,OAAO,OAAO,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC/C,cAAQ,OAAO,CAAC;AAChB,cAAQ,MAAM,MAAM;AACpB,cAAQ,MAAM;AACd,YAAM,WAAW,SAAS,MAAM,YAAY;AAC5C,YAAM,WAAW,MAAM,QAAQ;AAC/B,eAAS,MAAM;AAAA,IACjB;AAEA,QAAI,OAAO;AACT,cAAQ,MAAM;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,QAAQ,SAAS,QAAQ,OAAO;AAC9B,QAAI,GAAG;AAKP,SAAK,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACvC,cAAQ,OAAO,CAAC,EAAE;AAElB,UAAI,SAAS,MAAM,YAAY,MAAM,KAAK,SAAS,KAAK,GAAG;AACzD,eAAO,OAAO,CAAC;AAAA,MACjB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,QAAI,GAAG,MAAM,OAAO,OAAO,UAAU;AAErC,SAAK,IAAI,GAAG,OAAO,OAAO,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC/C,cAAQ,OAAO,CAAC;AAChB,cAAQ,MAAM;AAEd,UAAI,MAAM,UAAU;AAClB,mBAAW,MAAM,SAAS;AAC1B,iBAAS,YAAY,MAAM,KAAK,MAAM,MAAM,GAAG,QAAQ;AACvD,cAAM,KAAK,OAAO,QAAQ,UAAU,MAAM,SAAS,CAAC;AACpD,cAAM,KAAK,OAAO,MAAM;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,YAAY,SAAS,OAAO;AAC9B,MAAI,cAAc,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ;AACZ,MAAI,MAAM,MAAM;AAChB,MAAI,SAAS,KAAK,GAAG;AACnB,QAAI,CAAC,cAAc,MAAM,KAAK,GAAG;AAC/B,cAAQ,MAAM;AAAA,IAChB,WAAW,CAAC,cAAc,MAAM,CAAC,GAAG;AAClC,cAAQ,MAAM;AAAA,IAChB,OAAO;AACL,cAAQ;AACR,aAAO,OAAO,KAAK,KAAK;AACxB,WAAK,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC7C,kBAAU,MAAM,IAAI,OAAO,MAAM,KAAK,CAAC,IAAI,OAAO,MAAM,KAAK,CAAC,CAAC;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAEA,SAAO,KAAK;AACd;AAOA,IAAIC,YAAW;AAAA,EACb,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,IACJ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,WAAW,CAAC;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,EACV,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,iBAAiB;AACnB;AAMA,IAAI,cAAc;AAClB,IAAI,cAAc;AAElB,SAAS,UAAU,SAAS,SAAS;AACnC,MAAI,WAAW,QAAQ;AACvB,MAAI,YAAY,CAAC;AACjB,MAAI,UAAU,CAAC;AACf,MAAI,QAAQ;AAEZ,MAAI,aAAa,OAAO;AACtB,WAAO;AAAA,EACT;AACA,MAAI,aAAa,MAAM;AACrB,eAAW,CAAC;AAAA,EACd;AAEA,YAAU,MAAM,CAAC,GAAG,CAAC,SAAS,QAAQ,CAAC;AACvC,WAAS,QAAQ,UAAU,CAAC;AAC5B,SAAO,OAAO,KAAK,MAAM;AACzB,SAAO,QAAQ;AAEf,MAAI,KAAK,QAAQ;AACf,SAAK,QAAQ,SAAS,KAAK;AACzB,UAAI,OAAO,GAAG,GAAG;AACf,gBAAQ,KAAK,MAAM,CAAC,GAAG;AAAA,UACrB;AAAA,UACA,OAAO,GAAG;AAAA,UACV,EAAC,MAAM,IAAG;AAAA,QACZ,CAAC,CAAC;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AAEL,YAAQ,KAAK,OAAO;AAAA,EACtB;AAGA,cAAY,QAAQ,OAAO,SAAS,QAAQ,QAAQ;AAClD,SAAK,OAAO,aAAa,CAAC,GAAG,SAAS,IAAI,OAAO;AAC/C,aAAO,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC;AAClC,aAAO,KAAK,EAAE,OAAO,QAAQ,WAAW,IAAI;AAAA,IAC9C,CAAC;AAED,WAAO,OAAO;AACd,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF;AACF;AAEA,SAAS,cAAc,OAAO,WAAW,OAAO,OAAO;AACrD,MAAI,CAAC,WAAW;AACd;AAAA,EACF;AAEA,MAAI,UAAU,MAAM;AACpB,MAAI,SAAS,MAAM;AACnB,MAAI;AAEJ,MAAI,CAAC,UAAU,OAAO,IAAI,GAAG;AAC3B;AAAA,EACF;AAEA,eAAa,UAAU,OAAO,IAAI,EAAE,OAAO,IAAI;AAC/C,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AAEA,MAAI,SAAS,YAAY,CAAC,SAAS,KAAK,CAAC,MAAM,MAAM;AAKnD,UAAM,WAAW,EAAE,SAAS;AAC5B,UAAM,OAAO,OAAO;AAAA,EACtB;AACF;AAEA,SAAS,mBAAmB,OAAO,WAAW,UAAU,OAAO,OAAO;AACpE,MAAI,OAAO;AAEX,MAAI,CAAC,YAAY,CAAC,OAAO;AACvB;AAAA,EACF;AAEA,MAAI,CAAC,UAAU;AACb,YAAQ;AAAA,EACV,WAAW,CAAC,OAAO;AACjB,YAAQ;AAAA,EACV,WAAW,aAAa,OAAO;AAC7B,YAAQ,QAAQ;AAAA,EAClB;AAEA,MAAI,OAAO;AACT,kBAAc,OAAO,UAAU,OAAO,UAAU,KAAK;AAAA,EACvD;AACA,MAAI,OAAO;AACT,kBAAc,OAAO,UAAU,OAAO,OAAO,KAAK;AAAA,EACpD;AACF;AAEA,SAAS,iBAAiB,OAAO,OAAO;AACtC,MAAI,UAAU,MAAM,WAAW;AAC/B,MAAI,YAAY,QAAQ;AACxB,MAAI,UAAU;AAEd,MAAI,CAAC,UAAU,SAAS,CAAC,UAAU,OAAO;AACxC;AAAA,EACF;AAEA,MAAI,MAAM,SAAS,aAAa;AAC9B,YAAQ,OAAO,OAAO,QAAQ,SAAS,KAAK;AAAA,EAC9C,WAAW,MAAM,SAAS,YAAY;AACpC;AAAA,EACF;AAEA,aAAW,QAAQ;AACnB,UAAQ,WAAW;AACnB,qBAAmB,OAAO,WAAW,UAAU,OAAO,KAAK;AAC7D;AAEA,SAAS,kBAAkB,OAAO,OAAO;AACvC,MAAI,UAAU,MAAM,WAAW;AAC/B,MAAI,WAAW,QAAQ,WAAW;AAClC,MAAI,QAAQ,YAAY,OAAO,OAAO,QAAQ,SAAS,KAAK;AAC5D,MAAI,OAAO;AACT,kBAAc,OAAO,UAAU,OAAO,KAAK;AAAA,EAC7C;AACF;AAEA,IAAI,SAAS;AAAA,EACX,IAAI;AAAA,EAEJ,UAAUA;AAAA,EAEV,YAAY,SAAS,OAAO;AAC1B,UAAM,WAAW,IAAI;AAAA,MACnB,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EAEA,cAAc,SAAS,OAAO;AAC5B,QAAI,UAAU,MAAM,WAAW;AAC/B,YAAQ,YAAY;AACpB,YAAQ,aAAa,CAAC;AACtB,YAAQ,YAAY,CAAC;AACrB,YAAQ,UAAU,CAAC;AAAA,EACrB;AAAA,EAEA,oBAAoB,SAAS,OAAO,MAAM,SAAS;AACjD,QAAI,eAAe,KAAK;AACxB,QAAI,UAAU,MAAM,WAAW;AAC/B,QAAI,SAAS,QAAQ,UAAU,YAAY,IAAI,CAAC;AAChD,QAAI,UAAU,MAAM,iBAAiB,YAAY;AACjD,QAAI,UAAU,MAAM,KAAK,SAAS,YAAY;AAC9C,QAAI,SAAS,UAAU,SAAS,OAAO;AACvC,QAAI,WAAW,KAAK,KAAK,QAAQ,CAAC;AAClC,QAAI,MAAM,MAAM;AAChB,QAAI,GAAG,GAAG,MAAM,MAAM,KAAK,KAAK,IAAI;AAEpC,QAAI,KAAK;AAET,SAAK,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,EAAE,GAAG;AACjD,WAAK,SAAS,CAAC;AACf,SAAG,WAAW,IAAI,CAAC;AAEnB,UAAI,WAAW,MAAM,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,MAAM;AAC3D,aAAK,IAAI,GAAG,OAAO,OAAO,OAAO,QAAQ,IAAI,MAAM,EAAE,GAAG;AACtD,gBAAM,OAAO,OAAO,CAAC;AACrB,gBAAM,IAAI;AAEV,kBAAQ,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC;AACjC,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,YACN,MAAM,OAAO;AAAA,UACf;AACA,gBAAM,WAAW;AAAA,YACf,QAAQ;AAAA,YACR;AAAA,YACA,WAAW;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAEA,gBAAM,OAAO,MAAM,QAAQ;AAC3B,aAAG,WAAW,EAAE,KAAK,KAAK;AAC1B,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,QAAQ;AAIZ,UAAM,QAAQ,YAAY,OAAO,WAAW;AAAA,MAC1C,QAAQ,SAAS,OAAO,QAAQ,QAAQ;AACtC,eAAO,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC;AAClC,eAAO,KAAK,EAAE,KAAK,KAAK,IAAI,OAAO,KAAK;AACxC,gBAAQ,YAAY;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,aAAa,SAAS,OAAO;AAC3B,UAAM,WAAW,EAAE,UAAU,OAAO,QAAQ,MAAM,WAAW,EAAE,SAAS;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,SAAS,OAAO;AACjC,WAAO,KAAK,OAAO,MAAM,WAAW,EAAE,OAAO;AAAA,EAC/C;AAAA,EAEA,aAAa,SAAS,OAAO,MAAM;AAIjC,QAAI,MAAM,WAAW,EAAE,WAAW;AAChC,UAAI,QAAQ,KAAK;AACjB,cAAQ,MAAM,MAAM;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AACH,2BAAiB,OAAO,KAAK;AAC7B;AAAA,QACF,KAAK;AACH,4BAAkB,OAAO,KAAK;AAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY,SAAS,OAAO;AAC1B,QAAI,UAAU,MAAM,WAAW;AAC/B,QAAI,WAAW,QAAQ;AACvB,QAAI,UAAU,QAAQ,WAAW,MAAM,kBAAkB;AACzD,QAAI,UAAU,MAAM,UAAU,UAAU,OAAO;AAC/C,QAAI,GAAG,MAAM,GAAG,MAAM,QAAQ,OAAO;AAErC,SAAK,IAAI,GAAG,OAAO,QAAQ,QAAQ,IAAI,MAAM,EAAE,GAAG;AAChD,eAAS,QAAQ,CAAC;AAClB,UAAI,OAAO,CAAC,GAAG;AACb,iBAAS,OAAO,CAAC,EAAE,QAAQ,WAAW,KAAK,CAAC;AAC5C,aAAK,IAAI,GAAG,OAAO,OAAO,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC/C,kBAAQ,OAAO,CAAC;AAChB,gBAAM,SAAS,SAAU,OAAO,CAAC,MAAM;AACvC,gBAAM,OAAO,MAAM,QAAQ;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAEA,QAAI,QAAQ,UAAU,QAAQ,QAAQ;AACpC,aAAO,OAAO,QAAQ,OAAO;AAC7B,YAAM,OAAO;AAAA,IACf;AAEA,WAAO,QAAQ;AAAA,EACjB;AACF;", "names": ["HALF_PI", "color", "defaults"]}