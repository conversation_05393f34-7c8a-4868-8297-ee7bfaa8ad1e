{"version": 3, "sources": ["../../../../node_modules/@orpc/standard-server-fetch/dist/index.mjs", "../../../../node_modules/@orpc/client/dist/shared/client.DwfV9Oyl.mjs", "../../../../node_modules/@orpc/client/dist/adapters/fetch/index.mjs"], "sourcesContent": ["import { AsyncIteratorClass, parseEmptyableJSON, isTypescriptObject, stringifyJSON, isAsyncIteratorObject, once } from '@orpc/shared';\nimport { EventDecoderStream, withEventMeta, ErrorEvent, encodeEventMessage, getEventMeta, getFilenameFromContentDisposition, generateContentDisposition } from '@orpc/standard-server';\n\nfunction toEventIterator(stream) {\n  const eventStream = stream?.pipeThrough(new TextDecoderStream()).pipeThrough(new EventDecoderStream());\n  const reader = eventStream?.getReader();\n  return new AsyncIteratorClass(async () => {\n    while (true) {\n      if (reader === void 0) {\n        return { done: true, value: void 0 };\n      }\n      const { done, value } = await reader.read();\n      if (done) {\n        return { done: true, value: void 0 };\n      }\n      switch (value.event) {\n        case \"message\": {\n          let message = parseEmptyableJSON(value.data);\n          if (isTypescriptObject(message)) {\n            message = withEventMeta(message, value);\n          }\n          return { done: false, value: message };\n        }\n        case \"error\": {\n          let error = new ErrorEvent({\n            data: parseEmptyableJSON(value.data)\n          });\n          error = withEventMeta(error, value);\n          throw error;\n        }\n        case \"done\": {\n          let done2 = parseEmptyableJSON(value.data);\n          if (isTypescriptObject(done2)) {\n            done2 = withEventMeta(done2, value);\n          }\n          return { done: true, value: done2 };\n        }\n      }\n    }\n  }, async () => {\n    await reader?.cancel();\n  });\n}\nfunction toEventStream(iterator, options = {}) {\n  const keepAliveEnabled = options.eventIteratorKeepAliveEnabled ?? true;\n  const keepAliveInterval = options.eventIteratorKeepAliveInterval ?? 5e3;\n  const keepAliveComment = options.eventIteratorKeepAliveComment ?? \"\";\n  let cancelled = false;\n  let timeout;\n  const stream = new ReadableStream({\n    async pull(controller) {\n      try {\n        if (keepAliveEnabled) {\n          timeout = setInterval(() => {\n            controller.enqueue(encodeEventMessage({\n              comments: [keepAliveComment]\n            }));\n          }, keepAliveInterval);\n        }\n        const value = await iterator.next();\n        clearInterval(timeout);\n        if (cancelled) {\n          return;\n        }\n        const meta = getEventMeta(value.value);\n        if (!value.done || value.value !== void 0 || meta !== void 0) {\n          controller.enqueue(encodeEventMessage({\n            ...meta,\n            event: value.done ? \"done\" : \"message\",\n            data: stringifyJSON(value.value)\n          }));\n        }\n        if (value.done) {\n          controller.close();\n        }\n      } catch (err) {\n        clearInterval(timeout);\n        if (cancelled) {\n          return;\n        }\n        controller.enqueue(encodeEventMessage({\n          ...getEventMeta(err),\n          event: \"error\",\n          data: err instanceof ErrorEvent ? stringifyJSON(err.data) : void 0\n        }));\n        controller.close();\n      }\n    },\n    async cancel() {\n      cancelled = true;\n      clearInterval(timeout);\n      await iterator.return?.();\n    }\n  }).pipeThrough(new TextEncoderStream());\n  return stream;\n}\n\nasync function toStandardBody(re) {\n  const contentDisposition = re.headers.get(\"content-disposition\");\n  if (typeof contentDisposition === \"string\") {\n    const fileName = getFilenameFromContentDisposition(contentDisposition) ?? \"blob\";\n    const blob2 = await re.blob();\n    return new File([blob2], fileName, {\n      type: blob2.type\n    });\n  }\n  const contentType = re.headers.get(\"content-type\");\n  if (!contentType || contentType.startsWith(\"application/json\")) {\n    const text = await re.text();\n    return parseEmptyableJSON(text);\n  }\n  if (contentType.startsWith(\"multipart/form-data\")) {\n    return await re.formData();\n  }\n  if (contentType.startsWith(\"application/x-www-form-urlencoded\")) {\n    const text = await re.text();\n    return new URLSearchParams(text);\n  }\n  if (contentType.startsWith(\"text/event-stream\")) {\n    return toEventIterator(re.body);\n  }\n  if (contentType.startsWith(\"text/plain\")) {\n    return await re.text();\n  }\n  const blob = await re.blob();\n  return new File([blob], \"blob\", {\n    type: blob.type\n  });\n}\nfunction toFetchBody(body, headers, options = {}) {\n  const currentContentDisposition = headers.get(\"content-disposition\");\n  headers.delete(\"content-type\");\n  headers.delete(\"content-disposition\");\n  if (body === void 0) {\n    return void 0;\n  }\n  if (body instanceof Blob) {\n    headers.set(\"content-type\", body.type);\n    headers.set(\"content-length\", body.size.toString());\n    headers.set(\n      \"content-disposition\",\n      currentContentDisposition ?? generateContentDisposition(body instanceof File ? body.name : \"blob\")\n    );\n    return body;\n  }\n  if (body instanceof FormData) {\n    return body;\n  }\n  if (body instanceof URLSearchParams) {\n    return body;\n  }\n  if (isAsyncIteratorObject(body)) {\n    headers.set(\"content-type\", \"text/event-stream\");\n    return toEventStream(body, options);\n  }\n  headers.set(\"content-type\", \"application/json\");\n  return stringifyJSON(body);\n}\n\nfunction toStandardHeaders(headers, standardHeaders = {}) {\n  for (const [key, value] of headers) {\n    if (Array.isArray(standardHeaders[key])) {\n      standardHeaders[key].push(value);\n    } else if (standardHeaders[key] !== void 0) {\n      standardHeaders[key] = [standardHeaders[key], value];\n    } else {\n      standardHeaders[key] = value;\n    }\n  }\n  return standardHeaders;\n}\nfunction toFetchHeaders(headers, fetchHeaders = new Headers()) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (Array.isArray(value)) {\n      for (const v of value) {\n        fetchHeaders.append(key, v);\n      }\n    } else if (value !== void 0) {\n      fetchHeaders.append(key, value);\n    }\n  }\n  return fetchHeaders;\n}\n\nfunction toStandardLazyRequest(request) {\n  return {\n    url: new URL(request.url),\n    signal: request.signal,\n    method: request.method,\n    body: once(() => toStandardBody(request)),\n    get headers() {\n      const headers = toStandardHeaders(request.headers);\n      Object.defineProperty(this, \"headers\", { value: headers, writable: true });\n      return headers;\n    },\n    set headers(value) {\n      Object.defineProperty(this, \"headers\", { value, writable: true });\n    }\n  };\n}\nfunction toFetchRequest(request, options = {}) {\n  const headers = toFetchHeaders(request.headers);\n  const body = toFetchBody(request.body, headers, options);\n  return new Request(request.url, {\n    signal: request.signal,\n    method: request.method,\n    headers,\n    body\n  });\n}\n\nfunction toFetchResponse(response, options = {}) {\n  const headers = toFetchHeaders(response.headers);\n  const body = toFetchBody(response.body, headers, options);\n  return new Response(body, { headers, status: response.status });\n}\nfunction toStandardLazyResponse(response) {\n  return {\n    body: once(() => toStandardBody(response)),\n    status: response.status,\n    get headers() {\n      const headers = toStandardHeaders(response.headers);\n      Object.defineProperty(this, \"headers\", { value: headers, writable: true });\n      return headers;\n    },\n    set headers(value) {\n      Object.defineProperty(this, \"headers\", { value, writable: true });\n    }\n  };\n}\n\nexport { toEventIterator, toEventStream, toFetchBody, toFetchHeaders, toFetchRequest, toFetchResponse, toStandardBody, toStandardHeaders, toStandardLazyRequest, toStandardLazyResponse };\n", "import { toArray, intercept, isObject, value, isAsyncIteratorObject, stringifyJSON } from '@orpc/shared';\nimport { mergeStandardHeaders, ErrorEvent } from '@orpc/standard-server';\nimport { C as COMMON_ORPC_ERROR_DEFS, b as isOR<PERSON><PERSON><PERSON>r<PERSON>tatus, c as is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d as createOR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, O as ORPCError, m as mapEventIterator, t as toORPCError } from './client.DHOfWE0c.mjs';\n\nclass CompositeStandardLinkPlugin {\n  plugins;\n  constructor(plugins = []) {\n    this.plugins = [...plugins].sort((a, b) => (a.order ?? 0) - (b.order ?? 0));\n  }\n  init(options) {\n    for (const plugin of this.plugins) {\n      plugin.init?.(options);\n    }\n  }\n}\n\nclass StandardLink {\n  constructor(codec, sender, options = {}) {\n    this.codec = codec;\n    this.sender = sender;\n    const plugin = new CompositeStandardLinkPlugin(options.plugins);\n    plugin.init(options);\n    this.interceptors = toArray(options.interceptors);\n    this.clientInterceptors = toArray(options.clientInterceptors);\n  }\n  interceptors;\n  clientInterceptors;\n  call(path, input, options) {\n    return intercept(this.interceptors, { ...options, path, input }, async ({ path: path2, input: input2, ...options2 }) => {\n      const output = await this.#call(path2, input2, options2);\n      return output;\n    });\n  }\n  async #call(path, input, options) {\n    const request = await this.codec.encode(path, input, options);\n    const response = await intercept(\n      this.clientInterceptors,\n      { ...options, input, path, request },\n      ({ input: input2, path: path2, request: request2, ...options2 }) => this.sender.call(request2, options2, path2, input2)\n    );\n    const output = await this.codec.decode(response, options, path, input);\n    return output;\n  }\n}\n\nconst STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES = {\n  BIGINT: 0,\n  DATE: 1,\n  NAN: 2,\n  UNDEFINED: 3,\n  URL: 4,\n  REGEXP: 5,\n  SET: 6,\n  MAP: 7\n};\nclass StandardRPCJsonSerializer {\n  customSerializers;\n  constructor(options = {}) {\n    this.customSerializers = options.customJsonSerializers ?? [];\n    if (this.customSerializers.length !== new Set(this.customSerializers.map((custom) => custom.type)).size) {\n      throw new Error(\"Custom serializer type must be unique.\");\n    }\n  }\n  serialize(data, segments = [], meta = [], maps = [], blobs = []) {\n    for (const custom of this.customSerializers) {\n      if (custom.condition(data)) {\n        const result = this.serialize(custom.serialize(data), segments, meta, maps, blobs);\n        meta.push([custom.type, ...segments]);\n        return result;\n      }\n    }\n    if (data instanceof Blob) {\n      maps.push(segments);\n      blobs.push(data);\n      return [data, meta, maps, blobs];\n    }\n    if (typeof data === \"bigint\") {\n      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.BIGINT, ...segments]);\n      return [data.toString(), meta, maps, blobs];\n    }\n    if (data instanceof Date) {\n      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.DATE, ...segments]);\n      if (Number.isNaN(data.getTime())) {\n        return [null, meta, maps, blobs];\n      }\n      return [data.toISOString(), meta, maps, blobs];\n    }\n    if (Number.isNaN(data)) {\n      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.NAN, ...segments]);\n      return [null, meta, maps, blobs];\n    }\n    if (data instanceof URL) {\n      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.URL, ...segments]);\n      return [data.toString(), meta, maps, blobs];\n    }\n    if (data instanceof RegExp) {\n      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.REGEXP, ...segments]);\n      return [data.toString(), meta, maps, blobs];\n    }\n    if (data instanceof Set) {\n      const result = this.serialize(Array.from(data), segments, meta, maps, blobs);\n      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.SET, ...segments]);\n      return result;\n    }\n    if (data instanceof Map) {\n      const result = this.serialize(Array.from(data.entries()), segments, meta, maps, blobs);\n      meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.MAP, ...segments]);\n      return result;\n    }\n    if (Array.isArray(data)) {\n      const json = data.map((v, i) => {\n        if (v === void 0) {\n          meta.push([STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.UNDEFINED, ...segments, i]);\n          return v;\n        }\n        return this.serialize(v, [...segments, i], meta, maps, blobs)[0];\n      });\n      return [json, meta, maps, blobs];\n    }\n    if (isObject(data)) {\n      const json = {};\n      for (const k in data) {\n        if (k === \"toJSON\" && typeof data[k] === \"function\") {\n          continue;\n        }\n        json[k] = this.serialize(data[k], [...segments, k], meta, maps, blobs)[0];\n      }\n      return [json, meta, maps, blobs];\n    }\n    return [data, meta, maps, blobs];\n  }\n  deserialize(json, meta, maps, getBlob) {\n    const ref = { data: json };\n    if (maps && getBlob) {\n      maps.forEach((segments, i) => {\n        let currentRef = ref;\n        let preSegment = \"data\";\n        segments.forEach((segment) => {\n          currentRef = currentRef[preSegment];\n          preSegment = segment;\n        });\n        currentRef[preSegment] = getBlob(i);\n      });\n    }\n    for (const item of meta) {\n      const type = item[0];\n      let currentRef = ref;\n      let preSegment = \"data\";\n      for (let i = 1; i < item.length; i++) {\n        currentRef = currentRef[preSegment];\n        preSegment = item[i];\n      }\n      for (const custom of this.customSerializers) {\n        if (custom.type === type) {\n          currentRef[preSegment] = custom.deserialize(currentRef[preSegment]);\n          break;\n        }\n      }\n      switch (type) {\n        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.BIGINT:\n          currentRef[preSegment] = BigInt(currentRef[preSegment]);\n          break;\n        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.DATE:\n          currentRef[preSegment] = new Date(currentRef[preSegment] ?? \"Invalid Date\");\n          break;\n        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.NAN:\n          currentRef[preSegment] = Number.NaN;\n          break;\n        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.UNDEFINED:\n          currentRef[preSegment] = void 0;\n          break;\n        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.URL:\n          currentRef[preSegment] = new URL(currentRef[preSegment]);\n          break;\n        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.REGEXP: {\n          const [, pattern, flags] = currentRef[preSegment].match(/^\\/(.*)\\/([a-z]*)$/);\n          currentRef[preSegment] = new RegExp(pattern, flags);\n          break;\n        }\n        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.SET:\n          currentRef[preSegment] = new Set(currentRef[preSegment]);\n          break;\n        case STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES.MAP:\n          currentRef[preSegment] = new Map(currentRef[preSegment]);\n          break;\n      }\n    }\n    return ref.data;\n  }\n}\n\nfunction toHttpPath(path) {\n  return `/${path.map(encodeURIComponent).join(\"/\")}`;\n}\nfunction getMalformedResponseErrorCode(status) {\n  return Object.entries(COMMON_ORPC_ERROR_DEFS).find(([, def]) => def.status === status)?.[0] ?? \"MALFORMED_ORPC_ERROR_RESPONSE\";\n}\n\nclass StandardRPCLinkCodec {\n  constructor(serializer, options) {\n    this.serializer = serializer;\n    this.baseUrl = options.url;\n    this.maxUrlLength = options.maxUrlLength ?? 2083;\n    this.fallbackMethod = options.fallbackMethod ?? \"POST\";\n    this.expectedMethod = options.method ?? this.fallbackMethod;\n    this.headers = options.headers ?? {};\n  }\n  baseUrl;\n  maxUrlLength;\n  fallbackMethod;\n  expectedMethod;\n  headers;\n  async encode(path, input, options) {\n    const expectedMethod = await value(this.expectedMethod, options, path, input);\n    let headers = await value(this.headers, options, path, input);\n    const baseUrl = await value(this.baseUrl, options, path, input);\n    const url = new URL(baseUrl);\n    url.pathname = `${url.pathname.replace(/\\/$/, \"\")}${toHttpPath(path)}`;\n    if (options.lastEventId !== void 0) {\n      headers = mergeStandardHeaders(headers, { \"last-event-id\": options.lastEventId });\n    }\n    const serialized = this.serializer.serialize(input);\n    if (expectedMethod === \"GET\" && !(serialized instanceof FormData) && !isAsyncIteratorObject(serialized)) {\n      const maxUrlLength = await value(this.maxUrlLength, options, path, input);\n      const getUrl = new URL(url);\n      getUrl.searchParams.append(\"data\", stringifyJSON(serialized));\n      if (getUrl.toString().length <= maxUrlLength) {\n        return {\n          body: void 0,\n          method: expectedMethod,\n          headers,\n          url: getUrl,\n          signal: options.signal\n        };\n      }\n    }\n    return {\n      url,\n      method: expectedMethod === \"GET\" ? this.fallbackMethod : expectedMethod,\n      headers,\n      body: serialized,\n      signal: options.signal\n    };\n  }\n  async decode(response) {\n    const isOk = !isORPCErrorStatus(response.status);\n    const deserialized = await (async () => {\n      let isBodyOk = false;\n      try {\n        const body = await response.body();\n        isBodyOk = true;\n        return this.serializer.deserialize(body);\n      } catch (error) {\n        if (!isBodyOk) {\n          throw new Error(\"Cannot parse response body, please check the response body and content-type.\", {\n            cause: error\n          });\n        }\n        throw new Error(\"Invalid RPC response format.\", {\n          cause: error\n        });\n      }\n    })();\n    if (!isOk) {\n      if (isORPCErrorJson(deserialized)) {\n        throw createORPCErrorFromJson(deserialized);\n      }\n      throw new ORPCError(getMalformedResponseErrorCode(response.status), {\n        status: response.status,\n        data: { ...response, body: deserialized }\n      });\n    }\n    return deserialized;\n  }\n}\n\nclass StandardRPCSerializer {\n  constructor(jsonSerializer) {\n    this.jsonSerializer = jsonSerializer;\n  }\n  serialize(data) {\n    if (isAsyncIteratorObject(data)) {\n      return mapEventIterator(data, {\n        value: async (value) => this.#serialize(value, false),\n        error: async (e) => {\n          return new ErrorEvent({\n            data: this.#serialize(toORPCError(e).toJSON(), false),\n            cause: e\n          });\n        }\n      });\n    }\n    return this.#serialize(data, true);\n  }\n  #serialize(data, enableFormData) {\n    const [json, meta_, maps, blobs] = this.jsonSerializer.serialize(data);\n    const meta = meta_.length === 0 ? void 0 : meta_;\n    if (!enableFormData || blobs.length === 0) {\n      return {\n        json,\n        meta\n      };\n    }\n    const form = new FormData();\n    form.set(\"data\", stringifyJSON({ json, meta, maps }));\n    blobs.forEach((blob, i) => {\n      form.set(i.toString(), blob);\n    });\n    return form;\n  }\n  deserialize(data) {\n    if (isAsyncIteratorObject(data)) {\n      return mapEventIterator(data, {\n        value: async (value) => this.#deserialize(value),\n        error: async (e) => {\n          if (!(e instanceof ErrorEvent)) {\n            return e;\n          }\n          const deserialized = this.#deserialize(e.data);\n          if (isORPCErrorJson(deserialized)) {\n            return createORPCErrorFromJson(deserialized, { cause: e });\n          }\n          return new ErrorEvent({\n            data: deserialized,\n            cause: e\n          });\n        }\n      });\n    }\n    return this.#deserialize(data);\n  }\n  #deserialize(data) {\n    if (!(data instanceof FormData)) {\n      return this.jsonSerializer.deserialize(data.json, data.meta ?? []);\n    }\n    const serialized = JSON.parse(data.get(\"data\"));\n    return this.jsonSerializer.deserialize(\n      serialized.json,\n      serialized.meta ?? [],\n      serialized.maps,\n      (i) => data.get(i.toString())\n    );\n  }\n}\n\nclass StandardRPCLink extends StandardLink {\n  constructor(linkClient, options) {\n    const jsonSerializer = new StandardRPCJsonSerializer(options);\n    const serializer = new StandardRPCSerializer(jsonSerializer);\n    const linkCodec = new StandardRPCLinkCodec(serializer, options);\n    super(linkCodec, linkClient, options);\n  }\n}\n\nexport { CompositeStandardLinkPlugin as C, StandardLink as S, STANDARD_RPC_JSON_SERIALIZER_BUILT_IN_TYPES as a, StandardRPCJsonSerializer as b, StandardRPCLink as c, StandardRPCLinkCodec as d, StandardRPCSerializer as e, getMalformedResponseErrorCode as g, toHttpPath as t };\n", "import { toFetchRequest, toStandardLazyResponse } from '@orpc/standard-server-fetch';\nimport '@orpc/shared';\nimport { c as StandardRPCLink } from '../../shared/client.DwfV9Oyl.mjs';\nimport '@orpc/standard-server';\nimport '../../shared/client.DHOfWE0c.mjs';\n\nclass LinkFetchClient {\n  fetch;\n  toFetchRequestOptions;\n  constructor(options) {\n    this.fetch = options?.fetch ?? globalThis.fetch.bind(globalThis);\n    this.toFetchRequestOptions = options;\n  }\n  async call(request, options, path, input) {\n    const fetchRequest = toFetchRequest(request, this.toFetchRequestOptions);\n    const fetchResponse = await this.fetch(fetchRequest, { redirect: \"manual\" }, options, path, input);\n    const lazyResponse = toStandardLazyResponse(fetchResponse);\n    return lazyResponse;\n  }\n}\n\nclass RPCLink extends StandardRPCLink {\n  constructor(options) {\n    const linkClient = new LinkFetchClient(options);\n    super(linkClient, options);\n  }\n}\n\nexport { LinkFetchClient, RPCLink };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,gBAAgB,QAAQ;AAC/B,QAAM,cAAc,QAAQ,YAAY,IAAI,kBAAkB,CAAC,EAAE,YAAY,IAAI,mBAAmB,CAAC;AACrG,QAAM,SAAS,aAAa,UAAU;AACtC,SAAO,IAAI,mBAAmB,YAAY;AACxC,WAAO,MAAM;AACX,UAAI,WAAW,QAAQ;AACrB,eAAO,EAAE,MAAM,MAAM,OAAO,OAAO;AAAA,MACrC;AACA,YAAM,EAAE,MAAM,OAAAA,OAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,OAAO;AAAA,MACrC;AACA,cAAQA,OAAM,OAAO;AAAA,QACnB,KAAK,WAAW;AACd,cAAI,UAAU,mBAAmBA,OAAM,IAAI;AAC3C,cAAI,mBAAmB,OAAO,GAAG;AAC/B,sBAAU,cAAc,SAASA,MAAK;AAAA,UACxC;AACA,iBAAO,EAAE,MAAM,OAAO,OAAO,QAAQ;AAAA,QACvC;AAAA,QACA,KAAK,SAAS;AACZ,cAAI,QAAQ,IAAI,WAAW;AAAA,YACzB,MAAM,mBAAmBA,OAAM,IAAI;AAAA,UACrC,CAAC;AACD,kBAAQ,cAAc,OAAOA,MAAK;AAClC,gBAAM;AAAA,QACR;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,QAAQ,mBAAmBA,OAAM,IAAI;AACzC,cAAI,mBAAmB,KAAK,GAAG;AAC7B,oBAAQ,cAAc,OAAOA,MAAK;AAAA,UACpC;AACA,iBAAO,EAAE,MAAM,MAAM,OAAO,MAAM;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,YAAY;AACb,UAAM,QAAQ,OAAO;AAAA,EACvB,CAAC;AACH;AACA,SAAS,cAAc,UAAU,UAAU,CAAC,GAAG;AAC7C,QAAM,mBAAmB,QAAQ,iCAAiC;AAClE,QAAM,oBAAoB,QAAQ,kCAAkC;AACpE,QAAM,mBAAmB,QAAQ,iCAAiC;AAClE,MAAI,YAAY;AAChB,MAAI;AACJ,QAAM,SAAS,IAAI,eAAe;AAAA,IAChC,MAAM,KAAK,YAAY;AACrB,UAAI;AACF,YAAI,kBAAkB;AACpB,oBAAU,YAAY,MAAM;AAC1B,uBAAW,QAAQ,mBAAmB;AAAA,cACpC,UAAU,CAAC,gBAAgB;AAAA,YAC7B,CAAC,CAAC;AAAA,UACJ,GAAG,iBAAiB;AAAA,QACtB;AACA,cAAMA,SAAQ,MAAM,SAAS,KAAK;AAClC,sBAAc,OAAO;AACrB,YAAI,WAAW;AACb;AAAA,QACF;AACA,cAAM,OAAO,aAAaA,OAAM,KAAK;AACrC,YAAI,CAACA,OAAM,QAAQA,OAAM,UAAU,UAAU,SAAS,QAAQ;AAC5D,qBAAW,QAAQ,mBAAmB;AAAA,YACpC,GAAG;AAAA,YACH,OAAOA,OAAM,OAAO,SAAS;AAAA,YAC7B,MAAM,cAAcA,OAAM,KAAK;AAAA,UACjC,CAAC,CAAC;AAAA,QACJ;AACA,YAAIA,OAAM,MAAM;AACd,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,SAAS,KAAK;AACZ,sBAAc,OAAO;AACrB,YAAI,WAAW;AACb;AAAA,QACF;AACA,mBAAW,QAAQ,mBAAmB;AAAA,UACpC,GAAG,aAAa,GAAG;AAAA,UACnB,OAAO;AAAA,UACP,MAAM,eAAe,aAAa,cAAc,IAAI,IAAI,IAAI;AAAA,QAC9D,CAAC,CAAC;AACF,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,IACA,MAAM,SAAS;AACb,kBAAY;AACZ,oBAAc,OAAO;AACrB,YAAM,SAAS,SAAS;AAAA,IAC1B;AAAA,EACF,CAAC,EAAE,YAAY,IAAI,kBAAkB,CAAC;AACtC,SAAO;AACT;AAEA,eAAe,eAAe,IAAI;AAChC,QAAM,qBAAqB,GAAG,QAAQ,IAAI,qBAAqB;AAC/D,MAAI,OAAO,uBAAuB,UAAU;AAC1C,UAAM,WAAW,kCAAkC,kBAAkB,KAAK;AAC1E,UAAM,QAAQ,MAAM,GAAG,KAAK;AAC5B,WAAO,IAAI,KAAK,CAAC,KAAK,GAAG,UAAU;AAAA,MACjC,MAAM,MAAM;AAAA,IACd,CAAC;AAAA,EACH;AACA,QAAM,cAAc,GAAG,QAAQ,IAAI,cAAc;AACjD,MAAI,CAAC,eAAe,YAAY,WAAW,kBAAkB,GAAG;AAC9D,UAAM,OAAO,MAAM,GAAG,KAAK;AAC3B,WAAO,mBAAmB,IAAI;AAAA,EAChC;AACA,MAAI,YAAY,WAAW,qBAAqB,GAAG;AACjD,WAAO,MAAM,GAAG,SAAS;AAAA,EAC3B;AACA,MAAI,YAAY,WAAW,mCAAmC,GAAG;AAC/D,UAAM,OAAO,MAAM,GAAG,KAAK;AAC3B,WAAO,IAAI,gBAAgB,IAAI;AAAA,EACjC;AACA,MAAI,YAAY,WAAW,mBAAmB,GAAG;AAC/C,WAAO,gBAAgB,GAAG,IAAI;AAAA,EAChC;AACA,MAAI,YAAY,WAAW,YAAY,GAAG;AACxC,WAAO,MAAM,GAAG,KAAK;AAAA,EACvB;AACA,QAAM,OAAO,MAAM,GAAG,KAAK;AAC3B,SAAO,IAAI,KAAK,CAAC,IAAI,GAAG,QAAQ;AAAA,IAC9B,MAAM,KAAK;AAAA,EACb,CAAC;AACH;AACA,SAAS,YAAY,MAAM,SAAS,UAAU,CAAC,GAAG;AAChD,QAAM,4BAA4B,QAAQ,IAAI,qBAAqB;AACnE,UAAQ,OAAO,cAAc;AAC7B,UAAQ,OAAO,qBAAqB;AACpC,MAAI,SAAS,QAAQ;AACnB,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,MAAM;AACxB,YAAQ,IAAI,gBAAgB,KAAK,IAAI;AACrC,YAAQ,IAAI,kBAAkB,KAAK,KAAK,SAAS,CAAC;AAClD,YAAQ;AAAA,MACN;AAAA,MACA,6BAA6B,2BAA2B,gBAAgB,OAAO,KAAK,OAAO,MAAM;AAAA,IACnG;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,iBAAiB;AACnC,WAAO;AAAA,EACT;AACA,MAAI,sBAAsB,IAAI,GAAG;AAC/B,YAAQ,IAAI,gBAAgB,mBAAmB;AAC/C,WAAO,cAAc,MAAM,OAAO;AAAA,EACpC;AACA,UAAQ,IAAI,gBAAgB,kBAAkB;AAC9C,SAAO,cAAc,IAAI;AAC3B;AAEA,SAAS,kBAAkB,SAAS,kBAAkB,CAAC,GAAG;AACxD,aAAW,CAAC,KAAKA,MAAK,KAAK,SAAS;AAClC,QAAI,MAAM,QAAQ,gBAAgB,GAAG,CAAC,GAAG;AACvC,sBAAgB,GAAG,EAAE,KAAKA,MAAK;AAAA,IACjC,WAAW,gBAAgB,GAAG,MAAM,QAAQ;AAC1C,sBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,GAAGA,MAAK;AAAA,IACrD,OAAO;AACL,sBAAgB,GAAG,IAAIA;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,SAAS,eAAe,IAAI,QAAQ,GAAG;AAC7D,aAAW,CAAC,KAAKA,MAAK,KAAK,OAAO,QAAQ,OAAO,GAAG;AAClD,QAAI,MAAM,QAAQA,MAAK,GAAG;AACxB,iBAAW,KAAKA,QAAO;AACrB,qBAAa,OAAO,KAAK,CAAC;AAAA,MAC5B;AAAA,IACF,WAAWA,WAAU,QAAQ;AAC3B,mBAAa,OAAO,KAAKA,MAAK;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AACT;AAkBA,SAAS,eAAe,SAAS,UAAU,CAAC,GAAG;AAC7C,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,OAAO,YAAY,QAAQ,MAAM,SAAS,OAAO;AACvD,SAAO,IAAI,QAAQ,QAAQ,KAAK;AAAA,IAC9B,QAAQ,QAAQ;AAAA,IAChB,QAAQ,QAAQ;AAAA,IAChB;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAOA,SAAS,uBAAuB,UAAU;AACxC,SAAO;AAAA,IACL,MAAM,KAAK,MAAM,eAAe,QAAQ,CAAC;AAAA,IACzC,QAAQ,SAAS;AAAA,IACjB,IAAI,UAAU;AACZ,YAAM,UAAU,kBAAkB,SAAS,OAAO;AAClD,aAAO,eAAe,MAAM,WAAW,EAAE,OAAO,SAAS,UAAU,KAAK,CAAC;AACzE,aAAO;AAAA,IACT;AAAA,IACA,IAAI,QAAQC,QAAO;AACjB,aAAO,eAAe,MAAM,WAAW,EAAE,OAAAA,QAAO,UAAU,KAAK,CAAC;AAAA,IAClE;AAAA,EACF;AACF;;;ACjOA,IAAM,8BAAN,MAAkC;AAAA,EAChC;AAAA,EACA,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,UAAU,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAAA,EAC5E;AAAA,EACA,KAAK,SAAS;AACZ,eAAW,UAAU,KAAK,SAAS;AACjC,aAAO,OAAO,OAAO;AAAA,IACvB;AAAA,EACF;AACF;AAEA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,OAAO,QAAQ,UAAU,CAAC,GAAG;AACvC,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,UAAM,SAAS,IAAI,4BAA4B,QAAQ,OAAO;AAC9D,WAAO,KAAK,OAAO;AACnB,SAAK,eAAe,QAAQ,QAAQ,YAAY;AAChD,SAAK,qBAAqB,QAAQ,QAAQ,kBAAkB;AAAA,EAC9D;AAAA,EACA;AAAA,EACA;AAAA,EACA,KAAK,MAAM,OAAO,SAAS;AACzB,WAAO,UAAU,KAAK,cAAc,EAAE,GAAG,SAAS,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,OAAO,OAAO,QAAQ,GAAG,SAAS,MAAM;AACtH,YAAM,SAAS,MAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ;AACvD,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,MAAM,MAAM,MAAM,OAAO,SAAS;AAChC,UAAM,UAAU,MAAM,KAAK,MAAM,OAAO,MAAM,OAAO,OAAO;AAC5D,UAAM,WAAW,MAAM;AAAA,MACrB,KAAK;AAAA,MACL,EAAE,GAAG,SAAS,OAAO,MAAM,QAAQ;AAAA,MACnC,CAAC,EAAE,OAAO,QAAQ,MAAM,OAAO,SAAS,UAAU,GAAG,SAAS,MAAM,KAAK,OAAO,KAAK,UAAU,UAAU,OAAO,MAAM;AAAA,IACxH;AACA,UAAM,SAAS,MAAM,KAAK,MAAM,OAAO,UAAU,SAAS,MAAM,KAAK;AACrE,WAAO;AAAA,EACT;AACF;AAEA,IAAM,8CAA8C;AAAA,EAClD,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,KAAK;AAAA,EACL,WAAW;AAAA,EACX,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAM,4BAAN,MAAgC;AAAA,EAC9B;AAAA,EACA,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,oBAAoB,QAAQ,yBAAyB,CAAC;AAC3D,QAAI,KAAK,kBAAkB,WAAW,IAAI,IAAI,KAAK,kBAAkB,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,EAAE,MAAM;AACvG,YAAM,IAAI,MAAM,wCAAwC;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,UAAU,MAAM,WAAW,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG;AAC/D,eAAW,UAAU,KAAK,mBAAmB;AAC3C,UAAI,OAAO,UAAU,IAAI,GAAG;AAC1B,cAAM,SAAS,KAAK,UAAU,OAAO,UAAU,IAAI,GAAG,UAAU,MAAM,MAAM,KAAK;AACjF,aAAK,KAAK,CAAC,OAAO,MAAM,GAAG,QAAQ,CAAC;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,gBAAgB,MAAM;AACxB,WAAK,KAAK,QAAQ;AAClB,YAAM,KAAK,IAAI;AACf,aAAO,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,IACjC;AACA,QAAI,OAAO,SAAS,UAAU;AAC5B,WAAK,KAAK,CAAC,4CAA4C,QAAQ,GAAG,QAAQ,CAAC;AAC3E,aAAO,CAAC,KAAK,SAAS,GAAG,MAAM,MAAM,KAAK;AAAA,IAC5C;AACA,QAAI,gBAAgB,MAAM;AACxB,WAAK,KAAK,CAAC,4CAA4C,MAAM,GAAG,QAAQ,CAAC;AACzE,UAAI,OAAO,MAAM,KAAK,QAAQ,CAAC,GAAG;AAChC,eAAO,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,MACjC;AACA,aAAO,CAAC,KAAK,YAAY,GAAG,MAAM,MAAM,KAAK;AAAA,IAC/C;AACA,QAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAK,KAAK,CAAC,4CAA4C,KAAK,GAAG,QAAQ,CAAC;AACxE,aAAO,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,IACjC;AACA,QAAI,gBAAgB,KAAK;AACvB,WAAK,KAAK,CAAC,4CAA4C,KAAK,GAAG,QAAQ,CAAC;AACxE,aAAO,CAAC,KAAK,SAAS,GAAG,MAAM,MAAM,KAAK;AAAA,IAC5C;AACA,QAAI,gBAAgB,QAAQ;AAC1B,WAAK,KAAK,CAAC,4CAA4C,QAAQ,GAAG,QAAQ,CAAC;AAC3E,aAAO,CAAC,KAAK,SAAS,GAAG,MAAM,MAAM,KAAK;AAAA,IAC5C;AACA,QAAI,gBAAgB,KAAK;AACvB,YAAM,SAAS,KAAK,UAAU,MAAM,KAAK,IAAI,GAAG,UAAU,MAAM,MAAM,KAAK;AAC3E,WAAK,KAAK,CAAC,4CAA4C,KAAK,GAAG,QAAQ,CAAC;AACxE,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB,KAAK;AACvB,YAAM,SAAS,KAAK,UAAU,MAAM,KAAK,KAAK,QAAQ,CAAC,GAAG,UAAU,MAAM,MAAM,KAAK;AACrF,WAAK,KAAK,CAAC,4CAA4C,KAAK,GAAG,QAAQ,CAAC;AACxE,aAAO;AAAA,IACT;AACA,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAM,OAAO,KAAK,IAAI,CAAC,GAAG,MAAM;AAC9B,YAAI,MAAM,QAAQ;AAChB,eAAK,KAAK,CAAC,4CAA4C,WAAW,GAAG,UAAU,CAAC,CAAC;AACjF,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,MAAM,MAAM,KAAK,EAAE,CAAC;AAAA,MACjE,CAAC;AACD,aAAO,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,IACjC;AACA,QAAI,SAAS,IAAI,GAAG;AAClB,YAAM,OAAO,CAAC;AACd,iBAAW,KAAK,MAAM;AACpB,YAAI,MAAM,YAAY,OAAO,KAAK,CAAC,MAAM,YAAY;AACnD;AAAA,QACF;AACA,aAAK,CAAC,IAAI,KAAK,UAAU,KAAK,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,MAAM,MAAM,KAAK,EAAE,CAAC;AAAA,MAC1E;AACA,aAAO,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,IACjC;AACA,WAAO,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,EACjC;AAAA,EACA,YAAY,MAAM,MAAM,MAAM,SAAS;AACrC,UAAM,MAAM,EAAE,MAAM,KAAK;AACzB,QAAI,QAAQ,SAAS;AACnB,WAAK,QAAQ,CAAC,UAAU,MAAM;AAC5B,YAAI,aAAa;AACjB,YAAI,aAAa;AACjB,iBAAS,QAAQ,CAAC,YAAY;AAC5B,uBAAa,WAAW,UAAU;AAClC,uBAAa;AAAA,QACf,CAAC;AACD,mBAAW,UAAU,IAAI,QAAQ,CAAC;AAAA,MACpC,CAAC;AAAA,IACH;AACA,eAAW,QAAQ,MAAM;AACvB,YAAM,OAAO,KAAK,CAAC;AACnB,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,qBAAa,WAAW,UAAU;AAClC,qBAAa,KAAK,CAAC;AAAA,MACrB;AACA,iBAAW,UAAU,KAAK,mBAAmB;AAC3C,YAAI,OAAO,SAAS,MAAM;AACxB,qBAAW,UAAU,IAAI,OAAO,YAAY,WAAW,UAAU,CAAC;AAClE;AAAA,QACF;AAAA,MACF;AACA,cAAQ,MAAM;AAAA,QACZ,KAAK,4CAA4C;AAC/C,qBAAW,UAAU,IAAI,OAAO,WAAW,UAAU,CAAC;AACtD;AAAA,QACF,KAAK,4CAA4C;AAC/C,qBAAW,UAAU,IAAI,IAAI,KAAK,WAAW,UAAU,KAAK,cAAc;AAC1E;AAAA,QACF,KAAK,4CAA4C;AAC/C,qBAAW,UAAU,IAAI,OAAO;AAChC;AAAA,QACF,KAAK,4CAA4C;AAC/C,qBAAW,UAAU,IAAI;AACzB;AAAA,QACF,KAAK,4CAA4C;AAC/C,qBAAW,UAAU,IAAI,IAAI,IAAI,WAAW,UAAU,CAAC;AACvD;AAAA,QACF,KAAK,4CAA4C,QAAQ;AACvD,gBAAM,CAAC,EAAE,SAAS,KAAK,IAAI,WAAW,UAAU,EAAE,MAAM,oBAAoB;AAC5E,qBAAW,UAAU,IAAI,IAAI,OAAO,SAAS,KAAK;AAClD;AAAA,QACF;AAAA,QACA,KAAK,4CAA4C;AAC/C,qBAAW,UAAU,IAAI,IAAI,IAAI,WAAW,UAAU,CAAC;AACvD;AAAA,QACF,KAAK,4CAA4C;AAC/C,qBAAW,UAAU,IAAI,IAAI,IAAI,WAAW,UAAU,CAAC;AACvD;AAAA,MACJ;AAAA,IACF;AACA,WAAO,IAAI;AAAA,EACb;AACF;AAEA,SAAS,WAAW,MAAM;AACxB,SAAO,IAAI,KAAK,IAAI,kBAAkB,EAAE,KAAK,GAAG,CAAC;AACnD;AACA,SAAS,8BAA8B,QAAQ;AAC7C,SAAO,OAAO,QAAQ,sBAAsB,EAAE,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM,IAAI,WAAW,MAAM,IAAI,CAAC,KAAK;AACjG;AAEA,IAAM,uBAAN,MAA2B;AAAA,EACzB,YAAY,YAAY,SAAS;AAC/B,SAAK,aAAa;AAClB,SAAK,UAAU,QAAQ;AACvB,SAAK,eAAe,QAAQ,gBAAgB;AAC5C,SAAK,iBAAiB,QAAQ,kBAAkB;AAChD,SAAK,iBAAiB,QAAQ,UAAU,KAAK;AAC7C,SAAK,UAAU,QAAQ,WAAW,CAAC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM,OAAO,MAAM,OAAO,SAAS;AACjC,UAAM,iBAAiB,MAAM,MAAM,KAAK,gBAAgB,SAAS,MAAM,KAAK;AAC5E,QAAI,UAAU,MAAM,MAAM,KAAK,SAAS,SAAS,MAAM,KAAK;AAC5D,UAAM,UAAU,MAAM,MAAM,KAAK,SAAS,SAAS,MAAM,KAAK;AAC9D,UAAM,MAAM,IAAI,IAAI,OAAO;AAC3B,QAAI,WAAW,GAAG,IAAI,SAAS,QAAQ,OAAO,EAAE,CAAC,GAAG,WAAW,IAAI,CAAC;AACpE,QAAI,QAAQ,gBAAgB,QAAQ;AAClC,gBAAU,qBAAqB,SAAS,EAAE,iBAAiB,QAAQ,YAAY,CAAC;AAAA,IAClF;AACA,UAAM,aAAa,KAAK,WAAW,UAAU,KAAK;AAClD,QAAI,mBAAmB,SAAS,EAAE,sBAAsB,aAAa,CAAC,sBAAsB,UAAU,GAAG;AACvG,YAAM,eAAe,MAAM,MAAM,KAAK,cAAc,SAAS,MAAM,KAAK;AACxE,YAAM,SAAS,IAAI,IAAI,GAAG;AAC1B,aAAO,aAAa,OAAO,QAAQ,cAAc,UAAU,CAAC;AAC5D,UAAI,OAAO,SAAS,EAAE,UAAU,cAAc;AAC5C,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL,QAAQ,QAAQ;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,QAAQ,mBAAmB,QAAQ,KAAK,iBAAiB;AAAA,MACzD;AAAA,MACA,MAAM;AAAA,MACN,QAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AAAA,EACA,MAAM,OAAO,UAAU;AACrB,UAAM,OAAO,CAAC,kBAAkB,SAAS,MAAM;AAC/C,UAAM,eAAe,OAAO,YAAY;AACtC,UAAI,WAAW;AACf,UAAI;AACF,cAAM,OAAO,MAAM,SAAS,KAAK;AACjC,mBAAW;AACX,eAAO,KAAK,WAAW,YAAY,IAAI;AAAA,MACzC,SAAS,OAAO;AACd,YAAI,CAAC,UAAU;AACb,gBAAM,IAAI,MAAM,gFAAgF;AAAA,YAC9F,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,cAAM,IAAI,MAAM,gCAAgC;AAAA,UAC9C,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF,GAAG;AACH,QAAI,CAAC,MAAM;AACT,UAAI,gBAAgB,YAAY,GAAG;AACjC,cAAM,wBAAwB,YAAY;AAAA,MAC5C;AACA,YAAM,IAAI,UAAU,8BAA8B,SAAS,MAAM,GAAG;AAAA,QAClE,QAAQ,SAAS;AAAA,QACjB,MAAM,EAAE,GAAG,UAAU,MAAM,aAAa;AAAA,MAC1C,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,UAAU,MAAM;AACd,QAAI,sBAAsB,IAAI,GAAG;AAC/B,aAAO,iBAAiB,MAAM;AAAA,QAC5B,OAAO,OAAOC,WAAU,KAAK,WAAWA,QAAO,KAAK;AAAA,QACpD,OAAO,OAAO,MAAM;AAClB,iBAAO,IAAI,WAAW;AAAA,YACpB,MAAM,KAAK,WAAW,YAAY,CAAC,EAAE,OAAO,GAAG,KAAK;AAAA,YACpD,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,KAAK,WAAW,MAAM,IAAI;AAAA,EACnC;AAAA,EACA,WAAW,MAAM,gBAAgB;AAC/B,UAAM,CAAC,MAAM,OAAO,MAAM,KAAK,IAAI,KAAK,eAAe,UAAU,IAAI;AACrE,UAAM,OAAO,MAAM,WAAW,IAAI,SAAS;AAC3C,QAAI,CAAC,kBAAkB,MAAM,WAAW,GAAG;AACzC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,UAAM,OAAO,IAAI,SAAS;AAC1B,SAAK,IAAI,QAAQ,cAAc,EAAE,MAAM,MAAM,KAAK,CAAC,CAAC;AACpD,UAAM,QAAQ,CAAC,MAAM,MAAM;AACzB,WAAK,IAAI,EAAE,SAAS,GAAG,IAAI;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,sBAAsB,IAAI,GAAG;AAC/B,aAAO,iBAAiB,MAAM;AAAA,QAC5B,OAAO,OAAOA,WAAU,KAAK,aAAaA,MAAK;AAAA,QAC/C,OAAO,OAAO,MAAM;AAClB,cAAI,EAAE,aAAa,aAAa;AAC9B,mBAAO;AAAA,UACT;AACA,gBAAM,eAAe,KAAK,aAAa,EAAE,IAAI;AAC7C,cAAI,gBAAgB,YAAY,GAAG;AACjC,mBAAO,wBAAwB,cAAc,EAAE,OAAO,EAAE,CAAC;AAAA,UAC3D;AACA,iBAAO,IAAI,WAAW;AAAA,YACpB,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,KAAK,aAAa,IAAI;AAAA,EAC/B;AAAA,EACA,aAAa,MAAM;AACjB,QAAI,EAAE,gBAAgB,WAAW;AAC/B,aAAO,KAAK,eAAe,YAAY,KAAK,MAAM,KAAK,QAAQ,CAAC,CAAC;AAAA,IACnE;AACA,UAAM,aAAa,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC;AAC9C,WAAO,KAAK,eAAe;AAAA,MACzB,WAAW;AAAA,MACX,WAAW,QAAQ,CAAC;AAAA,MACpB,WAAW;AAAA,MACX,CAAC,MAAM,KAAK,IAAI,EAAE,SAAS,CAAC;AAAA,IAC9B;AAAA,EACF;AACF;AAEA,IAAM,kBAAN,cAA8B,aAAa;AAAA,EACzC,YAAY,YAAY,SAAS;AAC/B,UAAM,iBAAiB,IAAI,0BAA0B,OAAO;AAC5D,UAAM,aAAa,IAAI,sBAAsB,cAAc;AAC3D,UAAM,YAAY,IAAI,qBAAqB,YAAY,OAAO;AAC9D,UAAM,WAAW,YAAY,OAAO;AAAA,EACtC;AACF;;;AC1VA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,QAAQ,SAAS,SAAS,WAAW,MAAM,KAAK,UAAU;AAC/D,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,MAAM,KAAK,SAAS,SAAS,MAAM,OAAO;AACxC,UAAM,eAAe,eAAe,SAAS,KAAK,qBAAqB;AACvE,UAAM,gBAAgB,MAAM,KAAK,MAAM,cAAc,EAAE,UAAU,SAAS,GAAG,SAAS,MAAM,KAAK;AACjG,UAAM,eAAe,uBAAuB,aAAa;AACzD,WAAO;AAAA,EACT;AACF;AAEA,IAAM,UAAN,cAAsB,gBAAgB;AAAA,EACpC,YAAY,SAAS;AACnB,UAAM,aAAa,IAAI,gBAAgB,OAAO;AAC9C,UAAM,YAAY,OAAO;AAAA,EAC3B;AACF;", "names": ["value", "value", "value"]}