[{"id": 139, "objectiveType": "encounters", "target": null, "targetAction": null, "quantity": 5, "questStatus": "ready_to_complete", "count": 5, "cashReward": 2600, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "21/5/2024 12:25:40", "updatedAt": "21/5/2024 14:08:28", "itemRewardId": null, "userId": 131}, {"id": 140, "objectiveType": "npc_kill_low_damage", "target": 21, "targetAction": null, "quantity": 4, "questStatus": "ready_to_complete", "count": 4, "cashReward": 0, "xpReward": 500, "itemRewardQuantity": null, "createdAt": "21/5/2024 12:25:40", "updatedAt": "21/5/2024 14:01:32", "itemRewardId": null, "userId": 131}, {"id": 141, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 3, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "21/5/2024 12:25:40", "updatedAt": "21/5/2024 12:25:40", "itemRewardId": 145, "userId": 131}, {"id": 151, "objectiveType": "train_stats", "target": null, "targetAction": null, "quantity": 21, "questStatus": "in_progress", "count": 4, "cashReward": 1500, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "21/5/2024 21:13:07", "updatedAt": "21/5/2024 22:14:59", "itemRewardId": null, "userId": 148}, {"id": 152, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 12, "questStatus": "in_progress", "count": 3, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "21/5/2024 21:13:07", "updatedAt": "21/5/2024 22:22:37", "itemRewardId": 144, "userId": 148}, {"id": 153, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 8, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 400, "itemRewardQuantity": null, "createdAt": "21/5/2024 21:13:07", "updatedAt": "21/5/2024 21:13:07", "itemRewardId": null, "userId": 148}, {"id": 288, "objectiveType": "craft_any", "target": null, "targetAction": null, "quantity": 1, "questStatus": "in_progress", "count": 0, "cashReward": 5800, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "23/5/2024 07:21:36", "updatedAt": "23/5/2024 07:21:36", "itemRewardId": null, "userId": 92}, {"id": 289, "objectiveType": "npc_kill_low_damage", "target": 25, "targetAction": null, "quantity": 4, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 1600, "itemRewardQuantity": null, "createdAt": "23/5/2024 07:21:36", "updatedAt": "23/5/2024 07:21:36", "itemRewardId": null, "userId": 92}, {"id": 290, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "leave", "quantity": 4, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "23/5/2024 07:21:36", "updatedAt": "23/5/2024 07:21:36", "itemRewardId": 144, "userId": 92}, {"id": 381, "objectiveType": "train_stats", "target": null, "targetAction": null, "quantity": 15, "questStatus": "ready_to_complete", "count": 17, "cashReward": 5000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "25/5/2024 00:01:45", "updatedAt": "25/5/2024 05:39:24", "itemRewardId": null, "userId": 140}, {"id": 382, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 8, "questStatus": "complete", "count": 8, "cashReward": 0, "xpReward": 1100, "itemRewardQuantity": null, "createdAt": "25/5/2024 00:01:45", "updatedAt": "25/5/2024 02:31:14", "itemRewardId": null, "userId": 140}, {"id": 383, "objectiveType": "defeat_player_xname", "target": null, "targetAction": "o", "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "25/5/2024 00:01:45", "updatedAt": "25/5/2024 00:01:45", "itemRewardId": 145, "userId": 140}, {"id": 468, "objectiveType": "gambling_slots", "target": null, "targetAction": null, "quantity": 8000, "questStatus": "complete", "count": 8000, "cashReward": 4600, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "26/5/2024 02:52:32", "updatedAt": "26/5/2024 20:32:30", "itemRewardId": null, "userId": 118}, {"id": 469, "objectiveType": "complete_zones", "target": null, "targetAction": null, "quantity": 3, "questStatus": "complete", "count": 3, "cashReward": 0, "xpReward": 1000, "itemRewardQuantity": null, "createdAt": "26/5/2024 02:52:32", "updatedAt": "26/5/2024 19:21:25", "itemRewardId": null, "userId": 118}, {"id": 470, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "mug", "quantity": 4, "questStatus": "complete", "count": 4, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "26/5/2024 02:52:32", "updatedAt": "26/5/2024 20:32:31", "itemRewardId": 144, "userId": 118}, {"id": 558, "objectiveType": "shrine_donation", "target": null, "targetAction": null, "quantity": 1300, "questStatus": "in_progress", "count": 0, "cashReward": 3900, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "27/5/2024 03:48:12", "updatedAt": "27/5/2024 03:48:12", "itemRewardId": null, "userId": 99}, {"id": 559, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 7, "questStatus": "complete", "count": 7, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "27/5/2024 03:48:12", "updatedAt": "27/5/2024 04:13:43", "itemRewardId": 144, "userId": 99}, {"id": 560, "objectiveType": "defeat_player", "target": 9, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 1200, "itemRewardQuantity": null, "createdAt": "27/5/2024 03:48:12", "updatedAt": "27/5/2024 03:48:12", "itemRewardId": null, "userId": 99}, {"id": 666, "objectiveType": "encounters", "target": null, "targetAction": null, "quantity": 6, "questStatus": "in_progress", "count": 3, "cashReward": 1200, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "29/5/2024 01:06:39", "updatedAt": "29/5/2024 01:11:17", "itemRewardId": null, "userId": 165}, {"id": 667, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 9, "questStatus": "complete", "count": 9, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "29/5/2024 01:06:39", "updatedAt": "29/5/2024 03:26:25", "itemRewardId": 144, "userId": 165}, {"id": 668, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 7, "questStatus": "in_progress", "count": 4, "cashReward": 0, "xpReward": 400, "itemRewardQuantity": null, "createdAt": "29/5/2024 01:06:39", "updatedAt": "29/5/2024 04:54:44", "itemRewardId": null, "userId": 165}, {"id": 816, "objectiveType": "shrine_donation", "target": null, "targetAction": null, "quantity": 3500, "questStatus": "in_progress", "count": 0, "cashReward": 9400, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "31/5/2024 05:16:07", "updatedAt": "31/5/2024 05:16:07", "itemRewardId": null, "userId": 91}, {"id": 817, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 2000, "itemRewardQuantity": null, "createdAt": "31/5/2024 05:16:07", "updatedAt": "31/5/2024 05:16:07", "itemRewardId": null, "userId": 91}, {"id": 818, "objectiveType": "pvp_kill", "target": null, "targetAction": null, "quantity": 6, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "31/5/2024 05:16:07", "updatedAt": "31/5/2024 05:16:07", "itemRewardId": 151, "userId": 91}, {"id": 870, "objectiveType": "shrine_donation", "target": null, "targetAction": null, "quantity": 650, "questStatus": "in_progress", "count": 0, "cashReward": 3800, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "1/6/2024 04:18:28", "updatedAt": "1/6/2024 04:18:28", "itemRewardId": null, "userId": 161}, {"id": 871, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 10, "questStatus": "in_progress", "count": 6, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "1/6/2024 04:18:28", "updatedAt": "1/6/2024 13:09:16", "itemRewardId": 144, "userId": 161}, {"id": 872, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 1100, "itemRewardQuantity": null, "createdAt": "1/6/2024 04:18:28", "updatedAt": "1/6/2024 04:18:28", "itemRewardId": null, "userId": 161}, {"id": 963, "objectiveType": "encounters", "target": null, "targetAction": null, "quantity": 9, "questStatus": "in_progress", "count": 0, "cashReward": 5300, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "2/6/2024 16:26:59", "updatedAt": "2/6/2024 16:26:59", "itemRewardId": null, "userId": 88}, {"id": 964, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 4, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "2/6/2024 16:26:59", "updatedAt": "2/6/2024 16:26:59", "itemRewardId": 144, "userId": 88}, {"id": 965, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 2000, "itemRewardQuantity": null, "createdAt": "2/6/2024 16:26:59", "updatedAt": "2/6/2024 16:26:59", "itemRewardId": null, "userId": 88}, {"id": 1224, "objectiveType": "suggestion_vote", "target": null, "targetAction": null, "quantity": 4, "questStatus": "in_progress", "count": 0, "cashReward": 1800, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "6/6/2024 04:58:11", "updatedAt": "6/6/2024 04:58:11", "itemRewardId": null, "userId": 175}, {"id": 1225, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 4, "questStatus": "in_progress", "count": 1, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 1, "createdAt": "6/6/2024 04:58:11", "updatedAt": "6/6/2024 07:15:33", "itemRewardId": 148, "userId": 175}, {"id": 1226, "objectiveType": "defeat_player_xname", "target": null, "targetAction": "e", "quantity": 4, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 700, "itemRewardQuantity": null, "createdAt": "6/6/2024 04:58:11", "updatedAt": "6/6/2024 04:58:11", "itemRewardId": null, "userId": 175}, {"id": 1353, "objectiveType": "complete_mission", "target": null, "targetAction": null, "quantity": 2, "questStatus": "complete", "count": 2, "cashReward": 17000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "8/6/2024 02:29:36", "updatedAt": "8/6/2024 15:33:51", "itemRewardId": null, "userId": 19}, {"id": 1354, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 4, "questStatus": "complete", "count": 4, "cashReward": 0, "xpReward": 7800, "itemRewardQuantity": null, "createdAt": "8/6/2024 02:29:36", "updatedAt": "8/6/2024 13:25:40", "itemRewardId": null, "userId": 19}, {"id": 1355, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 1, "questStatus": "complete", "count": 1, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "8/6/2024 02:29:36", "updatedAt": "8/6/2024 04:09:59", "itemRewardId": 144, "userId": 19}, {"id": 1431, "objectiveType": "train_stats", "target": null, "targetAction": null, "quantity": 28, "questStatus": "in_progress", "count": 6, "cashReward": 15000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "9/6/2024 11:53:47", "updatedAt": "9/6/2024 11:54:02", "itemRewardId": null, "userId": 62}, {"id": 1432, "objectiveType": "npc_kill_turns", "target": 4, "targetAction": null, "quantity": 2, "questStatus": "complete", "count": 2, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "9/6/2024 11:53:47", "updatedAt": "9/6/2024 11:57:39", "itemRewardId": 145, "userId": 62}, {"id": 1433, "objectiveType": "defeat_player_xname", "target": null, "targetAction": "e", "quantity": 3, "questStatus": "complete", "count": 3, "cashReward": 0, "xpReward": 7600, "itemRewardQuantity": null, "createdAt": "9/6/2024 11:53:47", "updatedAt": "9/6/2024 11:55:44", "itemRewardId": null, "userId": 62}, {"id": 1578, "objectiveType": "shrine_donation", "target": null, "targetAction": null, "quantity": 2800, "questStatus": "complete", "count": 2800, "cashReward": 13000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/6/2024 00:35:01", "updatedAt": "12/6/2024 00:35:17", "itemRewardId": null, "userId": 69}, {"id": 1579, "objectiveType": "complete_zones", "target": null, "targetAction": null, "quantity": 4, "questStatus": "in_progress", "count": 2, "cashReward": 0, "xpReward": 4700, "itemRewardQuantity": null, "createdAt": "12/6/2024 00:35:01", "updatedAt": "12/6/2024 01:29:05", "itemRewardId": null, "userId": 69}, {"id": 1580, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 7, "questStatus": "in_progress", "count": 2, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 1, "createdAt": "12/6/2024 00:35:01", "updatedAt": "12/6/2024 01:29:05", "itemRewardId": 132, "userId": 69}, {"id": 1692, "objectiveType": "encounters", "target": null, "targetAction": null, "quantity": 9, "questStatus": "in_progress", "count": 0, "cashReward": 5600, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "14/6/2024 00:01:06", "updatedAt": "14/6/2024 00:01:06", "itemRewardId": null, "userId": 172}, {"id": 1693, "objectiveType": "npc_kill_low_damage", "target": 22, "targetAction": null, "quantity": 4, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 1200, "itemRewardQuantity": null, "createdAt": "14/6/2024 00:01:06", "updatedAt": "14/6/2024 00:01:06", "itemRewardId": null, "userId": 172}, {"id": 1694, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "14/6/2024 00:01:06", "updatedAt": "14/6/2024 00:01:06", "itemRewardId": 144, "userId": 172}, {"id": 1782, "objectiveType": "train_stats", "target": null, "targetAction": null, "quantity": 25, "questStatus": "in_progress", "count": 9, "cashReward": 16000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "15/6/2024 05:52:59", "updatedAt": "15/6/2024 21:00:41", "itemRewardId": null, "userId": 89}, {"id": 1783, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 3, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 5100, "itemRewardQuantity": null, "createdAt": "15/6/2024 05:52:59", "updatedAt": "15/6/2024 05:52:59", "itemRewardId": null, "userId": 89}, {"id": 1784, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "cripple", "quantity": 3, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "15/6/2024 05:52:59", "updatedAt": "15/6/2024 05:52:59", "itemRewardId": 145, "userId": 89}, {"id": 1929, "objectiveType": "craft_any", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 1300, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "18/6/2024 05:48:07", "updatedAt": "18/6/2024 05:48:07", "itemRewardId": null, "userId": 195}, {"id": 1930, "objectiveType": "complete_zones", "target": null, "targetAction": null, "quantity": 5, "questStatus": "ready_to_complete", "count": 5, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "18/6/2024 05:48:07", "updatedAt": "18/6/2024 08:47:56", "itemRewardId": 147, "userId": 195}, {"id": 1931, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 8, "questStatus": "in_progress", "count": 6, "cashReward": 0, "xpReward": 400, "itemRewardQuantity": null, "createdAt": "18/6/2024 05:48:07", "updatedAt": "18/6/2024 10:33:23", "itemRewardId": null, "userId": 195}, {"id": 2157, "objectiveType": "shrine_donation", "target": null, "targetAction": null, "quantity": 600, "questStatus": "in_progress", "count": 0, "cashReward": 4000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "22/6/2024 18:45:10", "updatedAt": "22/6/2024 18:45:10", "itemRewardId": null, "userId": 196}, {"id": 2158, "objectiveType": "use_skill", "target": null, "targetAction": null, "quantity": 14, "questStatus": "in_progress", "count": 11, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "22/6/2024 18:45:10", "updatedAt": "22/6/2024 22:37:30", "itemRewardId": 145, "userId": 196}, {"id": 2159, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "cripple", "quantity": 4, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 1100, "itemRewardQuantity": null, "createdAt": "22/6/2024 18:45:10", "updatedAt": "22/6/2024 18:45:10", "itemRewardId": null, "userId": 196}, {"id": 2202, "objectiveType": "craft_any", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 3900, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "23/6/2024 12:51:24", "updatedAt": "23/6/2024 12:51:24", "itemRewardId": null, "userId": 96}, {"id": 2203, "objectiveType": "use_skill", "target": null, "targetAction": null, "quantity": 18, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 1, "createdAt": "23/6/2024 12:51:24", "updatedAt": "23/6/2024 12:51:24", "itemRewardId": 140, "userId": 96}, {"id": 2204, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 7, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 1300, "itemRewardQuantity": null, "createdAt": "23/6/2024 12:51:24", "updatedAt": "23/6/2024 12:51:24", "itemRewardId": null, "userId": 96}, {"id": 2205, "objectiveType": "gambling_slots", "target": null, "targetAction": null, "quantity": 19200, "questStatus": "in_progress", "count": 0, "cashReward": 11000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "23/6/2024 17:58:03", "updatedAt": "23/6/2024 17:58:03", "itemRewardId": null, "userId": 169}, {"id": 2206, "objectiveType": "win_any_battles", "target": null, "targetAction": null, "quantity": 9, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 2200, "itemRewardQuantity": null, "createdAt": "23/6/2024 17:58:03", "updatedAt": "23/6/2024 17:58:03", "itemRewardId": null, "userId": 169}, {"id": 2207, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "cripple", "quantity": 4, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 1, "createdAt": "23/6/2024 17:58:03", "updatedAt": "23/6/2024 17:58:03", "itemRewardId": 150, "userId": 169}, {"id": 2274, "objectiveType": "suggestion_vote", "target": null, "targetAction": null, "quantity": 4, "questStatus": "complete", "count": 4, "cashReward": 14000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "25/6/2024 01:19:08", "updatedAt": "25/6/2024 06:45:04", "itemRewardId": null, "userId": 156}, {"id": 2275, "objectiveType": "npc_kill_low_damage", "target": 25, "targetAction": null, "quantity": 4, "questStatus": "complete", "count": 4, "cashReward": 0, "xpReward": 6000, "itemRewardQuantity": null, "createdAt": "25/6/2024 01:19:08", "updatedAt": "25/6/2024 06:43:48", "itemRewardId": null, "userId": 156}, {"id": 2276, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 3, "questStatus": "complete", "count": 3, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "25/6/2024 01:19:08", "updatedAt": "25/6/2024 14:32:14", "itemRewardId": 144, "userId": 156}, {"id": 2439, "objectiveType": "train_stats", "target": null, "targetAction": null, "quantity": 28, "questStatus": "in_progress", "count": 18, "cashReward": 8300, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "29/6/2024 01:37:47", "updatedAt": "29/6/2024 04:31:45", "itemRewardId": null, "userId": 170}, {"id": 2440, "objectiveType": "npc_kill_turns", "target": 5, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 1, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "29/6/2024 01:37:47", "updatedAt": "29/6/2024 02:33:35", "itemRewardId": 144, "userId": 170}, {"id": 2441, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 2, "questStatus": "complete", "count": 2, "cashReward": 0, "xpReward": 2800, "itemRewardQuantity": null, "createdAt": "29/6/2024 01:37:47", "updatedAt": "29/6/2024 01:39:58", "itemRewardId": null, "userId": 170}, {"id": 2544, "objectiveType": "complete_mission", "target": null, "targetAction": null, "quantity": 1, "questStatus": "in_progress", "count": 0, "cashReward": 5300, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "1/7/2024 12:28:58", "updatedAt": "1/7/2024 12:28:58", "itemRewardId": null, "userId": 16}, {"id": 2545, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "1/7/2024 12:28:58", "updatedAt": "1/7/2024 12:28:58", "itemRewardId": 145, "userId": 16}, {"id": 2546, "objectiveType": "pvp_kill", "target": null, "targetAction": null, "quantity": 6, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 2000, "itemRewardQuantity": null, "createdAt": "1/7/2024 12:28:58", "updatedAt": "1/7/2024 12:28:58", "itemRewardId": null, "userId": 16}, {"id": 2547, "objectiveType": "craft_any", "target": null, "targetAction": null, "quantity": 1, "questStatus": "in_progress", "count": 0, "cashReward": 9700, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "1/7/2024 13:24:44", "updatedAt": "1/7/2024 13:24:44", "itemRewardId": null, "userId": 105}, {"id": 2548, "objectiveType": "use_skill", "target": null, "targetAction": null, "quantity": 14, "questStatus": "in_progress", "count": 1, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "1/7/2024 13:24:44", "updatedAt": "1/7/2024 13:25:58", "itemRewardId": 145, "userId": 105}, {"id": 2549, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "leave", "quantity": 5, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 2900, "itemRewardQuantity": null, "createdAt": "1/7/2024 13:24:44", "updatedAt": "1/7/2024 13:24:44", "itemRewardId": null, "userId": 105}, {"id": 2727, "objectiveType": "gambling_slots", "target": null, "targetAction": null, "quantity": 28000, "questStatus": "in_progress", "count": 0, "cashReward": 14000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "6/7/2024 17:57:47", "updatedAt": "6/7/2024 17:57:47", "itemRewardId": null, "userId": 51}, {"id": 2728, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 9, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "6/7/2024 17:57:47", "updatedAt": "6/7/2024 17:57:47", "itemRewardId": 145, "userId": 51}, {"id": 2729, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 8, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 10300, "itemRewardQuantity": null, "createdAt": "6/7/2024 17:57:47", "updatedAt": "6/7/2024 17:57:47", "itemRewardId": null, "userId": 51}, {"id": 2757, "objectiveType": "complete_mission", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 7900, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "7/7/2024 16:30:46", "updatedAt": "7/7/2024 16:30:46", "itemRewardId": null, "userId": 143}, {"id": 2758, "objectiveType": "use_skill", "target": null, "targetAction": null, "quantity": 17, "questStatus": "in_progress", "count": 2, "cashReward": 0, "xpReward": 1700, "itemRewardQuantity": null, "createdAt": "7/7/2024 16:30:46", "updatedAt": "7/7/2024 16:31:32", "itemRewardId": null, "userId": 143}, {"id": 2759, "objectiveType": "defeat_player", "target": 16, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "7/7/2024 16:30:46", "updatedAt": "7/7/2024 16:30:46", "itemRewardId": 145, "userId": 143}, {"id": 2844, "objectiveType": "craft_any", "target": null, "targetAction": null, "quantity": 3, "questStatus": "in_progress", "count": 1, "cashReward": 14000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "10/7/2024 13:23:15", "updatedAt": "10/7/2024 15:10:27", "itemRewardId": null, "userId": 77}, {"id": 2845, "objectiveType": "npc_kill_turns", "target": 4, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 6300, "itemRewardQuantity": null, "createdAt": "10/7/2024 13:23:15", "updatedAt": "10/7/2024 13:23:15", "itemRewardId": null, "userId": 77}, {"id": 2846, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 3, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "10/7/2024 13:23:15", "updatedAt": "10/7/2024 13:23:15", "itemRewardId": 144, "userId": 77}, {"id": 2865, "objectiveType": "gambling_slots", "target": null, "targetAction": null, "quantity": 32800, "questStatus": "in_progress", "count": 0, "cashReward": 16000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "11/7/2024 02:17:11", "updatedAt": "11/7/2024 02:17:11", "itemRewardId": null, "userId": 3}, {"id": 2866, "objectiveType": "use_skill", "target": null, "targetAction": null, "quantity": 18, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 1, "createdAt": "11/7/2024 02:17:11", "updatedAt": "11/7/2024 02:17:11", "itemRewardId": 150, "userId": 3}, {"id": 2873, "objectiveType": "craft_any", "target": null, "targetAction": null, "quantity": 3, "questStatus": "in_progress", "count": 0, "cashReward": 17000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "11/7/2024 05:37:22", "updatedAt": "11/7/2024 05:37:22", "itemRewardId": null, "userId": 57}, {"id": 2874, "objectiveType": "win_any_battles", "target": null, "targetAction": null, "quantity": 14, "questStatus": "in_progress", "count": 9, "cashReward": 0, "xpReward": 9700, "itemRewardQuantity": null, "createdAt": "11/7/2024 05:37:22", "updatedAt": "11/7/2024 22:26:03", "itemRewardId": null, "userId": 57}, {"id": 2875, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "cripple", "quantity": 5, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "11/7/2024 05:37:22", "updatedAt": "11/7/2024 05:37:22", "itemRewardId": 151, "userId": 57}, {"id": 2879, "objectiveType": "train_stats", "target": null, "targetAction": null, "quantity": 25, "questStatus": "in_progress", "count": 0, "cashReward": 9700, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "11/7/2024 09:35:09", "updatedAt": "11/7/2024 09:35:09", "itemRewardId": null, "userId": 181}, {"id": 2880, "objectiveType": "use_skill", "target": null, "targetAction": null, "quantity": 16, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 2200, "itemRewardQuantity": null, "createdAt": "11/7/2024 09:35:09", "updatedAt": "11/7/2024 09:35:09", "itemRewardId": null, "userId": 181}, {"id": 2881, "objectiveType": "pvp_kill", "target": null, "targetAction": null, "quantity": 8, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "11/7/2024 09:35:09", "updatedAt": "11/7/2024 09:35:09", "itemRewardId": 145, "userId": 181}, {"id": 2885, "objectiveType": "suggestion_vote", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 15000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "11/7/2024 11:01:20", "updatedAt": "11/7/2024 11:01:20", "itemRewardId": null, "userId": 3}, {"id": 2886, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 9, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 1, "createdAt": "11/7/2024 11:01:20", "updatedAt": "11/7/2024 11:01:20", "itemRewardId": 150, "userId": 3}, {"id": 2890, "objectiveType": "gambling_slots", "target": null, "targetAction": null, "quantity": 31200, "questStatus": "in_progress", "count": 0, "cashReward": 19000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 00:09:40", "updatedAt": "12/7/2024 00:09:40", "itemRewardId": null, "userId": 60}, {"id": 2891, "objectiveType": "npc_kill_low_damage", "target": 15, "targetAction": null, "quantity": 3, "questStatus": "complete", "count": 3, "cashReward": 0, "xpReward": 6400, "itemRewardQuantity": null, "createdAt": "12/7/2024 00:09:40", "updatedAt": "12/7/2024 00:53:49", "itemRewardId": null, "userId": 60}, {"id": 2892, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 1, "questStatus": "complete", "count": 1, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "12/7/2024 00:09:40", "updatedAt": "12/7/2024 00:10:28", "itemRewardId": 147, "userId": 60}, {"id": 2893, "objectiveType": "suggestion_vote", "target": null, "targetAction": null, "quantity": 2, "questStatus": "complete", "count": 2, "cashReward": 20000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 02:57:04", "updatedAt": "12/7/2024 02:57:42", "itemRewardId": null, "userId": 49}, {"id": 2894, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 6, "questStatus": "complete", "count": 6, "cashReward": 0, "xpReward": 6800, "itemRewardQuantity": null, "createdAt": "12/7/2024 02:57:04", "updatedAt": "12/7/2024 05:35:58", "itemRewardId": null, "userId": 49}, {"id": 2895, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 6, "questStatus": "complete", "count": 6, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "12/7/2024 02:57:04", "updatedAt": "12/7/2024 07:26:24", "itemRewardId": 144, "userId": 49}, {"id": 2896, "objectiveType": "encounters", "target": null, "targetAction": null, "quantity": 10, "questStatus": "complete", "count": 10, "cashReward": 15000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 03:19:52", "updatedAt": "12/7/2024 13:28:37", "itemRewardId": null, "userId": 12}, {"id": 2897, "objectiveType": "npc_kill_low_damage", "target": 18, "targetAction": null, "quantity": 2, "questStatus": "complete", "count": 2, "cashReward": 0, "xpReward": 9600, "itemRewardQuantity": null, "createdAt": "12/7/2024 03:19:52", "updatedAt": "12/7/2024 03:23:22", "itemRewardId": null, "userId": 12}, {"id": 2898, "objectiveType": "defeat_player", "target": 19, "targetAction": null, "quantity": 2, "questStatus": "complete", "count": 2, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "12/7/2024 03:19:52", "updatedAt": "12/7/2024 13:28:38", "itemRewardId": 144, "userId": 12}, {"id": 2899, "objectiveType": "gambling_slots", "target": null, "targetAction": null, "quantity": 31200, "questStatus": "complete", "count": 31200, "cashReward": 14000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 04:29:34", "updatedAt": "12/7/2024 15:29:45", "itemRewardId": null, "userId": 39}, {"id": 2900, "objectiveType": "npc_kill_turns", "target": 4, "targetAction": null, "quantity": 4, "questStatus": "complete", "count": 4, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "12/7/2024 04:29:34", "updatedAt": "12/7/2024 15:30:41", "itemRewardId": 144, "userId": 39}, {"id": 2901, "objectiveType": "collect_bounties", "target": null, "targetAction": null, "quantity": 2, "questStatus": "complete", "count": 2, "cashReward": 0, "xpReward": 13500, "itemRewardQuantity": null, "createdAt": "12/7/2024 04:29:34", "updatedAt": "12/7/2024 15:17:14", "itemRewardId": null, "userId": 39}, {"id": 2902, "objectiveType": "gambling_slots", "target": null, "targetAction": null, "quantity": 32000, "questStatus": "in_progress", "count": 0, "cashReward": 18000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 05:25:18", "updatedAt": "12/7/2024 05:25:18", "itemRewardId": null, "userId": 56}, {"id": 2903, "objectiveType": "complete_zones", "target": null, "targetAction": null, "quantity": 3, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 1, "createdAt": "12/7/2024 05:25:18", "updatedAt": "12/7/2024 05:25:18", "itemRewardId": 150, "userId": 56}, {"id": 2904, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 8, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 14100, "itemRewardQuantity": null, "createdAt": "12/7/2024 05:25:18", "updatedAt": "12/7/2024 05:25:18", "itemRewardId": null, "userId": 56}, {"id": 2905, "objectiveType": "gambling_slots", "target": null, "targetAction": null, "quantity": 12000, "questStatus": "in_progress", "count": 400, "cashReward": 7400, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 06:16:32", "updatedAt": "12/7/2024 09:09:24", "itemRewardId": null, "userId": 38}, {"id": 2906, "objectiveType": "win_any_battles", "target": null, "targetAction": null, "quantity": 11, "questStatus": "in_progress", "count": 3, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "12/7/2024 06:16:32", "updatedAt": "12/7/2024 09:09:09", "itemRewardId": 145, "userId": 38}, {"id": 2907, "objectiveType": "defeat_player", "target": 9, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 1, "cashReward": 0, "xpReward": 2000, "itemRewardQuantity": null, "createdAt": "12/7/2024 06:16:32", "updatedAt": "12/7/2024 06:16:56", "itemRewardId": null, "userId": 38}, {"id": 2908, "objectiveType": "craft_any", "target": null, "targetAction": null, "quantity": 2, "questStatus": "complete", "count": 2, "cashReward": 17000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 06:20:41", "updatedAt": "12/7/2024 06:46:41", "itemRewardId": null, "userId": 15}, {"id": 2909, "objectiveType": "npc_kill", "target": null, "targetAction": null, "quantity": 8, "questStatus": "ready_to_complete", "count": 8, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "12/7/2024 06:20:41", "updatedAt": "12/7/2024 09:51:04", "itemRewardId": 144, "userId": 15}, {"id": 2910, "objectiveType": "pvp_post_battle_choice", "target": null, "targetAction": "leave", "quantity": 4, "questStatus": "in_progress", "count": 1, "cashReward": 0, "xpReward": 14000, "itemRewardQuantity": null, "createdAt": "12/7/2024 06:20:41", "updatedAt": "12/7/2024 06:47:05", "itemRewardId": null, "userId": 15}, {"id": 2911, "objectiveType": "craft_any", "target": null, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 1, "cashReward": 9600, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 12:21:18", "updatedAt": "12/7/2024 12:21:32", "itemRewardId": null, "userId": 17}, {"id": 2912, "objectiveType": "win_any_battles", "target": null, "targetAction": null, "quantity": 12, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 3400, "itemRewardQuantity": null, "createdAt": "12/7/2024 12:21:18", "updatedAt": "12/7/2024 12:21:18", "itemRewardId": null, "userId": 17}, {"id": 2913, "objectiveType": "defeat_player", "target": 4, "targetAction": null, "quantity": 2, "questStatus": "in_progress", "count": 0, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "12/7/2024 12:21:18", "updatedAt": "12/7/2024 12:21:18", "itemRewardId": 145, "userId": 17}, {"id": 2914, "objectiveType": "shrine_donation", "target": null, "targetAction": null, "quantity": 3900, "questStatus": "in_progress", "count": 0, "cashReward": 14000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 15:24:52", "updatedAt": "12/7/2024 15:24:52", "itemRewardId": null, "userId": 48}, {"id": 2915, "objectiveType": "win_any_battles", "target": null, "targetAction": null, "quantity": 10, "questStatus": "complete", "count": 10, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 1, "createdAt": "12/7/2024 15:24:52", "updatedAt": "12/7/2024 19:46:50", "itemRewardId": 140, "userId": 48}, {"id": 2916, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 8, "questStatus": "complete", "count": 8, "cashReward": 0, "xpReward": 12500, "itemRewardQuantity": null, "createdAt": "12/7/2024 15:24:52", "updatedAt": "12/7/2024 19:46:48", "itemRewardId": null, "userId": 48}, {"id": 2917, "objectiveType": "train_stats", "target": null, "targetAction": null, "quantity": 26, "questStatus": "in_progress", "count": 2, "cashReward": 14000, "xpReward": 0, "itemRewardQuantity": null, "createdAt": "12/7/2024 17:43:40", "updatedAt": "12/7/2024 18:07:05", "itemRewardId": null, "userId": 87}, {"id": 2918, "objectiveType": "win_any_battles", "target": null, "targetAction": null, "quantity": 12, "questStatus": "in_progress", "count": 2, "cashReward": 0, "xpReward": 0, "itemRewardQuantity": 2, "createdAt": "12/7/2024 17:43:40", "updatedAt": "12/7/2024 18:07:13", "itemRewardId": 144, "userId": 87}, {"id": 2919, "objectiveType": "npc_boss_kill", "target": null, "targetAction": null, "quantity": 7, "questStatus": "in_progress", "count": 1, "cashReward": 0, "xpReward": 10600, "itemRewardQuantity": null, "createdAt": "12/7/2024 17:43:40", "updatedAt": "12/7/2024 17:54:31", "itemRewardId": null, "userId": 87}]