import { HEALTH_THRESHOLD, PRIORITY_MULTIPLIERS } from "./battle.ai.js";
import type { BattlePlayer, EquippedAbility } from "../types/battle.types.js";

export const evaluateHealOverTimeAbility = (
    aiPlayer: BattlePlayer,
    defender: BattlePlayer,
    healthPercentage: number,
    missingHealthAmount: number,
    staminaCost: number
): number => {
    let score;
    // Base healing calculation: 15% of max health over 3 turns
    const totalHealingPotential = aiPlayer.maxHealth * 0.15 * 3;

    // Give higher priority when health is low
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score = PRIORITY_MULTIPLIERS.LOW_HEALTH_HEALING;
    } else if (healthPercentage < 0.4) {
        score = 3.0; // Priority for below 40% health
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score = 1.5;
    } else if (healthPercentage < HEALTH_THRESHOLD.MEDIUM) {
        score = 0.7;
    } else {
        score = 0.2; // Low priority when health is high
    }

    // Reduce score if potential healing greatly exceeds missing health
    if (totalHealingPotential > missingHealthAmount * 2) {
        score *= 0.6;
    }

    // Check if enemy is close to death - maybe save stamina for attacks
    const enemyHealthPercentage = defender.currentHealth / defender.maxHealth;
    if (enemyHealthPercentage < 0.3 && healthPercentage > 0.4) {
        score *= 0.7; // Reduce healing priority if we can finish off enemy
    }

    if (aiPlayer.currentStamina < staminaCost * 1.5) {
        score *= 0.8; // Reduce priority when stamina is limited
    }

    return score;
};

export const evaluateMaxHPHealAbility = (
    aiPlayer: BattlePlayer,
    defender: BattlePlayer,
    healthPercentage: number,
    missingHealthAmount: number,
    staminaCost: number
): number => {
    let score;
    // Base healing calculation: 75% of max health immediately
    const healingAmount = aiPlayer.maxHealth * 0.75;

    // Give higher priority when health is very low
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score = PRIORITY_MULTIPLIERS.LOW_HEALTH_HEALING + 1.0; // Higher than HoT at critical health
    } else if (healthPercentage < 0.4) {
        score = 3.5; // Priority for below 40% health
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score = 1.7;
    } else {
        score = 0.1; // Very low priority when health is not low
    }

    // Prevent wasting the big heal - reduce score if healing would be wasted
    if (healingAmount > missingHealthAmount * 1.5) {
        score *= 0.4;
    }

    // Consider stamina - this is a high cost ability
    if (aiPlayer.currentStamina < staminaCost * 1.3) {
        score *= 0.7; // Significant penalty when stamina is limited
    }

    // Consider context of the battle
    const enemyHealthPercentage = defender.currentHealth / defender.maxHealth;
    if (enemyHealthPercentage < 0.2 && healthPercentage > 0.3) {
        score *= 0.5; // Greatly reduce priority if enemy is almost dead
    }

    return score;
};

export const evaluateHealingAbility = (
    ability: EquippedAbility,
    aiPlayer: BattlePlayer,
    defender: BattlePlayer
): number => {
    let score = 1.0;
    const healthPercentage = aiPlayer.currentHealth / aiPlayer.maxHealth;
    const missingHealthAmount = aiPlayer.maxHealth - aiPlayer.currentHealth;

    if (!ability.staminaCost) {
        return 0;
    }

    switch (ability.name) {
        case "heal_over_time": {
            score = evaluateHealOverTimeAbility(
                aiPlayer,
                defender,
                healthPercentage,
                missingHealthAmount,
                ability.staminaCost
            );
            break;
        }
        case "max_hp_heal": {
            score = evaluateMaxHPHealAbility(
                aiPlayer,
                defender,
                healthPercentage,
                missingHealthAmount,
                ability.staminaCost
            );
            break;
        }
    }

    // Consider stamina efficiency
    if (aiPlayer.currentStamina < ability.staminaCost * 1.5) {
        score *= 0.8; // Reduce priority when stamina is limited
    }

    return score;
};
