const default_story_chapters = [
    // Season 1: Academy Foundations - Chapters
    {
        id: 1,
        seasonId: 1,
        name: "Arrival",
        description:
            "Your first day at Chikara Academy. Meet fellow students, explore the grounds, and begin to understand the academy's traditions.",
        order: 1,
        unlockDate: new Date("2024-01-01T00:00:00Z"),
        requiredLevel: 1,
        requiredChapterIds: null,
    },
    {
        id: 2,
        seasonId: 1,
        name: "Finding Your Path",
        description:
            "Choose your martial arts focus and meet potential mentors. Each path offers different opportunities and challenges.",
        order: 2,
        unlockDate: new Date("2024-01-15T00:00:00Z"),
        requiredLevel: 3,
        requiredChapterIds: JSON.stringify([1]),
    },
    {
        id: 3,
        seasonId: 1,
        name: "Rivalries Form",
        description:
            "Social dynamics emerge as students compete for recognition. Form alliances and navigate the complex relationships within the academy.",
        order: 3,
        unlockDate: new Date("2024-02-01T00:00:00Z"),
        requiredLevel: 5,
        requiredChapterIds: JSON.stringify([2]),
    },
    {
        id: 4,
        seasonId: 1,
        name: "Hidden Depths",
        description:
            "Discover the academy's secrets and access advanced training areas. Not everything is as it seems at Chikara Academy.",
        order: 4,
        unlockDate: new Date("2024-02-22T00:00:00Z"),
        requiredLevel: 8,
        requiredChapterIds: JSON.stringify([3]),
    },
    {
        id: 5,
        seasonId: 1,
        name: "First Tournament",
        description:
            "Prove your abilities in the academy's internal tournament. Your performance will determine your standing among your peers.",
        order: 5,
        unlockDate: new Date("2024-03-15T00:00:00Z"),
        requiredLevel: 12,
        requiredChapterIds: JSON.stringify([4]),
    },

    // Season 2: Rising Tensions - Chapters (Preview)
    {
        id: 6,
        seasonId: 2,
        name: "New Challenges",
        description: "Advanced training begins as you face more experienced opponents and complex moral dilemmas.",
        order: 1,
        unlockDate: new Date("2024-07-01T00:00:00Z"),
        requiredLevel: 15,
        requiredChapterIds: null,
    },
    {
        id: 7,
        seasonId: 2,
        name: "Political Intrigue",
        description: "Navigate the complex politics between different martial arts schools and their philosophies.",
        order: 2,
        unlockDate: new Date("2024-07-22T00:00:00Z"),
        requiredLevel: 18,
        requiredChapterIds: JSON.stringify([6]),
    },
];

export default default_story_chapters;
