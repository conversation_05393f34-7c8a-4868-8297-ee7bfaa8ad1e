import{e as D,g as F,c as u,l as U,aj as xe,j as e,ak as he,h as M,ag as fe,al as Y,am as O,C as L,b as z,an as m,S as ge,r as c,v as H,ad as X,y as B,B as ye,a5 as Q,a7 as je,t as Z,m as be,ae as we,ao as Ne,N as ve,ap as Ie,a as Ce,aq as V}from"./index--cEnoMkg.js";import{r as ee}from"./rarityColours-Bvm3hwLY.js";import{D as ke}from"./DataTable-CSAwvRw3.js";import{u as Ee}from"./useGetInventory-O1uR82rJ.js";import"./ag-theme-quartz-2665BHd2.js";const te=()=>{const r=D(),t=F(u.user.equip.mutationOptions({onMutate:async n=>{const i=n._userItem;await r.cancelQueries({queryKey:u.user.getEquippedItems.key()});const o=r.getQueryData(u.user.getEquippedItems.key());return r.setQueryData(u.user.getEquippedItems.key(),f=>({...{...f},[i.itemType]:i})),{previousEquippedItems:o}},onError:(n,i,o)=>{r.setQueryData(u.user.getEquippedItems.key(),o.previousEquippedItems),U.error(n.message||"An error occurred")},onSettled:async()=>{await r.invalidateQueries({queryKey:u.user.getEquippedItems.key()})},onSuccess:async()=>{await r.invalidateQueries({queryKey:u.user.equippedItems.key()}),U.success("Item equipped!")}}));return{equipItem:{...t,mutate:n=>{const{currentUser:i,userItem:o}=n;return i?.hospitalisedUntil>0?(U.error("Can't equip items while hospitalised!"),Promise.reject(new Error("Can't equip items while hospitalised!"))):i?.jailedUntil>0?(U.error("Can't equip items while jailed!"),Promise.reject(new Error("Can't equip items while jailed!"))):t.mutate({userItemId:o.id,_userItem:o})}}}},Se=({inventory:r,currentUser:t,equippedItems:s})=>{const{equipItem:n}=te(),{hideItemTooltip:i,setHideItemTooltip:o}=xe(),f=a=>Object.entries(a).map(([x,j])=>{const g=(j-1)*100;return e.jsxs("p",{className:"text-base",children:[e.jsxs("span",{className:"text-custom-yellow",children:["+",Math.round(x==="health"?j:g)]}),x!=="health"&&"%"," ",x.replace("health","HP").replace("strength","STR").replace("dexterity","DEX").replace("intelligence","INT").replace("lifesteal","LIFE STEAL").replace("defence","DEF").replace("npcDamage","Increased DMG vs NPCs").replace("fleeChance","chance to Flee").replace("encounterReward","Yen gained from encounters")]},x)}),p=a=>{const x=s?.[a.itemType]||null;if(!x)return!1;if(a.id===x.id)return!0};return e.jsx(he,{openOnClick:!0,clickable:!0,id:"equip-tooltip",afterHide:()=>i&&o(!1),className:"pointer-events-auto z-600 max-h-[40dvh] overflow-y-auto overflow-x-hidden border border-gray-600/50",opacity:"1",style:{backgroundColor:"rgba(7, 6, 7, 0.97)",color:"#FFF",padding:0},render:()=>e.jsxs("div",{className:"flex h-full w-72 flex-col py-2 text-center xl:w-80",children:[(!r||r.length===0)&&e.jsx("p",{className:"text-center text-sm",children:"No items available"}),r?.map((a,x)=>e.jsxs("div",{children:[e.jsx("hr",{className:M("my-1.5 border-gray-600/75",x===0?"hidden":"")}),e.jsxs("div",{className:"flex items-center justify-center gap-3 px-4",children:[e.jsx("div",{className:"w-[13%]",children:e.jsx("img",{className:M("z-10 mx-auto aspect-square max-w-10 grid-cols-2"),src:"https://cloudflare-image.jamessut.workers.dev/"+a.item.image,alt:"",onError:j=>j.target.src=fe(!0)})}),e.jsxs("div",{className:"flex flex-1 flex-col",children:[e.jsxs("p",{className:M(ee(a.item.rarity),"text-base"),children:[a.item.name," ",a.upgradeLevel>0&&e.jsxs("span",{children:["+",a.upgradeLevel]})]}),e.jsxs("div",{className:"flex min-w-24 flex-col text-gray-200 text-sm",children:[a.item.damage>0&&e.jsxs("div",{children:[e.jsx("span",{className:"mr-1 text-blue-500 text-lg",children:Y(a,"damage")})," ","DMG",e.jsx(O,{item:a.item,equippedItems:s,type:"damage"})]}),a.item.armour>0&&e.jsxs("div",{className:"",children:[e.jsx("span",{className:"mr-1 text-blue-500 text-sm",children:Y(a,"armour")})," ","ARMOR",e.jsx(O,{item:a.item,equippedItems:s,type:"armour"})]}),a.item.baseAmmo>0||a.item.strength>0||a.item.dexterity>0||a.item.statModifiers?e.jsxs(e.Fragment,{children:[a.item.baseAmmo>0&&e.jsxs("div",{className:"text-base",children:[a.item.baseAmmo," Base Ammo"," ",e.jsx(O,{item:a.item,equippedItems:s,type:"ammo"})]}),a.item.strength>0&&e.jsxs("div",{className:"text-base text-custom-yellow",children:["+",a.item.strength,"% STR"," ",e.jsx(O,{item:a.item,equippedItems:s,type:"strength"})]}),a.item.dexterity>0&&e.jsxs("div",{className:"text-base text-custom-yellow",children:["+",a.item.dexterity,"% DEX"," ",e.jsx(O,{item:a.item,equippedItems:s,type:"dexterity"})]}),a.item.statModifiers&&e.jsxs("div",{className:"mx-auto flex text-base text-custom-yellow",children:[f(a),e.jsx(O,{item:a.item,equippedItems:s,type:"modifiers"})]})]}):null]})]}),e.jsx("div",{className:"-mr-2 flex flex-1 justify-end md:mr-0",children:p(a.item)?e.jsx("span",{className:"mr-2 text-custom-yellow",children:"Equipped"}):e.jsx(L,{onClick:()=>n.mutate({currentUser:t,userItem:a}),children:"Equip"})})]})]},a.id))]})})},Te=r=>{const t=D();return F(u.user.useItem.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:u.user.getInventory.key()}),t.invalidateQueries({queryKey:u.user.getCurrentUserInfo.key()}),t.invalidateQueries({queryKey:u.user.getStatusEffects.key()}),U.success("Item used successfully!")},onError:s=>{U.error(s.message||"Failed to use item")}}))},Me=(r={})=>z(u.specialItems.dailyChestItems.queryOptions({staleTime:3e5,...r}));class ${itemId;itemName;itemQuantity;rarity;itemImage;dropRate;constructor(t,s){this.itemId=t,this.itemName=s.itemName,this.itemQuantity=s.itemQuantity,this.rarity=s.rarity,this.itemImage=s.itemImage}}class qe{winner;allWeapons;rouletteWrapper;weaponWrapper;weaponsRef;weapons;weaponsCount;weaponPrizeId;transitionDuration;itemWidth;constructor(t){this.winner={itemName:"Placeholder",itemId:-1,itemQuantity:"",rarity:"",itemImage:"",dropRate:0},this.allWeapons=t.weapons,this.weapons=[],this.weaponsRef=t.weaponsRef,this.rouletteWrapper=t.weaponsRef,this.weaponWrapper=t.weaponsRef,this.weaponsCount=t.weaponsCount||50,this.weaponPrizeId=this.randomRange(this.weaponsCount/2,this.weaponsCount-5),this.transitionDuration=t.transitionDuration||10,this.itemWidth=t.itemWidth||100}randomRange=(t,s)=>Math.floor(Math.random()*(s-t+1))+t;shuffle=t=>{for(let s=t?.length-1;s>0;s--){const n=Math.floor(Math.random()*(s+1));[t[s],t[n]]=[t[n],t[s]]}};set_weapons=()=>{let t=[];const s=this.allWeapons?.length,n=(i,o)=>{let f=0;const p=[];for(let a=i;a<=o;a+=1)p.push(new $(a,this.allWeapons[f])),f=f===s-1?0:f+1;return this.shuffle(p),p};if(s===0)throw new Error("Ошибка! Нет актёров.");t=t.concat(n(0,this.weaponPrizeId-1)),t[this.weaponPrizeId]=new $(this.weaponPrizeId,this.winner),t=t.concat(n(this.weaponPrizeId+1,this.weaponsCount-1)),this.weapons=t};spin=()=>{const t=Math.floor(this.itemWidth/2),s=Math.floor(this.itemWidth/20),n=(this.weaponPrizeId-4)*this.itemWidth+t+this.randomRange(s,18*s);return this.weaponWrapper.current.style.transition=`left ${this.transitionDuration}s ease-out`,setTimeout(()=>{this.weaponWrapper.current.style.left=`-${n}px`},100),this.weaponPrizeId};setWinner=t=>{this.weapons[this.weaponPrizeId]=t}}const Re=()=>{const r=D();return F(u.specialItems.dailyChest.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:u.user.inventory.key()})},6e3)},onError:t=>{const s=t.message||"Unknown error occurred";console.error(s),m.error(s)}}))},Fe=({items:r,itemsCount:t,transitionDuration:s,isSpin:n,setIsSpin:i,handleClose:o})=>{const[f,p]=c.useState(r),[a,x]=c.useState(-1),[j,g]=c.useState(!1),[E,N]=c.useState([]),[S,b]=c.useState(!1),{mutate:C,data:h,isSuccess:I}=Re(),k=H(),W=c.useRef(null),P=c.useRef(null),R=c.useRef(null),_=()=>{N(E.concat(f[a])),i(!1),g(!0)};c.useLayoutEffect(()=>{const w=new qe({winner:null,weapons:r,rouletteContainerRef:W.current,weaponsRef:P,weaponsCount:t,transitionDuration:s,itemWidth:k?100:125});w.set_weapons(),p(w.weapons),R.current=w},[r,t,s]);const v=async()=>{C()};return c.useEffect(()=>{if(I&&h){const w=r.find(T=>T.itemId===h.itemId);w&&(R.current.setWinner(w),b(!0),i(!0),setTimeout(()=>{const T=R.current.spin();x(T)},1e3))}},[I,h,r]),e.jsx("div",{children:e.jsxs("div",{className:"rouletteWrapper",children:[e.jsx("div",{ref:W,children:e.jsxs("div",{className:"relative h-[160px] w-[700px] overflow-hidden rounded-[5px] border border-[#232730] md:w-[875px]",children:[e.jsx("div",{className:"evTarget"}),e.jsx("div",{ref:P,className:"evWeapons",onTransitionEnd:_,children:f?.map((w,T)=>e.jsx(Oe,{id:T,isLoser:T!==a&&!n&&j,itemName:w.itemName,rarity:w.rarity,itemImage:w.itemImage,itemQuantity:w.itemQuantity},T))})]})}),S?e.jsx("div",{className:"mt-5 flex h-16 flex-col gap-2",children:E?.length>0&&e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("p",{className:"font-body font-semibold text-lg text-white md:text-2xl",children:["You received ",e.jsxs("span",{className:"text-custom-yellow",children:[E[0]?.itemName," "]})," ","x",E[0]?.itemQuantity,"!"]}),e.jsx("button",{className:"mx-auto w-1/5 cursor-pointer rounded-md border border-gray-700 bg-red-600 p-2 px-5 text-stroke-sm text-white hover:brightness-95",onClick:()=>o(),children:"Close"})]})}):e.jsxs("button",{type:"button",disabled:n||S,className:"weapons-center darkBlueButtonBGSVG mx-auto mt-5 flex h-16 w-1/2 justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:brightness-75 md:w-3/4 dark:text-slate-200",onClick:v,children:[e.jsx("img",{className:"-ml-4 my-auto mr-2.5 inline-block size-8",src:"https://cloudflare-image.jamessut.workers.dev/ui-images/KWEkQyF.png",alt:""}),e.jsx("span",{className:"my-auto text-2xl",children:"Open"})]})]})})},We=({potentialChestItems:r,isLoading:t,isSpin:s,setIsSpin:n,handleClose:i})=>e.jsx("div",{className:"LootboxWrapper",children:t?e.jsx(ge,{center:!0}):e.jsx(Fe,{items:r,itemsCount:150,transitionDuration:5,isSpin:s,setIsSpin:n,handleClose:i})}),Oe=({id:r,itemName:t,rarity:s,itemImage:n,isLoser:i,itemQuantity:o})=>e.jsx("div",{className:"evWeapon",style:i?{opacity:"0.5"}:{opacity:"1"},children:e.jsxs("div",{className:"evWeaponInner",id:String(r),children:[e.jsx("div",{className:"evWeaponBorder"}),e.jsx("img",{className:"w-[70px]! h-[70px]! absolute! top-[25px]! left-[50%]! -translate-x-1/2",src:n,alt:t}),e.jsxs("span",{className:"-translate-x-1/2 absolute top-0 left-[25px] font-body font-semibold text-base text-blue-500 text-stroke-s-sm",children:[o,"x"]}),e.jsx("div",{className:"evWeaponRarity "+s}),e.jsx("div",{className:"evWeaponText mt-auto font-body font-semibold text-stroke-sm",children:e.jsx("p",{className:"text-sm! "+s,children:t})})]})});function Ue({openModal:r,setOpenModal:t}){const[s,n]=c.useState(!1),{data:i,isLoading:o}=Me(),f=()=>{t(!1)};return e.jsx(X,{open:r,showClose:!s,modalMaxWidth:"md:max-w-fit",contentPadding:"px-1 md:px-6 py-10",iconBackground:"shadow-lg",contentHeight:"overflow-x-hidden!",title:"Daily Chest",Icon:()=>e.jsx("img",{src:"https://d13cmcqz8qkryo.cloudfront.net/static/items/special/dailychest.png",alt:"",className:"mt-0.5 size-11"}),onOpenChange:s?null:f,children:e.jsx(We,{potentialChestItems:i,isLoading:o,isSpin:s,setIsSpin:n,handleClose:f})})}const De=()=>{const r=D();return F(u.specialItems.redeemMaterialsCrate.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:u.user.inventory.key()}),r.invalidateQueries({queryKey:B.USER.CURRENTUSERINFO})},30),m.success("You redeemed 10 Raw Materials for your gang!")},onError:t=>{const s=t.message||"Unknown error occurred";console.error(s),m.error(s)}}))},Pe=()=>{const r=D();return F(u.specialItems.redeemToolsCrate.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:u.user.inventory.key()}),r.invalidateQueries({queryKey:B.USER.CURRENTUSERINFO})},30),m.success("You redeemed 10 Tools for your gang!")},onError:t=>{const s=t.message||"Unknown error occurred";console.error(s),m.error(s)}}))};function _e({open:r,setOpen:t,title:s,item:n,currentUser:i}){const[o,f]=c.useState(null),[p,a]=c.useState(""),[x,j]=c.useState("accuracy"),g=ye(),E=D(),{HOSPITALISE_ITEM_NAME:N,REVIVE_ITEM_NAME:S,JAIL_ITEM_NAME:b,MEGAPHONE_ITEM_NAME:C}=g,h=v=>F({...v,onSuccess:()=>{m.success("Item used successfully"),t(!1),E.invalidateQueries({queryKey:B.USER.INVENTORY})},onError:w=>{console.error("Failed to use item:",w),m.error(w.message||"Failed to use item")}}),I=h(u.specialItems.deathNote.mutationOptions()),k=h(u.specialItems.lifeNote.mutationOptions()),W=h(u.specialItems.kompromat.mutationOptions()),P=h(u.specialItems.megaphone.mutationOptions()),R=()=>{if(n.name===N){if(p===""){m.error("You must enter an injury name!");return}I.mutate({userId:o,injuryName:p,injuryType:x})}else if(n.name===S){if(!o){m.error("You must enter a target student ID!");return}k.mutate({userId:o})}else if(n.name===b){if(p===""){m.error("You must enter a reason!");return}if(!o){m.error("You must enter a target student ID!");return}W.mutate({userId:o,reason:p})}else if(n.name===C){if(!p||p.length<5){m.error("Enter a message with at least 5 characters!");return}P.mutate({message:p})}else{m.error("Unknown item type!");return}},_=v=>{v.preventDefault(),R()};return e.jsx(X,{showClose:!0,open:r,title:s,iconBackground:"shadow-lg",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/JXozQjh.png",alt:"",className:"mt-0.5 h-11 w-auto"}),onOpenChange:t,children:e.jsxs("form",{className:"mt-4 w-full max-w-lg",onSubmit:_,children:[n.name!==C&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mb-2",children:e.jsxs("div",{className:"mb-6 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"studentid",children:["Target Student ID",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",children:"#"}),e.jsx("input",{type:"number",name:"studentid",min:1,id:"studentid",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"0",onChange:v=>f(Number.parseInt(v.target.value))})]})]})}),n.name!==S&&n.name!==C&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsxs("div",{className:"mb-2 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"grid-first-name",children:[n.name===N?"Injury Name":"Reason",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("input",{type:"text",name:"text",id:"text",maxLength:100,className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"",onChange:v=>a(v.target.value)})})]})}),n.name===C&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"-mt-2 text-center text-gray-200 text-lg",children:[e.jsx("p",{className:"text-custom-yellow",children:"Broadcast a global message to all students"}),e.jsx("p",{className:"text-red-400 text-xs",children:"Messages are monitored by Staff, inappropriate messages may result in punishment"})]}),e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsxs("div",{className:"mb-2 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 flex flex-row items-center text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"grid-first-name",children:["Global Message",e.jsx("span",{className:"text-red-500",children:" *"}),e.jsx("small",{className:"my-auto ml-4 text-gray-400",children:"(Max length: 400 characters)"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("textarea",{id:"globalMessage",name:"globalMessage",rows:10,maxLength:i?.userType==="admin"?4e3:400,className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"Add a message..",value:p,onChange:v=>a(v.target.value)})})]})})]}),n.name===N&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsx("div",{className:"mb-4 flex w-full px-3 md:mb-0",children:e.jsxs("fieldset",{className:"mx-auto mt-1 flex w-3/4 flex-col gap-3 rounded-md border-2 border-gray-600 bg-slate-800 px-4 py-1 text-gray-200 text-stroke-sm md:p-4",children:[e.jsxs("legend",{className:"text-stroke-sm!",children:["Select an Injury Debuff ",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"concussion",name:"injury",value:"concussion",checked:x==="accuracy",onChange:()=>j("accuracy")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"concussion",children:["Concussion"," ",e.jsx("img",{src:Q.concussion_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Increased chance to miss attacks"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"bleed",name:"injury",value:"bleed",checked:x==="bleed",onChange:()=>j("bleed")}),e.jsxs("label",{className:"ml-2 inline-flex",htmlFor:"bleed",children:["Bleed",e.jsx("img",{src:Q.bleeding_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"HP loss each combat turn"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"fracture",name:"injury",value:"fracture",checked:x==="damage",onChange:()=>j("damage")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"fracture",children:["Fracture",e.jsx("img",{src:Q.fracture_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Reduced damage output"})]}),e.jsxs("div",{children:[e.jsx("input",{className:"",type:"radio",id:"fatigue",name:"injury",value:"fatigue",checked:x==="ability_lock",onChange:()=>j("ability_lock")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"fatigue",children:["Fatigue",e.jsx("img",{src:Q.fatigue_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Can't use abilities"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"muscle",name:"injury",value:"muscle",checked:x==="defence",onChange:()=>j("defence")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"muscle",children:["Muscle Injury",e.jsx("img",{src:Q.muscle_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Reduced defence"})]})]})})}),e.jsx("div",{className:"sm:mt-6",children:e.jsx("button",{type:"submit",className:"inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-base text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm",children:"Use Item"})})]})})}function J({equippedItems:r}){const t=H(),{isLoading:s,error:n,data:i}=Ee(),[o,f]=c.useState("All"),[p,a]=c.useState(!1),[x,j]=c.useState({}),g=je("good_stomach"),[E,N]=c.useState(!1),{equipItem:S}=te(),{data:b}=Z(),C=Te(),[h,I]=c.useState(null),k=c.useRef(null),W=["Death Book","Megaphone","Life Book","Kompromat","Daily Chest","Small Raw Materials Crate","Small Tools Crate"],R=F({mutationFn:async l=>{const{item:d}=l;if(d.health>0&&b.currentHealth===b.health){m.error("You're already full hp!");return}if(b.jailedUntil>0||b.hospitalisedUntil>0){m.error("You can't use this in your current state!");return}try{const y=await C.mutateAsync({userItemId:l.id});return d.health>0&&m.success(`You recovered ${d.health} HP!`),d.energy>0&&m.success(`You recovered ${d.energy} Energy!`),d.actionPoints>0&&m.success(`You recovered ${d.actionPoints} AP!`),y}catch(y){throw console.error("Error using item:",y),y}},onError:l=>{console.log(l)},onSuccess:l=>{if(l?.info?.injuryRemoved){m.success("Injury treated successfully!");return}if(l?.info?.recipeUnlocked){m.success("Recipe unlocked successfully!");return}!l?.info?.injuryRemoved&&!l?.info?.recipeUnlocked&&m.success("Item used successfully!")}}),{mutate:_}=De(),{mutate:v}=Pe(),w=l=>{const{item:d}=l;if(b?.hospitalisedUntil>0){m.error("Cant use items while hospitalised!");return}if(b?.jailedUntil>0){m.error("Cant use items while jailed!");return}if(b?.missionEnds>0){m.error("Cant use items while on a mission!");return}if(d?.itemType==="special"){if(d?.name==="Daily Chest"){N(!0);return}if(d?.name==="Small Raw Materials Crate"){_();return}if(d?.name==="Small Tools Crate"){v();return}a(!0),j(d)}else R.mutate(l)},T=l=>{S.mutate({currentUser:b,userItem:l})},se=l=>{const{equippedItems:d}=l,y=l.data;if(!y)return null;const{item:q}=y,le=A=>["consumable","crafting","upgrade","quest","junk","recipe","pet"].includes(A),oe=A=>W.includes(A),ce=A=>["consumable","special","recipe","pet"].includes(A),de=d?.[q.itemType]?.userItemId===y.id,me=b?.level>=q.level,ue=()=>e.jsx(L,{className:"text-base",onClick:()=>w(y),children:"Use"}),pe=()=>e.jsx(L,{disabled:!me,className:"text-base",onClick:()=>T(y),children:"Equip"});return e.jsx("div",{className:"flex size-full items-center justify-center",children:le(q.itemType)||oe(q.name)?e.jsx(e.Fragment,{children:ce(q.itemType)?ue():null}):e.jsx(e.Fragment,{children:de?e.jsx("p",{className:"m-auto inline font-semibold text-base text-custom-yellow md:text-sm",children:"Equipped"}):pe()})})};c.useEffect(()=>{k.current&&k.current.api&&(o==="All"?k.current.api.setFilterModel({itemTypeFilter:{type:"notEqual",filter:null}}):k.current.api.setFilterModel({itemTypeFilter:{type:"includes",filter:ae[o]}}))},[o]);const G=be.memo(l=>{const{value:d}=l,y=l.data.upgradeLevel||0;return e.jsxs("div",{className:"relative flex h-full items-center gap-3 px-1 py-2 md:w-full md:flex-row md:items-start md:gap-4 md:p-1",children:[e.jsx(we,{itemTypeFrame:!0,item:l.data,className:"my-auto size-14"}),e.jsxs("div",{className:"flex flex-1 flex-col gap-1.5 py-1.5 md:my-auto md:gap-0",children:[e.jsxs("p",{className:M(d.name.length>15?"md:text-sm! text-[0.65rem]":"text-sm! md:text-base!","text-wrap! leading-none! truncate text-left font-semibold text-custom-yellow md:text-base"),children:[d.name,y>0&&e.jsxs("span",{children:[" +",y]})]}),e.jsx("p",{className:M(ee(d.rarity),"text-xs! leading-none! md:text-sm! text-left font-semibold"),children:K(d.itemType)}),e.jsx("div",{className:"mt-1 flex flex-col font-bold text-slate-700 text-xs dark:text-indigo-400",children:Ne(d,g).map(q=>e.jsx("p",{children:q},q))})]})]})});G.displayName="DisplayItemCell";const ae={All:[],Weapons:["weapon","ranged","offhand"],Armor:["head","chest","hands","legs","feet","finger","shield"],Consumables:["consumable","recipe","pet"],Material:["crafting","upgrade"],Misc:["special","quest","junk"]},K=l=>l==="weapon"?"Melee Weapon":l==="ranged"?"Ranged Weapon":l==="quest"?"Task Item":l==="crafting"?"Material":l==="special"?"Special Item":ve(l);c.useEffect(()=>{i&&I(i)},[i]);const re=[{headerName:"Item",field:"item",cellRenderer:G,cellClass:"items-center! flex!",minWidth:t?183:250,sortable:!1,autoHeight:!0,wrapText:!0,getQuickFilterText:l=>l.data.item.itemType,filter:"agTextColumnFilter",valueFormatter:l=>l.data.item,filterValueGetter:l=>l.data.item.name,filterParams:{filterOptions:["contains"],defaultOption:"contains"}},{headerName:t?"Qty":"Quantity",field:"count",headerClass:"centerGridHeader",maxWidth:120,cellClass:"items-center! justify-center! flex! font-semibold font-body text-base",filter:"agNumberColumnFilter",filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Category",field:"item.itemType",headerClass:"centerGridHeader",hide:t,cellClass:"md:text-base text-sm font-semibold text-center flex! items-center! justify-center! truncate",valueFormatter:l=>K(l.data.item.itemType)},{headerName:"Level",field:"item.level",hide:t,initialSortIndex:1,maxWidth:120,sort:"desc",cellClass:"items-center! justify-center! flex! font-semibold font-body text-base",headerClass:"centerGridHeader",filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Actions",field:"actions",sortable:!1,headerClass:"centerGridHeader",cellClass:"flex! items-center! justify-center!",cellRenderer:se,cellRendererParams:{equippedItems:r},valueFormatter:l=>l.data,filter:!1,floatingFilter:!1},{headerName:"CategoryHidden",field:"itemTypeFilter",hide:!0,filterParams:{filterOptions:[{displayKey:"includes",displayName:"Includes",predicate:([l],d)=>l.includes(d)}]},filterValueGetter:l=>l.data.item?.itemType}],[ne,ie]=c.useState(re);return c.useEffect(()=>{const l={force:!0,suppressFlash:!0,columns:["actions"]};k?.current?.api?.refreshCells(l),ie(d=>d.map(y=>y.field==="actions"?{...y,cellRendererParams:{equippedItems:r}}:y))},[r]),n?"An error has occurred: "+n.message:e.jsxs(e.Fragment,{children:[e.jsx(Ae,{selectedTab:o,setSelectedTab:f}),e.jsx(Ue,{openModal:E,setOpenModal:N}),e.jsx(_e,{open:p,setOpen:a,title:x.name,item:x,currentUser:b}),e.jsx("div",{className:"mb-8 md:mb-0 md:max-w-6xl 2xl:mx-auto",children:e.jsx(ke,{keyProp:JSON.stringify(r),dataList:h,colDefs:ne,isLoading:s,customGridRef:k})})]})}const Ae=({selectedTab:r,setSelectedTab:t})=>{const s=i=>r===i,n=[{name:"All",current:s("All")},{name:"Weapons",current:s("Weapons")},{name:"Armor",current:s("Armor")},{name:"Consumables",current:s("Consumables")},{name:"Material",current:s("Material")},{name:"Misc",current:s("Misc")}];return e.jsxs("div",{children:[e.jsxs("div",{className:"mx-2 my-1 2xl:hidden",children:[e.jsx("label",{htmlFor:"tabs",className:"sr-only",children:"Select a tab"}),e.jsx("select",{id:"tabs",name:"tabs",className:"mb-3 block w-full rounded-md border-gray-300 px-2 text-stroke-md focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white",defaultValue:n.find(i=>i.current).name,onChange:i=>t(i.target.value),children:n.map(i=>e.jsx("option",{children:i.name},i.name))})]}),e.jsx("div",{className:"hidden 2xl:block",children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-200 border-gray-600 border-b shadow-sm dark:divide-gray-600","aria-label":"Tabs",children:n.map((i,o)=>e.jsxs("button",{"aria-current":i.current?"page":void 0,className:M(i.current?"text-gray-900":"text-gray-500 hover:text-gray-700",o===0?"rounded-tl-lg":"",o===n.length-1?"rounded-tr-lg":"","group relative min-w-0 flex-1 overflow-hidden bg-white p-4 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-white"),onClick:()=>t(i.name),children:[e.jsx("span",{children:i.name}),e.jsx("span",{"aria-hidden":"true",className:M(i.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-0.5")})]},i.name))})})]})};function Ge(){const[r,t]=c.useState("/inventory"),[s,n]=c.useState("head"),i=H(!0),o=Ie(),f=Ce();c.useLayoutEffect(()=>{o.pathname&&o.pathname!==r&&t(o.pathname)},[o.pathname]);const{data:p,error:a,isLoading:x}=Z(),{data:j}=z(u.user.inventory.queryOptions()),{data:g}=z(u.user.equippedItems.queryOptions());if(x)return null;if(a)return"An error has occurred: "+a.message;const N=["weapon","offhand","ranged"].includes(s)?"damage":"armour",S=s==="shield"?"offhand":null,b=j?.filter(h=>h.item?.itemType===s||h.item?.itemType===S)?.sort((h,I)=>h.item[N]>I.item[N]?-1:h.item[N]<I.item[N]?1:0)||[];g&&g.shield&&(g.offhand=g.shield);const C=[{name:"/inventory",text:"Items",current:o.pathname==="/inventory"},{name:"/equipment",text:"Equipment",current:o.pathname==="/equipment"}];return e.jsxs("div",{className:"flex h-full flex-col gap-2 md:mx-auto md:max-w-208 2xl:max-w-(--breakpoint-xl) 2xl:flex-row",children:[e.jsxs("div",{children:[e.jsx(Se,{inventory:b,currentUser:p,equippedItems:g||[]}),e.jsx("div",{className:"block 2xl:hidden",children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-700 shadow-sm","aria-label":"Tabs",children:C.map((h,I)=>e.jsxs("a",{"aria-current":h.current?"page":void 0,className:M(h.current?"text-gray-900":"text-gray-500 hover:text-gray-700",I===C.length-1?"md:rounded-r-lg":"",I===0?"md:rounded-l-lg":"","group relative min-w-0 flex-1 cursor-pointer overflow-hidden bg-white p-4 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-slate-200"),onClick:()=>f(h.name),children:[e.jsx("span",{children:h.text}),e.jsx("span",{"aria-hidden":"true",className:M(h.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-0.5")})]},h.name))})})]}),typeof i=="number"&&i<=1536?r==="/inventory"?e.jsx(J,{currentUser:p,equippedItems:g}):e.jsx("div",{className:"flex flex-col gap-4",children:e.jsx(V,{mobile:!0,equippedItems:g,setEquipTooltipFilter:n})}):e.jsxs("div",{className:"flex w-full gap-6",children:[e.jsx("div",{className:"2xl:mt-0 2xl:w-[75%]",children:e.jsx(J,{currentUser:p,equippedItems:g})}),e.jsx("div",{className:"2xl:w-[30%]",children:e.jsx(V,{equippedItems:g,setEquipTooltipFilter:n})})]})]})}export{Ge as default};
