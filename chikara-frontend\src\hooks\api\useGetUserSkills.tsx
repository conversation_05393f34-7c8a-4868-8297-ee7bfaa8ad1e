import { api } from "@/helpers/api";
import { QueryOptions } from "@/lib/orpc";
import { useQuery } from "@tanstack/react-query";

export interface SkillData {
    level: number;
    experience: number;
    expToNextLevel: number;
    talentPoints: number;
    maxTalentPoints: number;
}

export type UserSkills = Record<string, SkillData>;

type SkillName = (typeof defaultSkillNames)[number];

const defaultSkillNames = [
    "mining",
    "scavenging",
    "foraging",
    "fabrication",
    "outfitting",
    "chemistry",
    "electronics",
    "strength",
    "intelligence",
    "dexterity",
    "defence",
    "endurance",
    "vitality",
] as const;

/**
 * Custom hook to fetch user's skills
 * @param skillNames - Array of skill names to fetch (optional)
 * @param options - Query options
 */
export const useGetUserSkills = (skillNames?: SkillName[], options: QueryOptions = {}) => {
    // Default skill names if none provided
    const skillsToFetch = skillNames || defaultSkillNames;

    return useQuery(
        api.user.getSkills.queryOptions({
            input: { skills: [...skillsToFetch] },
            staleTime: 60 * 1000, // 1 minute
            ...options,
        })
    );
};

export default useGetUserSkills;
