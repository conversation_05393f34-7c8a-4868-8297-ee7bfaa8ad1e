import Spinner from "@/components/Spinners/Spinner";
import TraderRep from "@/components/TraderRep";
import checkIfFirefox from "@/helpers/checkIfFirefox";
import { levelGates } from "@/helpers/levelGates";
import { shopkeeperBackgroundImage, shopkeeper<PERSON>mage<PERSON>arge, shopkeeperImageShadow } from "@/helpers/shopkeeperImages";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";

export default function Shops() {
    const { isLoading, error, data } = useQuery(api.shops.shopList.queryOptions());

    const { data: currentUser } = useFetchCurrentUser();
    const isMobile = useCheckMobileScreen();
    const isFirefox = checkIfFirefox();
    if (error) return "An error has occurred: " + error.message;

    const shopAvatarCSS = (name, type) => {
        // https://hypercolor.dev/
        const shops = {
            Nagao: {
                top: "12rem",
                left: "-0.5rem",
                color: "bg-linear-to-r from-purple-500 to-pink-500",
            },
            Goda: {
                top: "13rem",
                left: "2.5rem",
                color: "bg-linear-to-r from-cyan-500 to-blue-500",
            },
            Mihara: {
                top: "13rem",
                left: "1rem",
                color: "bg-linear-to-r from-yellow-100 via-yellow-300 to-yellow-500",
            },
            Shoko: {
                top: "12rem",
                left: "1.2rem",
                color: "bg-linear-to-r from-red-500 to-red-800",
            },
            Otake: {
                top: "13rem",
                left: "0.5rem",
                color: "bg-linear-to-r from-rose-400 to-orange-300",
            },
            Honda: {
                top: "13rem",
                left: "0.5rem",
                color: "bg-linear-to-r from-rose-400 to-orange-300",
            },
        };

        const shopsMobile = {
            Nagao: {
                top: "6rem",
                left: "-0.5rem",
            },
            Goda: {
                top: "6.3rem",
                left: "1.25rem",
            },
            Mihara: {
                top: "6rem",
                left: "0.8rem",
            },
            Shoko: {
                top: "6rem",
                left: "0.6rem",
            },
            Otake: {
                top: "6.25rem",
                left: "0.5rem",
            },
            Honda: {
                top: "6rem",
                left: "0.5rem",
            },
        };

        if (isMobile) {
            if (type === "left" || type === "top") {
                return shopsMobile[name][type];
            }
        }
        return shops[name][type];
    };

    const isHidden = (shop, level, i) => {
        if (shop.disabled) return true;
        if (levelGates("shop", i, level)) return true;
        return false;
    };

    const shopNames = (name, shopkeeper) => {
        if (shopkeeper === "Otake") {
            return "Rare Goods";
        }
        switch (name) {
            case "general":
                return "General Goods";
            case "weapon":
                return "Weapons";
            case "armour":
                return "Armor";
            case "food":
                return "Food";
            case "furniture":
                return "Sunday Shop";
            default:
                return "";
        }
    };
    const shopsList = data?.filter((shop) => shop.shopType !== "gang");

    return (
        <>
            {isLoading ? (
                <Spinner center />
            ) : (
                <ul
                    role="list"
                    className="m-2 mb-12 grid h-[calc(100dvh)] grid-cols-2 gap-x-3 gap-y-4 sm:grid-cols-2 md:mx-auto md:mb-0 md:h-auto md:max-w-7xl md:grid-cols-2 md:gap-5 md:gap-y-20 lg:grid-cols-3 2xl:gap-y-4"
                >
                    {shopsList.map((shop, i) => (
                        <li key={shop.id} className="col-span-1 flex max-h-[45vh] flex-col text-center">
                            <div className="flex h-full flex-col p-0 md:flex-1">
                                <Link
                                    className="h-full"
                                    to={"/shops/" + shop.id}
                                    style={{
                                        pointerEvents: isHidden(shop, currentUser?.level, i) ? "none" : "",
                                    }}
                                >
                                    <figure
                                        style={shopkeeperBackgroundImage(shop.name)}
                                        className={cn(
                                            shopAvatarCSS(shop.name, "color"),
                                            "shopKeeperAvatarWrapper relative inline-block size-full overflow-hidden rounded-md border-x-green-600 ring-3 ring-gray-300 drop-shadow-md hover:ring-[#7060FE] md:h-[26.6rem] dark:ring-gray-600"
                                        )}
                                    >
                                        {isHidden(shop, currentUser?.level, i) ? null : (
                                            <div className="-translate-x-1/2 traderRepContainer absolute top-2 left-1/2 z-10 flex w-fit rounded-full border-2 border-slate-600/50 bg-slate-200/50 px-2 shadow-xl blur-[0.3px]">
                                                <TraderRep heartWidth="md:w-12 w-5" shopId={shop.id} />
                                            </div>
                                        )}
                                        <figcaption
                                            className={`${
                                                isHidden(shop, currentUser?.level, i)
                                                    ? "disabledShopKeeperName pb-3 text-[2rem] md:h-[130px] md:pb-0 md:text-[3rem]"
                                                    : "shopKeeperName pb-3 text-[2rem] md:h-[130px] md:pb-0 md:text-[3rem]"
                                            }`}
                                        >
                                            <p className="mt-8 drop-shadow-lg md:h-16">
                                                {isHidden(shop, currentUser?.level, i) ? "???" : shop.name}
                                            </p>
                                            <p className="text-[#ffdc11] text-sm drop-shadow-2xl">
                                                {isHidden(shop, currentUser?.level, i)
                                                    ? "Open on Sundays"
                                                    : levelGates("shop", i, currentUser?.level)
                                                      ? `Unlocked at level ${levelGates("shop", i, currentUser?.level)}`
                                                      : shopNames(shop.shopType, shop.name)}
                                            </p>
                                        </figcaption>
                                        <div
                                            className={cn(
                                                !isFirefox ? "backdrop-blur-[1px]" : "backdrop-blur-firefox",
                                                "absolute"
                                            )}
                                        >
                                            <img
                                                loading="eager"
                                                alt="Shopkeeper Avatar"
                                                className={cn(
                                                    shopAvatarCSS(shop.name, "left"),
                                                    isHidden(shop, currentUser?.level, i)
                                                        ? "disabledShop"
                                                        : "shopKeeperAvatar"
                                                )}
                                                src={
                                                    isHidden(shop, currentUser?.level, i)
                                                        ? shopkeeperImageShadow(shop.name)
                                                        : shopkeeperImageLarge(shop.name)
                                                }
                                                style={{
                                                    marginLeft: shopAvatarCSS(shop.name, "left"),
                                                    marginTop: shopAvatarCSS(shop.name, "top"),
                                                }}
                                            />
                                        </div>
                                    </figure>
                                </Link>
                            </div>
                        </li>
                    ))}
                </ul>
            )}
        </>
    );
}
