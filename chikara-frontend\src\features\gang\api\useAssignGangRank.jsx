import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useAssignGangRank = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.assignRank.mutationOptions({
            onSuccess: () => {
                toast.success(`Rank Changed!`);
                queryClient.invalidateQueries({
                    queryKey: api.gang.getCurrentGang.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    const assignGangRank = ({ studentId, rank }) => {
        if (!studentId) {
            toast.error("Gang Member ID is required");
            return;
        }

        mutation.mutate({
            targetUserId: parseInt(studentId),
            rank: parseInt(rank),
        });
    };

    return {
        assignGangRank,
    };
};

export default useAssignGangRank;
