import * as TalentHelper from "../features/talents/talents.helpers.js";
import { logAction } from "../lib/actionLogger.js";
import { StatusEffectModel, UserModel, db } from "../lib/db.js";
import { LogErrorStack } from "../utils/log.js";
import type { StatusEffectTier } from "@prisma/client";

export const GetUserStatusEffects = async (user: UserModel) => {
    try {
        const now = Date.now();
        const effects = await db.user_status_effect.findMany({
            where: { userId: user.id, endsAt: { gt: now } },
            include: { effect: true },
        });
        return effects || null;
    } catch (error) {
        LogErrorStack({ error });
        throw new Error(error as string);
    }
};

export const removeUserStatusEffects = async (userId: number) => {
    await db.user_status_effect.deleteMany({
        where: { userId },
    });
};

export const GetRandomInjury = async (tier: StatusEffectTier = "Minor", source: string | null = null) => {
    try {
        // Define the where clause
        const whereClause: {
            tier: StatusEffectTier;
            disabled: boolean;
            source?: string;
        } = {
            tier,
            disabled: false,
        };

        if (source) {
            whereClause.source = source;
        }
        const injuries = await db.status_effect.findMany({
            where: whereClause,
        });

        if (!injuries || injuries.length === 0) {
            return null;
        }

        const randomIndex = Math.floor(Math.random() * injuries.length);
        return injuries[randomIndex];
    } catch (error) {
        LogErrorStack({ error });
        return null;
    }
};

export const GetInjuryOfType = async (injuryType: string, tier: StatusEffectTier = "Minor") => {
    try {
        const injuries = await db.status_effect.findMany({
            where: {
                tier,
                disabled: false,
                category: injuryType,
            },
        });

        if (!injuries || injuries.length === 0) return null;

        // Select a random injury from the available ones
        const randomIndex = Math.floor(Math.random() * injuries.length);
        return injuries[randomIndex];
    } catch (error) {
        LogErrorStack({ error });
        return null;
    }
};

export const GetBattleStatusEffects = async (user: UserModel) => {
    try {
        const effects = await GetUserStatusEffects(user);
        if (!effects) return {};

        // Define a proper interface for the effect values
        type EffectValues = Record<string, { value: number }>;

        const effectValues = effects.reduce<EffectValues>((acc, effect) => {
            if (!effect.effect) return acc;

            const key = `${effect.effect.category}_${effect.effect.effectType.toLowerCase()}`;
            // Use nullish coalescing to handle potentially null modifier
            const value = (effect.effect.modifier ?? 0) * effect.stacks;

            if (acc[key]) {
                switch (effect.effect.modifierType) {
                    case "multiply": {
                        acc[key].value *= value;
                        break;
                    }
                    case "add": {
                        acc[key].value += value;
                        break;
                    }
                    case "divide": {
                        if (value !== 0) {
                            // Avoid division by zero
                            acc[key].value /= value;
                        }
                        break;
                    }
                }
            } else {
                // Replace nested ternary with if/else for better readability
                switch (effect.effect.modifierType) {
                    case "multiply": {
                        acc[key] = { value };
                        break;
                    }
                    case "add": {
                        acc[key] = { value: 1 + value };
                        break;
                    }
                    case "divide": {
                        acc[key] = { value: value === 0 ? 1 : 1 / value }; // Guard against division by zero
                        break;
                    }
                    default: {
                        acc[key] = { value: 1 };
                    }
                }
            }
            return acc;
        }, {});

        return effectValues;
    } catch (error) {
        LogErrorStack({ error });
        return {};
    }
};

export const ApplyStatusEffectToUser = async (
    user: UserModel,
    effect: StatusEffectModel,
    customName: string | null = null
) => {
    try {
        const existingEffect = await db.user_status_effect.findFirst({
            where: {
                userId: user.id,
                effectId: effect.id,
                customName: customName,
            },
        });

        // Check for Strong Bones talent if this is a damage debuff
        if (effect.effectType === "DEBUFF" && effect.category === "damage") {
            const strongBonesTalent = await TalentHelper.UserHasStrongBonesTalent(user.id);
            // Make sure strongBonesTalent and its modifier exist
            if (strongBonesTalent?.modifier && Math.random() < strongBonesTalent.modifier) {
                return null;
            }
        }

        if (existingEffect) {
            if (!effect.stackable) {
                // If not stackable, overwrite the duration instead of extending it
                const effectDuration = BigInt(effect.duration);
                existingEffect.endsAt = BigInt(Date.now()) + effectDuration;
                await db.user_status_effect.update({
                    where: { id: existingEffect.id },
                    data: existingEffect,
                });
                return existingEffect;
            } else if (effect.maxStacks && existingEffect.stacks >= effect.maxStacks) {
                // If at max stacks, just extend duration
                const existingEndsAt = Number(existingEffect.endsAt);
                const effectDuration = effect.duration;
                existingEffect.endsAt = BigInt(existingEndsAt + Math.floor(effectDuration / 2));
                await db.user_status_effect.update({
                    where: { id: existingEffect.id },
                    data: existingEffect,
                });
                return existingEffect;
            }
            // Add a stack and extend duration
            const existingEndsAt = Number(existingEffect.endsAt);
            const effectDuration = effect.duration;
            existingEffect.endsAt = BigInt(existingEndsAt + Math.floor(effectDuration / 2));
            existingEffect.stacks += 1;
            await db.user_status_effect.update({
                where: { id: existingEffect.id },
                data: existingEffect,
            });
            return existingEffect;
        }

        const currentTime = BigInt(Date.now());
        const effectDuration = BigInt(effect.duration);
        const newTime = currentTime + effectDuration;

        const newEffect = await db.user_status_effect.create({
            data: {
                customName: customName,
                userId: user.id,
                effectId: effect.id,
                endsAt: newTime,
                stacks: 1,
            },
        });

        return newEffect;
    } catch (error) {
        LogErrorStack({ message: `Error applying status effect`, error });
        return null;
    }
};

const tiers = ["Minor", "Moderate", "Severe", "Critical"] as const;
export const removeStatusEffect = async (userId: number, effectType: string, tier: string, amount = 1) => {
    const now = Date.now();

    if (!tier || !effectType) {
        return false;
    }

    // Build the where clause for the effect
    const effectWhere: {
        source: string;
        tier?: {
            in: StatusEffectTier[];
        };
    } = {
        source: `${effectType}_injury`,
    };

    if (tier !== "All") {
        const tierIndex = tiers.indexOf(tier as StatusEffectTier);
        if (tierIndex === -1) {
            return false; // Invalid tier
        }

        // Get all tiers up to and including the specified tier
        effectWhere.tier = {
            in: tiers.slice(0, tierIndex + 1) as StatusEffectTier[],
        };
    }

    const effects = await db.user_status_effect.findMany({
        where: {
            userId,
            endsAt: { gt: now },
            effect: effectWhere,
        },
        include: { effect: true },
        orderBy: [{ effect: { tier: "desc" } }, { createdAt: "asc" }],
    });

    if (!effects || effects.length === 0) return false;
    const effect = effects[0];

    logAction({
        action: "STATUS_EFFECT_REMOVED",
        userId: userId,
        info: {
            effectName: effect.effect?.name ?? "Unknown",
            amount: amount,
        },
    });

    if (effect.stacks === undefined || effect.stacks === null) return false;

    if (effect.stacks > amount) {
        effect.stacks -= amount;
        await db.user_status_effect.update({
            where: { id: effect.id },
            data: { stacks: effect.stacks },
        });
        return true;
    }
    await db.user_status_effect.delete({ where: { id: effect.id } });
    return true;
};
