import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery } from "@tanstack/react-query";

const useGetDailyQuests = (options: QueryOptions = {}) => {
    return useQuery(
        orpc.dailyQuest.getDailyQuests.queryOptions({
            staleTime: 15000,
            ...options,
        })
    );
};

export default useGetDailyQuests;
