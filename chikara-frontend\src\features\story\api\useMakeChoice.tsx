import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";

export interface MakeChoiceRequest {
    episodeId: number;
    choiceId: string;
    choiceValue: string;
}

/**
 * Hook to make a choice in an episode
 */
export const useMakeChoice = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.story.makeChoice.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['story'] });
            },
        })
    );
};
