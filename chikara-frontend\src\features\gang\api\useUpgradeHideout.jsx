import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useUpgradeHideout = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.upgradeHideout.mutationOptions({
            onSuccess: () => {
                toast.success(`Hideout upgraded successfully!`);
                queryClient.invalidateQueries({
                    queryKey: api.gang.currentGangInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    return {
        upgradeHideout: mutation.mutate,
    };
};

export default useUpgradeHideout;
