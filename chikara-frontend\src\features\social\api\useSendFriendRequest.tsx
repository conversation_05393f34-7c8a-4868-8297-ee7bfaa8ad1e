import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

const useSendFriendRequest = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.social.sendFriendRequest.mutationOptions({
            onSuccess: () => {
                toast.success("Friend request sent successfully!");
                // Invalidate friend requests list to refresh the data
                queryClient.invalidateQueries({ queryKey: api.social.friendRequests.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to send friend request");
            },
        })
    );
};

export default useSendFriendRequest;
