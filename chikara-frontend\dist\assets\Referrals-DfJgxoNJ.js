import{b as o,c as m,j as e,S as h,I as g,u,d as j,cT as p,an as y}from"./index--cEnoMkg.js";const f=(s={})=>o(m.registrationCodes.referralCodeList.queryOptions({staleTime:3e5,...s}));function b(){const{data:s,isLoading:t}=f();return e.jsx(e.Fragment,{children:e.jsxs("section",{className:"mx-auto rounded-lg bg-gray-100 py-6 md:max-w-3xl dark:bg-gray-800",children:[e.jsx("div",{className:"container mx-auto px-0 md:px-6",children:e.jsxs("div",{className:"mx-auto max-w-xl text-center",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h1",{className:"font-medium text-3xl text-custom-yellow tracking-tighter sm:text-4xl md:text-3xl",children:"Refer Your Friends"}),e.jsx("p",{className:" text-gray-500 md:text-lg dark:text-gray-200",children:"Earn awesome rewards by inviting your friends to join the game."})]}),e.jsx("div",{className:"-mx-5 rounded-lg border border-gray-600 bg-white px-4 py-5 text-left shadow-xl md:mx-0 md:p-6 dark:bg-gray-900",children:t?e.jsx(h,{center:!0}):e.jsxs(e.Fragment,{children:[e.jsx("h2",{className:"font-medium text-blue-500 text-lg text-stroke-s-sm",children:"Your Referral Links"}),e.jsx(a,{code:s[0]}),e.jsx(a,{code:s[1]}),e.jsx(a,{code:s[2]}),e.jsxs("p",{className:"-mb-2.5 mt-2.5 flex w-full justify-center gap-2 text-center text-amber-500 text-sm",children:[e.jsxs("span",{className:"-ml-3 my-auto text-amber-400",children:[" ",e.jsx(g,{})]}),"Temporarily limited to 3 invites per person."]})]})})]})}),e.jsx("div",{className:"mt-4 w-full px-4 md:container md:px-6",children:e.jsxs("div",{className:"mx-auto w-full space-y-6 md:max-w-3xl",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"font-medium text-2xl text-custom-yellow tracking-tighter sm:text-2xl",children:"Rewards"}),e.jsx("p",{className:"mt-0 text-gray-500 text-sm md:text-lg dark:text-gray-300",children:"Earn these awesome rewards by referring your friends."})]}),e.jsxs("div",{className:"mx-auto grid gap-6 px-14 sm:grid-cols-2 md:grid-cols-3 md:px-12",children:[e.jsx("div",{className:"rounded-lg border border-gray-600 shadow-xl bg-indigo-950",children:e.jsxs("div",{className:"flex h-full flex-col items-center justify-center gap-2 p-6",children:[e.jsx(x,{className:"size-10 text-gray-500 dark:text-gray-400"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-green-500 text-lg",children:"Small Gift Box"}),e.jsxs("div",{className:"flex flex-col text-custom-yellow text-xs",children:[e.jsx("p",{children:"4x Stimulant"}),e.jsx("p",{children:"3x Gourmet Dish"})]}),e.jsx("p",{className:"mt-2 text-gray-500 text-sm dark:text-blue-400",children:"When your friend reaches level 10."})]})]})}),e.jsx("div",{className:"rounded-lg border border-gray-600 shadow-xl bg-indigo-950",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-2 p-6",children:[e.jsx(x,{className:"size-10 text-gray-500 dark:text-gray-400"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-green-500 text-lg",children:"Large Gift Box"}),e.jsxs("div",{className:"flex flex-col text-custom-yellow text-xs",children:[e.jsx("p",{children:"5x Cola"}),e.jsx("p",{children:"5x Stimulant"}),e.jsx("p",{children:"1x Bitcoin"})]}),e.jsx("p",{className:"mt-2 text-gray-500 text-sm dark:text-blue-400",children:"When your friend reaches level 20."})]})]})}),e.jsx("div",{className:"rounded-lg border border-gray-600 shadow-xl bg-indigo-950",children:e.jsxs("div",{className:"flex h-full flex-col items-center justify-center gap-2 p-6",children:[e.jsx(v,{className:"size-10 text-gray-500 dark:text-gray-400"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"font-semibold text-green-500 text-lg",children:"???"}),e.jsx("p",{className:"text-gray-500 text-sm dark:text-blue-400",children:"When your friend reaches level 30."})]})]})})]})]})})]})})}function x(s){return e.jsxs("svg",{...s,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("rect",{x:"3",y:"8",width:"18",height:"4",rx:"1"}),e.jsx("path",{d:"M12 8v13"}),e.jsx("path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7"}),e.jsx("path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5"})]})}const a=s=>{const t=s.code,l="https://app.battleacademy.io/register?alphaKey=",{data:d}=u(t.claimerId,{enabled:!!t.claimerId}),i=r=>{const c=l+r;navigator.clipboard.writeText(c),y.success("Copied to clipboard")},n=r=>r.claimerId?"Claimed by "+d?.username:l+r.code;return e.jsxs("div",{className:"mt-4 flex items-center text-gray-300",children:[e.jsx("input",{readOnly:!0,value:n(t),className:j(t.claimerId&&"text-center text-green-500","flex-1 rounded-lg bg-gray-800 text-sm")}),e.jsx(p,{disabled:t.claimerId,className:"hover:border! hover:border-gray-600! ml-4 rounded-lg text-gray-200",variant:"outline",onClick:()=>i(t.code),children:"Copy"})]})};function v(s){return e.jsxs("svg",{...s,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6"}),e.jsx("path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18"}),e.jsx("path",{d:"M4 22h16"}),e.jsx("path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"}),e.jsx("path",{d:"M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"}),e.jsx("path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z"})]})}export{b as default};
