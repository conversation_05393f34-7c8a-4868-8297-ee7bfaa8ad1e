import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Hook to vote on a suggestion
 */
export const useSuggestionVote = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.suggestionVote.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.suggestionList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.voteHistory.key(),
                });
            },
            onError: (error) => {
                console.error("Suggestion vote error:", error);
                toast.error(error.message || "Failed to vote on suggestion");
            },
        })
    );
};

/**
 * Hook to comment on a suggestion
 */
export const useSuggestionComment = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.suggestionComment.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.comments.key(),
                });
                toast.success("Comment added successfully!");
            },
            onError: (error) => {
                console.error("Suggestion comment error:", error);
                toast.error(error.message || "Failed to add comment");
            },
        })
    );
};

/**
 * Hook to create a new suggestion
 */
export const useCreateSuggestion = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.createSuggestion.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.suggestionList.key(),
                });
                toast.success("Suggestion created successfully!");
            },
            onError: (error) => {
                console.error("Create suggestion error:", error);
                toast.error(error.message || "Failed to create suggestion");
            },
        })
    );
};

/**
 * Hook to change suggestion state (admin only)
 */
export const useChangeSuggestionState = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.updateSuggestionState.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.suggestionList.key(),
                });
                toast.success("Suggestion state updated!");
            },
            onError: (error) => {
                console.error("Change suggestion state error:", error);
                toast.error(error.message || "Failed to update suggestion state");
            },
        })
    );
};
