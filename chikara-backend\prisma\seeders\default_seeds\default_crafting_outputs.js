const crafting_outputs = [
    {
        itemId: 197,
        count: 3,
        itemType: "output",
        craftingRecipeId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 198,
        count: 1,
        itemType: "output",
        craftingRecipeId: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        count: 1,
        itemType: "output",
        craftingRecipeId: 3,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 202,
        count: 1,
        itemType: "output",
        craftingRecipeId: 4,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 203,
        count: 1,
        itemType: "output",
        craftingRecipeId: 5,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 207,
        count: 1,
        itemType: "output",
        craftingRecipeId: 6,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 205,
        count: 1,
        itemType: "output",
        craftingRecipeId: 7,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 200,
        count: 1,
        itemType: "output",
        craftingRecipeId: 8,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 192,
        count: 1,
        itemType: "output",
        craftingRecipeId: 9,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 208,
        count: 1,
        itemType: "output",
        craftingRecipeId: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 196,
        count: 1,
        itemType: "output",
        craftingRecipeId: 11,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 209,
        count: 1,
        itemType: "output",
        craftingRecipeId: 12,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 146,
        count: 2,
        itemType: "output",
        craftingRecipeId: 13,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 149,
        count: 1,
        itemType: "output",
        craftingRecipeId: 14,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 150,
        count: 1,
        itemType: "output",
        craftingRecipeId: 15,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 151,
        count: 1,
        itemType: "output",
        craftingRecipeId: 16,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 183,
        count: 1,
        itemType: "output",
        craftingRecipeId: 17,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 188,
        count: 1,
        itemType: "output",
        craftingRecipeId: 18,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 4,
        count: 1,
        itemType: "output",
        craftingRecipeId: 19,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 8,
        count: 1,
        itemType: "output",
        craftingRecipeId: 20,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 11,
        count: 1,
        itemType: "output",
        craftingRecipeId: 21,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 17,
        count: 1,
        itemType: "output",
        craftingRecipeId: 22,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 21,
        count: 1,
        itemType: "output",
        craftingRecipeId: 23,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 25,
        count: 1,
        itemType: "output",
        craftingRecipeId: 24,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 27,
        count: 1,
        itemType: "output",
        craftingRecipeId: 25,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 30,
        count: 1,
        itemType: "output",
        craftingRecipeId: 26,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 33,
        count: 1,
        itemType: "output",
        craftingRecipeId: 27,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 35,
        count: 1,
        itemType: "output",
        craftingRecipeId: 28,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 115,
        count: 1,
        itemType: "output",
        craftingRecipeId: 29,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 117,
        count: 1,
        itemType: "output",
        craftingRecipeId: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 22,
        count: 1,
        itemType: "output",
        craftingRecipeId: 31,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 37,
        count: 1,
        itemType: "output",
        craftingRecipeId: 32,
        createdAt: new Date(),
        updatedAt: new Date(),
    },

    {
        itemId: 49,
        count: 1,
        itemType: "output",
        craftingRecipeId: 35,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 50,
        count: 1,
        itemType: "output",
        craftingRecipeId: 36,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 51,
        count: 1,
        itemType: "output",
        craftingRecipeId: 37,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 52,
        count: 1,
        itemType: "output",
        craftingRecipeId: 38,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 53,
        count: 1,
        itemType: "output",
        craftingRecipeId: 39,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 59,
        count: 1,
        itemType: "output",
        craftingRecipeId: 40,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 60,
        count: 1,
        itemType: "output",
        craftingRecipeId: 41,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 61,
        count: 1,
        itemType: "output",
        craftingRecipeId: 42,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 62,
        count: 1,
        itemType: "output",
        craftingRecipeId: 43,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 63,
        count: 1,
        itemType: "output",
        craftingRecipeId: 44,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 69,
        count: 1,
        itemType: "output",
        craftingRecipeId: 45,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 70,
        count: 1,
        itemType: "output",
        craftingRecipeId: 46,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 71,
        count: 1,
        itemType: "output",
        craftingRecipeId: 47,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 72,
        count: 1,
        itemType: "output",
        craftingRecipeId: 48,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 73,
        count: 1,
        itemType: "output",
        craftingRecipeId: 49,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 79,
        count: 1,
        itemType: "output",
        craftingRecipeId: 50,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 80,
        count: 1,
        itemType: "output",
        craftingRecipeId: 51,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 81,
        count: 1,
        itemType: "output",
        craftingRecipeId: 52,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 82,
        count: 1,
        itemType: "output",
        craftingRecipeId: 53,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 83,
        count: 1,
        itemType: "output",
        craftingRecipeId: 54,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 89,
        count: 1,
        itemType: "output",
        craftingRecipeId: 55,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 90,
        count: 1,
        itemType: "output",
        craftingRecipeId: 56,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 91,
        count: 1,
        itemType: "output",
        craftingRecipeId: 57,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 92,
        count: 1,
        itemType: "output",
        craftingRecipeId: 58,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 93,
        count: 1,
        itemType: "output",
        craftingRecipeId: 59,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 101,
        count: 1,
        itemType: "output",
        craftingRecipeId: 60,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 102,
        count: 1,
        itemType: "output",
        craftingRecipeId: 61,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 120,
        count: 1,
        itemType: "output",
        craftingRecipeId: 62,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 122,
        count: 1,
        itemType: "output",
        craftingRecipeId: 63,
        createdAt: new Date(),
        updatedAt: new Date(),
    },

    {
        itemId: 125,
        count: 1,
        itemType: "output",
        craftingRecipeId: 66,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 128,
        count: 1,
        itemType: "output",
        craftingRecipeId: 67,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 131,
        count: 1,
        itemType: "output",
        craftingRecipeId: 68,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 138,
        count: 1,
        itemType: "output",
        craftingRecipeId: 69,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 134,
        count: 1,
        itemType: "output",
        craftingRecipeId: 70,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 161,
        count: 1,
        itemType: "output",
        craftingRecipeId: 72,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 176,
        count: 1,
        itemType: "output",
        craftingRecipeId: 73,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 177,
        count: 1,
        itemType: "output",
        craftingRecipeId: 74,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
];

export default crafting_outputs;
