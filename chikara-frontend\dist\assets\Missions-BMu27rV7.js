import{b as j,c as i,e as f,g as b,an as u,y as v,t as U,r as O,B as L,cR as _,j as e,ah as H,ae as w,bH as P,C as g,b_ as k,cS as D}from"./index--cEnoMkg.js";import{C as K}from"./Callout-B-FXvRY0.js";const B=(t={})=>j(i.missions.getList.queryOptions({staleTime:6e4,...t})),V=(t=!0,a={})=>j(i.missions.currentMission.queryOptions({enabled:t,staleTime:3e4,...a})),F=(t={})=>{const a=f();return b(i.missions.startMission.mutationOptions({onSuccess:()=>{a.invalidateQueries({queryKey:v.USER.CURRENTUSERINFO}),a.invalidateQueries({queryKey:i.missions.missionList.key()}),a.invalidateQueries({queryKey:i.missions.currentMission.key()}),u.success("Mission Started!")},onError:r=>{const s=r.message||"Unknown error occurred";console.error(s),u.error(s)},...t}))},X=(t={})=>{const a=f();return b(i.missions.cancelMission.mutationOptions({onSuccess:()=>{a.invalidateQueries({queryKey:v.USER.CURRENTUSERINFO}),a.invalidateQueries({queryKey:i.missions.missionList.key()}),a.invalidateQueries({queryKey:i.missions.currentMission.key()}),u.success("Mission Cancelled!")},onError:r=>{const s=r.message||"Unknown error occurred";console.error(s),u.error(s)},...t}))};function c(...t){return t.filter(Boolean).join(" ")}const N=t=>Math.floor(t/36e5);function W(){const{isLoading:t,data:a}=B(),{data:r}=U(),{data:s}=V(!!r?.currentMission),[l,n]=O.useState(1),m=L(),{MISSION_TIER_REQ_LEVELS:p,MISSION_TIER_REQ_HOURS:R}=m,M=o=>l===o,C=F(),E=X(),S=o=>{C.mutate({id:o})},y=()=>{E.mutate()},T=(o,Q)=>Q/o*100,d=R[l-1],h=p[l-1],q=a?.filter(o=>o.tier===l),I=_(),x=r?.user_achievements?.totalMissionHours||0;return t?null:e.jsxs("div",{className:"flex flex-col px-4 text-shadow sm:px-6 md:mx-auto md:max-w-6xl lg:px-8",children:[e.jsx(K,{className:"mt-3 mb-4",title:"You cannot access any other gameplay activities whilst you're on a mission."}),e.jsxs("div",{className:"mb-0.5 ml-auto flex gap-1 text-xs",children:[e.jsx("p",{className:"text-center text-gray-400",children:"New missions in:"}),e.jsx("div",{"data-testid":"countdown-timer",className:"text-custom-yellow",children:e.jsx(H,{showHours:!0,targetDate:I,showSeconds:!1})})]}),r?.currentMission&&s?e.jsx("div",{className:"mb-4 sm:flex sm:items-center",children:e.jsxs("div",{className:"mx-auto mt-3 flex w-fit flex-col rounded-lg border-2 border-indigo-600 bg-gray-800 px-10 py-2.5 lg:block lg:px-14",children:[e.jsxs("div",{className:"flex justify-center gap-7",children:[e.jsxs("div",{className:"my-auto flex flex-col",children:[e.jsxs("p",{className:"text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200",children:[" ","Current Mission"," "]}),e.jsx("h1",{className:"text-center font-normal text-gray-900 text-stroke-sm text-xl leading-6 lg:text-2xl dark:text-slate-400",children:e.jsx("span",{className:"text-custom-yellow",children:s?.missionName})})]}),e.jsxs("div",{className:"my-auto flex flex-col gap-1",children:[e.jsxs("p",{className:"text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200",children:[" ","Rewards"," "]}),e.jsxs("div",{className:"text-center font-normal text-green-400 text-stroke-sm leading-6 lg:text-xl",children:[s.minCashReward>0&&e.jsxs("p",{className:"text-green-500",children:["¥",s.minCashReward," ~ ¥",s.maxCashReward]}),s.minExpReward>0&&e.jsxs("p",{className:"text-blue-500",children:[s.minExpReward," EXP ~ ","  ",s.maxExpReward," EXP"]}),s.itemReward&&e.jsx("div",{className:"flex w-full",children:e.jsxs("div",{className:"mx-auto flex gap-1",children:[e.jsx(w,{item:s.itemReward,height:"h-6 w-6"}),e.jsxs("p",{className:"my-auto text-base text-pink-400",children:["x",s.itemRewardQuantity]})]})})]})]}),e.jsxs("div",{className:"my-auto flex flex-col",children:[e.jsxs("p",{className:"text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200",children:[" ","Finishes in"," "]}),e.jsx("p",{className:"text-center font-normal text-green-400 text-stroke-sm leading-6 lg:text-2xl",children:P(r?.missionEnds)})]}),e.jsx(g,{className:"font-medium! text-base! mx-auto! mt-1! lg:block! hidden! text-stroke-sm",type:"danger",onClick:()=>y(),children:"Cancel Mission"})]}),e.jsx(g,{className:"font-medium! text-base! mx-auto! mt-2! lg:hidden! h-9! text-stroke-sm",type:"danger",onClick:()=>y(),children:"Cancel Mission"})]})}):null,e.jsx(A,{currentTab:M,setSelectedTier:n,currentLevel:r?.level,missionTierLevelReqs:p}),x<d||r?.level<h?e.jsxs("div",{className:"mt-3 min-h-24 w-full rounded-lg border border-gray-600 bg-slate-800 p-3 text-center text-lg",children:[e.jsxs("p",{className:k(r?.level<h?"text-red-500":"text-green-500 opacity-75","text-stroke-sm"),children:["Requires level ",h]}),x<d&&e.jsxs("div",{className:"mx-auto mt-2 flex flex-col rounded-md border border-gray-600 bg-slate-900 p-2 md:w-3/4",children:[e.jsxs("p",{className:"mx-auto mb-1 text-base text-red-500 text-stroke-sm md:text-lg",children:["Requires ",d," total mission hours completed"]}),e.jsx(D,{barClassName:"bg-blue-600",className:"mx-auto mt-1 w-3/4",value:T(d,x)}),e.jsxs("p",{className:"mt-1 text-gray-200",children:[x," / ",d]})]})]}):e.jsx(z,{missions:q,handleStartMission:S,currentUser:r})]})}const z=({missions:t,handleStartMission:a,currentUser:r})=>e.jsx("div",{className:"-mx-4 mt-3 overflow-hidden bg-white ring-1 ring-gray-300 sm:mx-0 md:mt-0 md:min-h-90 dark:bg-slate-800 dark:ring-gray-600",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-300 dark:divide-gray-600",children:[e.jsx("thead",{className:"text-gray-900 dark:bg-gray-800 dark:text-gray-200 dark:text-stroke-sm ",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"py-3.5 pr-3 pl-4 text-left font-semibold text-sm sm:pl-6 dark:font-normal",children:"Mission"}),e.jsx("th",{scope:"col",className:"hidden px-3 py-3.5 text-left font-semibold text-sm lg:table-cell dark:font-normal",children:"Duration"}),e.jsx("th",{scope:"col",className:"table-cell px-3 py-3.5 text-center font-semibold text-sm dark:font-normal",children:"Rewards"}),e.jsx("th",{scope:"col",className:"relative py-3.5 pr-4 pl-3 sm:pr-6",children:e.jsx("span",{className:"sr-only",children:"Select"})})]})}),e.jsx("tbody",{children:t?.map((s,l)=>e.jsxs("tr",{className:"h-24",children:[e.jsxs("td",{className:c(l===0?"":"border-transparent border-t","relative py-4 pl-4 text-sm text-stroke-sm sm:pr-3 sm:pl-6"),children:[e.jsx("div",{className:"font-medium text-custom-yellow text-stroke-md md:text-lg",children:s.missionName}),e.jsx("div",{className:"mt-1 flex flex-col font-body text-gray-500 text-xs md:text-sm dark:text-gray-200",children:s.description}),l!==0?e.jsx("div",{className:"-top-px absolute right-0 left-6 h-px bg-gray-200 dark:bg-gray-600"}):null]}),e.jsxs("td",{className:c(l===0?"":"border-gray-200 border-t dark:border-gray-600","hidden max-w-96 px-3 py-3.5 text-center text-custom-yellow text-sm text-stroke-sm lg:table-cell lg:text-lg"),children:[N(s.duration)," hrs"]}),e.jsxs("td",{className:c(l===0?"":"border-gray-200 border-t dark:border-gray-600","table-cell px-3 py-3.5 text-center font-medium font-body text-stroke-sm text-xs md:text-sm"),children:[s.minCashReward>0&&e.jsxs("p",{className:"text-green-500",children:["¥",s.minCashReward," ~ ¥",s.maxCashReward]}),s.minExpReward>0&&e.jsxs("p",{className:"text-blue-500",children:[s.minExpReward," EXP ~ ","  ",s.maxExpReward," EXP"]}),s.itemReward&&e.jsx("div",{className:"flex w-full",children:e.jsxs("div",{className:"mx-auto flex gap-1",children:[e.jsx(w,{item:s.itemReward,height:"h-10 w-10"}),e.jsxs("p",{className:"my-auto text-base text-pink-400",children:["x",s.itemRewardQuantity]})]})})]}),e.jsxs("td",{className:c(l===0?"":"border-transparent border-t","relative py-3.5 pr-4 pl-3 text-right font-medium text-sm sm:pr-6"),children:[e.jsxs("p",{className:"-mt-2 mb-1 text-center text-base text-custom-yellow lg:hidden",children:[" ",N(s.duration)," hrs"]}),e.jsx(g,{type:"primary",className:"md:h-10! text-sm! w-20!",disabled:r.level<s.levelReq||r.missionEnds>0,onClick:()=>a(s.id),children:"Begin"}),l!==0?e.jsx("div",{className:"-top-px absolute right-6 left-0 h-px bg-gray-200 dark:bg-gray-600"}):null]})]},s.id))})]})}),A=({currentTab:t,setSelectedTier:a,currentLevel:r,missionTierLevelReqs:s})=>{const l=[{id:1,name:"Tier I",current:t(1)},{id:2,name:"Tier II",current:t(2)},{id:3,name:"Tier III",current:t(3)},{id:4,name:"Tier IV",current:t(4)},{id:5,name:"Tier V",current:t(5)}];return e.jsx("div",{className:"block",children:e.jsx("nav",{className:"-mx-4 relative z-0 flex divide-x divide-gray-200 border-gray-600 border-b shadow-sm sm:mx-0 dark:divide-gray-600","aria-label":"Tabs",children:l.map((n,m)=>e.jsxs("button",{"aria-current":n.current?"page":void 0,className:c(n.current?"text-gray-900":"text-gray-500 hover:text-gray-700",m===0?"md:rounded-tl-lg":"",m===l.length-1?"md:rounded-tr-lg":"","group relative min-w-0 flex-1 overflow-hidden bg-white py-2 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-white"),onClick:()=>a(n.id),children:[e.jsx("span",{className:k(n.current&&"text-indigo-400","text-lg text-stroke-sm"),children:n.name}),s[n.id-1]>r?e.jsxs("p",{className:"text-red-500 text-sm",children:["Requires level ",s[n.id-1]]}):null,e.jsx("span",{"aria-hidden":"true",className:c(n.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-0.5")})]},n.name))})})};export{W as default};
