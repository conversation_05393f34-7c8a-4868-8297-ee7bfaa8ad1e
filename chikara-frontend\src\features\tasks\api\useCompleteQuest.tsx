import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useCompleteQuest() {
    const queryClient = useQueryClient();

    return useMutation(
        api.quests.complete.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.quests.getCompleted.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.quests.getActive.key(),
                });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
            },
            onError: (error) => {
                console.error("Failed to complete quest:", error);
            },
        })
    );
}
