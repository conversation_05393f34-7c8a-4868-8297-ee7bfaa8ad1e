import { battleConfig, levelGatesConfig } from "../../../config/gameConfig.js";
import * as EquipmentService from "../../../core/equipment.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import { NotificationTypes } from "../../../types/notification.js";
import * as UserService from "../../../core/user.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import { GetWeaponForCombatant } from "./battle.equipment.js";
import { ATTACK_TYPE_MELEE } from "./battle.constants.js";
import { validateBattleState } from "../battle.state.js";
import { getDotResistance, getEffectiveMaxStamina } from "./battle.scaling.js";
import type { BattlePlayer, BattleStatusEffects, BattleType, NPCUser, RooftopNPCUser } from "../types/battle.types.js";
import type { ExtUserModel } from "../../../lib/db.js";
import type { ItemEffect } from "../../../types/item.js";
import { LogErrorStack, logger } from "../../../utils/log.js";
import * as ChatHelper from "../../chat/chat.helpers.js";

const { PVP_BATTLE_AP_COST, ROOFTOP_BATTLE_AP_COST } = battleConfig.public;
const { ROOFTOP_BATTLES_LEVEL_GATE } = levelGatesConfig.public;

export const IsUserInBattle = async (user: { id: number }) => {
    const { battleState, error } = await validateBattleState(user.id.toString());
    if (error) return false;

    return battleState;
};

export const GetMaxStamina = (enduranceLevel: number, statusEffects: BattleStatusEffects, userId: number) => {
    const staminaBuff = statusEffects?.["stamina_buff"]?.value || 0;

    if (!enduranceLevel || enduranceLevel < 1) {
        logger.warn(`User ${userId} has invalid endurance level value: ${enduranceLevel}`);
        enduranceLevel = 1;
    }

    // Use the effective endurance calculation from the scaling system
    const baseMaxStamina = getEffectiveMaxStamina(enduranceLevel);

    return baseMaxStamina + staminaBuff;
};

const determineInjury = async (severeChance: number, moderateChance: number, minorChance: number) => {
    const roll = Math.random();

    // Mutually exclusive ranges
    if (roll < severeChance) {
        return await StatusEffectService.GetRandomInjury("Severe");
    } else if (roll < severeChance + moderateChance) {
        return await StatusEffectService.GetRandomInjury("Moderate");
    } else if (roll < severeChance + moderateChance + minorChance) {
        return await StatusEffectService.GetRandomInjury("Minor");
    }

    return null;
};

type DebuffType = "cripple" | "leave" | "mug" | "pvp_loss" | "npc_loss" | "";

export const ApplyBattleLossDebuff = async (
    user: ExtUserModel,
    attacker: BattlePlayer | ExtUserModel,
    type: DebuffType = ""
) => {
    let severeInjuryChance = 0;
    let moderateInjuryChance = 0;
    let minorInjuryChance = 0;

    switch (type) {
        case "cripple": {
            severeInjuryChance = 0.3;
            moderateInjuryChance = 0.3;
            minorInjuryChance = 0.4;
            break;
        }
        case "leave": {
            minorInjuryChance = 0.25;
            break;
        }
        case "mug": {
            moderateInjuryChance = 0.3;
            minorInjuryChance = 0.2;
            break;
        }
        case "pvp_loss":
        case "npc_loss": {
            minorInjuryChance = 0.5;
            break;
        }
        default: {
            break;
        }
    }
    if (severeInjuryChance + moderateInjuryChance + minorInjuryChance > 1.0001) {
        logger.warn(
            `Invalid injury chances for type ${type}: ${severeInjuryChance} ${moderateInjuryChance} ${minorInjuryChance}`
        );
    }

    // const bullyTalent = await TalentHelper.UserHasBullyTalent(attacker.id);

    // if (bullyTalent) {
    //     if (minorInjuryChance !== 0) {
    //         minorInjuryChance *= bullyTalent.modifier;
    //     }
    //     if (bullyTalent.level > 1 && moderateInjuryChance !== 0) {
    //         moderateInjuryChance *= bullyTalent.modifier;
    //     }
    //     if (bullyTalent.level > 2 && severeInjuryChance !== 0) {
    //         severeInjuryChance *= bullyTalent.modifier;
    //     }
    // }

    if (user.level < 15) {
        severeInjuryChance *= 0.75;
        moderateInjuryChance *= 0.75;
        minorInjuryChance *= 0.75;
    }

    const injury = await determineInjury(severeInjuryChance, moderateInjuryChance, minorInjuryChance);

    if (injury) {
        await StatusEffectService.ApplyStatusEffectToUser(user, injury);
        return injury;
    }
    return null;
};

export const GetBeginBattleError = async (
    currentUser: ExtUserModel,
    target: ExtUserModel | NPCUser | RooftopNPCUser,
    battleType: BattleType
) => {
    if (await IsUserInBattle(currentUser)) {
        return "Already in a battle";
    }

    if (currentUser.currentHealth === 0 || currentUser.hospitalisedUntil) {
        return "Can't start a fight when you're dead";
    }

    switch (battleType) {
        case "pvp": {
            const userTarget = target as ExtUserModel;
            if (userTarget === null) {
                return "Opponent not found";
            }

            if (userTarget.level < 5 && currentUser.userType !== "admin") {
                return "Target is too low level";
            }

            if (userTarget.hospitalisedUntil) {
                return "You can't attack people in hospital";
            }

            if (userTarget.jailedUntil) {
                return "You can't attack people in jail";
            }

            if (await IsUserInBattle(userTarget)) {
                return "Target is already in combat";
            }

            if (userTarget.currentHealth === 0) {
                LogErrorStack({
                    error: new Error("User has 0 hp but isn't in hospital:" + JSON.stringify(userTarget)),
                });
                return "You can't attack dead people";
            }

            if (currentUser.actionPoints < PVP_BATTLE_AP_COST) {
                return "Not enough action points";
            }

            break;
        }
        case "pve":
        case "pve-explore": {
            const npcTarget = target as NPCUser;
            if (!npcTarget.name) {
                return "No enemy name";
            }

            if (!npcTarget.strength && npcTarget.strength !== 0) {
                return "No enemy strength";
            }
            if (!npcTarget.defence && npcTarget.defence !== 0) {
                return "No enemy defence";
            }
            if (!npcTarget.weaponDamage && npcTarget.weaponDamage !== 0) {
                return "No enemy weaponDamage";
            }

            break;
        }
        case "pve-rooftop": {
            const rooftopTarget = target as RooftopNPCUser;
            if (!rooftopTarget.name) {
                return "No enemy name";
            }

            if (!rooftopTarget.strength && rooftopTarget.strength !== 0) {
                return "No enemy strength";
            }
            if (!rooftopTarget.defence && rooftopTarget.defence !== 0) {
                return "No enemy defence";
            }
            if (!rooftopTarget.weaponDamage && rooftopTarget.weaponDamage !== 0) {
                return "No enemy weaponDamage";
            }
            if (currentUser.level < ROOFTOP_BATTLES_LEVEL_GATE) {
                return "Level too low!";
            }
            if (currentUser.actionPoints < ROOFTOP_BATTLE_AP_COST) {
                return "Not enough action points";
            }

            break;
        }
        default: {
            return "Unsupported battle type";
        }
    }
    return null;
};

const countAbilitiesEquipped = (user: ExtUserModel) => {
    return ["equippedAbility1Id", "equippedAbility2Id", "equippedAbility3Id", "equippedAbility4Id"].filter(
        (ability) => {
            const abilityKey = ability as keyof ExtUserModel;
            return abilityKey in user && Boolean(user[abilityKey]);
        }
    ).length;
};

export const AddDefenderBuff = (enemyDamage: number, currentUser: ExtUserModel) => {
    let damage = enemyDamage;
    const currentUserAbilities = countAbilitiesEquipped(currentUser);
    if (currentUserAbilities > 0) {
        damage *= 1 + currentUserAbilities * 0.08;
        damage = Math.max(1, Math.round(damage));
    }
    return damage;
};

export const applyRooftopBattleOffensivePassives = (npc: BattlePlayer, finalDamage: number, user: BattlePlayer) => {
    const statusEffects = npc.statusEffects;

    if (statusEffects["npc_stamina_buff_attack"]) {
        const missingStamina = user.maxStamina - user.currentStamina;
        const bonusdmg = 1 + missingStamina * 0.03;
        finalDamage *= bonusdmg;
    }

    if (statusEffects["npc_enrage"]) {
        const currentTurn = npc["currentTurn"];
        finalDamage *= Math.pow(1.25, currentTurn);
    }

    if (statusEffects["deep_sleep"]) {
        return 0;
    }

    return Math.round(finalDamage);
};

export const applyRooftopBattleDefensivePassives = (
    npc: BattlePlayer,
    finalDamage: number,
    user: BattlePlayer,
    isMeleeAttack: boolean,
    poisonDamage: number
) => {
    const statusEffects = npc.statusEffects;

    if (statusEffects["anti_melee"] && isMeleeAttack) {
        finalDamage *= 0.25;
    }
    if (statusEffects["anti_ranged"] && !isMeleeAttack) {
        finalDamage *= 0.25;
    }

    if (statusEffects["anti_attack"]) {
        finalDamage -= poisonDamage;
        finalDamage *= 0.2;
        finalDamage += poisonDamage;
    }

    if (statusEffects["npc_heal_over_time"]) {
        const missingHealth = npc.maxHealth - npc.currentHealth;
        npc.currentHealth += missingHealth * 0.15;
        npc.currentHealth = Math.round(Math.min(npc.maxHealth, npc.currentHealth));
    }

    if (statusEffects["npc_weakened"]) {
        const currentTurn = npc["currentTurn"];
        finalDamage *= Math.pow(1.25, currentTurn);
    }
    return Math.round(finalDamage);
};

/**
 * Helper function to process item effects for stat modifiers
 * @param user - The battle player
 * @param effectKey - The effect key to look for ('strength', 'dexterity', 'defence')
 * @param baseValue - The base stat value to modify
 * @returns The total modifier from all equipped items
 */
export const processStatItemEffects = (user: BattlePlayer, effectKey: string, baseValue: number): number => {
    if (user.userType === "npc" || user.userType === "rooftop_npc" || !user.equipment) {
        return 0; // NPCs don't have equipment effects
    }

    // Guard against division by zero or negative values
    if (baseValue <= 0) {
        return 0;
    }

    let totalModifier = 0;

    // Process all equipped items
    for (const equippedItem of Object.values(user.equipment)) {
        if (equippedItem?.itemEffects) {
            // Process all matching effects for stacking support, not just the first one
            const statEffects = equippedItem.itemEffects.filter((effect: ItemEffect) => effect.effectKey === effectKey);

            for (const statEffect of statEffects) {
                if (statEffect.effectValue !== undefined) {
                    const effectValue = EquipmentService.CalculateItemEffectValue(baseValue, statEffect);

                    // For stat effects, we typically want additive bonuses
                    // Convert percentage-based effects to additive modifiers
                    totalModifier += effectValue - baseValue;
                }
            }
        }
    }

    return totalModifier / baseValue; // Return as a multiplier
};

/**
 * Helper function to process npcDamage item effects
 * @param user - The battle player (attacker)
 * @returns The total multiplier for damage against NPCs
 */
export const processNpcDamageItemEffects = (user: BattlePlayer): number => {
    if (user.userType !== "player" || !user.equipment) {
        return 1; // Only players have equipment effects
    }

    let totalMultiplier = 1;

    // Process all equipped items
    for (const equippedItem of Object.values(user.equipment)) {
        if (equippedItem?.itemEffects) {
            const npcDamageEffect = equippedItem.itemEffects.find(
                (effect: ItemEffect) => effect.effectKey === "npcDamage"
            );
            if (npcDamageEffect && npcDamageEffect.effectValue !== undefined) {
                const effectValue = EquipmentService.CalculateItemEffectValue(1, npcDamageEffect);

                // For npcDamage, we want multiplicative effects
                if (npcDamageEffect.effectModifier === "multiply") {
                    totalMultiplier *= effectValue;
                } else {
                    // Treat other modifiers as additive percentage bonuses
                    totalMultiplier *= 1 + effectValue / 100;
                }
            }
        }
    }

    return totalMultiplier;
};

export const ApplyLifeSteal = (user: BattlePlayer, attackType: string, damage: number) => {
    if (attackType !== "ranged" && attackType !== "melee") return 0;
    const isMeleeAttack = attackType === ATTACK_TYPE_MELEE;

    const weapon = GetWeaponForCombatant(user, isMeleeAttack);

    if ("itemEffects" in weapon) {
        const itemEffects = weapon.itemEffects;
        const lifestealEffect = itemEffects?.find((effect) => effect.effectKey === "lifesteal");

        if (!lifestealEffect || !lifestealEffect.effectValue) {
            return 0;
        }

        const lifestealValue = EquipmentService.CalculateItemEffectValue(damage, lifestealEffect);

        // For lifesteal, we typically want a percentage/multiplier effect
        // So if it's not already a percentage, treat the effectValue as a percentage
        const healthToSteal =
            lifestealEffect.effectModifier === "multiply"
                ? lifestealValue
                : damage * (lifestealEffect.effectValue / 100);

        user.currentHealth = Math.min(user.maxHealth, user.currentHealth + healthToSteal);

        return healthToSteal;
    }

    return 0;
};

export const ApplyBleed = (user: BattlePlayer) => {
    const bleedDebuff = user.statusEffects["bleed_debuff"];
    if (!bleedDebuff || bleedDebuff.value === undefined) {
        return 0;
    }

    let bleedDamage = Math.max(0, Math.round(user.maxHealth * bleedDebuff.value));

    // Apply DoT resistance from defence stat for players
    if (user.userType === "player") {
        const dotResistance = getDotResistance(user.attributes.defence);
        bleedDamage = Math.round(bleedDamage * (1 - dotResistance));
    }

    user.currentHealth = Math.max(0, user.currentHealth - bleedDamage);

    return bleedDamage;
};

export const ApplyCombatRegen = (user: BattlePlayer, modifier: number | undefined) => {
    if (!modifier) return;
    return (user.currentHealth = Math.min(user.maxHealth, Math.round(user.currentHealth + user.maxHealth * modifier)));
};

export const SubtractTurnFromStatusEffects = (user: BattlePlayer) => {
    const statusEffects = user.statusEffects;

    for (const statusName in statusEffects) {
        if (statusEffects[statusName].turns) {
            if (statusEffects[statusName].turns === 1) {
                delete statusEffects[statusName];
            } else {
                --statusEffects[statusName].turns;
            }
        }
    }
    user.statusEffects = statusEffects;
};

export const hospitaliseUser = async (
    user: ExtUserModel,
    attacker: BattlePlayer | ExtUserModel,
    type: "pvp_loss" | "npc_loss" | "cripple" | "leave" | "mug"
) => {
    const injury = await ApplyBattleLossDebuff(user, attacker, type);

    // Update user health in memory for notification consistency if needed
    user.currentHealth = 1;

    NotificationService.NotifyUser(user.id, NotificationTypes.hospitalised, {
        reason: attacker.username ? "battle" : "roguelike",
        attackerId: attacker.id,
        name: attacker.username,
        injury: injury ? injury.name : null,
        injuryTier: injury ? injury.tier : null,
    });

    // Update only the health in the database
    await UserService.updateUser(user.id, { currentHealth: 1 });
    return injury;
};

export const xpPenaltyModifier = (currentUser: ExtUserModel, target: BattlePlayer) => {
    if (target.userType !== "player") {
        // NPCS - 5% XP gain/penalty per level difference capped at +25% or -100%
        const MAX_XP_MODIFIER = 1.25;
        const MIN_XP_MODIFIER = 0;

        return Math.min(Math.max(1 - (currentUser.level - target.level) * 0.05, MIN_XP_MODIFIER), MAX_XP_MODIFIER);
    }
    // PLAYERS- 10% XP gain/penalty per level difference capped at + 50% or -70%
    const MAX_XP_MODIFIER = 1.5;
    const MIN_XP_MODIFIER = 0.3;

    return Math.min(Math.max(1 - (currentUser.level - target.level) * 0.1, MIN_XP_MODIFIER), MAX_XP_MODIFIER);
};

/**
 * Announces when certain NPCs are defeated in the global chat
 */
export const rooftopNPCDefeatedAnnouncement = async (currentUser: ExtUserModel, target: RooftopNPCUser) => {
    const announceNPCs = [8]; // NPCs that should trigger a global announcement when defeated
    if (announceNPCs.includes(target.id)) {
        await ChatHelper.SendAnnouncementMessage(
            "npcDefeated",
            JSON.stringify({ id: currentUser.id, username: currentUser.username, targetName: target.name })
        );
    }
};
