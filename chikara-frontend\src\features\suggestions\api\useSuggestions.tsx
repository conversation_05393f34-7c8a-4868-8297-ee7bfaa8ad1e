import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}

/**
 * Hook to get all suggestions
 */
export const useGetSuggestions = (options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.suggestionList.queryOptions({
            ...options,
        })
    );
};

/**
 * Hook to get user's vote history
 */
export const useGetVoteHistory = (options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.voteHistory.queryOptions({
            ...options,
        })
    );
};

/**
 * Hook to get comments for a suggestion
 */
export const useGetSuggestionComments = (suggestionId: number, options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.comments.queryOptions({
            input: { id: suggestionId },
            ...options,
        })
    );
};
