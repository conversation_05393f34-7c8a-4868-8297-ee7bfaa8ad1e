import { Prisma } from "@prisma/client";
import ms from "ms";

const twelveHoursInMs = ms("12h");

const statusEffects: Prisma.status_effectCreateInput[] = [
    // Converted from Fracture Injuries
    {
        name: "Bruised Ribs",
        source: "fracture_injury",
        effectType: "DEBUFF",
        category: "damage",
        tier: "Minor",
        duration: twelveHoursInMs,
        modifier: 0.9, // Converted from 0.1 reduction to 0.9 multiplier
        modifierType: "multiply",
        description: "Reduces damage output due to bruised ribs",
    },
    {
        name: "Broken Hand",
        source: "fracture_injury",
        effectType: "DEBUFF",
        category: "damage",
        tier: "Moderate",
        duration: twelveHoursInMs,
        modifier: 0.75, // Converted from 0.25 reduction to 0.75 multiplier
        modifierType: "multiply",
        description: "Significantly reduces damage output due to broken hand",
    },
    {
        name: "Broken Foot",
        source: "fracture_injury",
        effectType: "DEBUFF",
        category: "damage",
        tier: "Moderate",
        duration: twelveHoursInMs,
        modifier: 0.75,
        modifierType: "multiply",
        description: "Significantly reduces damage output due to broken foot",
    },
    {
        name: "Compound Fracture",
        source: "fracture_injury",
        effectType: "DEBUFF",
        category: "damage",
        tier: "Severe",
        duration: twelveHoursInMs,
        modifier: 0.5,
        modifierType: "multiply",
        description: "Severely reduces damage output due to compound fracture",
    },

    // Converted from Bleed Injuries
    {
        name: "Cuts and Scrapes",
        source: "bleeding_injury",
        effectType: "DEBUFF",
        category: "health",
        tier: "Minor",
        duration: twelveHoursInMs,
        modifier: 0.03,
        modifierType: "multiply",
        description: "Causes minor health loss over time",
    },
    {
        name: "Light Bleed",
        source: "bleeding_injury",
        effectType: "DEBUFF",
        category: "health",
        tier: "Moderate",
        duration: twelveHoursInMs,
        modifier: 0.06,
        modifierType: "multiply",
        description: "Causes moderate health loss over time",
    },
    {
        name: "Heavy Bleeding",
        source: "bleeding_injury",
        effectType: "DEBUFF",
        category: "health",
        tier: "Severe",
        duration: twelveHoursInMs,
        modifier: 0.1,
        modifierType: "multiply",
        description: "Causes severe health loss over time",
    },

    // Converted from Concussion Injuries
    {
        name: "Mild Headache",
        source: "concussion_injury",
        effectType: "DEBUFF",
        category: "accuracy",
        tier: "Minor",
        duration: twelveHoursInMs,
        modifier: 0.9,
        modifierType: "multiply",
        description: "Slightly reduces accuracy due to headache",
    },
    {
        name: "Dizziness",
        source: "concussion_injury",
        effectType: "DEBUFF",
        category: "accuracy",
        tier: "Minor",
        duration: twelveHoursInMs,
        modifier: 0.9,
        modifierType: "multiply",
        description: "Slightly reduces accuracy due to dizziness",
    },
    {
        name: "Concussion",
        source: "concussion_injury",
        effectType: "DEBUFF",
        category: "accuracy",
        tier: "Moderate",
        duration: twelveHoursInMs,
        modifier: 0.6,
        modifierType: "multiply",
        description: "Significantly reduces accuracy due to concussion",
    },
    {
        name: "Severe Concussion",
        source: "concussion_injury",
        effectType: "DEBUFF",
        category: "accuracy",
        tier: "Severe",
        duration: twelveHoursInMs,
        modifier: 0.4,
        modifierType: "multiply",
        description: "Severely reduces accuracy due to severe concussion",
    },

    // Converted from Contusion Injuries
    {
        name: "Skin Bruise",
        source: "contusion_injury",
        effectType: "DEBUFF",
        category: "defence",
        tier: "Minor",
        duration: twelveHoursInMs,
        modifier: 0.9,
        modifierType: "multiply",
        description: "Slightly reduces defence due to skin bruising",
    },
    {
        name: "Deep Muscle Contusion",
        source: "contusion_injury",
        effectType: "DEBUFF",
        category: "defence",
        tier: "Moderate",
        duration: twelveHoursInMs,
        modifier: 0.75,
        modifierType: "multiply",
        description: "Significantly reduces defence due to muscle contusion",
    },
    {
        name: "Bone Contusion",
        source: "contusion_injury",
        effectType: "DEBUFF",
        category: "defence",
        tier: "Severe",
        duration: twelveHoursInMs,
        modifier: 0.5,
        modifierType: "multiply",
        description: "Severely reduces defence due to bone contusion",
    },

    // Converted from Fatigue Injuries
    {
        name: "Tiredness",
        source: "fatigue_injury",
        effectType: "DEBUFF",
        category: "stamina_regen",
        tier: "Minor",
        duration: twelveHoursInMs,
        modifier: 0.5,
        modifierType: "multiply",
        description: "Reduces stamina regeneration due to tiredness",
    },
    {
        name: "Exhaustion",
        source: "fatigue_injury",
        effectType: "DEBUFF",
        category: "stamina",
        tier: "Moderate",
        duration: twelveHoursInMs,
        modifier: 0.7,
        modifierType: "multiply",
        disabled: true,
        description: "Significantly reduces maximum stamina due to exhaustion",
    },
    {
        name: "Burnout",
        source: "fatigue_injury",
        effectType: "DEBUFF",
        category: "ability_usage",
        tier: "Severe",
        duration: twelveHoursInMs,
        modifier: 0,
        modifierType: "multiply",
        description: "Prevents ability usage due to complete burnout",
    },

    // Converted from Trauma Injuries
    {
        name: "Twisted Ankle",
        source: "trauma_injury",
        effectType: "DEBUFF",
        category: "ability_damage",
        tier: "Minor",
        duration: twelveHoursInMs,
        modifier: 0.9,
        modifierType: "multiply",
        description: "Slightly reduces ability damage due to twisted ankle",
    },
    {
        name: "Wrist Sprain",
        source: "trauma_injury",
        effectType: "DEBUFF",
        category: "ability_damage",
        tier: "Moderate",
        duration: twelveHoursInMs,
        modifier: 0.75,
        modifierType: "multiply",
        description: "Significantly reduces ability damage due to wrist sprain",
    },
    {
        name: "Severe Knee Sprain",
        source: "trauma_injury",
        effectType: "DEBUFF",
        category: "ability_damage",
        tier: "Severe",
        duration: twelveHoursInMs,
        modifier: 0.5,
        modifierType: "multiply",
        description: "Severely reduces ability damage due to knee sprain",
    },
];

export default statusEffects;
