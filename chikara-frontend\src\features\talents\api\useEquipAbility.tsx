import { useMutation, useQueryClient, type UseMutationOptions } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import toast from "react-hot-toast";
import type { Ability } from "../types/talents";

type AbilitySlot = 1 | 2 | 3 | 4;
const MAX_SLOTS = 4;

interface EquipAbilityParams {
    talentId: number;
    slot: AbilitySlot;
}

interface EquipAbilityOptions {
    onClose?: () => void;
    successMessage?: string;
}

const useEquipAbility = (
    { onClose, successMessage }: EquipAbilityOptions = {},
    options?: UseMutationOptions<any, Error, EquipAbilityParams>
) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.talents.equipAbility.mutationOptions({
            onMutate: async (variables) => {
                const equippedQueryOptions = api.talents.getEquippedAbilities.queryOptions();
                await queryClient.cancelQueries(equippedQueryOptions);

                const previousData = queryClient.getQueryData<(Ability | null)[]>(
                    api.talents.getEquippedAbilities.key()
                );

                // Get the talent info for optimistic update
                const talentInfo = await queryClient.getQueryData(api.talents.getUnlockedTalents.key());
                const talent = (talentInfo as any)?.talentList?.find(
                    (t: any) => t.talentInfo.id === variables.talentId
                );

                if (talent) {
                    const updatedData = previousData ? [...previousData] : Array(MAX_SLOTS).fill(null);
                    updatedData[variables.slot - 1] = talent.talentInfo;
                    queryClient.setQueryData(api.talents.getEquippedAbilities.key(), updatedData);
                }

                return { previousData };
            },
            onError: (error: any, _vars, context: any) => {
                // Revert to previous data if the mutation fails
                if (context?.previousData) {
                    queryClient.setQueryData(api.talents.getEquippedAbilities.key(), context.previousData);
                }

                toast.error(error.response?.data?.error || "Failed to equip ability");
            },
            onSuccess: (_data, variables) => {
                const defaultMessage = successMessage || `Ability equipped to slot ${variables.slot}`;
                toast.success(defaultMessage);
                onClose?.();
            },
            onSettled: () => {
                // Ensure we refetch to stay in sync with the server
                const equippedQueryOptions = api.talents.getEquippedAbilities.queryOptions();
                queryClient.invalidateQueries(equippedQueryOptions);
                queryClient.invalidateQueries({ queryKey: api.talents.getUnlockedTalents.key() });
            },
            ...options,
        })
    );
};

export default useEquipAbility;
