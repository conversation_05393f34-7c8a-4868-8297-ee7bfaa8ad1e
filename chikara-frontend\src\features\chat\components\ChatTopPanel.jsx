import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { ChevronRight, Scroll, Volume2, VolumeX, X } from "lucide-react";
import { Link } from "react-router-dom";
import { useSessionStore } from "../../../app/store/stores";
import ChatDropdownButton from "./ChatDropdownButton";
import { api } from "@/helpers/api";

export default function ChatTopPanel({ muteChat, setMuteChat, currentUser, socket, setHideChat, chatRoom, fullSize }) {
    const { hidePollNotification, setHidePollNotification } = useSessionStore();
    const { data: availablePolls } = useQuery(api.suggestions.availablePolls.queryOptions());

    return (
        <div className="-mx-2 relative h-16 border-slate-300 border-b dark:border-gray-600/25">
            <div className="mx-2 flex h-full md:mr-5">
                <div className="relative mx-1 my-auto">
                    <ButtonWrapper>
                        {muteChat ? (
                            <VolumeX size={20} color="#938ea2" onClick={() => setMuteChat(false)} />
                        ) : (
                            <Volume2 size={20} color="#938ea2" onClick={() => setMuteChat(true)} />
                        )}
                    </ButtonWrapper>
                </div>

                <div className="m-auto flex pr-6 text-sm md:pr-0">
                    <ChatDropdownButton chatRoom={chatRoom} currentUser={currentUser} />
                </div>

                <div className="relative mx-1 my-auto">
                    <ButtonWrapper>
                        <Scroll size={20} color="#938ea2" />
                    </ButtonWrapper>
                </div>
                <div
                    className="relative mx-1 my-auto hidden translate-x-0 transition duration-200 lg:block"
                    onClick={() => setHideChat(true)}
                >
                    <ButtonWrapper>
                        <ChevronRight size={20} color="#938ea2" />
                    </ButtonWrapper>
                </div>
            </div>

            <div
                className={cn(
                    fullSize ? "md:-bottom-12 -bottom-10 md:h-12" : "-bottom-10",
                    "absolute left-0 z-2 h-11 w-[calc(100%-1px)] bg-linear-to-b from-[#191924] to-transparent"
                )}
            >
                {!hidePollNotification && availablePolls?.length > 0 ? (
                    <div className="relative flex h-12 w-full rounded-b-lg border-2 border-black bg-indigo-500/90 text-center text-gray-400 text-sm dark:text-gray-200">
                        <Link to="/polls" className="m-auto cursor-pointer">
                            <p className="m-auto animate-pulse text-custom-yellow text-lg font-display">
                                Vote in the new Poll
                            </p>
                        </Link>

                        <div
                            className="-translate-y-1/2 absolute top-1/2 right-2 z-10 cursor-pointer rounded-lg border-2 border-transparent p-1 hover:border-black hover:bg-indigo-600 hover:text-white"
                            onClick={() => {
                                setHidePollNotification(true);
                            }}
                        >
                            <X className="size-6" />
                        </div>
                    </div>
                ) : null}
            </div>
        </div>
    );
}

const ButtonWrapper = ({ children }) => {
    return (
        <button className="size-9 cursor-pointer rounded-md border-[#1F1F2D] border-b bg-[#28283C] shadow-[0_1px_0_0_#303045_inset,0_2px_2px_0_rgba(0,0,0,0.25)] transition hover:brightness-110">
            <div className="flex items-center justify-center">{children}</div>
        </button>
    );
};
