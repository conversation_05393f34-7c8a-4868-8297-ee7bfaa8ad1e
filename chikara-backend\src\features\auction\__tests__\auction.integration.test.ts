import prismaMock from "../../../__tests__/prismaClientMock";
import { defaultMockAuctionItem, defaultMockItem, defaultMockUser } from "../../../__tests__/prismaModelMocks";
import AchievementHelpers from "../../../core/achievement.service";
import * as InventoryService from "../../../core/inventory.service";
import * as NotificationService from "../../../core/notification.service";
import * as AuctionController from "../auction.controller";
import { logAction } from "../../../lib/actionLogger";
import { db } from "../../../lib/db";
import { NotificationTypes } from "../../../types/notification.js";
import { getNow } from "../../../utils/dateHelpers";
/* eslint-disable unicorn/no-useless-undefined */
import { UTCDate } from "@date-fns/utc";
import { AuctionItemStatus, ItemTypes } from "@prisma/client";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

// Mock the database and core services
vi.mock("../../../lib/db", () => ({
    db: {
        $transaction: vi.fn(async (callback) => {
            return await callback(prismaMock);
        }),
        auction_item: {
            findMany: vi.fn(),
            findFirst: vi.fn(),
            findUnique: vi.fn(),
            create: vi.fn(),
            update: vi.fn(),
        },
        item: {
            findUnique: vi.fn(),
        },
        user: {
            findUnique: vi.fn(),
            update: vi.fn(),
        },
    },
}));

vi.mock("../../../core/inventory.service", () => ({
    UserHasTradeableNumberOfItem: vi.fn().mockResolvedValue(true),
    SubtractItemFromUser: vi.fn().mockResolvedValue(undefined),
    AddItemToUser: vi.fn().mockResolvedValue(undefined),
}));

vi.mock("../../../core/notification.service", () => ({
    NotifyUser: vi.fn().mockResolvedValue(undefined),
}));

vi.mock("../../../core/achievement.service");
vi.mock("../../../lib/actionLogger");
vi.mock("../../../utils/dateHelpers");

describe("Auction Feature Integration Tests", () => {
    const mockDate = new UTCDate("2023-01-01T00:00:00Z");

    // Setup common test data
    const mockUser = { ...defaultMockUser, id: 1, cash: 1000 };
    const mockBuyer = { ...defaultMockUser, id: 2, cash: 2000 };
    const mockWeapon = { ...defaultMockItem, id: 10, itemType: "weapon" as ItemTypes, name: "Test Weapon" };
    const mockAuctionItem = {
        ...defaultMockAuctionItem,
        id: 100,
        sellerId: mockUser.id,
        itemId: mockWeapon.id,
        buyoutPrice: 500,
        quantity: 2,
        bankFunds: false,
        item: mockWeapon,
        user: {
            id: mockUser.id,
            username: "testuser",
            avatar: "avatar.png",
        },
    };

    beforeEach(() => {
        vi.resetAllMocks();
        vi.mocked(getNow).mockReturnValue(mockDate);
        vi.mocked(logAction).mockResolvedValue(undefined);
        vi.mocked(AchievementHelpers.UpdateUserAchievement).mockResolvedValue({} as any);
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    describe("End-to-end Auction Flow", () => {
        it("should handle the full auction lifecycle: create, buyout, cancel", async () => {
            // 1. Setup mocks for listing creation
            vi.mocked(db.item.findUnique).mockResolvedValue(mockWeapon);
            vi.mocked(db.user.findUnique).mockResolvedValueOnce(mockUser);
            vi.mocked(InventoryService.UserHasTradeableNumberOfItem).mockResolvedValue(true);
            vi.mocked(db.user.update).mockResolvedValue({ ...mockUser, cash: mockUser.cash - 50 }); // 50 is min fee
            vi.mocked(InventoryService.SubtractItemFromUser).mockResolvedValue(undefined);
            vi.mocked(db.auction_item.create).mockResolvedValue(mockAuctionItem);

            // 2. Create auction listing
            const createResult = await AuctionController.createAuctionListing(
                mockUser.id,
                mockWeapon.id,
                2, // Quantity
                500, // Buyout price
                24, // 24-hour auction
                false // Don't bank funds
            );

            expect(createResult).toEqual({ data: mockAuctionItem });
            expect(InventoryService.SubtractItemFromUser).toHaveBeenCalled();
            expect(logAction).toHaveBeenCalledWith(
                expect.objectContaining({
                    action: "AUCTION_LISTING_CREATED",
                })
            );

            // 3. Setup mocks for auction listing buyout
            vi.mocked(db.auction_item.findFirst).mockResolvedValue(mockAuctionItem);
            vi.mocked(db.user.findUnique).mockResolvedValueOnce(mockBuyer);

            // Mock transaction behavior for buying only one item
            const mockUpdatedAuctionItem = {
                ...mockAuctionItem,
                quantity: 1, // One item remaining
                status: "in_progress" as AuctionItemStatus, // Still in progress
            };

            // Mock the transaction to resolve with our desired result
            vi.mocked(db.$transaction).mockImplementationOnce(async (callback) => {
                // Create a basic transaction context that can pass validation
                const txContext = {
                    user: {
                        findUnique: vi.fn().mockResolvedValue(mockUser),
                        update: vi.fn().mockResolvedValue(mockUser),
                    },
                    auction_item: {
                        update: vi.fn().mockResolvedValue(mockUpdatedAuctionItem),
                    },
                } as any; // Use 'as any' to bypass TypeScript checking for the test

                // Execute the callback to trigger the InventoryService.AddItemToUser call
                await callback(txContext);

                return {
                    seller: mockUser,
                    updatedAuctionItem: mockUpdatedAuctionItem,
                };
            });

            // 4. Buy one item from the auction
            const buyResult = await AuctionController.buyoutAuctionListing(
                mockBuyer.id,
                mockAuctionItem.id,
                1 // Buy only one item
            );

            expect(buyResult).toEqual({ data: mockUpdatedAuctionItem });
            expect(InventoryService.AddItemToUser).toHaveBeenCalledWith(
                expect.objectContaining({
                    userId: mockBuyer.id,
                    itemId: mockWeapon.id,
                    amount: 1,
                })
            );
            expect(AchievementHelpers.UpdateUserAchievement).toHaveBeenCalledTimes(2);
            expect(NotificationService.NotifyUser).toHaveBeenCalled();
            expect(logAction).toHaveBeenCalledWith(
                expect.objectContaining({
                    action: "AUCTION_ITEM_PURCHASED",
                })
            );

            // 5. Setup mocks for cancellation
            vi.mocked(db.auction_item.findFirst).mockResolvedValue(mockUpdatedAuctionItem);

            // Mock the transaction for cancellation
            vi.mocked(db.$transaction).mockImplementationOnce(async (callback) => {
                // Create a basic transaction context
                const txContext = {
                    user: {
                        findUnique: vi.fn().mockResolvedValue(mockUser),
                        update: vi.fn().mockResolvedValue(mockUser),
                    },
                    auction_item: {
                        update: vi.fn().mockResolvedValue({
                            ...mockUpdatedAuctionItem,
                            status: "cancelled" as AuctionItemStatus,
                        }),
                    },
                } as any; // Use 'as any' to bypass TypeScript checking for the test

                // Execute the callback to trigger InventoryService calls
                await callback(txContext);

                return "Auction item successfully cancelled.";
            });

            // 6. Cancel the remaining auction
            const cancelResult = await AuctionController.cancelAuctionListing(mockUser.id, mockAuctionItem.id);

            expect(cancelResult).toEqual({ data: "Auction item successfully cancelled." });
            expect(InventoryService.AddItemToUser).toHaveBeenCalledWith(
                expect.objectContaining({
                    userId: mockUser.id,
                    itemId: mockWeapon.id,
                    amount: 1, // Only one item should be returned (remaining in auction)
                    isTradeable: true,
                })
            );
            expect(logAction).toHaveBeenCalledWith(
                expect.objectContaining({
                    action: "AUCTION_LISTING_CANCELLED",
                })
            );
        });

        it("should handle a complete buyout with bank funds option", async () => {
            // Create a bank funds enabled auction item
            const bankFundsAuction = {
                ...mockAuctionItem,
                bankFunds: true,
            };

            // 1. Setup mocks for finding the auction with bank funds
            vi.mocked(db.auction_item.findFirst).mockResolvedValue(bankFundsAuction);
            vi.mocked(db.user.findUnique).mockResolvedValue(mockBuyer);

            // 2. Setup the transaction result
            const completedAuction = {
                ...bankFundsAuction,
                quantity: 0,
                status: "complete" as AuctionItemStatus,
            };

            // 3. Mock the transaction to handle bank funds
            vi.mocked(db.$transaction).mockImplementationOnce(async (callback) => {
                // Calculate expected bank balance (85% of sale goes to bank)
                const expectedBankBalance =
                    (mockUser.bank_balance || 0) + bankFundsAuction.buyoutPrice * bankFundsAuction.quantity * 0.85;

                // Create a basic transaction context
                const txContext = {
                    user: {
                        findUnique: vi.fn().mockResolvedValue({
                            ...mockUser,
                            bank_balance: expectedBankBalance,
                        }),
                        update: vi.fn().mockResolvedValue({
                            ...mockUser,
                            bank_balance: expectedBankBalance,
                        }),
                    },
                    auction_item: {
                        update: vi.fn().mockResolvedValue(completedAuction),
                    },
                } as any; // Use 'as any' to bypass TypeScript checking for the test

                // Execute the callback to trigger InventoryService calls
                await callback(txContext);

                return {
                    seller: { ...mockUser, bank_balance: expectedBankBalance },
                    updatedAuctionItem: completedAuction,
                };
            });

            // 4. Buy all items from the auction
            const buyResult = await AuctionController.buyoutAuctionListing(
                mockBuyer.id,
                bankFundsAuction.id,
                bankFundsAuction.quantity // Buy all items
            );

            expect(buyResult).toEqual({ data: completedAuction });

            // Check if notification sends proper bank funds flag
            expect(NotificationService.NotifyUser).toHaveBeenCalledWith(
                mockUser.id,
                NotificationTypes.auction_item_sold,
                expect.objectContaining({
                    fundsBanked: true,
                    listingComplete: true,
                })
            );
        });

        it("should fail to create listing with invalid auction length", async () => {
            // Attempt to create with invalid auction length
            const result = await AuctionController.createAuctionListing(
                mockUser.id,
                mockWeapon.id,
                1,
                500,
                36, // Invalid auction length (not 12, 24, or 48)
                false
            );

            expect(result).toEqual({ error: "Invalid auction length. Must be 12, 24, or 48 hours." });

            // No mocks should be called after early validation failure
            expect(db.item.findUnique).not.toHaveBeenCalled();
            expect(db.user.findUnique).not.toHaveBeenCalled();
            expect(InventoryService.UserHasTradeableNumberOfItem).not.toHaveBeenCalled();
        });

        it("should fail to buy from own auction", async () => {
            // Setup auction owned by the same user trying to buy
            const selfOwnedAuction = {
                ...mockAuctionItem,
                sellerId: mockBuyer.id,
            };

            vi.mocked(db.auction_item.findFirst).mockResolvedValue(selfOwnedAuction);

            // Attempt to buy your own auction
            const result = await AuctionController.buyoutAuctionListing(
                mockBuyer.id, // Same as sellerId
                selfOwnedAuction.id,
                1
            );

            expect(result).toEqual({ error: "Can't buy your own auction item." });
            expect(db.$transaction).not.toHaveBeenCalled();
        });

        it("should fail to cancel another user's auction", async () => {
            // Setup auction
            vi.mocked(db.auction_item.findFirst).mockResolvedValue(mockAuctionItem);

            // Attempt to cancel auction by different user
            const result = await AuctionController.cancelAuctionListing(
                mockBuyer.id, // Different from mockAuctionItem.sellerId
                mockAuctionItem.id
            );

            expect(result).toEqual({ error: "Can't cancel auction item that is not your own." });
            expect(db.$transaction).not.toHaveBeenCalled();
        });
    });

    describe("Auction Listing Access Control", () => {
        it("should prevent blacklisted item types from being listed", async () => {
            // Create a quest item (not in allowed types)
            const questItem = {
                ...mockWeapon,
                itemType: "quest" as ItemTypes,
            };

            vi.mocked(db.item.findUnique).mockResolvedValue(questItem);
            vi.mocked(db.user.findUnique).mockResolvedValue(mockUser);

            const result = await AuctionController.createAuctionListing(mockUser.id, questItem.id, 1, 500, 24, false);

            expect(result).toEqual({ error: "Can't list this item type!" });
        });

        it("should prevent blacklisted item IDs from being listed", async () => {
            // Create an item with blacklisted ID
            const blacklistedItem = {
                ...mockWeapon,
                id: 232, // In blacklist from mock config
            };

            vi.mocked(db.item.findUnique).mockResolvedValue(blacklistedItem);
            vi.mocked(db.user.findUnique).mockResolvedValue(mockUser);

            const result = await AuctionController.createAuctionListing(
                mockUser.id,
                blacklistedItem.id,
                1,
                500,
                24,
                false
            );

            expect(result).toEqual({ error: "This item is blacklisted!" });
        });
    });

    describe("Error Handling", () => {
        it("should handle transaction errors during buyout", async () => {
            // Setup auction
            vi.mocked(db.auction_item.findFirst).mockResolvedValue(mockAuctionItem);
            vi.mocked(db.user.findUnique).mockResolvedValue(mockBuyer);

            // Force transaction to throw error
            vi.mocked(db.$transaction).mockRejectedValueOnce(new Error("Database error"));

            const result = await AuctionController.buyoutAuctionListing(mockBuyer.id, mockAuctionItem.id, 1);

            expect(result).toEqual({ statusCode: 500, error: "Unexpected error buying auction item." });
        });

        it("should handle transaction errors during cancellation", async () => {
            // Setup auction
            vi.mocked(db.auction_item.findFirst).mockResolvedValue(mockAuctionItem);

            // Force transaction to throw error
            vi.mocked(db.$transaction).mockRejectedValueOnce(new Error("Database error"));

            const result = await AuctionController.cancelAuctionListing(mockUser.id, mockAuctionItem.id);

            expect(result).toEqual({ statusCode: 500, error: "Unexpected error cancelling auction item." });
        });
    });
});
