<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>BullMQ Queue Status</title>
        <style>
            body {
                font-family:
                    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            h1 {
                color: #333;
                border-bottom: 2px solid #eee;
                padding-bottom: 10px;
            }
            .queue-section {
                background: white;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .queue-name {
                color: #2c3e50;
                font-size: 1.2em;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .job-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 15px;
                margin-top: 10px;
            }
            .job-card {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
            .job-name {
                font-weight: 600;
                color: #495057;
                margin-bottom: 8px;
            }
            .status {
                display: inline-block;
                padding: 3px 8px;
                border-radius: 12px;
                font-size: 0.85em;
                font-weight: 500;
            }
            .status.active {
                background: #d4edda;
                color: #155724;
            }
            .status.failed {
                background: #f8d7da;
                color: #721c24;
            }
            .status.completed {
                background: #cce5ff;
                color: #004085;
            }
            .error {
                color: #dc3545;
                font-size: 0.9em;
                margin-top: 8px;
                word-break: break-word;
            }
            .completed-jobs {
                margin-top: 30px;
            }
            .completed-jobs table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
            }
            .completed-jobs th,
            .completed-jobs td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #dee2e6;
            }
            .completed-jobs th {
                background: #f8f9fa;
                font-weight: 600;
            }
            .refresh-info {
                color: #6c757d;
                font-size: 0.9em;
                margin-top: 5px;
            }
            @media (max-width: 768px) {
                .job-grid {
                    grid-template-columns: 1fr;
                }
                .completed-jobs {
                    overflow-x: auto;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>BullMQ Queue Status</h1>
            <div id="queues"></div>
            <div class="queue-section">
                <h2>Upcoming Jobs</h2>
                <div id="upcoming" class="job-grid"></div>
            </div>
            <div class="completed-jobs">
                <h2>Recently Completed Jobs</h2>
                <div id="completed"></div>
            </div>
            <p class="refresh-info">Auto-refreshes every 10 seconds</p>
        </div>

        <script>
            function formatDate(dateString) {
                return new Date(dateString).toLocaleString();
            }

            function formatDuration(ms) {
                if (ms < 1000) return `${ms}ms`;
                const seconds = Math.floor(ms / 1000);
                if (seconds < 60) return `${seconds}s`;
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                return `${minutes}m ${remainingSeconds}s`;
            }

            function renderQueues(data) {
                const queuesDiv = document.getElementById("queues");
                queuesDiv.innerHTML = "";

                Object.entries(data.queues).forEach(([queueName, jobs]) => {
                    const section = document.createElement("div");
                    section.className = "queue-section";

                    section.innerHTML = `
                    <div class="queue-name">${queueName}</div>
                    <div class="job-grid">
                        ${jobs
                            .map(
                                (job) => `
                            <div class="job-card">
                                <div class="job-name">${job.name}</div>
                                <div>
                                    <span class="status ${job.status.toLowerCase()}">${job.status}</span>
                                </div>
                                <div>Next Run: ${formatDate(job.nextRun)}</div>
                                <div>Pattern: ${job.pattern}</div>
                                ${job.error ? `<div class="error">Error: ${job.error}</div>` : ""}
                            </div>
                        `
                            )
                            .join("")}
                    </div>
                `;

                    queuesDiv.appendChild(section);
                });

                // Render upcoming jobs
                const upcomingDiv = document.getElementById("upcoming");
                if (data.upcomingJobs && data.upcomingJobs.length > 0) {
                    upcomingDiv.innerHTML = data.upcomingJobs
                        .map(
                            (job) => `
                            <div class="job-card">
                                <div class="job-name">${job.name}</div>
                                <div>Queue: ${job.queueName}</div>
                                <div>Scheduled For: ${formatDate(job.scheduledFor)}</div>
                                ${job.data ? `<div>Data: ${JSON.stringify(job.data)}</div>` : ""}
                            </div>
                        `
                        )
                        .join("");
                } else {
                    upcomingDiv.innerHTML = '<div class="job-card">No upcoming jobs</div>';
                }

                const completedDiv = document.getElementById("completed");
                completedDiv.innerHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>Queue</th>
                            <th>Job</th>
                            <th>Status</th>
                            <th>Duration</th>
                            <th>Finished</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.completedJobs
                            .map(
                                (job) => `
                            <tr>
                                <td>${job.queueName}</td>
                                <td>${job.name}</td>
                                <td><span class="status ${job.status.toLowerCase()}">${job.status}</span></td>
                                <td>${formatDuration(job.duration)}</td>
                                <td>${formatDate(job.finishedAt)}</td>
                            </tr>
                        `
                            )
                            .join("")}
                    </tbody>
                </table>
            `;
            }

            async function fetchQueueStatus() {
                try {
                    const response = await fetch("/queueData");
                    const data = await response.json();
                    renderQueues(data);
                } catch (error) {
                    console.error("Error fetching queue status:", error);
                }
            }

            // Initial fetch
            fetchQueueStatus();

            // Refresh every 10 seconds
            setInterval(fetchQueueStatus, 10000);
        </script>
    </body>
</html>
