import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { APIROUTES } from "@/helpers/apiRoutes";

/**
 * Custom hook to use materials crate
 */
export const useMaterialsCrate = () => {
    const queryClient = useQueryClient();

    return useMutation(
        // eslint-disable-next-line react-hooks/react-compiler
        api.specialItems.redeemMaterialsCrate.mutationOptions({
            onSuccess: () => {
                setTimeout(() => {
                    queryClient.invalidateQueries({
                        queryKey: api.user.inventory.key(),
                    });
                    queryClient.invalidateQueries({
                        queryKey: APIROUTES.USER.CURRENTUSERINFO,
                    });
                }, 30);

                toast.success("You redeemed 10 Raw Materials for your gang!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};

export default useMaterialsCrate;
