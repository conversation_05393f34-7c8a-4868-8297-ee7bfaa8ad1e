import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}

const useExploreMap = (options: QueryOptions = {}) => {
    return useQuery(
        api.explore.map.queryOptions({
            staleTime: 60000, // Cache for 1 minute
            ...options,
        })
    );
};

export default useExploreMap;
