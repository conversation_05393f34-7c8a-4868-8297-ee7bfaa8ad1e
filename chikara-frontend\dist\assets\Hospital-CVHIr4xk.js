import{r as b,j as e,W as ee,Y as te,_ as D,$ as se,a0 as U,a1 as z,a2 as ae,e as G,g as Y,c as S,l as T,y as O,h as w,a3 as re,a4 as ne,B as le,a5 as ce,a6 as H,C as K,b as B,a7 as ie,t as oe,v as de,a8 as M,D as ue,o as me}from"./index--cEnoMkg.js";import{D as xe}from"./DataTable-CSAwvRw3.js";import{u as fe}from"./index-DiwBIzu2.js";import"./ag-theme-quartz-2665BHd2.js";var L="Checkbox",[he,Re]=ee(L),[pe,F]=he(L);function be(t){const{__scopeCheckbox:u,checked:c,children:j,defaultChecked:f,disabled:a,form:h,name:p,onCheckedChange:i,required:g,value:d="on",internal_do_not_use_render:v}=t,[y,k]=se({prop:c,defaultProp:f??!1,onChange:i,caller:L}),[m,s]=b.useState(null),[C,o]=b.useState(null),n=b.useRef(!1),N=m?!!h||!!m.closest("form"):!0,R={checked:y,disabled:a,setChecked:k,control:m,setControl:s,name:p,form:h,value:d,hasConsumerStoppedPropagationRef:n,required:g,defaultChecked:I(f)?!1:f,isFormControl:N,bubbleInput:C,setBubbleInput:o};return e.jsx(pe,{scope:u,...R,children:ge(v)?v(R):j})}var Q="CheckboxTrigger",V=b.forwardRef(({__scopeCheckbox:t,onKeyDown:u,onClick:c,...j},f)=>{const{control:a,value:h,disabled:p,checked:i,required:g,setControl:d,setChecked:v,hasConsumerStoppedPropagationRef:y,isFormControl:k,bubbleInput:m}=F(Q,t),s=U(f,d),C=b.useRef(i);return b.useEffect(()=>{const o=a?.form;if(o){const n=()=>v(C.current);return o.addEventListener("reset",n),()=>o.removeEventListener("reset",n)}},[a,v]),e.jsx(D.button,{type:"button",role:"checkbox","aria-checked":I(i)?"mixed":i,"aria-required":g,"data-state":Z(i),"data-disabled":p?"":void 0,disabled:p,value:h,...j,ref:s,onKeyDown:z(u,o=>{o.key==="Enter"&&o.preventDefault()}),onClick:z(c,o=>{v(n=>I(n)?!0:!n),m&&k&&(y.current=o.isPropagationStopped(),y.current||o.stopPropagation())})})});V.displayName=Q;var q=b.forwardRef((t,u)=>{const{__scopeCheckbox:c,name:j,checked:f,defaultChecked:a,required:h,disabled:p,value:i,onCheckedChange:g,form:d,...v}=t;return e.jsx(be,{__scopeCheckbox:c,checked:f,defaultChecked:a,disabled:p,required:h,onCheckedChange:g,name:j,form:d,value:i,internal_do_not_use_render:({isFormControl:y})=>e.jsxs(e.Fragment,{children:[e.jsx(V,{...v,ref:u,__scopeCheckbox:c}),y&&e.jsx(J,{__scopeCheckbox:c})]})})});q.displayName=L;var W="CheckboxIndicator",$=b.forwardRef((t,u)=>{const{__scopeCheckbox:c,forceMount:j,...f}=t,a=F(W,c);return e.jsx(te,{present:j||I(a.checked)||a.checked===!0,children:e.jsx(D.span,{"data-state":Z(a.checked),"data-disabled":a.disabled?"":void 0,...f,ref:u,style:{pointerEvents:"none",...t.style}})})});$.displayName=W;var X="CheckboxBubbleInput",J=b.forwardRef(({__scopeCheckbox:t,...u},c)=>{const{control:j,hasConsumerStoppedPropagationRef:f,checked:a,defaultChecked:h,required:p,disabled:i,name:g,value:d,form:v,bubbleInput:y,setBubbleInput:k}=F(X,t),m=U(c,k),s=fe(a),C=ae(j);b.useEffect(()=>{const n=y;if(!n)return;const N=window.HTMLInputElement.prototype,E=Object.getOwnPropertyDescriptor(N,"checked").set,_=!f.current;if(s!==a&&E){const r=new Event("click",{bubbles:_});n.indeterminate=I(a),E.call(n,I(a)?!1:a),n.dispatchEvent(r)}},[y,s,a,f]);const o=b.useRef(I(a)?!1:a);return e.jsx(D.input,{type:"checkbox","aria-hidden":!0,defaultChecked:h??o.current,required:p,disabled:i,name:g,value:d,form:v,...u,tabIndex:-1,ref:m,style:{...u.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});J.displayName=X;function ge(t){return typeof t=="function"}function I(t){return t==="indeterminate"}function Z(t){return I(t)?"indeterminate":t?"checked":"unchecked"}const je=()=>{const t=G();return{healUser:Y(S.infirmary.revivePlayer.mutationOptions({onSuccess:()=>{T.success("You healed the user!"),t.invalidateQueries({queryKey:O.INFIRMARY.INJUREDLIST})},onError:c=>{T.error(c?.message||"An error occurred")}}))}},A=b.forwardRef(({className:t,...u},c)=>e.jsx(q,{ref:c,className:w("peer size-4 shrink-0 rounded-xs border border-zinc-900 bg-gray-900 shadow-sm focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-zinc-950 disabled:cursor-not-allowed disabled:opacity-25 data-[state=checked]:bg-gray-900 data-[state=checked]:text-zinc-50 dark:border-zinc-700 dark:data-[state=checked]:bg-zinc-800 dark:data-[state=checked]:text-gray-900 dark:focus-visible:ring-zinc-300",t),...u,children:e.jsx($,{className:w("flex items-center justify-center text-current"),children:e.jsx(re,{className:"size-4 text-white"})})}));A.displayName=q.displayName;const ve=()=>{const t=G();return{hospitalCheckIn:Y(S.infirmary.checkIn.mutationOptions({onSuccess:()=>{T.success("Checked in successfully!"),t.invalidateQueries({queryKey:O.INFIRMARY.INFIRMARYLIST}),t.invalidateQueries({queryKey:O.USER.CURRENTUSERINFO})},onError:c=>{T.error(c?.message||"An error occurred")}}))}},ye=({currentUser:t})=>{const{data:u}=ne(),{hospitalCheckIn:c}=ve(),{MINOR_COST_PER_LEVEL:j,MODERATE_COST_PER_LEVEL:f,SEVERE_COST_PER_LEVEL:a,COST_PER_HP:h}=le(),[p,i]=b.useState(!1),g=t?.health-t?.currentHealth,d=u.filter(s=>s.effect&&s.effect.effectType==="DEBUFF"),v=()=>{if(!d||!t)return 0;const s=t.health-t.currentHealth,C=t.level,o=d.filter(l=>l.effect.tier==="Minor"),n=d.filter(l=>l.effect.tier==="Moderate"),N=d.filter(l=>l.effect.tier==="Severe"),R=o?.length*(C*j)||0,E=n?.length*(C*f)||0,_=N?.length*(C*a)||0,r=s*h||0;let x=R+E+_;return p||(x+=r),Math.round(x)},y=(s,C)=>{if(!d||!t)return 0;const o=t.level;let n=0;switch(s){case"Minor":n=o*j;break;case"Moderate":n=o*f;break;case"Severe":n=o*a;break;case"missingHP":n=C*h;break;default:n=0;break}return Math.round(n)},k=s=>{switch(s){case"Minor":return"text-red-400";case"Moderate":return"text-red-500";case"Severe":return"text-red-600";case"missingHP":return"text-red-400";default:return"text-red-400"}},m=v();return e.jsxs("div",{className:"mx-auto my-4 flex w-[90%] flex-col justify-between rounded-lg border border-gray-600 bg-slate-800 px-4 py-2 md:w-fit md:min-w-96 md:px-6",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex flex-row justify-between",children:[e.jsx("p",{className:"mb-0.5 text-blue-300 text-sm uppercase",children:"Your Injuries"}),!t?.hospitalisedUntil&&e.jsxs("div",{className:"flex items-center space-x-2 md:my-auto md:hidden dark:text-gray-200",children:[e.jsx(A,{disabled:!m||d.length<1,checked:p,onCheckedChange:s=>i(s)}),e.jsx("label",{className:"font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Heal Injuries Only"})]})]}),d?.length===0&&g===0&&e.jsx("li",{className:"text-gray-300 text-sm",children:"None"}),d?.map(s=>e.jsx("div",{children:s.effect.name!=="Zombified"&&e.jsxs("li",{className:"flex flex-row gap-1.5 text-gray-200",children:[s.stacks,"x"," ",e.jsx("img",{src:ce[s?.effect?.source]?.icon,alt:"",className:"mt-0.5 h-5 w-auto rounded-md border border-red-500"}),e.jsx("p",{className:k(s.effect.tier),children:s.effect.name}),e.jsxs("div",{className:"ml-2 flex flex-row items-center gap-1 text-yellow-500",children:[e.jsx("img",{className:"mb-0.5 h-4 w-auto",src:H,alt:""}),e.jsx("p",{className:"text-yellow-500",children:y(s.effect.tier)})]})]})},s.id)),g>0&&e.jsxs(e.Fragment,{children:[e.jsx("hr",{className:"my-2 border-gray-600/75"}),e.jsx("div",{children:e.jsxs("li",{className:w(p&&"line-through","flex flex-row gap-1.5 text-gray-200"),children:[g,e.jsx("p",{className:k("missingHP"),children:"Health Points"}),e.jsxs("div",{className:"ml-2 flex flex-row items-center gap-1 text-yellow-500",children:[e.jsx("img",{className:"mb-0.5 h-4 w-auto",src:H,alt:""}),e.jsx("p",{className:"text-yellow-500",children:y("missingHP",g)})]})]})})]})]}),e.jsx("hr",{className:"my-2 border-gray-600/75"}),t?.hospitalisedUntil?e.jsx("div",{className:"flex flex-col items-center",children:e.jsx("p",{className:"text-green-500 text-lg",children:"Receiving Treatment"})}):e.jsxs("div",{className:"flex flex-row items-end gap-6",children:[m>0?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"text-gray-300 text-xs uppercase",children:"Length"}),e.jsx("div",{className:"flex flex-row items-center gap-1.5 text-blue-500 text-lg",children:e.jsx("p",{children:"10 Minutes"})})]}),e.jsxs("div",{className:"ml-auto flex flex-col",children:[e.jsx("p",{className:"text-gray-300 text-xs uppercase",children:"Total Cost"}),e.jsxs("div",{className:"flex flex-row items-center gap-1.5 text-xl text-yellow-500",children:[e.jsx("img",{className:"h-6 w-auto",src:H,alt:""}),e.jsx("p",{children:m})]})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"text-gray-300 text-xs uppercase",children:"Length"}),e.jsx("div",{className:"text-center text-xl",children:"-"})]}),e.jsxs("div",{className:"ml-auto flex flex-col",children:[e.jsx("p",{className:"text-gray-300 text-xs uppercase",children:"Total Cost"}),e.jsx("div",{className:"text-center text-xl",children:"-"})]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row md:gap-6",children:[e.jsxs("div",{className:"hidden items-center space-x-2 md:my-auto md:flex dark:text-gray-200",children:[e.jsx(A,{checked:p,disabled:!m||d.length<1,onCheckedChange:s=>i(s)}),e.jsx("label",{className:"font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Heal Injuries Only"})]}),e.jsx(K,{className:"w-24! ml-auto!",disabled:!m,onClick:()=>c.mutate(p),children:"Check In"})]})]})]})};function Ee(){const{isLoading:t,error:u,data:c}=B(S.infirmary.infirmaryList.queryOptions({select:r=>r.sort((x,l)=>x.hospitalisedUntil>l.hospitalisedUntil?1:-1)})),{data:j,isLoading:f}=B(S.infirmary.injuredList.queryOptions({select:r=>r.sort((x,l)=>l.endsAt>x.endsAt?1:-1)})),a=ie("revive"),[h,p]=b.useState("injured"),i=h==="injured",g=0,d=a?.modifier-g||0,{healUser:v}=je(),y=(r,x,l,P)=>l.data.id-P.data.id,{data:k}=oe(),m=de(),s=r=>r?r.id===1?"text-green-500":"text-red-400":"text-gray-400",C=r=>{const x=r?.data?.user_status_effect;return x&&x?.length<1?e.jsx("div",{className:"!flex items-center! justify-center! w-full! font-semibold",children:e.jsx("p",{className:"m-auto! text-red-400 text-xs md:text-sm",children:"Regaining Health"})}):e.jsxs("div",{className:"h-full! flex! text-center! justify-center! flex-col items-center gap-1.5 py-1.5 font-semibold",children:[x?.map(l=>e.jsx("div",{children:e.jsxs("li",{className:"flex flex-row gap-1.5 text-gray-200 text-xs leading-none md:text-sm",children:[l?.stacks,"x"," ",e.jsx("p",{className:"text-red-500",children:l?.customName?l?.customName:l?.effect?.name})]})},l?.id)),i&&e.jsxs("p",{className:"text-[0.6rem] text-blue-400 leading-none md:text-sm",children:["Fully healed in ",M(r?.data?.endsAt)]})]})},o=r=>{const x=r?.data?.user_status_effect?.[0]?.id;return e.jsx("div",{className:"flex size-full",children:e.jsxs(K,{className:"m-auto!",disabled:d===0,onClick:()=>v.mutate(x),children:["Heal (",d,")"]})})},n=r=>{const{username:x,id:l,gang:P}=r?.data;return e.jsxs("div",{className:w(r.data?.defeated&&"grayscale",r.data?.disabled&&"opacity-25 grayscale","relative flex h-full items-center justify-normal px-0.5 py-0 font-lili md:w-full md:gap-2"),children:[e.jsxs("div",{className:"mt-1.5 flex min-w-13 flex-col items-center justify-center gap-1 text-center md:mt-0",children:[e.jsx(ue,{src:r?.data,className:"aspect-square! size-11 rounded-full border border-blue-800 md:size-13"}),l&&e.jsxs("small",{className:"block font-semibold text-gray-500 text-xs md:hidden dark:text-blue-400",children:["#",l]})]}),e.jsx(me,{to:x?`/profile/${l}`:null,children:e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:w("font-bold text-sm text-stroke-sm md:text-base",x?"text-blue-600":"text-gray-200"),children:[x||"Anonymous"," "]}),l&&e.jsxs("div",{className:"hidden text-gray-500 text-stroke-sm text-xs md:block md:text-sm dark:text-gray-400",children:["ID ",e.jsxs("span",{className:"text-indigo-400",children:["#",l]})]}),e.jsx("div",{className:w(s(P),"mt-1 block font-semibold text-xs md:hidden md:text-sm dark:text-stroke-sm"),children:P===null?"No Gang":P?.name})]})})]})};b.useLayoutEffect(()=>{E(N)},[h,g]);const N=[{headerName:"Name",field:"id",comparator:y,cellRenderer:n,minWidth:m?160:300,maxWidth:m?160:null,suppressFloatingFilterButton:!0,filterParams:{filterOptions:["contains"],defaultOption:"contains"}},{headerName:"Injuries",field:"hospitalisedReason",minWidth:m?120:null,maxWidth:m?120:null,cellRenderer:C,cellClass:"items-center! justify-center! flex!",headerClass:"centerGridHeader",filter:!1,autoHeight:!0},{headerName:i?"Actions":"Duration",field:i?"endsAt":"hospitalisedUntil",headerClass:"centerGridHeader",cellClass:"md:text-base text-sm font-semibold text-center flex! items-center! justify-center! truncate",cellRenderer:i?o:null,filter:!1,floatingFilter:!1,valueFormatter:i?null:r=>M(r.value),filterValueGetter:i?null:r=>M(r.value)}],[R,E]=b.useState(N);if(u)return"An error has occurred: "+u.message;const _=[{name:"Injured List",value:"injured",current:h==="injured"},{name:"Patients List",value:"treatment",current:h==="treatment"}];return e.jsxs("div",{className:"mb-8 md:mx-auto md:mb-0 md:max-w-6xl",children:[e.jsx(ye,{currentUser:k}),e.jsx(xe,{dataList:i?j:c,colDefs:R,isLoading:t||f,setCurrentTab:p,currentTab:h,tabs:_})]})}export{Ee as default};
