import { PrismaClient } from "@prisma/client";
import defaultRecipeInputs from "./seeders/default_seeds/default_crafting_inputs";
import defaultRecipeOutputs from "./seeders/default_seeds/default_crafting_outputs";
import defaultCraftingRecipes from "./seeders/default_seeds/default_crafting_recipes";
import defaultCreatures from "./seeders/default_seeds/default_creatures";
import defaultDropChances from "./seeders/default_seeds/default_drop_chances";
import defaultItems from "./seeders/default_seeds/default_items";
import defaultJobs from "./seeders/default_seeds/default_jobs";
import defaultPets from "./seeders/default_seeds/default_pets";
import defaultProperty from "./seeders/default_seeds/default_property";
import defaultQuestObjectives from "./seeders/default_seeds/default_quest_objectives";
import defaultQuestRewards from "./seeders/default_seeds/default_quest_rewards";
import defaultQuests from "./seeders/default_seeds/default_quests";
import defaultShopListings from "./seeders/default_seeds/default_shop_listings";
import defaultShops from "./seeders/default_seeds/default_shops";
import defaultStatusEffects from "./seeders/default_seeds/default_status_effects";
import defaultTalents from "./seeders/default_seeds/default_talents";
import defaultStaticNodes from "./seeders/default_seeds/default_static_nodes";
import { accounts, users } from "./seeders/default_seeds/default_users";
import defaultStorySeasons from "./seeders/default_seeds/default_story_seasons";
import defaultStoryChapters from "./seeders/default_seeds/default_story_chapters";
import defaultStoryEpisodes from "./seeders/default_seeds/default_story_episodes";
import defaultStoryQuests from "./seeders/default_seeds/default_story_quests";
import defaultStoryQuestObjectives from "./seeders/default_seeds/default_story_quest_objectives";

const prisma = new PrismaClient();

const insertData = async <T>(model: any, data: any[]): Promise<void> => {
    if (data.length > 0) {
        // Use createMany for bulk insert
        try {
            await model.createMany({
                data,
                skipDuplicates: true,
            });
        } catch (error) {
            console.error(`Error inserting data into ${model.name}:`, error);
            throw error;
        }
    }
};

async function main() {
    const now = new Date();
    const oneDay = 86400000;

    // --- Chat Rooms ---
    await insertData(prisma.chat_room, [
        {
            id: 1,
            name: "global",
        },
    ]);

    // --- Crafting Recipes ---
    await insertData(prisma.crafting_recipe, defaultCraftingRecipes);

    // Pets
    await insertData(prisma.pet, defaultPets);

    // --- Items ---
    await insertData(prisma.item, defaultItems);

    // --- Crafting Recipe Materials ---
    await insertData(prisma.recipe_item, defaultRecipeInputs);
    await insertData(prisma.recipe_item, defaultRecipeOutputs);

    // --- Shops ---
    await insertData(prisma.shop, defaultShops);
    await insertData(prisma.shop_listing, defaultShopListings);

    // --- Creatures ---
    await insertData(prisma.creature, defaultCreatures);

    // --- Jobs ---
    await insertData(prisma.job, defaultJobs);

    // Properties
    await insertData(prisma.property, defaultProperty);

    // Talents
    await insertData(prisma.talent, defaultTalents);

    // Drop Chances
    await insertData(prisma.drop_chance, defaultDropChances);

    // Quests
    await insertData(prisma.quest, defaultQuests);
    await insertData(prisma.quest_objective, defaultQuestObjectives);
    await insertData(prisma.quest_reward, defaultQuestRewards);

    // Story Mode Content
    await insertData(prisma.story_season, defaultStorySeasons);
    await insertData(prisma.story_chapter, defaultStoryChapters);

    // Story Quests (separate from regular quests)
    await insertData(prisma.quest, defaultStoryQuests);
    await insertData(prisma.quest_objective, defaultStoryQuestObjectives);

    await insertData(prisma.story_episode, defaultStoryEpisodes);

    // --- Status Effects ---
    await insertData(prisma.status_effect, defaultStatusEffects);

    // --- Users ---
    await insertData(prisma.user, users);
    await insertData(prisma.account, accounts);

    // Static Nodes
    await insertData(prisma.explore_static_node, defaultStaticNodes);

    // Gangs
    await insertData(prisma.gang, [
        {
            name: "Staff Association",
            about: "Staff",
            ownerId: 1,
        },
    ]);

    // Add all users to the staff association gang
    for (const user of users) {
        await prisma.user.update({
            where: { id: user.id },
            data: { gangId: 1 },
        });
        await prisma.gang_member.create({
            data: {
                gangId: 1,
                userId: user.id,
                rank: 5,
            },
        });
    }
}

main()
    .catch((e) => {
        console.error(e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
