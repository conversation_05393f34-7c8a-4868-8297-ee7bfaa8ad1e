<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <link rel="icon" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="theme-color" content="#000000" />
        <meta
            name="description"
            content="Chikara Battle Academy is a unique multiplayer RPG, set in a dynamic, social world full of
    community driven adventures."
        />
        <link rel="apple-touch-icon" href="/logo192.png" />
        <link rel="preconnect" href="https://fonts.gstatic.com" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <style>
            #root {
                height: 100dvh;
            }
            body {
                margin: 0;
                height: 100%;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
                background-color: #111521;
                scrollbar-gutter: stable;
            }

            html {
                overflow: hidden;
                width: 100%;
            }

            .divLoader {
                width: 100vw;
                height: 100vh;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background-color: #191919;
            }

            .dot-spinner {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                --uib-speed: 0.9s;
                height: 4rem;
                width: 4rem;
            }

            .dot-spinner__dot::before {
                content: "";
                height: 20%;
                width: 20%;
                border-radius: 50%;
                background-color: #fff;
                filter: drop-shadow(0 0 10px rgb(95, 150, 202));
                box-shadow:
                    -6px -6px 11px #c1c1c1,
                    6px 6px 11px #ffffff;
                transform: scale(0);
                opacity: 0.5;
                animation: pulse0112 calc(var(--uib-speed) * 1.111) ease-in-out infinite;
                box-shadow: 0 0 20px rgba(18, 31, 53, 0.3);
            }

            .dot-spinner__dot {
                position: absolute;
                top: 0;
                left: 0;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                height: 100%;
                width: 100%;
            }

            .dot-spinner__dot:nth-child(2) {
                transform: rotate(45deg);
            }

            .dot-spinner__dot:nth-child(2)::before {
                animation-delay: calc(var(--uib-speed) * -0.875);
            }

            .dot-spinner__dot:nth-child(3) {
                transform: rotate(90deg);
            }

            .dot-spinner__dot:nth-child(3)::before {
                animation-delay: calc(var(--uib-speed) * -0.75);
            }

            .dot-spinner__dot:nth-child(4) {
                transform: rotate(135deg);
            }

            .dot-spinner__dot:nth-child(4)::before {
                animation-delay: calc(var(--uib-speed) * -0.625);
            }

            .dot-spinner__dot:nth-child(5) {
                transform: rotate(180deg);
            }

            .dot-spinner__dot:nth-child(5)::before {
                animation-delay: calc(var(--uib-speed) * -0.5);
            }

            .dot-spinner__dot:nth-child(6) {
                transform: rotate(225deg);
            }

            .dot-spinner__dot:nth-child(6)::before {
                animation-delay: calc(var(--uib-speed) * -0.375);
            }

            .dot-spinner__dot:nth-child(7) {
                transform: rotate(270deg);
            }

            .dot-spinner__dot:nth-child(7)::before {
                animation-delay: calc(var(--uib-speed) * -0.25);
            }

            .dot-spinner__dot:nth-child(8) {
                transform: rotate(315deg);
            }

            .dot-spinner__dot:nth-child(8)::before {
                animation-delay: calc(var(--uib-speed) * -0.125);
            }

            @keyframes pulse0112 {
                0%,
                100% {
                    transform: scale(0);
                    opacity: 0.5;
                }

                50% {
                    transform: scale(1);
                    opacity: 1;
                }
            }
        </style>
        <title>Chikara Battle Academy | Anime Social MMORPG</title>
      <script type="module" crossorigin src="/assets/index--cEnoMkg.js"></script>
      <link rel="stylesheet" crossorigin href="/assets/index-CAuTn8O8.css">
    <link rel="manifest" href="/manifest.webmanifest"></head>
    <body>
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <div id="root">
            <div class="divLoader">
                <div class="dot-spinner">
                    <div class="dot-spinner__dot"></div>
                    <div class="dot-spinner__dot"></div>
                    <div class="dot-spinner__dot"></div>
                    <div class="dot-spinner__dot"></div>
                    <div class="dot-spinner__dot"></div>
                    <div class="dot-spinner__dot"></div>
                    <div class="dot-spinner__dot"></div>
                    <div class="dot-spinner__dot"></div>
                </div>
            </div>
        </div>
    </body>
</html>
