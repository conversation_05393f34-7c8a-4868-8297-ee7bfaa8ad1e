import{F as E,e as S,g as D,c as k,G as U,H as R,J as $,Z as P,K as B,M as H,j as e,X as O,N as M,O as F,P as V,Q as X,T as _,t as Q,B as z,r as I,V as K}from"./index--cEnoMkg.js";import{u as Z}from"./useGetUserSkills-DmDnGBHH.js";import{d as J,a as W,s as Y,i as q}from"./stamina-LF5EZP0M.js";import{B as ee}from"./brain-Ck1RtpOT.js";import{T as L}from"./trending-up-Bh6mAcwG.js";import{C as se}from"./circle-question-mark-K0A6Y0gi.js";import{B as te}from"./book-open-ByrAQ_Rx.js";import{D as G}from"./dumbbell-CX2fW577.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],re=E("target",ae),ie=({onSuccess:s,onError:a}={})=>{const n=S();return D(k.user.train.mutationOptions({onSuccess:(c,r)=>{const{statProgress:o,focusRemaining:d,dailyFatigueRemaining:h}=c,i={statProgress:o?{...o,statName:r.stat}:void 0,focusRemaining:d,dailyFatigueRemaining:h};s?.(i,r.stat),n.invalidateQueries({queryKey:k.user.currentUserInfo.key()})},onError:c=>{const r=c.message||"Unknown error occurred";console.error("Training error:",r),a?.(r)}}))},ne="/assets/HPicon2-BpgU42R7.png",N=[{id:"strength",name:"Strength",valueId:"strengthValue",activity:"Weight Training",location:"Gym",image:R,icon:U,effect:"Increases melee damage & combat skills",effect2:"Armor penetration & impact resistance",color:"red",bgColor:"bg-red-950/25",borderColor:"border-red-900/50",accentColor:"text-red-400",progressColor:"from-red-600 to-red-500"},{id:"defence",name:"Defence",valueId:"defenceValue",activity:"Defensive Training",location:"Gym",image:J,icon:$,effect:"Flat & percentage damage reduction",effect2:"Critical hit & debuff resistance",color:"blue",bgColor:"bg-blue-950/25",borderColor:"border-blue-900/50",accentColor:"text-blue-400",progressColor:"from-blue-600 to-blue-500"},{id:"dexterity",name:"Dexterity",valueId:"dexterityValue",activity:"Agility Training",location:"Sports Hall",image:W,icon:P,effect:"Increases ranged damage & combat skills",effect2:"Critical hit chance & initiative",color:"green",bgColor:"bg-green-950/25",borderColor:"border-green-900/50",accentColor:"text-green-400",progressColor:"from-green-600 to-green-500"},{id:"endurance",name:"Endurance",valueId:"enduranceValue",activity:"Cardio Training",location:"Sports Hall",image:Y,icon:B,effect:"Increases max stamina & regeneration",effect2:"Buff duration & consumable effectiveness",color:"yellow",bgColor:"bg-yellow-950/25",borderColor:"border-yellow-900/50",accentColor:"text-yellow-400",progressColor:"from-yellow-600 to-yellow-500"},{id:"intelligence",name:"Intelligence",valueId:"intelligenceValue",activity:"Study Session",location:"Library",image:q,icon:ee,effect:"Increases tech skill effectiveness",effect2:"Critical damage & debuff potency",color:"purple",bgColor:"bg-purple-950/25",borderColor:"border-purple-900/50",accentColor:"text-purple-400",progressColor:"from-purple-600 to-purple-500"},{id:"vitality",name:"Vitality",valueId:"vitalityValue",activity:"Fitness Training",location:"Gym",image:ne,icon:H,effect:"Increases max health & healing received",effect2:"DoT resistance & survival chance",color:"pink",bgColor:"bg-pink-950/25",borderColor:"border-pink-900/50",accentColor:"text-pink-400",progressColor:"from-pink-600 to-pink-500"}];function le({isOpen:s,onClose:a,result:n,statName:c}){if(!s)return null;const{statProgress:r,focusRemaining:o,dailyFatigueRemaining:d}=n;return e.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center animate-in fade-in duration-300",children:[e.jsx("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"}),e.jsxs("div",{className:"relative w-full max-w-md mx-4 bg-gradient-to-br from-[#1a2f4a] to-[#0d1b2a] rounded-xl border border-[#2a4a7c] shadow-2xl animate-in zoom-in-95 slide-in-from-bottom-4 duration-300 ease-out",children:[e.jsx("button",{className:"absolute top-4 right-4 p-1 rounded-lg hover:bg-[#2a4a7c] transition-colors",onClick:a,children:e.jsx(O,{className:"w-5 h-5 text-gray-400"})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-violet-600 to-purple-600 rounded-full flex items-center justify-center",children:e.jsx(L,{className:"w-8 h-8 text-white"})}),e.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"Training Complete!"}),e.jsxs("p",{className:"text-gray-400",children:[M(c)," training session finished"]})]}),e.jsxs("div",{className:"space-y-4",children:[r?.leveledUp&&e.jsxs("div",{className:"relative overflow-hidden rounded-lg bg-gradient-to-r from-yellow-600/20 to-orange-600/20 border border-yellow-600/30 p-4",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-600/10 to-orange-600/10 animate-pulse"}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"p-2 bg-yellow-600/20 rounded-lg",children:e.jsx(F,{className:"w-5 h-5 text-yellow-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold text-yellow-400 text-lg",children:"Level Up!"}),e.jsxs("p",{className:"text-yellow-300 text-sm",children:[M(c)," reached level"," ",r.currentLevel]})]})]}),r.levelsGained>1&&e.jsxs("p",{className:"text-yellow-300 text-sm font-medium",children:["+",r.levelsGained," levels gained!"]})]})]}),r&&e.jsx("div",{className:"bg-[#0d1b2a]/50 rounded-lg p-4 border border-[#2a4a7c]",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-violet-600/20 rounded-lg",children:e.jsx(L,{className:"w-5 h-5 text-violet-400"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-white",children:"Experience Gained"}),e.jsxs("p",{className:"text-violet-400 font-bold text-lg",children:["+",r.expGained," XP"]})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[o!==void 0&&e.jsxs("div",{className:"bg-[#0d1b2a]/50 rounded-lg p-3 border border-[#2a4a7c]",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(P,{className:"w-4 h-4 text-violet-400"}),e.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Focus"})]}),e.jsx("p",{className:"text-violet-400 font-bold",children:o}),e.jsx("p",{className:"text-xs text-gray-500",children:"remaining"})]}),d!==void 0&&e.jsxs("div",{className:"bg-[#0d1b2a]/50 rounded-lg p-3 border border-[#2a4a7c]",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(V,{className:"w-4 h-4 text-orange-400"}),e.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Daily"})]}),e.jsx("p",{className:"text-orange-400 font-bold",children:d}),e.jsx("p",{className:"text-xs text-gray-500",children:"capacity left"})]})]}),e.jsx("button",{className:"w-full mt-6 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-[1.02]",onClick:a,children:"Continue Training"})]})]})]})]})}function oe({value:s,onChange:a,valueId:n}){const o=()=>{s<50&&a(s+1,n)},d=()=>{s>1&&a(s-1,n)},h=i=>{const v=parseInt(i.target.value)||1,g=Math.max(1,Math.min(50,v));a(g,n)};return e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("label",{className:"text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Intensity"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{type:"button",disabled:s<=1,className:"flex items-center justify-center w-8 h-8 rounded-lg bg-[#1a2f4a] disabled:opacity-30 disabled:cursor-not-allowed hover:bg-[#2a4a7c] transition-all duration-200 border border-[#2a4a7c]",onClick:d,children:e.jsx(X,{className:"w-4 h-4 text-gray-300"})}),e.jsx("div",{className:"flex-1 relative",children:e.jsx("input",{type:"number",min:1,max:50,value:s,className:"w-full h-8 text-center text-sm font-bold bg-[#1a2f4a]/50 border border-[#2a4a7c] rounded-lg text-white placeholder-gray-500 focus:border-violet-500 focus:ring-1 focus:ring-violet-500 focus:outline-none transition-all duration-200",onChange:h})}),e.jsx("button",{type:"button",disabled:s>=50,className:"flex items-center justify-center w-8 h-8 rounded-lg bg-[#1a2f4a] disabled:opacity-30 disabled:cursor-not-allowed hover:bg-[#2a4a7c] transition-all duration-200 border border-[#2a4a7c]",onClick:o,children:e.jsx(_,{className:"w-4 h-4 text-gray-300"})})]}),e.jsx("div",{className:"flex gap-1",children:[1,5,10,25,50].map(i=>e.jsx("button",{type:"button",className:`flex-1 h-6 rounded text-xs font-medium transition-all duration-200 ${s===i?"bg-violet-600 text-white":"bg-[#1a2f4a] text-gray-400 hover:bg-[#2a4a7c] hover:text-gray-200"}`,onClick:()=>a(i,n),children:i},i))})]})}function pe(){const{data:s}=Q(),a=z(),{data:n}=Z(["strength","intelligence","dexterity","defence","endurance","vitality"]),[c,r]=I.useState({strength:1,intelligence:1,dexterity:1,defence:1,endurance:1,vitality:1}),[o,d]=I.useState(null),[h,i]=I.useState(null),{mutate:v,isPending:g}=ie({onSuccess:(t,f)=>{d({result:t,statName:f}),i(null)},onError:t=>{i(t),setTimeout(()=>i(null),5e3)}}),u=s?.focus||0,b=a?.DAILY_FATIGUE_CAP||200,l=s?.dailyFatigueUsed||0,m=b-l,x=a?.FOCUS_TO_EXP_RATIO||10,w=(t,f)=>{const j=Math.min(50,u,m),p=Math.max(1,Math.min(j,f));r(T=>({...T,[t]:p}))},C=(t,f)=>{t.preventDefault();const j=f.toLowerCase();v({stat:j,focusAmount:c[j]||1})},y={Gym:N.filter(t=>t.location==="Gym"),"Sports Hall":N.filter(t=>t.location==="Sports Hall"),Library:N.filter(t=>t.location==="Library")},A={Gym:G,"Sports Hall":re,Library:te};return e.jsxs("div",{className:"min-h-screen text-white bg-[#0d1b2a]",children:[e.jsx("div",{className:"border-b border-[#1e3a5f] bg-gradient-to-b from-[#0d1b2a] to-[#162639]",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 py-4",children:e.jsx("div",{className:"grid grid-cols-6 gap-3",children:N.map(t=>e.jsxs("div",{className:"rounded-lg p-3 border border-[#2a4a7c] text-center bg-[#1a2f4a]/50",children:[e.jsx(t.icon,{className:`w-5 h-5 ${t.accentColor} mx-auto mb-1`}),e.jsxs("div",{className:"text-xs font-bold text-gray-300",children:["LV ",n?.[t.id]?.level||"-"]}),e.jsx("div",{className:`text-xs ${t.accentColor}`,children:t.name})]},t.id))})})}),e.jsx("div",{className:"max-w-3xl mx-auto mt-4",children:e.jsx("div",{className:"p-4 rounded-xl border border-[#2a4a7c] bg-gradient-to-br from-[#1a2f4a] to-[#162639]",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(F,{className:"w-4 h-4 text-violet-400"}),e.jsx("h3",{className:"text-sm font-semibold text-white",children:"Focus Points"})]}),e.jsxs("div",{className:"flex items-baseline gap-2",children:[e.jsx("div",{className:"text-2xl font-bold text-violet-400",children:u}),e.jsxs("div",{className:"text-xs text-gray-400",children:[x," XP per focus"]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"w-4 h-4 text-yellow-400"}),e.jsx("h3",{className:"text-sm font-semibold text-white",children:"Daily Fatigue"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-baseline gap-2",children:[e.jsxs("div",{className:"text-lg font-bold text-white",children:[l," / ",b]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Resets at midnight"})]}),e.jsx("div",{className:"h-2 rounded-full overflow-hidden bg-[#0d1b2a]",children:e.jsx("div",{className:"h-full transition-all duration-500",style:{width:`${Math.min(100,l/b*100)}%`,background:l>=b*.8?"linear-gradient(to right, rgb(220 38 38), rgb(239 68 68))":l>=b*.6?"linear-gradient(to right, rgb(202 138 4), rgb(234 179 8))":"linear-gradient(to right, rgb(22 163 74), rgb(34 197 94))"}})}),l>=b*.8&&e.jsxs("p",{className:"text-xs text-red-400 flex items-center gap-1",children:[e.jsx(se,{className:"w-3 h-3"}),"Daily training limit almost reached!"]})]})]})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-4 py-6",children:e.jsx("div",{className:"grid xl:grid-cols-3 lg:grid-cols-2 grid-cols-1 gap-6",children:Object.entries(y).map(([t,f])=>{const j=A[t];return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded-lg border border-[#2a4a7c] bg-[#1a2f4a]/30",children:[e.jsx(j,{className:"w-5 h-5 text-blue-400"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold text-white",children:t}),e.jsxs("p",{className:"text-xs text-stroke-0 text-gray-400",children:[t==="Gym"&&"Physical training",t==="Sports Hall"&&"Agility & endurance",t==="Library"&&"Mental training"]})]})]}),e.jsx("div",{className:"space-y-3",children:f.map(p=>e.jsx(ce,{stat:p,userSkill:n?.[p.id],trainingAmount:c[p.id]||1,submit:C,currentFocus:u,dailyFatigueRemaining:m,focusToExpRatio:x,isLoading:g,trainingError:h,onAmountChange:T=>w(p.id,T)},p.id))})]},t)})})}),o&&e.jsx(le,{isOpen:!!o,result:o.result,statName:o.statName,onClose:()=>d(null)})]})}const ce=({userSkill:s,stat:a,trainingAmount:n,onAmountChange:c,submit:r,currentFocus:o,dailyFatigueRemaining:d,focusToExpRatio:h,isLoading:i,trainingError:v})=>{if(!s)return null;const g=s.level<100?s.experience/(s.experience+s.expToNextLevel)*100:100,u=n,b=u*h,l=o<u,m=d<u;return e.jsx("div",{className:`rounded-xl ${a.bgColor} border ${a.borderColor} transition-all duration-300 hover:border-[#3a5a8c]`,children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("img",{src:a.image,className:"w-8 h-8 object-contain",alt:a.name}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-base font-semibold text-white",children:a.name}),e.jsx("p",{className:"text-xs text-gray-400 text-stroke-0",children:a.activity})]}),e.jsx("div",{className:"px-2 py-1 rounded border bg-[#0d1b2a] border-[#2a4a7c]",children:e.jsx("span",{className:"text-xs font-bold text-gray-300",children:s.level>=100?"MAX":`LV ${s.level}`})})]}),e.jsx("div",{className:"mb-3 p-2 rounded-lg border bg-[#0d1b2a]/50 border-[#2a4a7c]",children:e.jsxs("div",{className:"flex items-start gap-1.5",children:[e.jsx(a.icon,{className:`w-3.5 h-3.5 ${a.accentColor} mt-0.5 flex-shrink-0`}),e.jsxs("div",{className:"space-y-0.5",children:[e.jsx("p",{className:"text-xs text-gray-300",children:a.effect}),a.effect2&&e.jsx("p",{className:"text-xs text-gray-400",children:a.effect2})]})]})}),s.level<100&&e.jsxs("div",{className:"mb-3",children:[e.jsxs("div",{className:"flex justify-between items-center mb-1",children:[e.jsxs("span",{className:"text-xs text-stroke-0 text-gray-400",children:[s.experience,"/",s.experience+s.expToNextLevel," XP"]}),e.jsxs("span",{className:"text-xs font-medium text-violet-400",children:["+",b," XP"]})]}),e.jsxs("div",{className:"relative h-2 rounded-full overflow-hidden bg-[#0d1b2a]",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 rounded-full transition-all duration-700",style:{width:`${Math.min(100,g)}%`,background:"linear-gradient(to right, rgb(37 99 235), rgb(96 165 250))"}}),(()=>{const x=n*h,w=s.experience+x,C=s.experience+s.expToNextLevel,y=Math.min(100,w/C*100);return y>g?e.jsx("div",{className:"absolute top-0 h-full border-l border-l-violet-300",style:{left:`${g}%`,width:`${Math.min(100-g,y-g)}%`,backgroundColor:"rgb(124 58 237 / 0.5)"}}):null})()]})]}),s.level<100&&e.jsxs("div",{className:"space-y-3",children:[e.jsx(oe,{value:n,valueId:a.valueId,onChange:c}),v&&e.jsx("div",{className:"p-2 rounded-lg border bg-red-900/30 border-red-800",children:e.jsx("p",{className:"text-xs text-red-400",children:v})}),e.jsx("form",{onSubmit:x=>r(x,a.name),children:e.jsx("button",{type:"submit",disabled:i||l||m,className:"w-full h-8 text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2",style:{backgroundColor:l?"rgb(120 53 15 / 0.5)":m?"rgb(127 29 29 / 0.5)":"#2563eb",color:l?"rgb(251 191 36)":m?"rgb(248 113 113)":"white",borderColor:l?"rgb(146 64 14)":m?"rgb(153 27 27)":"transparent",cursor:l||m?"not-allowed":"pointer"},onMouseEnter:x=>{!l&&!m&&!i&&(x.currentTarget.style.backgroundColor="#1e40af")},onMouseLeave:x=>{!l&&!m&&!i&&(x.currentTarget.style.backgroundColor="#2563eb")},children:i?e.jsxs(e.Fragment,{children:[e.jsx(K,{}),e.jsx("span",{children:"Training..."})]}):l?e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"w-3 h-3"}),e.jsxs("span",{children:["Need ",u," focus"]})]}):m?e.jsxs(e.Fragment,{children:[e.jsx(V,{className:"w-3 h-3"}),e.jsx("span",{children:"Daily limit reached"})]}):e.jsxs(e.Fragment,{children:[e.jsx(G,{className:"w-3 h-3"}),e.jsxs("span",{children:["Train (",u," focus)"]})]})})})]})]})})};export{pe as default};
