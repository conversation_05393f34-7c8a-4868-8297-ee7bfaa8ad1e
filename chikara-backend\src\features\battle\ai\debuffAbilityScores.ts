import { HEALTH_THRESHOLD, calculateMeleeDamage } from "./battle.ai.js";
import { STATUS_ABILITIES } from "../logic/battle.abilities.js";
import type { BattlePlayer, EquippedAbility } from "../types/battle.types.js";

export const evaluateSleepAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 2.0;

    // Already active check - no need to reapply
    if (isActive) {
        return 0.1; // Extremely low priority if already asleep
    }

    // More valuable when AI is low on health (defensive strategy)
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 1.8; // Gives time to recover or plan next move
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.4;
    }

    // Consider enemy's damage output
    const incomingDamage = calculateMeleeDamage(enemy, aiPlayer);
    if (incomingDamage > aiPlayer.currentHealth * 0.3) {
        score *= 1.7; // More valuable against high-damage enemies
    }

    // Less valuable late in battle
    if (battleProgress > 0.7) {
        score *= 0.8;
    }

    // Consider stamina cost vs. benefit
    if (aiPlayer.currentStamina < 100) {
        score *= 0.9; // Slight reduction when stamina isn't abundant
    }

    // Consider if we have damaging abilities that would wake them up
    // If we have mostly damage abilities, sleep is less valuable
    if (
        aiPlayer.abilities &&
        aiPlayer.abilities.filter((ab) => ab.name.includes("bash") || ab.name.includes("headbutt")).length > 1
    ) {
        score *= 0.8;
    }

    // Consider intelligence stat - better for intelligence builds
    if (aiPlayer.attributes.intelligence > 60) {
        score *= 1.3;
    }

    return score;
};

export const evaluateStunAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 2.2;

    // Already active check
    if (isActive) {
        return 0.1; // Extremely low priority if already stunned
    }

    // More valuable when AI is low on health
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 2.0; // Very valuable in critical situations
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.5;
    }

    // Consider enemy's damage output
    const incomingDamage = calculateMeleeDamage(enemy, aiPlayer);
    if (incomingDamage > aiPlayer.currentHealth * 0.25) {
        score *= 1.8; // More valuable against high-damage enemies
    }

    // Consider battle progress
    if (battleProgress < 0.3) {
        score *= 1.3; // More valuable early in battle to gain advantage
    } else if (battleProgress > 0.7) {
        score *= 0.9; // Slightly less valuable late in battle
    }

    // Consider strength stat - better for strength builds
    if (aiPlayer.attributes.strength > 60) {
        score *= 1.4;
    }

    // Consider if enemy is about to die
    if (enemy.currentHealth <= calculateMeleeDamage(aiPlayer, enemy) * 1.5) {
        score *= 0.5; // Less valuable if enemy is nearly dead
    }

    return score;
};

export const evaluateCrippleAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 1.7;

    // Already active check
    if (isActive) {
        return 0.2; // Very low priority if already crippled
    }

    // More valuable early in battle to maximize effect duration
    if (battleProgress < 0.4) {
        score *= 1.4;
    } else if (battleProgress > 0.7) {
        score *= 0.8; // Less valuable late in battle
    }

    // Consider enemy's defence - more valuable against high-defence enemies
    if (enemy.attributes.defence > 60) {
        score *= 1.8; // Much more effective against high-defence enemies
    } else if (enemy.attributes.defence > 40) {
        score *= 1.4; // Still effective against medium-defence enemies
    }

    // Consider our attack power - more valuable for strength-based builds
    if (aiPlayer.attributes.strength > 60) {
        score *= 1.3; // Better for strength builds (aligns with ability tree)
    }

    // Consider if enemy has defensive buffs active
    if (enemy.statusEffects && enemy.statusEffects.high_guard?.turns && enemy.statusEffects.high_guard.turns > 0) {
        score *= 1.6; // More valuable if enemy has damage reduction active
    }

    // Consider if we have high damage abilities that would benefit from reduced enemy defence
    if (
        aiPlayer.abilities &&
        aiPlayer.abilities.some((ab) => ab.name.includes("bash") || ab.name.includes("headbutt"))
    ) {
        score *= 1.5; // More valuable if we have strong damage abilities
    }

    // Less valuable at critical health - other actions may be more important
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 0.7;
    }

    // Consider if enemy is nearly dead
    if (enemy.currentHealth <= calculateMeleeDamage(aiPlayer, enemy) * 1.5) {
        score *= 0.5; // Less valuable if enemy is nearly dead
    }

    return score;
};

export const evaluateShockwaveAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 1.9;

    // Already active check
    if (isActive) {
        return 0.1; // Extremely low priority if already stunned/knocked back
    }

    // More valuable when AI is low on health
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 1.8; // Valuable in critical situations
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.4;
    }

    // Consider enemy's damage output
    const incomingDamage = calculateMeleeDamage(enemy, aiPlayer);
    if (incomingDamage > aiPlayer.currentHealth * 0.25) {
        score *= 1.7; // More valuable against high-damage enemies
    }

    // Consider battle progress
    if (battleProgress < 0.3) {
        score *= 1.2; // More valuable early in battle
    } else if (battleProgress > 0.7) {
        score *= 0.9; // Slightly less valuable late in battle
    }

    // Consider defence stat - better for defence builds
    if (aiPlayer.attributes.defence > 60) {
        score *= 1.5;
    }

    // Consider if enemy is about to die
    if (enemy.currentHealth <= calculateMeleeDamage(aiPlayer, enemy) * 1.5) {
        score *= 0.5; // Less valuable if enemy is nearly dead
    }

    return score;
};

export const evaluateDisarmAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 2.1;

    // Already active check
    if (isActive) {
        return 0.1; // Extremely low priority if already disarmed
    }

    // Check if enemy has a weapon equipped
    const enemyHasWeapon = enemy.equipment && (enemy.equipment.weapon || enemy.equipment.ranged);
    if (!enemyHasWeapon) {
        return 0.1; // Almost useless if enemy has no weapon
    }

    // More valuable against enemies with high-damage weapons
    if (
        (enemy.equipment?.weapon?.damage && enemy.equipment?.weapon?.damage > 20) ||
        (enemy.equipment?.ranged?.damage && enemy.equipment?.ranged?.damage > 15)
    ) {
        score *= 1.8;
    }

    // Consider battle progress
    if (battleProgress < 0.4) {
        score *= 1.4; // More valuable early in battle
    } else if (battleProgress > 0.7) {
        score *= 0.8; // Less valuable late in battle
    }

    // Consider endurance stat - better for endurance builds
    // if (aiPlayer.endurance > 60) {
    //     score *= 1.3;
    // }

    // Consider if enemy relies heavily on weapon attacks vs abilities
    // This is a placeholder - would need actual data on enemy attack patterns
    if (enemy.abilities && enemy.abilities.length < 3) {
        score *= 1.6; // More valuable if enemy relies mostly on weapon attacks
    }

    // More valuable when AI is low on health
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 1.7;
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.3;
    }

    return score;
};

export const evaluateExhaustAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 1.8;

    // Already active check
    if (isActive) {
        return 0.2; // Very low priority if already exhausted
    }

    // More valuable against strength-based enemies
    if (enemy.attributes.strength > 60) {
        score *= 1.7; // Much more effective against high-strength enemies
    }

    // Consider battle progress
    if (battleProgress < 0.3) {
        score *= 1.5; // More valuable early in battle for maximum effect duration
    } else if (battleProgress > 0.7) {
        score *= 0.7; // Less valuable late in battle
    }

    // Consider enemy's current health
    if (enemy.currentHealth < enemy.maxHealth * 0.4) {
        score *= 0.8; // Less valuable if enemy is already low on health
    }

    // Consider dexterity stat - better for dexterity builds
    if (aiPlayer.attributes.dexterity > 60) {
        score *= 1.3;
    }

    // Consider if enemy relies on strength-based attacks
    // This is a placeholder - would need actual data on enemy attack patterns
    if (enemy.attributes.strength > enemy.attributes.dexterity * 1.3) {
        score *= 1.6; // More valuable against enemies that rely on strength
    }

    // Consider AI's health - more valuable when in danger
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 1.4;
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.2;
    }

    return score;
};

export const evaluateDebuffAbility = (
    ability: EquippedAbility,
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer
): number => {
    let score = 1.0;
    const healthPercentage = aiPlayer.currentHealth / aiPlayer.maxHealth;
    const enemyHealthPercentage = enemy.currentHealth / enemy.maxHealth;
    const battleProgress = 1 - enemyHealthPercentage; // 0 = battle just started, 1 = enemy nearly dead

    // Check if ability is already active
    const isDebuffActive = (debuffName: string) => {
        return (
            enemy.statusEffects && enemy.statusEffects[debuffName]?.turns && enemy.statusEffects[debuffName].turns > 0
        );
    };

    switch (ability.name) {
        case "cripple": {
            const isActive = !!isDebuffActive("cripple");
            score = evaluateCrippleAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "stun": {
            const isActive = !!isDebuffActive("stun");
            score = evaluateStunAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "shockwave": {
            const isActive = !!isDebuffActive("shockwave");
            score = evaluateShockwaveAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "sleep": {
            const isActive = !!isDebuffActive("sleep");
            score = evaluateSleepAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "disarm": {
            const isActive = !!isDebuffActive("disarm");
            score = evaluateDisarmAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "exhaust": {
            const isActive = !!isDebuffActive("exhaust");
            score = evaluateExhaustAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
    }

    // Consider overall battle context
    if (aiPlayer.currentStamina < 40) {
        score *= 0.8; // Reduce priority when low on stamina
    }

    // Consider turns remaining - debuffs are more valuable when there's enough time
    const battleLengthEstimate = enemy.currentHealth / calculateMeleeDamage(aiPlayer, enemy);
    const abilityTurns = STATUS_ABILITIES.find((s) => s.name === ability.name)?.turns;
    if (abilityTurns && battleLengthEstimate < abilityTurns - 1) {
        score *= 0.7; // Reduce score if battle might end before debuff has full effect
    }

    // Consider if enemy is nearly dead
    if (enemy.currentHealth < calculateMeleeDamage(aiPlayer, enemy) * 1.5) {
        score *= 0.5; // Significantly reduce value if we can kill enemy in 1-2 hits
    }

    return score;
};
