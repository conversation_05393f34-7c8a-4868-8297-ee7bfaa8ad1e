import{b as _,N as i}from"./index--cEnoMkg.js";const A=(t={})=>_(orpc.dailyQuest.getDailyQuests.queryOptions({staleTime:15e3,...t})),E={DEFEAT_NPC:"DEFEAT_NPC",DEFEAT_NPC_IN_TURNS:"DEFEAT_NPC_IN_TURNS",DEFEAT_NPC_WITH_LOW_DAMAGE:"DEFEAT_NPC_WITH_LOW_DAMAGE",DEFEAT_PLAYER:"DEFEAT_PLAYER",PVP_POST_BATTLE_CHOICE:"PVP_POST_BATTLE_CHOICE",ACQUIRE_ITEM:"ACQUIRE_ITEM",CRAFT_ITEM:"CRAFT_ITEM",DELIVER_ITEM:"DELIVER_ITEM",PLACE_BOUNTY:"PLACE_BOUNTY",UNIQUE_OBJECTIVE:"UNIQUE_OBJECTIVE",COMPLETE_MISSIONS:"COMPLETE_MISSIONS",DONATE_TO_SHRINE:"DONATE_TO_SHRINE",DEFEAT_BOSS:"DEFEAT_BOSS",VOTE_ON_SUGGESTION:"VOTE_ON_SUGGESTION",CHARACTER_ENCOUNTERS:"CHARACTER_ENCOUNTERS",WIN_BATTLE:"WIN_BATTLE",COLLECT_BOUNTY_REWARD:"COLLECT_BOUNTY_REWARD",TRAIN_STATS:"TRAIN_STATS",GAMBLING_SLOTS:"GAMBLING_SLOTS",DEFEAT_PLAYER_XNAME:"DEFEAT_PLAYER_XNAME",DEFEAT_SPECIFIC_PLAYER:"DEFEAT_SPECIFIC_PLAYER",COMPLETE_STORY_EPISODE:"COMPLETE_STORY_EPISODE"},n={[E.TRAIN_STATS]:"Train {quantity} Stat Points",[E.GAMBLING_SLOTS]:"Gamble ¥{quantity}",[E.DEFEAT_SPECIFIC_PLAYER]:"Defeat player #{target} {quantity} Time{pluralSuffix}",[E.DEFEAT_NPC]:"Defeat {quantity} NPC{pluralSuffix}",[E.PVP_POST_BATTLE_CHOICE]:"'{targetAction}' {quantity} player{pluralSuffix} in PvP",[E.COMPLETE_MISSIONS]:"Complete {quantity} Mission{pluralSuffix}",[E.DEFEAT_NPC_WITH_LOW_DAMAGE]:"Defeat NPCs while taking <{target}% Damage",[E.DEFEAT_PLAYER_XNAME]:"Beat players who have an '{targetAction}' in their name",[E.VOTE_ON_SUGGESTION]:"Vote on {quantity} Suggestion{pluralSuffix}",[E.CHARACTER_ENCOUNTERS]:"Complete {quantity} Character encounter{pluralSuffix}",[E.DONATE_TO_SHRINE]:"Donate to the Shrine",[E.DEFEAT_NPC_IN_TURNS]:"Defeat {quantity} NPC{pluralSuffix} in exactly {target} Turns",[E.DEFEAT_BOSS]:"Defeat {quantity} NPC Bosses",[E.WIN_BATTLE]:"Win {quantity} Battle{pluralSuffix}",[E.DEFEAT_PLAYER]:"Win {quantity} PvP Battle{pluralSuffix}",[E.COLLECT_BOUNTY_REWARD]:"Claim {quantity} Bounty{pluralSuffix}",[E.ACQUIRE_ITEM]:"Find {quantity} {targetItemName} in the {location}",[E.CRAFT_ITEM]:"Craft {quantity} {targetItemName}",[E.DELIVER_ITEM]:"Hand in {quantity} {targetItemName}",[E.PLACE_BOUNTY]:"Place a bounty worth at least ¥{target}",[E.UNIQUE_OBJECTIVE]:"",[E.COMPLETE_STORY_EPISODE]:"Complete the story episode at {location}"},S=t=>{let a=n[t.objectiveType]||t.objectiveType||"Complete the objective";if(a.includes("{quantity}")&&(a=a.replace("{quantity}",t.quantity===null||t.quantity===void 0?"-":t.quantity.toString())),a.includes("{location}")&&(a=a.replace("{location}",t.location===null||t.location===void 0?"-":i(t.location)||"-")),a.includes("{target}")&&(a=a.replace("{target}",t.target===null||t.target===void 0?"-":t.target.toString())),a.includes("{targetAction}")&&(a=a.replace("{targetAction}",t.targetAction===null||t.targetAction===void 0?"-":i(t.targetAction)||"-")),a.includes("{targetItemName}"))if(t.objectiveType===E.CRAFT_ITEM&&(!t.item||t.item===null)){const T=(t.quantity===null||t.quantity===void 0?0:t.quantity)===1?"":"s";a=a.replace("{targetItemName}",`Item${T}`)}else a=a.replace("{targetItemName}",t.item===null||t.item===void 0?"-":t.item.name);if(a.includes("{pluralSuffix}")){const e=t.quantity===null||t.quantity===void 0?0:t.quantity;a=a.replace("{pluralSuffix}",e===1?"":"s")}return a};export{E as Q,S as g,n as o,A as u};
