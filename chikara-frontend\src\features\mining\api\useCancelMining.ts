import { useMutation, useQueryClient } from "@tanstack/react-query";
import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";

const useCancelMining = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.mining.cancel.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.MINING.SESSION });
            },
        })
    );
};

export default useCancelMining;
