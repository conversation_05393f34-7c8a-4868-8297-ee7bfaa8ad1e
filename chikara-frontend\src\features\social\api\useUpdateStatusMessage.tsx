import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

// interface UpdateStatusMessageParams {
//     message: string | null;
// }

const useUpdateStatusMessage = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.social.updateStatusMessage.mutationOptions({
            onSuccess: () => {
                toast.success("Status message updated!");
                // Invalidate current user info to refresh with new status
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to update status message");
            },
        })
    );
};

export default useUpdateStatusMessage;
