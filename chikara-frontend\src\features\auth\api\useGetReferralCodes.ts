import { useQuery } from "@tanstack/react-query";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { api } from "@/helpers/api";

export const useGetReferralCodes = (options: QueryOptions = {}) => {
    return useQuery(
        api.registrationCodes.referralCodeList.queryOptions({
            staleTime: 300000, // 5 minutes
            ...options,
        })
    );
};
