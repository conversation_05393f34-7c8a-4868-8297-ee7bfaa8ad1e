import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// ../node_modules/react-string-replace/index.js
var require_react_string_replace = __commonJS({
  "../node_modules/react-string-replace/index.js"(exports, module) {
    var isRegExp = function(re) {
      return re instanceof RegExp;
    };
    var escapeRegExp = function escapeRegExp2(string) {
      var reRegExpChar = /[\\^$.*+?()[\]{}|]/g, reHasRegExpChar = RegExp(reRegExpChar.source);
      return string && reHasRegExpChar.test(string) ? string.replace(reRegExpChar, "\\$&") : string;
    };
    var isString = function(value) {
      return typeof value === "string";
    };
    var flatten = function(array) {
      var newArray = [];
      array.forEach(function(item) {
        if (Array.isArray(item)) {
          newArray = newArray.concat(item);
        } else {
          newArray.push(item);
        }
      });
      return newArray;
    };
    function replaceString(str, match, fn) {
      var curCharStart = 0;
      var curCharLen = 0;
      if (str === "") {
        return str;
      } else if (!str || !isString(str)) {
        throw new TypeError("First argument to react-string-replace#replaceString must be a string");
      }
      var re = match;
      if (!isRegExp(re)) {
        re = new RegExp("(" + escapeRegExp(re) + ")", "gi");
      }
      var result = str.split(re);
      for (var i = 1, length = result.length; i < length; i += 2) {
        if (result[i] === void 0 || result[i - 1] === void 0) {
          console.warn("reactStringReplace: Encountered undefined value during string replacement. Your RegExp may not be working the way you expect.");
          continue;
        }
        curCharLen = result[i].length;
        curCharStart += result[i - 1].length;
        result[i] = fn(result[i], i, curCharStart);
        curCharStart += curCharLen;
      }
      return result;
    }
    module.exports = function reactStringReplace(source, match, fn) {
      if (!Array.isArray(source)) source = [source];
      return flatten(source.map(function(x) {
        return isString(x) ? replaceString(x, match, fn) : x;
      }));
    };
  }
});
export default require_react_string_replace();
//# sourceMappingURL=react-string-replace.js.map
