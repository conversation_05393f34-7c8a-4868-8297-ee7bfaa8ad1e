import { logger, LogErrorStack } from "../../utils/log.js";
import { dispatchEvent, registerEventHandler } from "./event-dispatcher.js";
import { GameEventType, GameEventPayloadMap } from "./event-types.js";
import { handleNPCBattleWonEvent, handlePVPBattleWonEvent } from "./handlers/battle-event-handlers.js";
import { handleItemDroppedEvent, handleItemCraftedEvent } from "./handlers/item-event-handlers.js";
import { handleBountyPlacedEvent, handleBountyCollectedEvent } from "./handlers/bounty-event-handlers.js";
import {
    handleSuggestionVotedEvent,
    handleEncounterCompletedEvent,
    handleMissionCompletedEvent,
    handleShrineDonationMadeEvent,
    handleStatsTrainedEvent,
    handleGamblingPerformedEvent,
} from "./handlers/misc-event-handlers.js";

/**
 * Initialize the game event system by registering all event handlers
 */
export const initializeGameEventSystem = (): void => {
    logger.info("Initializing game event system...");

    registerEventHandler(GameEventType.NPC_BATTLE_WON, handleNPCBattleWonEvent);
    registerEventHandler(GameEventType.PVP_BATTLE_WON, handlePVPBattleWonEvent);
    registerEventHandler(GameEventType.ITEM_DROPPED, handleItemDroppedEvent);
    registerEventHandler(GameEventType.ITEM_CRAFTED, handleItemCraftedEvent);
    registerEventHandler(GameEventType.BOUNTY_PLACED, handleBountyPlacedEvent);
    registerEventHandler(GameEventType.BOUNTY_COLLECTED, handleBountyCollectedEvent);
    registerEventHandler(GameEventType.MISSION_COMPLETED, handleMissionCompletedEvent);
    registerEventHandler(GameEventType.SHRINE_DONATION_MADE, handleShrineDonationMadeEvent);
    registerEventHandler(GameEventType.SUGGESTION_VOTED, handleSuggestionVotedEvent);
    registerEventHandler(GameEventType.ENCOUNTER_COMPLETED, handleEncounterCompletedEvent);
    registerEventHandler(GameEventType.STATS_TRAINED, handleStatsTrainedEvent);
    registerEventHandler(GameEventType.GAMBLING_PERFORMED, handleGamblingPerformedEvent);

    logger.info("Game event system initialized successfully");
};

/**
 * Emit a game event with type safety
 * @param eventType - The type of event to emit
 * @param payload - The event payload data
 */
export const emitGameEvent = async <T extends GameEventType>(
    eventType: T,
    payload: GameEventPayloadMap[T]
): Promise<void> => {
    try {
        await dispatchEvent(eventType, payload);
    } catch (error) {
        LogErrorStack({ message: `Failed to emit game event ${eventType}`, error });
        throw error;
    }
};

/**
 * Convenience functions for common game events
 */

/**
 * Emit an NPC battle won event
 */
export const emitNPCBattleWon = async (payload: GameEventPayloadMap[GameEventType.NPC_BATTLE_WON]): Promise<void> => {
    await emitGameEvent(GameEventType.NPC_BATTLE_WON, payload);
};

/**
 * Emit a PVP battle won event
 */
export const emitPVPBattleWon = async (payload: GameEventPayloadMap[GameEventType.PVP_BATTLE_WON]): Promise<void> => {
    await emitGameEvent(GameEventType.PVP_BATTLE_WON, payload);
};

/**
 * Emit an item crafted event
 */
export const emitItemCrafted = async (payload: GameEventPayloadMap[GameEventType.ITEM_CRAFTED]): Promise<void> => {
    await emitGameEvent(GameEventType.ITEM_CRAFTED, payload);
};

/**
 * Emit a mission completed event
 */
export const emitMissionCompleted = async (
    payload: GameEventPayloadMap[GameEventType.MISSION_COMPLETED]
): Promise<void> => {
    await emitGameEvent(GameEventType.MISSION_COMPLETED, payload);
};

/**
 * Emit a stats trained event
 */
export const emitStatsTrained = async (payload: GameEventPayloadMap[GameEventType.STATS_TRAINED]): Promise<void> => {
    await emitGameEvent(GameEventType.STATS_TRAINED, payload);
};

/**
 * Emit a gambling performed event
 */
export const emitGamblingPerformed = async (
    payload: GameEventPayloadMap[GameEventType.GAMBLING_PERFORMED]
): Promise<void> => {
    await emitGameEvent(GameEventType.GAMBLING_PERFORMED, payload);
};

/**
 * Emit a story episode completed event
 */
export const emitStoryEpisodeCompleted = async (
    payload: GameEventPayloadMap[GameEventType.STORY_EPISODE_COMPLETED]
): Promise<void> => {
    await emitGameEvent(GameEventType.STORY_EPISODE_COMPLETED, payload);
};

/**
 * Emit an item dropped event
 */
export const emitItemDropped = async (payload: GameEventPayloadMap[GameEventType.ITEM_DROPPED]): Promise<void> => {
    await emitGameEvent(GameEventType.ITEM_DROPPED, payload);
};

/**
 * Emit a shrine donation made event
 */
export const emitShrineDonationMade = async (
    payload: GameEventPayloadMap[GameEventType.SHRINE_DONATION_MADE]
): Promise<void> => {
    await emitGameEvent(GameEventType.SHRINE_DONATION_MADE, payload);
};

/**
 * Emit a bounty placed event
 */
export const emitBountyPlaced = async (payload: GameEventPayloadMap[GameEventType.BOUNTY_PLACED]): Promise<void> => {
    await emitGameEvent(GameEventType.BOUNTY_PLACED, payload);
};

/**
 * Emit a bounty collected event
 */
export const emitBountyCollected = async (
    payload: GameEventPayloadMap[GameEventType.BOUNTY_COLLECTED]
): Promise<void> => {
    await emitGameEvent(GameEventType.BOUNTY_COLLECTED, payload);
};

/**
 * Emit a suggestion voted event
 */
export const emitSuggestionVoted = async (
    payload: GameEventPayloadMap[GameEventType.SUGGESTION_VOTED]
): Promise<void> => {
    await emitGameEvent(GameEventType.SUGGESTION_VOTED, payload);
};

/**
 * Emit an encounter completed event
 */
export const emitEncounterCompleted = async (
    payload: GameEventPayloadMap[GameEventType.ENCOUNTER_COMPLETED]
): Promise<void> => {
    await emitGameEvent(GameEventType.ENCOUNTER_COMPLETED, payload);
};

/**
 * Emit a roguelike level reached event
 */
export const emitRoguelikeLevelReached = async (
    payload: GameEventPayloadMap[GameEventType.ROGUELIKE_LEVEL_REACHED]
): Promise<void> => {
    await emitGameEvent(GameEventType.ROGUELIKE_LEVEL_REACHED, payload);
};

/**
 * Emit a quest completed event
 */
export const emitQuestCompleted = async (
    payload: GameEventPayloadMap[GameEventType.QUEST_COMPLETED]
): Promise<void> => {
    await emitGameEvent(GameEventType.QUEST_COMPLETED, payload);
};

/**
 * Emit a daily quest completed event
 */
export const emitDailyQuestCompleted = async (
    payload: GameEventPayloadMap[GameEventType.DAILY_QUEST_COMPLETED]
): Promise<void> => {
    await emitGameEvent(GameEventType.DAILY_QUEST_COMPLETED, payload);
};

/**
 * Emit an ability used event
 */
export const emitAbilityUsed = async (payload: GameEventPayloadMap[GameEventType.ABILITY_USED]): Promise<void> => {
    await emitGameEvent(GameEventType.ABILITY_USED, payload);
};

/**
 * Emit a zone completed event
 */
export const emitZoneCompleted = async (payload: GameEventPayloadMap[GameEventType.ZONE_COMPLETED]): Promise<void> => {
    await emitGameEvent(GameEventType.ZONE_COMPLETED, payload);
};
