import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery } from "@tanstack/react-query";

const useJobInfo = (options: QueryOptions = {}) => {
    return useQuery(
        api.jobs.currentJobInfo.queryOptions({
            staleTime: 30000, // Cache for 30 seconds
            ...options,
        })
    );
};

export default useJobInfo;
