[{"userId": 1, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 1, "skillType": "dexterity", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 1, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 1, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 1, "skillType": "intelligence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 2, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 2, "skillType": "dexterity", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 2, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 2, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 2, "skillType": "intelligence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 3, "skillType": "strength", "level": 100, "experience": 0, "talentPoints": 0}, {"userId": 3, "skillType": "dexterity", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 3, "skillType": "defence", "level": 100, "experience": 0, "talentPoints": 0}, {"userId": 3, "skillType": "endurance", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 3, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 4, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 4, "skillType": "dexterity", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 4, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 4, "skillType": "endurance", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 4, "skillType": "intelligence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 5, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 5, "skillType": "dexterity", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 5, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 5, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 5, "skillType": "intelligence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 6, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 6, "skillType": "dexterity", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 6, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 6, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 6, "skillType": "intelligence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 7, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 7, "skillType": "dexterity", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 7, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 7, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 7, "skillType": "intelligence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 8, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 8, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 8, "skillType": "defence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 8, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 8, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 9, "skillType": "strength", "level": 31, "experience": 0, "talentPoints": 0}, {"userId": 9, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 9, "skillType": "defence", "level": 22, "experience": 0, "talentPoints": 0}, {"userId": 9, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 9, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 10, "skillType": "strength", "level": 25, "experience": 0, "talentPoints": 0}, {"userId": 10, "skillType": "dexterity", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 10, "skillType": "defence", "level": 20, "experience": 0, "talentPoints": 0}, {"userId": 10, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 10, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 11, "skillType": "strength", "level": 36, "experience": 0, "talentPoints": 0}, {"userId": 11, "skillType": "dexterity", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 11, "skillType": "defence", "level": 43, "experience": 0, "talentPoints": 0}, {"userId": 11, "skillType": "endurance", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 11, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 12, "skillType": "strength", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 12, "skillType": "dexterity", "level": 58, "experience": 0, "talentPoints": 0}, {"userId": 12, "skillType": "defence", "level": 57, "experience": 0, "talentPoints": 0}, {"userId": 12, "skillType": "endurance", "level": 51, "experience": 0, "talentPoints": 0}, {"userId": 12, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 13, "skillType": "strength", "level": 15, "experience": 0, "talentPoints": 0}, {"userId": 13, "skillType": "dexterity", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 13, "skillType": "defence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 13, "skillType": "endurance", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 13, "skillType": "intelligence", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 14, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 14, "skillType": "dexterity", "level": 20, "experience": 0, "talentPoints": 0}, {"userId": 14, "skillType": "defence", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 14, "skillType": "endurance", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 14, "skillType": "intelligence", "level": 45, "experience": 0, "talentPoints": 0}, {"userId": 15, "skillType": "strength", "level": 57, "experience": 0, "talentPoints": 0}, {"userId": 15, "skillType": "dexterity", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 15, "skillType": "defence", "level": 60, "experience": 0, "talentPoints": 0}, {"userId": 15, "skillType": "endurance", "level": 48, "experience": 0, "talentPoints": 0}, {"userId": 15, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 16, "skillType": "strength", "level": 32, "experience": 0, "talentPoints": 0}, {"userId": 16, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 16, "skillType": "defence", "level": 32, "experience": 0, "talentPoints": 0}, {"userId": 16, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 16, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 17, "skillType": "strength", "level": 44, "experience": 0, "talentPoints": 0}, {"userId": 17, "skillType": "dexterity", "level": 43, "experience": 0, "talentPoints": 0}, {"userId": 17, "skillType": "defence", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 17, "skillType": "endurance", "level": 35, "experience": 0, "talentPoints": 0}, {"userId": 17, "skillType": "intelligence", "level": 42, "experience": 0, "talentPoints": 0}, {"userId": 18, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 18, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 18, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 18, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 18, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 19, "skillType": "strength", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 19, "skillType": "dexterity", "level": 52, "experience": 0, "talentPoints": 0}, {"userId": 19, "skillType": "defence", "level": 55, "experience": 0, "talentPoints": 0}, {"userId": 19, "skillType": "endurance", "level": 49, "experience": 0, "talentPoints": 0}, {"userId": 19, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 20, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 20, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 20, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 20, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 20, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 21, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 21, "skillType": "dexterity", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 21, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 21, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 21, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 22, "skillType": "strength", "level": 26, "experience": 0, "talentPoints": 0}, {"userId": 22, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 22, "skillType": "defence", "level": 25, "experience": 0, "talentPoints": 0}, {"userId": 22, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 22, "skillType": "intelligence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 23, "skillType": "strength", "level": 20, "experience": 0, "talentPoints": 0}, {"userId": 23, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 23, "skillType": "defence", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 23, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 23, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 24, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 24, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 24, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 24, "skillType": "endurance", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 24, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 25, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 25, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 25, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 25, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 25, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 26, "skillType": "strength", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 26, "skillType": "dexterity", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 26, "skillType": "defence", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 26, "skillType": "endurance", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 26, "skillType": "intelligence", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 27, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 27, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 27, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 27, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 27, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 28, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 28, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 28, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 28, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 28, "skillType": "intelligence", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 29, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 29, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 29, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 29, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 29, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 30, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 30, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 30, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 30, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 30, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 31, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 31, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 31, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 31, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 31, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 32, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 32, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 32, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 32, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 32, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 33, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 33, "skillType": "dexterity", "level": 18, "experience": 0, "talentPoints": 0}, {"userId": 33, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 33, "skillType": "endurance", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 33, "skillType": "intelligence", "level": 36, "experience": 0, "talentPoints": 0}, {"userId": 34, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 34, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 34, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 34, "skillType": "endurance", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 34, "skillType": "intelligence", "level": 21, "experience": 0, "talentPoints": 0}, {"userId": 35, "skillType": "strength", "level": 28, "experience": 0, "talentPoints": 0}, {"userId": 35, "skillType": "dexterity", "level": 27, "experience": 0, "talentPoints": 0}, {"userId": 35, "skillType": "defence", "level": 28, "experience": 0, "talentPoints": 0}, {"userId": 35, "skillType": "endurance", "level": 19, "experience": 0, "talentPoints": 0}, {"userId": 35, "skillType": "intelligence", "level": 21, "experience": 0, "talentPoints": 0}, {"userId": 36, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 36, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 36, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 36, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 36, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 37, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 37, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 37, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 37, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 37, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 38, "skillType": "strength", "level": 43, "experience": 0, "talentPoints": 0}, {"userId": 38, "skillType": "dexterity", "level": 38, "experience": 0, "talentPoints": 0}, {"userId": 38, "skillType": "defence", "level": 44, "experience": 0, "talentPoints": 0}, {"userId": 38, "skillType": "endurance", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 38, "skillType": "intelligence", "level": 42, "experience": 0, "talentPoints": 0}, {"userId": 39, "skillType": "strength", "level": 51, "experience": 0, "talentPoints": 0}, {"userId": 39, "skillType": "dexterity", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 39, "skillType": "defence", "level": 56, "experience": 0, "talentPoints": 0}, {"userId": 39, "skillType": "endurance", "level": 56, "experience": 0, "talentPoints": 0}, {"userId": 39, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 40, "skillType": "strength", "level": 19, "experience": 0, "talentPoints": 0}, {"userId": 40, "skillType": "dexterity", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 40, "skillType": "defence", "level": 21, "experience": 0, "talentPoints": 0}, {"userId": 40, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 40, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 41, "skillType": "strength", "level": 42, "experience": 0, "talentPoints": 0}, {"userId": 41, "skillType": "dexterity", "level": 25, "experience": 0, "talentPoints": 0}, {"userId": 41, "skillType": "defence", "level": 27, "experience": 0, "talentPoints": 0}, {"userId": 41, "skillType": "endurance", "level": 32, "experience": 0, "talentPoints": 0}, {"userId": 41, "skillType": "intelligence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 42, "skillType": "strength", "level": 44, "experience": 0, "talentPoints": 0}, {"userId": 42, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 42, "skillType": "defence", "level": 24, "experience": 0, "talentPoints": 0}, {"userId": 42, "skillType": "endurance", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 42, "skillType": "intelligence", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 43, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 43, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 43, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 43, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 43, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 44, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 44, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 44, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 44, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 44, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 45, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 45, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 45, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 45, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 45, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 46, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 46, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 46, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 46, "skillType": "endurance", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 46, "skillType": "intelligence", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 47, "skillType": "strength", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 47, "skillType": "dexterity", "level": 26, "experience": 0, "talentPoints": 0}, {"userId": 47, "skillType": "defence", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 47, "skillType": "endurance", "level": 28, "experience": 0, "talentPoints": 0}, {"userId": 47, "skillType": "intelligence", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 48, "skillType": "strength", "level": 57, "experience": 0, "talentPoints": 0}, {"userId": 48, "skillType": "dexterity", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 48, "skillType": "defence", "level": 57, "experience": 0, "talentPoints": 0}, {"userId": 48, "skillType": "endurance", "level": 51, "experience": 0, "talentPoints": 0}, {"userId": 48, "skillType": "intelligence", "level": 48, "experience": 0, "talentPoints": 0}, {"userId": 49, "skillType": "strength", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 49, "skillType": "dexterity", "level": 50, "experience": 0, "talentPoints": 0}, {"userId": 49, "skillType": "defence", "level": 55, "experience": 0, "talentPoints": 0}, {"userId": 49, "skillType": "endurance", "level": 54, "experience": 0, "talentPoints": 0}, {"userId": 49, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 50, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 50, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 50, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 50, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 50, "skillType": "intelligence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 51, "skillType": "strength", "level": 51, "experience": 0, "talentPoints": 0}, {"userId": 51, "skillType": "dexterity", "level": 53, "experience": 0, "talentPoints": 0}, {"userId": 51, "skillType": "defence", "level": 54, "experience": 0, "talentPoints": 0}, {"userId": 51, "skillType": "endurance", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 51, "skillType": "intelligence", "level": 45, "experience": 0, "talentPoints": 0}, {"userId": 52, "skillType": "strength", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 52, "skillType": "dexterity", "level": 24, "experience": 0, "talentPoints": 0}, {"userId": 52, "skillType": "defence", "level": 24, "experience": 0, "talentPoints": 0}, {"userId": 52, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 52, "skillType": "intelligence", "level": 18, "experience": 0, "talentPoints": 0}, {"userId": 53, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 53, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 53, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 53, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 53, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 54, "skillType": "strength", "level": 18, "experience": 0, "talentPoints": 0}, {"userId": 54, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 54, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 54, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 54, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 55, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 55, "skillType": "dexterity", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 55, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 55, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 55, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 56, "skillType": "strength", "level": 52, "experience": 0, "talentPoints": 0}, {"userId": 56, "skillType": "dexterity", "level": 20, "experience": 0, "talentPoints": 0}, {"userId": 56, "skillType": "defence", "level": 52, "experience": 0, "talentPoints": 0}, {"userId": 56, "skillType": "endurance", "level": 48, "experience": 0, "talentPoints": 0}, {"userId": 56, "skillType": "intelligence", "level": 47, "experience": 0, "talentPoints": 0}, {"userId": 57, "skillType": "strength", "level": 53, "experience": 0, "talentPoints": 0}, {"userId": 57, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 57, "skillType": "defence", "level": 55, "experience": 0, "talentPoints": 0}, {"userId": 57, "skillType": "endurance", "level": 49, "experience": 0, "talentPoints": 0}, {"userId": 57, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 58, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 58, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 58, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 58, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 58, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 59, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 59, "skillType": "dexterity", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 59, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 59, "skillType": "endurance", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 59, "skillType": "intelligence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 60, "skillType": "strength", "level": 51, "experience": 0, "talentPoints": 0}, {"userId": 60, "skillType": "dexterity", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 60, "skillType": "defence", "level": 56, "experience": 0, "talentPoints": 0}, {"userId": 60, "skillType": "endurance", "level": 56, "experience": 0, "talentPoints": 0}, {"userId": 60, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 61, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 61, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 61, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 61, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 61, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 62, "skillType": "strength", "level": 44, "experience": 0, "talentPoints": 0}, {"userId": 62, "skillType": "dexterity", "level": 38, "experience": 0, "talentPoints": 0}, {"userId": 62, "skillType": "defence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 62, "skillType": "endurance", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 62, "skillType": "intelligence", "level": 49, "experience": 0, "talentPoints": 0}, {"userId": 63, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 63, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 63, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 63, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 63, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 64, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 64, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 64, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 64, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 64, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 65, "skillType": "strength", "level": 15, "experience": 0, "talentPoints": 0}, {"userId": 65, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 65, "skillType": "defence", "level": 15, "experience": 0, "talentPoints": 0}, {"userId": 65, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 65, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 66, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 66, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 66, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 66, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 66, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 67, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 67, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 67, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 67, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 67, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 68, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 68, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 68, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 68, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 68, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 69, "skillType": "strength", "level": 42, "experience": 0, "talentPoints": 0}, {"userId": 69, "skillType": "dexterity", "level": 42, "experience": 0, "talentPoints": 0}, {"userId": 69, "skillType": "defence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 69, "skillType": "endurance", "level": 45, "experience": 0, "talentPoints": 0}, {"userId": 69, "skillType": "intelligence", "level": 43, "experience": 0, "talentPoints": 0}, {"userId": 70, "skillType": "strength", "level": 20, "experience": 0, "talentPoints": 0}, {"userId": 70, "skillType": "dexterity", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 70, "skillType": "defence", "level": 20, "experience": 0, "talentPoints": 0}, {"userId": 70, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 70, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 71, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 71, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 71, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 71, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 71, "skillType": "intelligence", "level": 27, "experience": 0, "talentPoints": 0}, {"userId": 72, "skillType": "strength", "level": 19, "experience": 0, "talentPoints": 0}, {"userId": 72, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 72, "skillType": "defence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 72, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 72, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 73, "skillType": "strength", "level": 43, "experience": 0, "talentPoints": 0}, {"userId": 73, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 73, "skillType": "defence", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 73, "skillType": "endurance", "level": 32, "experience": 0, "talentPoints": 0}, {"userId": 73, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 74, "skillType": "strength", "level": 15, "experience": 0, "talentPoints": 0}, {"userId": 74, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 74, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 74, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 74, "skillType": "intelligence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 75, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 75, "skillType": "dexterity", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 75, "skillType": "defence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 75, "skillType": "endurance", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 75, "skillType": "intelligence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 76, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 76, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 76, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 76, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 76, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 77, "skillType": "strength", "level": 56, "experience": 0, "talentPoints": 0}, {"userId": 77, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 77, "skillType": "defence", "level": 51, "experience": 0, "talentPoints": 0}, {"userId": 77, "skillType": "endurance", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 77, "skillType": "intelligence", "level": 44, "experience": 0, "talentPoints": 0}, {"userId": 78, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 78, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 78, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 78, "skillType": "endurance", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 78, "skillType": "intelligence", "level": 20, "experience": 0, "talentPoints": 0}, {"userId": 79, "skillType": "strength", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 79, "skillType": "dexterity", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 79, "skillType": "defence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 79, "skillType": "endurance", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 79, "skillType": "intelligence", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 80, "skillType": "strength", "level": 26, "experience": 0, "talentPoints": 0}, {"userId": 80, "skillType": "dexterity", "level": 25, "experience": 0, "talentPoints": 0}, {"userId": 80, "skillType": "defence", "level": 25, "experience": 0, "talentPoints": 0}, {"userId": 80, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 80, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 81, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 81, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 81, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 81, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 81, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 82, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 82, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 82, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 82, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 82, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 83, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 83, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 83, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 83, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 83, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 84, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 84, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 84, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 84, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 84, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 85, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 85, "skillType": "dexterity", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 85, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 85, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 85, "skillType": "intelligence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 86, "skillType": "strength", "level": 15, "experience": 0, "talentPoints": 0}, {"userId": 86, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 86, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 86, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 86, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 87, "skillType": "strength", "level": 55, "experience": 0, "talentPoints": 0}, {"userId": 87, "skillType": "dexterity", "level": 48, "experience": 0, "talentPoints": 0}, {"userId": 87, "skillType": "defence", "level": 53, "experience": 0, "talentPoints": 0}, {"userId": 87, "skillType": "endurance", "level": 47, "experience": 0, "talentPoints": 0}, {"userId": 87, "skillType": "intelligence", "level": 39, "experience": 0, "talentPoints": 0}, {"userId": 88, "skillType": "strength", "level": 40, "experience": 0, "talentPoints": 0}, {"userId": 88, "skillType": "dexterity", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 88, "skillType": "defence", "level": 39, "experience": 0, "talentPoints": 0}, {"userId": 88, "skillType": "endurance", "level": 36, "experience": 0, "talentPoints": 0}, {"userId": 88, "skillType": "intelligence", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 89, "skillType": "strength", "level": 48, "experience": 0, "talentPoints": 0}, {"userId": 89, "skillType": "dexterity", "level": 43, "experience": 0, "talentPoints": 0}, {"userId": 89, "skillType": "defence", "level": 53, "experience": 0, "talentPoints": 0}, {"userId": 89, "skillType": "endurance", "level": 33, "experience": 0, "talentPoints": 0}, {"userId": 89, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 90, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 90, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 90, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 90, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 90, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 91, "skillType": "strength", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 91, "skillType": "dexterity", "level": 22, "experience": 0, "talentPoints": 0}, {"userId": 91, "skillType": "defence", "level": 31, "experience": 0, "talentPoints": 0}, {"userId": 91, "skillType": "endurance", "level": 35, "experience": 0, "talentPoints": 0}, {"userId": 91, "skillType": "intelligence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 92, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 92, "skillType": "dexterity", "level": 21, "experience": 0, "talentPoints": 0}, {"userId": 92, "skillType": "defence", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 92, "skillType": "endurance", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 92, "skillType": "intelligence", "level": 38, "experience": 0, "talentPoints": 0}, {"userId": 93, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 93, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 93, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 93, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 93, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 94, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 94, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 94, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 94, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 94, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 95, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 95, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 95, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 95, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 95, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 96, "skillType": "strength", "level": 26, "experience": 0, "talentPoints": 0}, {"userId": 96, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 96, "skillType": "defence", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 96, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 96, "skillType": "intelligence", "level": 51, "experience": 0, "talentPoints": 0}, {"userId": 97, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 97, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 97, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 97, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 97, "skillType": "intelligence", "level": 18, "experience": 0, "talentPoints": 0}, {"userId": 98, "skillType": "strength", "level": 26, "experience": 0, "talentPoints": 0}, {"userId": 98, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 98, "skillType": "defence", "level": 22, "experience": 0, "talentPoints": 0}, {"userId": 98, "skillType": "endurance", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 98, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 99, "skillType": "strength", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 99, "skillType": "dexterity", "level": 26, "experience": 0, "talentPoints": 0}, {"userId": 99, "skillType": "defence", "level": 27, "experience": 0, "talentPoints": 0}, {"userId": 99, "skillType": "endurance", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 99, "skillType": "intelligence", "level": 20, "experience": 0, "talentPoints": 0}, {"userId": 100, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 100, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 100, "skillType": "defence", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 100, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 100, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 101, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 101, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 101, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 101, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 101, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 102, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 102, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 102, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 102, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 102, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 103, "skillType": "strength", "level": 38, "experience": 0, "talentPoints": 0}, {"userId": 103, "skillType": "dexterity", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 103, "skillType": "defence", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 103, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 103, "skillType": "intelligence", "level": 32, "experience": 0, "talentPoints": 0}, {"userId": 104, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 104, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 104, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 104, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 104, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 105, "skillType": "strength", "level": 50, "experience": 0, "talentPoints": 0}, {"userId": 105, "skillType": "dexterity", "level": 26, "experience": 0, "talentPoints": 0}, {"userId": 105, "skillType": "defence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 105, "skillType": "endurance", "level": 18, "experience": 0, "talentPoints": 0}, {"userId": 105, "skillType": "intelligence", "level": 39, "experience": 0, "talentPoints": 0}, {"userId": 106, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 106, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 106, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 106, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 106, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 107, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 107, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 107, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 107, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 107, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 108, "skillType": "strength", "level": 24, "experience": 0, "talentPoints": 0}, {"userId": 108, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 108, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 108, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 108, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 109, "skillType": "strength", "level": 17, "experience": 0, "talentPoints": 0}, {"userId": 109, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 109, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 109, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 109, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 110, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 110, "skillType": "dexterity", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 110, "skillType": "defence", "level": 21, "experience": 0, "talentPoints": 0}, {"userId": 110, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 110, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 111, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 111, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 111, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 111, "skillType": "endurance", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 111, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 112, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 112, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 112, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 112, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 112, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 113, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 113, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 113, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 113, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 113, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 114, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 114, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 114, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 114, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 114, "skillType": "intelligence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 115, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 115, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 115, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 115, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 115, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 116, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 116, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 116, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 116, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 116, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 117, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 117, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 117, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 117, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 117, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 118, "skillType": "strength", "level": 36, "experience": 0, "talentPoints": 0}, {"userId": 118, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 118, "skillType": "defence", "level": 33, "experience": 0, "talentPoints": 0}, {"userId": 118, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 118, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 119, "skillType": "strength", "level": 24, "experience": 0, "talentPoints": 0}, {"userId": 119, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 119, "skillType": "defence", "level": 26, "experience": 0, "talentPoints": 0}, {"userId": 119, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 119, "skillType": "intelligence", "level": 22, "experience": 0, "talentPoints": 0}, {"userId": 120, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 120, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 120, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 120, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 120, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 122, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 122, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 122, "skillType": "defence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 122, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 122, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 123, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 123, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 123, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 123, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 123, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 124, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 124, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 124, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 124, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 124, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 125, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 125, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 125, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 125, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 125, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 126, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 126, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 126, "skillType": "defence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 126, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 126, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 127, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 127, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 127, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 127, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 127, "skillType": "intelligence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 128, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 128, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 128, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 128, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 128, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 129, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 129, "skillType": "dexterity", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 129, "skillType": "defence", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 129, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 129, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 130, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 130, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 130, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 130, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 130, "skillType": "intelligence", "level": 17, "experience": 0, "talentPoints": 0}, {"userId": 131, "skillType": "strength", "level": 36, "experience": 0, "talentPoints": 0}, {"userId": 131, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 131, "skillType": "defence", "level": 37, "experience": 0, "talentPoints": 0}, {"userId": 131, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 131, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 132, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 132, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 132, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 132, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 132, "skillType": "intelligence", "level": 18, "experience": 0, "talentPoints": 0}, {"userId": 133, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 133, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 133, "skillType": "defence", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 133, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 133, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 134, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 134, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 134, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 134, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 134, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 135, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 135, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 135, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 135, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 135, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 136, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 136, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 136, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 136, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 136, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 137, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 137, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 137, "skillType": "defence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 137, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 137, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 138, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 138, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 138, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 138, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 138, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 139, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 139, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 139, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 139, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 139, "skillType": "intelligence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 140, "skillType": "strength", "level": 24, "experience": 0, "talentPoints": 0}, {"userId": 140, "skillType": "dexterity", "level": 43, "experience": 0, "talentPoints": 0}, {"userId": 140, "skillType": "defence", "level": 35, "experience": 0, "talentPoints": 0}, {"userId": 140, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 140, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 141, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 141, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 141, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 141, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 141, "skillType": "intelligence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 142, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 142, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 142, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 142, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 142, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 143, "skillType": "strength", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 143, "skillType": "dexterity", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 143, "skillType": "defence", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 143, "skillType": "endurance", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 143, "skillType": "intelligence", "level": 39, "experience": 0, "talentPoints": 0}, {"userId": 144, "skillType": "strength", "level": 22, "experience": 0, "talentPoints": 0}, {"userId": 144, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 144, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 144, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 144, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 145, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 145, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 145, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 145, "skillType": "endurance", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 145, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 146, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 146, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 146, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 146, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 146, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 147, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 147, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 147, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 147, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 147, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 148, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 148, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 148, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 148, "skillType": "endurance", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 148, "skillType": "intelligence", "level": 25, "experience": 0, "talentPoints": 0}, {"userId": 149, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 149, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 149, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 149, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 149, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 150, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 150, "skillType": "dexterity", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 150, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 150, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 150, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 151, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 151, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 151, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 151, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 151, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 152, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 152, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 152, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 152, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 152, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 153, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 153, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 153, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 153, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 153, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 154, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 154, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 154, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 154, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 154, "skillType": "intelligence", "level": 18, "experience": 0, "talentPoints": 0}, {"userId": 155, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 155, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 155, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 155, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 155, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 156, "skillType": "strength", "level": 54, "experience": 0, "talentPoints": 0}, {"userId": 156, "skillType": "dexterity", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 156, "skillType": "defence", "level": 51, "experience": 0, "talentPoints": 0}, {"userId": 156, "skillType": "endurance", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 156, "skillType": "intelligence", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 157, "skillType": "strength", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 157, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 157, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 157, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 157, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 158, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 158, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 158, "skillType": "defence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 158, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 158, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 159, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 159, "skillType": "dexterity", "level": 21, "experience": 0, "talentPoints": 0}, {"userId": 159, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 159, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 159, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 160, "skillType": "strength", "level": 21, "experience": 0, "talentPoints": 0}, {"userId": 160, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 160, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 160, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 160, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 161, "skillType": "strength", "level": 36, "experience": 0, "talentPoints": 0}, {"userId": 161, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 161, "skillType": "defence", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 161, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 161, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 162, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 162, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 162, "skillType": "defence", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 162, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 162, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 163, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 163, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 163, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 163, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 163, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 164, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 164, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 164, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 164, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 164, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 165, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 165, "skillType": "dexterity", "level": 33, "experience": 0, "talentPoints": 0}, {"userId": 165, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 165, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 165, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 166, "skillType": "strength", "level": 11, "experience": 0, "talentPoints": 0}, {"userId": 166, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 166, "skillType": "defence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 166, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 166, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 167, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 167, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 167, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 167, "skillType": "endurance", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 167, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 168, "skillType": "strength", "level": 16, "experience": 0, "talentPoints": 0}, {"userId": 168, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 168, "skillType": "defence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 168, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 168, "skillType": "intelligence", "level": 13, "experience": 0, "talentPoints": 0}, {"userId": 169, "skillType": "strength", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 169, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 169, "skillType": "defence", "level": 45, "experience": 0, "talentPoints": 0}, {"userId": 169, "skillType": "endurance", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 169, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 170, "skillType": "strength", "level": 45, "experience": 0, "talentPoints": 0}, {"userId": 170, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 170, "skillType": "defence", "level": 41, "experience": 0, "talentPoints": 0}, {"userId": 170, "skillType": "endurance", "level": 39, "experience": 0, "talentPoints": 0}, {"userId": 170, "skillType": "intelligence", "level": 46, "experience": 0, "talentPoints": 0}, {"userId": 171, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 171, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 171, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 171, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 171, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 172, "skillType": "strength", "level": 42, "experience": 0, "talentPoints": 0}, {"userId": 172, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 172, "skillType": "defence", "level": 39, "experience": 0, "talentPoints": 0}, {"userId": 172, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 172, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 173, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 173, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 173, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 173, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 173, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 174, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 174, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 174, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 174, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 174, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 175, "skillType": "strength", "level": 29, "experience": 0, "talentPoints": 0}, {"userId": 175, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 175, "skillType": "defence", "level": 24, "experience": 0, "talentPoints": 0}, {"userId": 175, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 175, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 176, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 176, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 176, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 176, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 176, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 177, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 177, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 177, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 177, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 177, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 178, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 178, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 178, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 178, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 178, "skillType": "intelligence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 179, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 179, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 179, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 179, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 179, "skillType": "intelligence", "level": 15, "experience": 0, "talentPoints": 0}, {"userId": 180, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 180, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 180, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 180, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 180, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 181, "skillType": "strength", "level": 45, "experience": 0, "talentPoints": 0}, {"userId": 181, "skillType": "dexterity", "level": 45, "experience": 0, "talentPoints": 0}, {"userId": 181, "skillType": "defence", "level": 45, "experience": 0, "talentPoints": 0}, {"userId": 181, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 181, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 182, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 182, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 182, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 182, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 182, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 183, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 183, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 183, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 183, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 183, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 184, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 184, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 184, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 184, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 184, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 185, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 185, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 185, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 185, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 185, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 186, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 186, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 186, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 186, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 186, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 187, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 187, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 187, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 187, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 187, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 188, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 188, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 188, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 188, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 188, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 189, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 189, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 189, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 189, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 189, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 190, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 190, "skillType": "dexterity", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 190, "skillType": "defence", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 190, "skillType": "endurance", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 190, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 191, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 191, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 191, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 191, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 191, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 192, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 192, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 192, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 192, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 192, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 193, "skillType": "strength", "level": 18, "experience": 0, "talentPoints": 0}, {"userId": 193, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 193, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 193, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 193, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 194, "skillType": "strength", "level": 17, "experience": 0, "talentPoints": 0}, {"userId": 194, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 194, "skillType": "defence", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 194, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 194, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 195, "skillType": "strength", "level": 25, "experience": 0, "talentPoints": 0}, {"userId": 195, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 195, "skillType": "defence", "level": 24, "experience": 0, "talentPoints": 0}, {"userId": 195, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 195, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 196, "skillType": "strength", "level": 34, "experience": 0, "talentPoints": 0}, {"userId": 196, "skillType": "dexterity", "level": 30, "experience": 0, "talentPoints": 0}, {"userId": 196, "skillType": "defence", "level": 31, "experience": 0, "talentPoints": 0}, {"userId": 196, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 196, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 197, "skillType": "strength", "level": 15, "experience": 0, "talentPoints": 0}, {"userId": 197, "skillType": "dexterity", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 197, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 197, "skillType": "endurance", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 197, "skillType": "intelligence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 198, "skillType": "strength", "level": 9, "experience": 0, "talentPoints": 0}, {"userId": 198, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 198, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 198, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 198, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 199, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 199, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 199, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 199, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 199, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 200, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 200, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 200, "skillType": "defence", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 200, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 200, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 201, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 201, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 201, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 201, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 201, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 202, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 202, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 202, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 202, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 202, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 203, "skillType": "strength", "level": 14, "experience": 0, "talentPoints": 0}, {"userId": 203, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 203, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 203, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 203, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 204, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 204, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 204, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 204, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 204, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 205, "skillType": "strength", "level": 6, "experience": 0, "talentPoints": 0}, {"userId": 205, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 205, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 205, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 205, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 206, "skillType": "strength", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 206, "skillType": "dexterity", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 206, "skillType": "defence", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 206, "skillType": "endurance", "level": 1, "experience": 0, "talentPoints": 0}, {"userId": 206, "skillType": "intelligence", "level": 1, "experience": 0, "talentPoints": 0}]