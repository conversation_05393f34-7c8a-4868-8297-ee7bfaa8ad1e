import Button from "@/components/Buttons/Button";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import useLeaveGang from "../api/useLeaveGang";
import GangLogDisplay from "./GangLogDisplay";
import GangPayoutsInfo from "./GangPayoutsInfo";
import GangSettingsModal from "./GangSettingsModal";

const GangInfo = ({ currentGang, currentUserId }) => {
    const [open, setOpen] = useState(false);
    const { leaveGang } = useLeaveGang();
    const { data: gangLogs } = useQuery(
        api.gang.gangLogs.queryOptions({
            input: { gangId: currentGang?.id },
            enabled: !!currentGang?.id && !isNaN(currentGang?.id) && currentGang?.id > 0,
        })
    );

    const confirmLeave = () => {
        window.confirm("Are you sure you want to leave this gang?") && leaveGang();
    };

    return (
        <div className="mx-2 flex flex-col gap-2 overflow-y-auto p-2 md:mx-6">
            <GangSettingsModal open={open} setOpen={setOpen} gang={currentGang} />
            <div className="mx-1 mt-3 flex justify-end gap-2">
                <Button className="" onClick={() => setOpen(true)}>
                    Gang Settings
                </Button>
            </div>

            <div className="grid grid-cols-1 gap-2 md:gap-6 lg:grid-cols-2">
                <GangPayoutsInfo currentGang={currentGang} currentUserId={currentUserId} />

                <div className="flex flex-col gap-2">
                    <p className="mt-2 ml-4 text-left text-gray-200 text-sm uppercase leading-none">Gang Ranks</p>
                    <div className="w-full rounded-lg border-2 border-indigo-600 bg-slate-800 px-4 py-2">
                        {/* <div className="flex gap-2 text-left text-xs">
              <p className="w-24">Rank</p>
              <p>Powers</p>
              <p className="text-right ml-auto">Bonus</p>
            </div> */}
                        <div className="flex flex-col text-sm md:h-56">
                            <RankDisplay number="6" />
                            <hr className="my-0.5 border-gray-600/25" />
                            <RankDisplay number="5" />
                            <hr className="my-0.5 border-gray-600/25" />
                            <RankDisplay number="4" />
                            <hr className="my-0.5 border-gray-600/25" />
                            <RankDisplay number="3" />
                            <hr className="my-0.5 border-gray-600/25" />
                            <RankDisplay number="2" />
                            <hr className="my-0.5 border-gray-600/25" />
                            <RankDisplay number="1" />
                        </div>
                    </div>
                </div>

                <div className="flex flex-1 flex-col gap-2 md:col-span-2">
                    <p className="mt-2 ml-4 text-left text-gray-200 text-sm uppercase leading-none">Gang Logs</p>
                    <div className="size-full rounded-lg border-2 border-indigo-600 bg-slate-800 px-4 py-2">
                        <div className="flex max-h-72 flex-col overflow-y-auto text-sm md:h-56">
                            {gangLogs?.map((log, i) => (
                                <div key={log.id}>
                                    <div>
                                        <GangLogDisplay log={log} />
                                        {i !== gangLogs.length - 1 && <hr className="my-1 border-gray-600/25" />}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
            <div className="mx-1 mt-3 mb-2 flex justify-end gap-2">
                <Button type="danger" className="" onClick={() => confirmLeave()}>
                    Leave Gang
                </Button>
            </div>
        </div>
    );
};

const RankDisplay = ({ number }) => {
    const gangRanks = {
        6: {
            name: "Leader",
            color: "text-red-500",
            powers: "Change Settings, Invite Members, Kick Members",
            payoutBonus: "50%",
        },
        5: {
            name: "Lieutenant",
            color: "text-yellow-400",
            powers: "Change Settings, Invite Members, Kick Members",
            payoutBonus: "40%",
        },
        4: {
            name: "Officer",
            color: "text-green-400",
            powers: "Invite Members, Kick Members",
            payoutBonus: "30%",
        },
        3: { name: "Thug", color: "text-indigo-500", powers: "Invite Members", payoutBonus: "20%" },
        2: { name: "Member", color: "text-blue-400", payoutBonus: "10%" },
        1: { name: "Rookie", color: "text-gray-300", payoutBonus: "0%" },
    };
    const { name, color, powers, payoutBonus } = gangRanks[number];

    return (
        <div className={cn("my-auto mb-0.5 flex h-8 items-center gap-2", color)}>
            <div className="flex w-32 gap-2">
                <p className="my-auto text-xs">{number}</p>
                <p className="truncate">{name}</p>
            </div>

            <div className="w-full text-left">
                <p className="my-auto text-blue-300 text-xs">{powers}</p>
            </div>
            <div className="flex-1 text-right">
                <p className="my-auto text-green-300 text-xs">{payoutBonus}</p>
            </div>
        </div>
    );
};

export default GangInfo;
