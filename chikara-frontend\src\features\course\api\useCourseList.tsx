import { api } from "@/helpers/api";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { useQuery } from "@tanstack/react-query";

const useCourseList = (options: QueryOptions = {}) => {
    return useQuery(
        api.courses.courseList.queryOptions({
            staleTime: 5 * 60 * 1000, // Cache for 5 minutes
            ...options,
        })
    );
};

export default useCourseList;
