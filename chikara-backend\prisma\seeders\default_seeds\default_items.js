const items = [
    {
        name: "Pen",
        itemType: "weapon",
        rarity: "novice",
        level: 1,
        about: "Not just for homework anymore, this mighty pen can write off your enemies in the most literal sense.",
        cashValue: 40,
        image: "static/items/weapons/pen.png",
        damage: 4,
    },
    {
        name: "Ruler",
        itemType: "weapon",
        rarity: "novice",
        level: 3,
        about: "This 12-inch ruler doesn't just measure up - it lays down the law, one whack at a time.",
        cashValue: 90,
        image: "static/items/weapons/ruler.png",
        damage: 8,
    },
    {
        name: "Sciss<PERSON>",
        itemType: "weapon",
        rarity: "novice",
        level: 6,
        about: "Perfect for cutting tension... and enemies, proving that running with scissors isn't always bad advice.",
        cashValue: 160,
        image: "static/items/weapons/scissors.png",
        damage: 14,
    },
    {
        name: "Frying Pan",
        itemType: "weapon",
        rarity: "novice",
        level: 7,
        about: "Serves up justice hot and ready - who knew breakfast equipment could be so terrifying?",
        cashValue: 200,
        image: "static/items/weapons/fryingpan.png",
        damage: 18,
    },
    {
        name: "<PERSON>nuckleduster",
        itemType: "weapon",
        rarity: "standard",
        level: 7,
        about: "The personal touch for when you really want to connect with your enemies, face-to-face and fist-to-face.",
        cashValue: 380,
        image: "static/items/weapons/duster.png",
        damage: 20,
    },
    {
        name: "Kitchen Knife",
        itemType: "weapon",
        rarity: "standard",
        level: 9,
        about: "It's not just for dicing vegetables - it's for slicing through the competition, one culinary catastrophe at a time.",
        cashValue: 400,
        image: "static/items/weapons/kitchenknife.png",
        damage: 22,
    },
    {
        name: "Baseball Bat",
        itemType: "weapon",
        rarity: "standard",
        level: 10,
        about: "It's out of the park—or at least your enemies will be—with this home-run hero.",
        cashValue: 300,
        image: "static/items/weapons/bat.png",
        damage: 22,
    },
    {
        name: "Crowbar",
        itemType: "weapon",
        rarity: "standard",
        level: 12,
        about: "Pry your way to victory and open cans of whoop-ass with unparalleled leverage.",
        cashValue: 420,
        image: "static/items/weapons/crowbar.png",
        damage: 35,
    },
    {
        name: "Golden Knuckleduster",
        itemType: "weapon",
        rarity: "standard",
        level: 13,
        about: 'When you need to throw a punch with a bit of class, nothing says "high-class hit" quite like gold.',
        cashValue: 1000,
        image: "static/items/weapons/goldenduster.png",
        damage: 45,
    },
    {
        name: "Machete",
        itemType: "weapon",
        rarity: "standard",
        level: 14,
        about: "Not your garden-variety blade, unless your garden is full of foes needing a trim.",
        cashValue: 600,
        image: "static/items/weapons/machete.png",
        damage: 42,
    },
    {
        name: "Flail",
        itemType: "weapon",
        rarity: "specialist",
        level: 15,
        about: "Swing around your problems and smack them away—chain reactions guaranteed.",
        cashValue: 800,
        image: "static/items/weapons/flail.png",
        damage: 65,
    },
    {
        name: "Chainsaw",
        itemType: "weapon",
        rarity: "specialist",
        level: 17,
        about: "Nothing says 'I love you' like a chainsaw's serenade, carving a path through hearts and obstacles alike.",
        cashValue: 1200,
        image: "static/items/weapons/chainsaw.png",
        damage: 75,
    },
    {
        name: "Scythe",
        itemType: "weapon",
        rarity: "specialist",
        level: 18,
        about: "Reaping more than just crops—it's harvest time for souls, and business is booming.",
        cashValue: 2100,
        image: "static/items/weapons/scythe.png",
        damage: 115,
    },
    {
        name: "Katana",
        itemType: "weapon",
        rarity: "specialist",
        level: 20,
        about: "So sharp, it can slice through the fabric of reality, or at least that's how you'll feel wielding it.",
        cashValue: 2000,
        image: "static/items/weapons/katana.png",
        damage: 120,
    },
    {
        name: "Golden Flail",
        itemType: "weapon",
        rarity: "specialist",
        level: 21,
        about: "Bling that brings pain, proving all that glitters can indeed be gold... and deadly.",
        cashValue: 5000,
        image: "static/items/weapons/goldenflail.png",
        damage: 160,
    },
    {
        name: "Short Sword",
        itemType: "weapon",
        rarity: "enhanced",
        level: 22,
        about: "Compact, convenient, and ready to carve out victory, proving size isn't everything when it comes to epic battles.",
        cashValue: 3000,
        image: "static/items/weapons/shortsword.png",
        damage: 175,
    },
    {
        name: "Refined Katana",
        itemType: "weapon",
        rarity: "enhanced",
        level: 24,
        about: "Like the original, but with more finesse and flair, for slicing enemies in style.",
        cashValue: 5000,
        image: "static/items/weapons/refinedkatana.png",
        damage: 210,
    },
    {
        name: "Fire Katana",
        itemType: "weapon",
        rarity: "enhanced",
        level: 25,
        about: "Not just for show, this fiery blade toasts enemies faster than a marshmallow at a campfire.",
        cashValue: 9200,
        image: "static/items/weapons/firekatana.png",
        damage: 265,
    },
    {
        name: "Longsword",
        itemType: "weapon",
        rarity: "military",
        level: 25,
        about: "When you need to make a point, but with extra reach, because social distancing matters.",
        cashValue: 6000,
        image: "static/items/weapons/longsword.png",
        damage: 250,
    },
    {
        name: "Golden Scythe",
        itemType: "weapon",
        rarity: "military",
        level: 26,
        about: "Harvests souls with a side of class, making the grim reaper green with envy.",
        cashValue: 15000,
        image: "static/items/weapons/goldenscythe.png",
        damage: 285,
    },
    {
        name: "Masterwork Short Sword",
        itemType: "weapon",
        rarity: "military",
        level: 27,
        about: "A tiny titan of a blade, proof that good things come in small packages—along with lethal cuts.",
        cashValue: 10000,
        image: "static/items/weapons/masterworkshortsword.png",
        damage: 315,
    },
    {
        name: "Sword of Fate",
        itemType: "weapon",
        rarity: "legendary",
        level: 30,
        about: "Decides your destiny with a slice, because why not let a sword choose your path?",
        cashValue: 15000,
        image: "static/items/weapons/legendary_sword.png",
        damage: 400,
        itemEffects: [
            {
                effectKey: "STRENGTH_PERCENT",
                effectValue: 1.15,
            },
        ],
    },
    {
        name: "Melee Placeholder 1",
        itemType: "weapon",
        rarity: "novice",
        level: 99,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "CD-ROM",
        itemType: "ranged",
        rarity: "novice",
        level: 5,
        about: "Not just obsolete tech, this disc is perfect for slicing through yesterday's problems and today's enemies.",
        cashValue: 120,
        image: "static/items/weapons/cdrom.png",
        damage: 25,
        baseAmmo: 2,
    },
    {
        name: "Shuriken",
        itemType: "ranged",
        rarity: "novice",
        level: 8,
        about: "Stars of the show, these aren't for autographs but for making sure the spotlight's all yours.",
        cashValue: 300,
        image: "static/items/weapons/shuriken.png",
        damage: 50,
        baseAmmo: 2,
    },
    {
        name: "Kunai",
        itemType: "ranged",
        rarity: "standard",
        level: 11,
        about: "It's not just for ninja cosplay - it's for when you need to stick it to your enemies, literally.",
        cashValue: 700,
        image: "static/items/weapons/kunai.png",
        damage: 100,
        baseAmmo: 2,
    },
    {
        name: "Throwing Axe",
        itemType: "ranged",
        rarity: "standard",
        level: 13,
        about: "Who said axes were for chopping wood? This one's for chopping down your competition.",
        cashValue: 1200,
        image: "static/items/weapons/axe.png",
        damage: 145,
        baseAmmo: 2,
    },
    {
        name: "Bow",
        itemType: "ranged",
        rarity: "standard",
        level: 15,
        about: "Old school but cool, this bow brings the action to archery, minus the funny hat.",
        cashValue: 1800,
        image: "static/items/weapons/bow.png",
        damage: 185,
        baseAmmo: 3,
    },
    {
        name: "Golden Kunai",
        itemType: "ranged",
        rarity: "specialist",
        level: 17,
        about: "When regular kunai just won't cut it, add a little bling to your fling.",
        cashValue: 5000,
        image: "static/items/weapons/goldenkunai.png",
        damage: 225,
        baseAmmo: 3,
    },
    {
        name: "Ice Bow",
        itemType: "ranged",
        rarity: "specialist",
        level: 18,
        about: "Why settle for plain arrows when you can freeze your foes in their tracks? It's chill time.",
        cashValue: 3600,
        image: "static/items/weapons/icebow.png",
        damage: 265,
        baseAmmo: 3,
    },
    {
        name: "Fire Bow",
        itemType: "ranged",
        rarity: "enhanced",
        level: 20,
        about: "For those who like to add a spark to their shots and a blaze to their battles.",
        cashValue: 5600,
        image: "static/items/weapons/firebow.png",
        damage: 305,
        baseAmmo: 3,
    },
    {
        name: "Golden Shuriken",
        itemType: "ranged",
        rarity: "enhanced",
        level: 22,
        about: "Like throwing money at your problems, but sharper, and more effective.",
        cashValue: 15000,
        image: "static/items/weapons/goldenshuriken.png",
        damage: 345,
        baseAmmo: 3,
    },
    {
        name: "Farmers Double-Barrel",
        itemType: "ranged",
        rarity: "enhanced",
        level: 23,
        about: "This isn't for shooting crows - it's for blasting anything that dares to trespass your field of view.",
        cashValue: 7500,
        image: "static/items/weapons/farmerdb.png",
        damage: 425,
        baseAmmo: 2,
    },
    {
        name: "Pistol",
        itemType: "ranged",
        rarity: "military",
        level: 25,
        about: "Small but mighty, this pistol makes sure you're heard loud and clear, one bullet at a time.",
        cashValue: 12000,
        image: "static/items/weapons/pistol.png",
        damage: 400,
        baseAmmo: 4,
    },
    {
        name: "Shotgun",
        itemType: "ranged",
        rarity: "military",
        level: 27,
        about: "Why target one enemy when you can target everyone in front of you? Talk about efficiency.",
        cashValue: 12500,
        image: "static/items/weapons/shotgun.png",
        damage: 460,
        baseAmmo: 3,
    },
    {
        name: "Golden Pistol",
        itemType: "ranged",
        rarity: "military",
        level: 27,
        about: "This pistol doesn't just shoot, it makes a statement, and that statement is \"I'm fabulously deadly.\"",
        cashValue: 15000,
        image: "static/items/weapons/goldenpistol.png",
        damage: 450,
        baseAmmo: 4,
    },
    {
        name: "The Decimator",
        itemType: "ranged",
        rarity: "legendary",
        level: 30,
        about: "Not just a weapon, but a promise of destruction, one pull of the trigger at a time.",
        cashValue: 15000,
        image: "static/items/weapons/legendary_gun.png",
        damage: 550,
        baseAmmo: 4,
        itemEffects: [
            {
                effectKey: "DEXTERITY_PERCENT",
                effectValue: 1.15,
            },
        ],
    },
    {
        name: "Ranged Placeholder 1",
        itemType: "ranged",
        rarity: "novice",
        level: 99,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Face mask",
        itemType: "head",
        rarity: "novice",
        level: 1,
        about: "For the warrior who likes to stay mysterious, this face mask not only hides your poker face but also filters out the bad vibes (and germs).",
        cashValue: 40,
        image: "static/items/armor/mask.png",
        armour: 2,
    },
    {
        name: "T-Shirt",
        itemType: "chest",
        rarity: "novice",
        level: 4,
        about: "This isn't your average band tee. It's the armor you wear when you want to say, \"I'm ready for battle, but also, it's laundry day.\"",
        cashValue: 70,
        image: "static/items/armor/shirt.png",
        armour: 6,
    },
    {
        name: "Small Handwraps",
        itemType: "hands",
        rarity: "novice",
        level: 2,
        about: "They might look like leftovers from a boxer's gym bag, but these handwraps pack a punch, magically turning every fist bump into a knockout.",
        cashValue: 50,
        image: "static/items/armor/small_handwraps.png",
        armour: 2,
    },
    {
        name: "Worn Jeans",
        itemType: "legs",
        rarity: "novice",
        level: 3,
        about: "These aren't just fashion statements - they're battle-tested, adventure-worn jeans that say \"I've seen things\" and have the patches to prove it.",
        cashValue: 60,
        image: "static/items/armor/wornjeans.png",
        armour: 4,
    },
    {
        name: "Wellies",
        itemType: "feet",
        rarity: "novice",
        level: 2,
        about: " Perfect for stomping through puddles or the competition, these wellies make sure you stay dry and stylish while you kick butt.",
        cashValue: 50,
        image: "static/items/armor/wellies.png",
        armour: 2,
    },
    {
        name: "Sunglasses",
        itemType: "head",
        rarity: "standard",
        level: 4,
        about: "Not just for blocking out the haters, these sunglasses come with built-in shade for your enemies and UV protection for your eyes—safety first!",
        cashValue: 70,
        image: "static/items/armor/sunglasses.png",
        armour: 6,
    },
    {
        name: "Biker Jacket",
        itemType: "chest",
        rarity: "standard",
        level: 7,
        about: 'This jacket doesn\'t just scream "rebel with a cause" - it whispers threats to your foes with its intimidating, armor-like studs and patches.',
        cashValue: 140,
        image: "static/items/armor/bikerjacket.png",
        armour: 14,
    },
    {
        name: "Large Handwraps",
        itemType: "hands",
        rarity: "standard",
        level: 5,
        about: "For those with a bigger handshake or punch, these handwraps ensure your high-fives are heard across the battlefield.",
        cashValue: 90,
        image: "static/items/armor/large_handwraps.png",
        armour: 8,
    },
    {
        name: "Biker Trousers",
        itemType: "legs",
        rarity: "standard",
        level: 6,
        about: "With extra padding where it counts, these trousers are for riding into battle or away from responsibility—equally effective.",
        cashValue: 110,
        image: "static/items/armor/biker_trousers.png",
        armour: 10,
    },
    {
        name: "Work Shoes",
        itemType: "feet",
        rarity: "standard",
        level: 5,
        about: "Tough enough to kick doors down but stylish enough for casual Fridays, these work shoes mean business in the front and party in the back.",
        cashValue: 90,
        image: "static/items/armor/workshoes.png",
        armour: 8,
    },
    {
        name: "Sports Cap",
        itemType: "head",
        rarity: "standard",
        level: 7,
        about: "This isn't just any cap - it's your lucky charm, deflecting both sunlight and the competition's hopes of victory with its brim.",
        cashValue: 140,
        image: "static/items/armor/sports_cap.png",
        armour: 14,
    },
    {
        name: "Sports Jersey",
        itemType: "chest",
        rarity: "standard",
        level: 10,
        about: "More than a team uniform, this jersey is a second skin that breathes victory and sweats determination.",
        cashValue: 260,
        image: "static/items/armor/sports_jersey.png",
        armour: 20,
    },
    {
        name: "Sports Gloves",
        itemType: "hands",
        rarity: "standard",
        level: 8,
        about: "Grip, grab, or high-five your way to triumph - these gloves don't just catch balls, they catch wins.",
        cashValue: 170,
        image: "static/items/armor/sports_gloves.png",
        armour: 16,
    },
    {
        name: "Sports Shorts",
        itemType: "legs",
        rarity: "standard",
        level: 9,
        about: "Built for speed and comfort, these shorts make sure your legs are free to sprint into legend—or away from trouble.",
        cashValue: 210,
        image: "static/items/armor/sports_shorts.png",
        armour: 18,
    },
    {
        name: "Sports Shoes",
        itemType: "feet",
        rarity: "standard",
        level: 8,
        about: "These aren't mere sneakers - they're your personal teleportation devices, ready to zoom you past challenges and onto the podium.",
        cashValue: 170,
        image: "static/items/armor/sports_shoes.png",
        armour: 16,
    },
    {
        name: "Fancy Top Hat",
        itemType: "head",
        rarity: "enhanced",
        level: 10,
        about: "Not just a statement piece, this top hat magically elevates your charisma, ensuring you're the life of any battle or ballroom.",
        cashValue: 260,
        image: "static/items/armor/fancy_top_hat.png",
        armour: 20,
    },
    {
        name: "Fancy Suit",
        itemType: "chest",
        rarity: "enhanced",
        level: 13,
        about: "This suit doesn't just fit like a dream - it's bulletproof charm woven into fabric, making enemies hesitate and allies swoon.",
        cashValue: 490,
        image: "static/items/armor/fancy_suit.png",
        armour: 30,
    },
    {
        name: "Fancy Gloves",
        itemType: "hands",
        rarity: "enhanced",
        level: 11,
        about: "These aren't your average gloves - they're a sleek combination of style and stealth, perfect for a discreet sleight of hand or a gentle jab.",
        cashValue: 320,
        image: "static/items/armor/fancy_gloves.png",
        armour: 24,
    },
    {
        name: "Fancy Trousers",
        itemType: "legs",
        rarity: "enhanced",
        level: 12,
        about: "With these trousers, stride into any skirmish or soiree with confidence - they're fashion-forward armor for the legs.",
        cashValue: 400,
        image: "static/items/armor/fancy_trousers.png",
        armour: 26,
    },
    {
        name: "Fancy Shoes",
        itemType: "feet",
        rarity: "enhanced",
        level: 11,
        about: "Glide across the battlefield or dance floor with these shoes, enchantingly designed to never miss a step or dodge.",
        cashValue: 320,
        image: "static/items/armor/fancy_shoes.png",
        armour: 24,
    },
    {
        name: "Samurai Helm",
        itemType: "head",
        rarity: "enhanced",
        level: 12,
        about: "Crafted from the finest steel, this helm isn't just for show - it intimidates foes with its mere silhouette, echoing the spirit of ancient warriors.",
        cashValue: 400,
        image: "static/items/armor/samuraihelm.png",
        armour: 26,
    },
    {
        name: "Samurai Chestpiece",
        itemType: "chest",
        rarity: "enhanced",
        level: 15,
        about: "This chestpiece, adorned with the emblem of the dragon, doesn't just protect your heart - it instills fear into those who dare cross its path.",
        cashValue: 740,
        image: "static/items/armor/samuraichestpiece.png",
        armour: 36,
    },
    {
        name: "Samurai Gauntlets",
        itemType: "hands",
        rarity: "enhanced",
        level: 13,
        about: "With each fist encased in these gauntlets, your hands become the harbingers of justice, swift and unyielding.",
        cashValue: 490,
        image: "static/items/armor/samuraigauntlets.png",
        armour: 30,
    },
    {
        name: "Samurai Trousers",
        itemType: "legs",
        rarity: "enhanced",
        level: 14,
        about: "These trousers offer more than agility - they carry the legacy of the samurai, blending stealth and honor in every stride.",
        cashValue: 600,
        image: "static/items/armor/samurai_trousers.png",
        armour: 32,
    },
    {
        name: "Samurai Boots",
        itemType: "feet",
        rarity: "enhanced",
        level: 13,
        about: "Crafted for the silent warrior, these boots whisper tales of the wind, making each step a testament to the silent strength of the samurai.",
        cashValue: 490,
        image: "static/items/armor/samuraiboots.png",
        armour: 30,
    },
    {
        name: "Golden Samurai Helm",
        itemType: "head",
        rarity: "specialist",
        level: 14,
        about: "This helm, gilded in pure gold, isn't just a protector - it's a crown, signifying the pinnacle of samurai prowess and leadership.",
        cashValue: 600,
        image: "static/items/armor/goldensamuraihelm.png",
        armour: 32,
    },
    {
        name: "Golden Samurai Chestpiece",
        itemType: "chest",
        rarity: "specialist",
        level: 17,
        about: "Encased in gold, this chestpiece shines not just with opulence but as a beacon of invincibility, rallying allies and daunting foes alike.",
        cashValue: 900,
        image: "static/items/armor/goldensamuraichestpiece.png",
        armour: 42,
    },
    {
        name: "Golden Samurai Gauntlets",
        itemType: "hands",
        rarity: "specialist",
        level: 15,
        about: "Infused with the power of gold, these gauntlets don't just defend - they dazzle, turning every block into a statement of power.",
        cashValue: 700,
        image: "static/items/armor/goldensamuraigauntlets.png",
        armour: 36,
    },
    {
        name: "Golden Samurai Trousers",
        itemType: "legs",
        rarity: "specialist",
        level: 16,
        about: "These trousers, woven with threads of gold, combine elegance with efficiency, ensuring every movement is both graceful and deadly.",
        cashValue: 800,
        image: "static/items/armor/golden_samurai_trousers.png",
        armour: 38,
    },
    {
        name: "Golden Samurai Boots",
        itemType: "feet",
        rarity: "specialist",
        level: 15,
        about: "These boots, kissed by gold, tread not just on the earth but on the fears of enemies, marking territories with the brilliance of the sun.",
        cashValue: 700,
        image: "static/items/armor/goldensamuraiboots.png",
        armour: 36,
    },
    {
        name: "Sleek Shadow Mask",
        itemType: "head",
        rarity: "enhanced",
        level: 15,
        about: "This mask wraps the wearer's face in mystery, casting their identity into the shadows while enhancing their enigmatic presence.",
        cashValue: 740,
        image: "static/items/armor/sleek_shadow_mask.png",
        armour: 36,
    },
    {
        name: "Sleek Shadow Suit",
        itemType: "chest",
        rarity: "enhanced",
        level: 18,
        about: "Tailored for stealth and agility, this suit blends into the night, making the wearer a whisper in the darkness, unseen but always felt.",
        cashValue: 1390,
        image: "static/items/armor/sleek_shadow_suit.png",
        armour: 44,
    },
    {
        name: "Sleek Shadow Gloves",
        itemType: "hands",
        rarity: "enhanced",
        level: 16,
        about: "These gloves offer precision and grip, allowing the wearer to manipulate their environment with the finesse of a shadow moving against the wall.",
        cashValue: 920,
        image: "static/items/armor/sleek_shadow_gloves.png",
        armour: 38,
    },
    {
        name: "Sleek Shadow Trousers",
        itemType: "legs",
        rarity: "enhanced",
        level: 17,
        about: "Designed for silent movement, these trousers are the wearer's ally in the dark, ensuring each step is as quiet as a shadow's caress.",
        cashValue: 1130,
        image: "static/items/armor/sleek_shadow_trousers.png",
        armour: 42,
    },
    {
        name: "Sleek Shadow Shoes",
        itemType: "feet",
        rarity: "enhanced",
        level: 16,
        about: "Silent and swift, these shoes are for those who walk the line between seen and unseen, leaving no trace but the memory of their passage.",
        cashValue: 920,
        image: "static/items/armor/sleek_shadow_shoes.png",
        armour: 38,
    },
    {
        name: "Yokai Faceplate",
        itemType: "head",
        rarity: "specialist",
        level: 18,
        about: "Engraved with ancient spirits, this faceplate offers more than protection - it whispers the secrets of yokai, bewildering foes with unseen fears.",
        cashValue: 1400,
        image: "static/items/armor/yokai_faceplate.png",
        armour: 44,
    },
    {
        name: "Yokai Chestpiece",
        itemType: "chest",
        rarity: "specialist",
        level: 22,
        about: "This chestpiece, adorned with ethereal patterns, harnesses the elusive essence of yokai, blending the wearer into the tapestry of myths.",
        cashValue: 3200,
        image: "static/items/armor/yokai_chestpiece.png",
        armour: 58,
    },
    {
        name: "Yokai Gauntlets",
        itemType: "hands",
        rarity: "specialist",
        level: 19,
        about: "Infused with the mystique of spirits, these gauntlets empower each strike with the unpredictable force of yokai, making every blow a dance with destiny.",
        cashValue: 1700,
        image: "static/items/armor/yokai_gauntlets.png",
        armour: 48,
    },
    {
        name: "Yokai Kilt",
        itemType: "legs",
        rarity: "specialist",
        level: 21,
        about: "Crafted from the threads of forgotten tales, this kilt swirls with the power of yokai, granting agility and protection wrapped in legend.",
        cashValue: 2600,
        image: "static/items/armor/yokai_kilt.png",
        armour: 56,
    },
    {
        name: "Yokai Sandals",
        itemType: "feet",
        rarity: "specialist",
        level: 19,
        about: "These sandals, silent as the ghostly steps of yokai, enable movements swift and soft as whispers on the wind, leaving no trace but the chill of passing spirits.",
        cashValue: 1700,
        image: "static/items/armor/yokai_sandals.png",
        armour: 48,
    },
    {
        name: "Shadow Helm",
        itemType: "head",
        rarity: "specialist",
        level: 22,
        about: "Emerging from the darkness, this helm cloaks the wearer's intentions, shrouding their visage in the mysteries of the night.",
        cashValue: 3200,
        image: "static/items/armor/shadow_helm.png",
        armour: 58,
    },
    {
        name: "Shadow Chest",
        itemType: "chest",
        rarity: "specialist",
        level: 25,
        about: "Woven from the essence of shadow, this chestpiece offers protection not by deflecting blows, but by absorbing them into the void.",
        cashValue: 5400,
        image: "static/items/armor/shadow_chest.png",
        armour: 70,
    },
    {
        name: "Shadow Gloves",
        itemType: "hands",
        rarity: "specialist",
        level: 23,
        about: "These gloves are not merely for combat - they're tools for shaping darkness, turning each gesture into a manipulation of the shadow itself.",
        cashValue: 3800,
        image: "static/items/armor/shadow_gloves.png",
        armour: 62,
    },
    {
        name: "Shadow Trousers",
        itemType: "legs",
        rarity: "specialist",
        level: 24,
        about: "Clad in these trousers, one strides through shadows, merging with the darkness to become unseen, untouchable, and unforgettable.",
        cashValue: 4500,
        image: "static/items/armor/shadow_trousers.png",
        armour: 66,
    },
    {
        name: "Shadow Boots",
        itemType: "feet",
        rarity: "specialist",
        level: 23,
        about: "Silent as the deepest night, these boots carry the wearer through shadows, where every step is a dance between the seen and unseen realms.",
        cashValue: 3800,
        image: "static/items/armor/shadow_boots.png",
        armour: 62,
    },
    {
        name: "Crimson Helm",
        itemType: "head",
        rarity: "military",
        level: 24,
        about: "This helm, bathed in the color of warriors, doesn't just protect your head—it ignites the battlefield with the fury of its hue.",
        cashValue: 4900,
        image: "static/items/armor/crimson_helm.png",
        armour: 66,
    },
    {
        name: "Crimson Chest",
        itemType: "chest",
        rarity: "military",
        level: 27,
        about: "Clad in the vibrant shades of valor, this chest armor pulses with the heartbeats of ancient heroes, daring anyone to challenge its wearer.",
        cashValue: 9100,
        image: "static/items/armor/crimson_chest.png",
        armour: 78,
    },
    {
        name: "Crimson Gloves",
        itemType: "hands",
        rarity: "military",
        level: 25,
        about: "More than mere handwear, these gloves weave the strength of the bear and the precision of the eagle into every punch and parry.",
        cashValue: 6000,
        image: "static/items/armor/crimson_gloves.png",
        armour: 70,
    },
    {
        name: "Crimson Trousers",
        itemType: "legs",
        rarity: "military",
        level: 26,
        about: "These trousers do more than cover legs - they carry the swift silence of the panther, ensuring every step lands with purpose and power.",
        cashValue: 7400,
        image: "static/items/armor/crimson_trousers.png",
        armour: 74,
    },
    {
        name: "Crimson Boots",
        itemType: "feet",
        rarity: "military",
        level: 25,
        about: "Sturdy and bold, these boots don't just tread paths—they create legends, leaving a trail of awe with each footprint.",
        cashValue: 6000,
        image: "static/items/armor/crimson_boots.png",
        armour: 70,
    },
    {
        name: "Dominator Helm",
        itemType: "head",
        rarity: "military",
        level: 26,
        about: "Forging dominance with every glance, this helm encases one's gaze in authority, commanding respect and fear in equal measure.",
        cashValue: 7750,
        image: "static/items/armor/dominator_helm.png",
        armour: 74,
    },
    {
        name: "Dominator Chest",
        itemType: "chest",
        rarity: "military",
        level: 29,
        about: "This chestpiece isn't just armor - it's a fortress, embodying the unyielding strength and resilience of those destined to rule.",
        cashValue: 14000,
        image: "static/items/armor/dominator_chest.png",
        armour: 86,
    },
    {
        name: "Dominator Gloves",
        itemType: "hands",
        rarity: "military",
        level: 27,
        about: "These aren't gloves - they're declarations of power, with each grasp and release shaping the fate of battles and bending wills.",
        cashValue: 9500,
        image: "static/items/armor/dominator_gloves.png",
        armour: 78,
    },
    {
        name: "Dominator Legs",
        itemType: "legs",
        rarity: "military",
        level: 28,
        about: "Encased in this leg armor, each stride becomes a march towards victory, crushing doubts and enemies beneath its weight.",
        cashValue: 11000,
        image: "static/items/armor/dominator_legs.png",
        armour: 82,
    },
    {
        name: "Dominator Boots",
        itemType: "feet",
        rarity: "military",
        level: 27,
        about: "Crafted for conquerors, these boots stamp authority with every step, declaring the ground they touch as their own.",
        cashValue: 9500,
        image: "static/items/armor/dominator_boots.png",
        armour: 78,
    },
    {
        name: "Helm of Fate",
        itemType: "head",
        rarity: "legendary",
        level: 30,
        about: "This helm doesn't just protect your thoughts - it weaves the destiny of its wearer, guiding them through the maze of fate with each decision.",
        cashValue: 17000,
        image: "static/items/armor/helm_of_fate.png",
        armour: 100,
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.05,
            },
        ],
    },
    {
        name: "Chest of Fate",
        itemType: "chest",
        rarity: "legendary",
        level: 30,
        about: "Clad in this chest armor, every heartbeat echoes the rhythms of destiny, drawing the wearer closer to their inevitable triumph or downfall.",
        cashValue: 20000,
        image: "static/items/armor/chest_of_fate.png",
        armour: 150,
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.05,
            },
        ],
    },
    {
        name: "Gauntlets of Fate",
        itemType: "hands",
        rarity: "legendary",
        level: 30,
        about: "Each finger movement within these gauntlets can tip the scales of destiny, turning the slightest touch into a monumental action.",
        cashValue: 17000,
        image: "static/items/armor/gauntlets_of_fate.png",
        armour: 100,
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.05,
            },
        ],
    },
    {
        name: "Trousers of Fate",
        itemType: "legs",
        rarity: "legendary",
        level: 30,
        about: "Wearing these trousers, one's steps are inscribed with the script of destiny, where each stride writes a line in the saga of time.",
        cashValue: 17000,
        image: "static/items/armor/trousers_of_fate.png",
        armour: 100,
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.05,
            },
        ],
    },
    {
        name: "Shoes of Fate",
        itemType: "feet",
        rarity: "legendary",
        level: 30,
        about: "More than mere footwear, these shoes tread lightly over the sands of time, leaving footprints destined to be followed by future generations.",
        cashValue: 17000,
        image: "static/items/armor/shoes_of_fate.png",
        armour: 100,
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.05,
            },
        ],
    },
    {
        name: "Wedding Ring",
        itemType: "finger",
        rarity: "standard",
        level: 6,
        about: "This isn't just a band of gold - it's the ultimate \"game over\" for freedom, but with the best co-op gameplay experience.",
        cashValue: 110,
        image: "static/items/armor/weddingring.png",
        itemEffects: [
            {
                effectKey: "HEALTH_PERCENT",
                effectValue: 100,
            },
        ],
    },
    {
        name: "Crystal Ring",
        itemType: "finger",
        rarity: "enhanced",
        level: 12,
        about: "This ring is so blue, it makes the sky look like it could use a little more color.",
        cashValue: 400,
        image: "static/items/armor/crystalring.png",
        itemEffects: [
            {
                effectKey: "STRENGTH_PERCENT",
                effectValue: 1.05,
            },
        ],
    },
    {
        name: "Gemstone Ring",
        itemType: "finger",
        rarity: "enhanced",
        level: 14,
        about: "Not just a pretty face, this ring is the ultimate rockstar of accessories, making every outfit a hit.",
        cashValue: 600,
        image: "static/items/armor/gemstonering.png",
        itemEffects: [
            {
                effectKey: "DEXTERITY_PERCENT",
                effectValue: 1.06,
            },
        ],
    },
    {
        name: "Amethyst Ring",
        itemType: "finger",
        rarity: "enhanced",
        level: 17,
        about: "With this purple powerhouse on your finger, you're not just wearing a ring - you're hosting a royal gem gala.",
        cashValue: 1130,
        image: "static/items/armor/amethystring.png",
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.08,
            },
        ],
    },
    {
        name: "Ring of Power",
        itemType: "finger",
        rarity: "specialist",
        level: 20,
        about: "It doesn't come with a manual, but wear this ring, and you'll feel like you can run the world (or at least a small city).",
        cashValue: 2600,
        image: "static/items/armor/ring_of_power.png",
        itemEffects: [
            {
                effectKey: "STRENGTH_PERCENT",
                effectValue: 1.125,
            },
        ],
    },
    {
        name: "Azure Gleam Ring",
        itemType: "finger",
        rarity: "military",
        level: 23,
        about: "Brighter than your future on a good day, this ring turns heads and tides with its piercing light blue hue.",
        cashValue: 7000,
        image: "static/items/armor/azure_gleam_ring.png",
        itemEffects: [
            {
                effectKey: "DEXTERITY_PERCENT",
                effectValue: 1.1,
            },
        ],
    },
    {
        name: "Scholar's Ring",
        itemType: "finger",
        rarity: "military",
        level: 26,
        about: "Wear this, and you'll feel smarter - it's like having cheat codes for life, but in stylish accessory form.",
        cashValue: 11000,
        image: "static/items/armor/scholars_ring.png",
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.14,
            },
        ],
    },
    {
        name: "Ring of Fate",
        itemType: "finger",
        rarity: "legendary",
        level: 30,
        about: "This isn't just a ring - it's a destiny decision maker, perfect for those \"left or right?\" moments.",
        cashValue: 13500,
        image: "static/items/armor/ring_of_fate.png",
        itemEffects: [
            {
                effectKey: "HEALTH_PERCENT",
                effectValue: 350,
            },
            {
                effectKey: "STRENGTH_PERCENT",
                effectValue: 1.15,
            },
            {
                effectKey: "DEXTERITY_PERCENT",
                effectValue: 1.15,
            },
        ],
    },
    {
        name: "Ocean Ring",
        itemType: "finger",
        rarity: "enhanced",
        level: 10,
        about: "Harness the power of the deep to send your enemies to Davy Jones' locker with a devastating splash!",
        cashValue: 400,
        image: "static/items/armor/oceanring.png",
        itemEffects: [
            {
                effectKey: "NPCDAMAGE_PERCENT",
                effectValue: 1.15,
            },
        ],
    },
    {
        name: "Ring of Wealth",
        itemType: "finger",
        rarity: "specialist",
        level: 23,
        about: "Why slay dragons for gold when you can simply wear this and watch your fortune grow effortlessly?",
        cashValue: 7000,
        image: "static/items/armor/wealthring.png",
        itemEffects: [
            {
                effectKey: "ENCOUNTERREWARD_PERCENT",
                effectValue: 1.25,
            },
        ],
    },
    {
        name: "Armour Placeholder 3",
        itemType: "head",
        rarity: "novice",
        level: 99,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Armour Placeholder 4",
        itemType: "head",
        rarity: "novice",
        level: 99,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Armour Placeholder 5",
        itemType: "head",
        rarity: "novice",
        level: 99,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Armour Placeholder 6",
        itemType: "head",
        rarity: "novice",
        level: 99,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Pocket Knife (Offhand)",
        itemType: "offhand",
        rarity: "standard",
        level: 10,
        about: "This isn't just for whittling wood - it's the perfect sneaky sidekick for when you need a little extra sharpness up your sleeve.",
        cashValue: 250,
        image: "static/items/weapons/pocket_knife_offhand.png",
        damage: 10,
    },
    {
        name: "Iron Cleaver (Offhand)",
        itemType: "offhand",
        rarity: "enhanced",
        level: 15,
        about: "More than just a butcher's tool, this hefty hunk of iron is your plan B for when conversations cut too close.",
        cashValue: 650,
        image: "static/items/weapons/iron_cleaver_offhand.png",
        damage: 25,
    },
    {
        name: "Steel Hatchet (Offhand)",
        itemType: "offhand",
        rarity: "specialist",
        level: 20,
        about: "Not your typical camping gear, this hatchet is for close encounters of the 'chop first, ask questions later' kind.",
        cashValue: 1500,
        image: "static/items/weapons/steel_hatchet_offhand.png",
        damage: 45,
    },
    {
        name: "ArmPower Implant (Offhand)",
        itemType: "offhand",
        rarity: "military",
        level: 25,
        about: "Who needs a gym membership when you have this bionic boost, turning your arm into a powerhouse with every swing?",
        cashValue: 5500,
        image: "static/items/weapons/armpower_implant_offhand.png",
        damage: 100,
    },
    {
        name: "ArmBlade Implant (Offhand)",
        itemType: "offhand",
        rarity: "military",
        level: 30,
        about: "Forget about sleeves, this blade is your ace in the arm, ready to slice through trouble at the flick of a wrist.",
        cashValue: 10000,
        image: "static/items/weapons/armblade_implant_offhand.png",
        damage: 150,
    },
    {
        name: "Parasol",
        itemType: "shield",
        rarity: "standard",
        level: 10,
        about: "Not just for sunny days or looking mysterious - this elegant canopy can parry more than just raindrops—it's perfect for deflecting incoming critiques and small projectiles.",
        cashValue: 220,
        image: "static/items/armor/parasol.png",
        armour: 16,
    },
    {
        name: "Rusted Shield",
        itemType: "shield",
        rarity: "enhanced",
        level: 15,
        about: "It may look like it's seen better days, but this shield carries the weight of history, making each block a lesson in resilience.",
        cashValue: 600,
        image: "static/items/armor/rusted_shield.png",
        armour: 30,
    },
    {
        name: "Steel Shield",
        itemType: "shield",
        rarity: "specialist",
        level: 20,
        about: "This isn't just a wall of metal - it's your personal bulwark against the slings and arrows of outrageous fortune (or actual arrows).",
        cashValue: 1600,
        image: "static/items/armor/steel_shield.png",
        armour: 44,
    },
    {
        name: "Deployable Shield Cover",
        itemType: "shield",
        rarity: "military",
        level: 25,
        about: "Like a pop-up tent for combat, this shield springs into action when you need it, turning any location into a fortress in seconds.",
        cashValue: 4800,
        image: "static/items/armor/deployable_shield_cover.png",
        armour: 60,
    },
    {
        name: "Shield Glove Implant",
        itemType: "shield",
        rarity: "military",
        level: 30,
        about: "Why carry a shield when you can wear one? This implant turns your forearm into an instant shield, ready at the flick of your wrist.",
        cashValue: 13500,
        image: "static/items/armor/shield_glove_implant.png",
        armour: 80,
    },
    {
        name: "Cabbage",
        itemType: "consumable",
        rarity: "novice",
        level: 1,
        about: "Not just for rabbits, this leafy green is nature's way of giving you armor against hunger.",
        cashValue: 30,
        image: "static/items/consumables/cabbage.png",
        health: 50,
    },
    {
        name: "Tuna",
        itemType: "consumable",
        rarity: "novice",
        level: 1,
        about: "The chicken of the sea, except it swims better and tastes fishier—perfect for flexing those omega-3 muscles.",
        cashValue: 60,
        image: "static/items/consumables/tuna.png",
        health: 100,
    },
    {
        name: "Bread",
        itemType: "consumable",
        rarity: "novice",
        level: 1,
        about: "The cornerstone of any meal, because even heroes need a solid foundation (plus, it's great for sandwiches).",
        cashValue: 140,
        image: "static/items/consumables/bread.png",
        health: 80,
    },
    {
        name: "Chicken",
        itemType: "consumable",
        rarity: "standard",
        level: 1,
        about: "Whether grilled, fried, or roasted, it's the ultimate comfort food for warriors and peacekeepers alike.",
        cashValue: 140,
        image: "static/items/consumables/chicken.png",
        health: 250,
    },
    {
        name: "Chocolate Bar",
        itemType: "consumable",
        rarity: "standard",
        level: 1,
        about: "This isn't just a treat - it's a mood-lifting, heart-winning, battle-ready bar of bliss.",
        cashValue: 200,
        image: "static/items/consumables/chocolate_bar.png",
        health: 350,
    },
    {
        name: "Bowl of Beans",
        itemType: "consumable",
        rarity: "standard",
        level: 1,
        about: "This humble dish is more than just fiber-packed - it's a magic bean pot of energy, proving that slow and steady can win the race and fuel the fight.",
        cashValue: 300,
        image: "static/items/consumables/bowl_of_beans.png",
        health: 500,
    },
    {
        name: "Sushi",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "Bite-sized pieces of raw bravery, wrapped in seaweed and daring you to dip them into the soy sauce of adventure.",
        cashValue: 350,
        image: "static/items/consumables/sushi.png",
        health: 650,
    },
    {
        name: "Burger",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "A towering stack of strength, a burger doesn't just fill you up - it powers you through the next quest.",
        cashValue: 420,
        image: "static/items/consumables/burger.png",
        health: 850,
    },
    {
        name: "Gourmet Dish",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "Not just a meal but a statement, this dish is so luxurious it makes your taste buds feel like they've won the lottery every bite.",
        cashValue: 840,
        image: "static/items/consumables/gourmet_steak.png",
        health: 1000,
    },
    {
        name: "Cola",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "The fizz that awakens the soul, perfect for toasting victories or washing down a tough defeat.",
        cashValue: 250,
        image: "static/items/consumables/cola.png",
        energy: 50,
    },
    {
        name: "Watermelon",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "Nature's juicy bomb, exploding with hydration and sweetness, best enjoyed in the heat of summer or battle.",
        cashValue: 230,
        image: "static/items/consumables/watermelon.png",
        health: 425,
    },
    {
        name: "Donut",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "The sugary ring of power, because every hero deserves a reward, and sometimes, that reward is glazed.",
        cashValue: 230,
        image: "static/items/consumables/donut.png",
        health: 600,
    },
    {
        name: "Red Wine",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "A potion of sophistication and strength, for toasting to victories and forgetting defeats.",
        cashValue: 1,
        image: "static/items/consumables/redwine.png",
    },
    {
        name: "Plaster",
        itemType: "consumable",
        rarity: "standard",
        level: 1,
        about: "The band-aid of champions, because even heroes get boo-boos.",
        cashValue: 400,
        image: "static/items/consumables/plaster.png",
        itemEffects: [
            {
                effect: "bleeding",
                source: "consumable_item",
                effectTier: "Minor",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Advanced Med-Pack",
        itemType: "consumable",
        rarity: "standard",
        level: 1,
        about: "The ultimate recovery box, turning near-death experiences into mere anecdotes.",
        cashValue: 2500,
        image: "static/items/consumables/medkit.png",
        itemEffects: [
            {
                effect: "bleeding",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "trauma",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Antibiotics",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "The tiny soldiers in the war against bacteria, proving that sometimes, victory comes in pill form.",
        cashValue: 660,
        image: "static/items/consumables/antibiotics.png",
        health: 800,
        itemEffects: [
            {
                effect: "fatigue",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Health Potion",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "One gulp, and you're back in the fight, because who has time for downtime?",
        cashValue: 420,
        image: "static/items/consumables/healthpotion.png",
    },
    {
        name: "Stimulant",
        itemType: "consumable",
        rarity: "enhanced",
        level: 1,
        about: "Get a quick fix with HyperBoost Syringe—when you need to sprint through your to-do list at superhuman speed!",
        cashValue: 250,
        image: "static/items/consumables/stimulant.png",
        actionPoints: 5,
    },
    {
        name: "Lisdexamfetamine",
        itemType: "consumable",
        rarity: "specialist",
        level: 1,
        about: "Unleash the power of productivity: sacrificing health for a surge of action points!",
        cashValue: 2500,
        image: "static/items/consumables/antibiotics.png",
        health: -200,
        actionPoints: 7,
    },
    {
        name: "Lighter",
        itemType: "junk",
        rarity: "novice",
        level: 1,
        about: "Not just for starting fires, it's the ultimate tool for lighting up your day and anything else that needs a spark.",
        cashValue: 100,
        image: "static/items/junk/lighter.png",
    },
    {
        name: "4GB Ram",
        itemType: "junk",
        rarity: "novice",
        level: 1,
        about: "Once a memory powerhouse, now a quaint reminder of simpler times when loading screens were a chance for meditation.",
        cashValue: 250,
        image: "static/items/junk/ram.png",
    },
    {
        name: "Old Earrings",
        itemType: "junk",
        rarity: "novice",
        level: 1,
        about: "A testament to fashion battles past, these ancient adornments prove that style is eternal, even if the sparkle has dulled.",
        cashValue: 500,
        image: "static/items/junk/earrings.png",
    },
    {
        name: "Outdated Smartphone",
        itemType: "junk",
        rarity: "novice",
        level: 1,
        about: "It may not run the latest apps, but it's a nostalgic piece of tech wizardry, capable of summoning ghosts of selfies past.",
        cashValue: 1000,
        image: "static/items/junk/smartphone.png",
    },
    {
        name: "VR Headset",
        itemType: "junk",
        rarity: "standard",
        level: 1,
        about: "A gateway to other worlds, slightly used, offering escapades into virtual realms where the graphics were almost as good as reality.",
        cashValue: 2000,
        image: "static/items/junk/vrheadset.png",
    },
    {
        name: "Bag of Money",
        itemType: "junk",
        rarity: "standard",
        level: 1,
        about: "It's not just a bag of money - it's a bag of dreams, adventures, and the occasional shopping spree, slightly counterfeit.",
        cashValue: 1000,
        image: "static/items/junk/bagofmoney.png",
    },
    {
        name: "Suitcase of Money",
        itemType: "junk",
        rarity: "standard",
        level: 1,
        about: "This isn't just a suitcase - it's a portable dragon's hoard, minus the dragon, offering endless possibilities (and probably paper cuts).",
        cashValue: 2500,
        image: "static/items/junk/suitcaseofmoney.png",
    },
    {
        name: "Physical Bitcoin",
        itemType: "junk",
        rarity: "standard",
        level: 1,
        about: "A rare artifact from the time when people thought money could be both digital and physical, a curious relic of speculative dreams.",
        cashValue: 10000,
        image: "static/items/junk/physicalbitcoin.png",
    },
    {
        name: "Death Book",
        itemType: "special",
        rarity: "specialist",
        level: 1,
        about: "One time use. Lets you hospitalise a selected player with a custom hospitalisation reason.",
        cashValue: 6000,
        image: "static/items/special/deathbook.png",
    },
    {
        name: "Life Book",
        itemType: "special",
        rarity: "specialist",
        level: 1,
        about: "One time use. Lets you heal a selected player to full health and remove all injuries.",
        cashValue: 5000,
        image: "static/items/special/lifebook.png",
    },
    {
        name: "Megaphone",
        itemType: "special",
        rarity: "enhanced",
        level: 1,
        about: "",
        cashValue: 1000,
        image: "static/items/special/megaphone.png",
    },
    {
        name: "Balaclava",
        itemType: "head",
        rarity: "enhanced",
        level: 1,
        about: "Keeps you anonymous when hospitalising players while the balaclava is equipped",
        cashValue: 6000,
        image: "static/items/special/balaclava.png",
    },
    {
        name: "Kompromat",
        itemType: "special",
        rarity: "enhanced",
        level: 1,
        about: "One time use. Lets you jail a selected player with a custom jail reason.",
        cashValue: 8000,
        image: "static/items/special/kompromat.png",
    },
    {
        name: "Armed Nuclear Warhead",
        itemType: "special",
        rarity: "legendary",
        level: 30,
        about: "Use this item to wipe out every player and end the Alpha test",
        cashValue: 10000000,
        image: "static/items/quest/nuclear_warhead.png",
    },
    {
        name: "Training Materials",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "Not your average textbooks - these come with a side of sweat, tears, and the occasional epiphany.",
        cashValue: 0,
        image: "static/items/quest/training_materials.png",
    },
    {
        name: "Clue",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "It's like a scavenger hunt, but instead of looking for treasure, you're piecing together the tech heist of the century.",
        cashValue: 0,
        image: "static/items/quest/clue.png",
    },
    {
        name: "Stolen Tech",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "The hot potato of technology, too cool to handle and too valuable to drop.",
        cashValue: 0,
        image: "static/items/quest/stolen_tech.png",
    },
    {
        name: "Graduation Cap",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "The ultimate badge of honor and headgear, marking the end of trials and the start of real battles.",
        cashValue: 0,
        image: "static/items/quest/graduation_cap.png",
    },
    {
        name: "Iron Fist Document",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "Packed with secrets so heavy they could punch through steel.",
        cashValue: 0,
        image: "static/items/quest/iron_fist_document.png",
    },
    {
        name: "Tactical Gadget",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "This isn't just a tool - it's your clever little sidekick in a box, ready to give you the upper hand with a beep, a click, or a zap",
        cashValue: 0,
        image: "static/items/quest/tactical_gadget.png",
    },
    {
        name: "Small Tech Component",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "The building block of wonder, because behind every great invention is a piece that looks suspiciously like it fell off something else.",
        cashValue: 0,
        image: "static/items/quest/tech_component.png",
    },
    {
        name: "Broken Gem Shard",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "As elusive as a compliment from a cat, this material is the stuff that dreams, and really shiny things, are made of.",
        cashValue: 0,
        image: "static/items/quest/broken_gem_shard.png",
    },
    {
        name: "'Special' Syrup",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "Not your ordinary pancake topping, this goo is a slippery step into monster biology.",
        cashValue: 0,
        image: "static/items/quest/special_syrup.png",
    },
    {
        name: "Blueprint",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "This isn't just a set of instructions - it's a roadmap to unrivaled power, detailing the creation of a weapon destined to become the stuff of legends.",
        cashValue: 0,
        image: "static/items/quest/blueprint.png",
    },
    {
        name: "Starforged Iron",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "Forged in the heart of a dying star, because your sword shouldn't just be sharp, it should be stellar.",
        cashValue: 0,
        image: "static/items/quest/starforged_iron.png",
    },
    {
        name: "Crystallized Moonlight",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "Not just for nightlights, this shimmering rock is proof that even the moon has a crush on you.",
        cashValue: 0,
        image: "static/items/quest/crystallized_moonlight.png",
    },
    {
        name: "Whispering Wind",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "They don't just mess up your hair - they carry secrets, spells, and occasionally, a really odd joke.",
        cashValue: 0,
        image: "static/items/quest/whispering_winds.png",
    },
    {
        name: "Legendary Weapon Mould",
        itemType: "quest",
        rarity: "legendary",
        level: 1,
        about: "The first step in forging a weapon that will carve its name into the annals of history, because every hero needs a good origin story.",
        cashValue: 0,
        image: "static/items/quest/legendary_mould.png",
    },
    {
        name: "Placeholder Quest Item",
        itemType: "quest",
        rarity: "legendary",
        level: 1,
        about: "This is where bullets begin their life as legends, ensuring every shot echoes through the ages.",
        cashValue: 0,
        image: "static/items/quest/legendary_mould.png",
    },
    {
        name: "Nuclear Warhead",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: 'The ultimate in "overkill" technology, perfect for when you absolutely, positively need to obliterate more than just the conversation.',
        cashValue: 0,
        image: "static/items/quest/nuclear_warhead.png",
    },
    {
        name: "Fuel Can",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "Not just for keeping the lights on, this can of liquid energy is your ticket to powering through any crisis, mechanical or otherwise.",
        cashValue: 0,
        image: "static/items/quest/fuel_can.png",
    },
    {
        name: "Legendary Ring Mould",
        itemType: "quest",
        rarity: "legendary",
        level: 1,
        about: "The blueprint for crafting rings that do more than look pretty—they change destinies.",
        cashValue: 0,
        image: "static/items/quest/legendary_mould.png",
    },
    {
        name: "Ring Of Fate (Incomplete)",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "A ring not yet whole, but even in its unfinished state, it's brimming with potential and prophetic power.",
        cashValue: 0,
        image: "static/items/armor/ring_of_fate.png",
    },
    {
        name: "Surveillance Equipment",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "This gear doesn't just watch, it whispers, collecting secrets like a gossip at a gala.",
        cashValue: 0,
        image: "static/items/quest/surveillance_equipment.png",
    },
    {
        name: "Festival Main Course",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "The highlight of the feast, this dish isn't just dinner, it's a culinary adventure on a plate, ready to impress and satiate.",
        cashValue: 0,
        image: "static/items/quest/festival_main_course.png",
    },
    {
        name: "Festival Dessert",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "More than just a sweet treat, this festival favorite is the cherry on top of a day well spent, packed with joy and jubilation.",
        cashValue: 0,
        image: "static/items/quest/festival_dessert.png",
    },
    {
        name: "Infused Fate Core",
        itemType: "quest",
        rarity: "legendary",
        level: 1,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Quest Placeholder 2",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Quest Placeholder 3",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Quest Placeholder 4",
        itemType: "quest",
        rarity: "standard",
        level: 1,
        about: "",
        cashValue: 0,
        image: "",
    },
    {
        name: "Flour",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "Not just for baking bread, this powdery staple can thicken any plot or recipe, proving its mettle beyond the kitchen.",
        cashValue: 20,
        image: "static/items/crafting/flour.png",
    },
    {
        name: "Healing Powder",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "A sprinkle of this can mend more than spirits - it's like emergency glue for battle wounds.",
        cashValue: 750,
        image: "static/items/crafting/healing_powder.png",
    },
    {
        name: "Acid",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Handle with care and maybe gloves, because this isn't your average lemon juice—it's the kind of stuff that dissolves more than just your problems.",
        cashValue: 200,
        image: "static/items/crafting/acid.png",
    },
    {
        name: "Shopping Basket",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "More than just a carrier for your groceries, this basket can haul anything from supplies to hopes and dreams across the battlefield.",
        cashValue: 90,
        image: "static/items/crafting/shopping_basket.png",
    },
    {
        name: "Tinned Beans",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "These aren't just good for your heart - they're the sturdy, shelf-stable sidekicks you never knew you needed in a pinch.",
        cashValue: 400,
        image: "static/items/crafting/tinned_beans.png",
    },
    {
        name: "Eggs",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "Potential chicken warriors aside, these shells harbor not only yolks but the building blocks of powerhouse potions.",
        cashValue: 50,
        image: "static/items/crafting/eggs.png",
    },
    {
        name: "Advanced Component",
        itemType: "crafting",
        rarity: "military",
        level: 1,
        about: "This high-tech gizmo is the secret sauce in your crafting recipe, turning ordinary objects into extraordinary gadgets.",
        cashValue: 10000,
        image: "static/items/crafting/advanced_component.png",
    },
    {
        name: "Tissues",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "Perfect for drying tears or wiping away evidence, these soft sheets are a must-have in any adventurer's pocket.",
        cashValue: 70,
        image: "static/items/crafting/tissues.png",
    },
    {
        name: "Cloth",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "More versatile than a Swiss army knife, this fabric can bind, blind, or become whatever you need in the moment.",
        cashValue: 750,
        image: "static/items/crafting/cloth.png",
    },
    {
        name: "Oreite Ore",
        itemType: "crafting",
        rarity: "military",
        level: 1,
        about: "Raw and rugged, this ore is the tough stuff of the earth, packed with potential and ready for refining.",
        cashValue: 1200,
        image: "static/items/crafting/oreite_ore.png",
    },
    {
        name: "Oreite Ingot",
        itemType: "crafting",
        rarity: "military",
        level: 1,
        about: "Smelted down and solidified, this ingot is a compact powerhouse of crafting prowess, ready to be refined into something legendary.",
        cashValue: 5000,
        image: "static/items/crafting/oreite_ingot.png",
    },
    {
        name: "Low Quality Blade",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: 'It may not win any beauty contests, but this blade has "I\'m trying my best" written all over it.',
        cashValue: 400,
        image: "static/items/crafting/low_quality_blade.png",
    },
    {
        name: "Component",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "A jack-of-all-trades in the crafting world, ready to be the missing puzzle piece in your next big build.",
        cashValue: 400,
        image: "static/items/crafting/component.png",
    },
    {
        name: "Exotic Spices",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Not just for flavor, these spices kick up any dish—or potion—to legendary levels.",
        cashValue: 700,
        image: "static/items/crafting/exotic_spices.png",
    },
    {
        name: "Basic Electrical Component",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "The bread and butter of gadgetry, without which your brilliant inventions might just be brilliant ideas.",
        cashValue: 550,
        image: "static/items/crafting/basic_electrical_component.png",
    },
    {
        name: "Scrap Metal",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "It's not trash, it's treasure waiting for a second chance to shine, or at least to be useful.",
        cashValue: 40,
        image: "static/items/crafting/scrap_metal.png",
    },
    {
        name: "Iron Ingot",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "Strong and reliable, this ingot is the backbone of any solid construction, from swords to shields.",
        cashValue: 300,
        image: "static/items/crafting/iron_ingot.png",
    },
    {
        name: "Iron Rod",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "Straightforward and sturdy, this rod is the go-to when you need something to hold everything else together.",
        cashValue: 700,
        image: "static/items/crafting/iron_rod.png",
    },
    {
        name: "Stack of Rods",
        itemType: "crafting",
        rarity: "enhanced",
        level: 1,
        about: "When one rod isn't enough to reinforce your dreams, a whole stack steps up to the challenge.",
        cashValue: 2200,
        image: "static/items/crafting/stack_of_rods.png",
    },
    {
        name: "Alloy",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "A blend of metals more versatile than a multi-tool, ready to lend its strength to whatever you're cooking up.",
        cashValue: 350,
        image: "static/items/crafting/alloy.png",
    },
    {
        name: "Steel Ingot",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "The tough guy of the metal world, ready to be forged into tools and tales of resilience.",
        cashValue: 1100,
        image: "static/items/crafting/steel_ingot.png",
    },
    {
        name: "High Quality Blade",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "Sharp enough to split hairs and durable enough to outlast any battle, this blade is a cut above the rest.",
        cashValue: 4750,
        image: "static/items/crafting/high_quality_blade.png",
    },
    {
        name: "Gold Ore",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Glittering with potential, this ore isn't just gold - it's the sparkly start of your next opulent project.",
        cashValue: 750,
        image: "static/items/crafting/gold_ore.png",
    },
    {
        name: "Gold Ingot",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Compressed luxury in a bar, ready to transform the mundane into the magnificent with a touch of Midas.",
        cashValue: 2250,
        image: "static/items/crafting/gold_ingot.png",
    },
    {
        name: "Ice Core",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Frozen at the heart, this chilly relic holds the cold hard secrets of winter's might.",
        cashValue: 2500,
        image: "static/items/crafting/ice_core.png",
    },
    {
        name: "AI Controller",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "The brain behind the brawn, this gadget is ready to bring your robotic dreams to life with a flash of intelligence.",
        cashValue: 4500,
        image: "static/items/crafting/ai_controller.png",
    },
    {
        name: "Refined Oreite",
        itemType: "crafting",
        rarity: "military",
        level: 1,
        about: "Sleek and streamlined, this refined ore is all dressed up and ready for high-tech party tricks.",
        cashValue: 16000,
        image: "static/items/crafting/refined_oreite.png",
    },
    {
        name: "CPU",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "The heart of any modern machine, this CPU is ready to calculate your path from chaos to control.",
        cashValue: 3000,
        image: "static/items/crafting/cpu.png",
    },
    {
        name: "Rare Herb",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Not your garden variety green, this herb packs a punch of mystical proportions, spicing up spells and stews alike.",
        cashValue: 300,
        image: "static/items/crafting/rare_herb.png",
    },
    {
        name: "Rough Gemstone",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Uncut but undeniably valuable, this gemstone is just waiting for a chance to shine.",
        cashValue: 250,
        image: "static/items/crafting/rough_gemstone.png",
    },
    {
        name: "Polished Gem",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "This gem has been buffed to brilliant perfection, ready to dazzle foes and friends with its lustrous charm.",
        cashValue: 600,
        image: "static/items/crafting/polished_gem.png",
    },
    {
        name: "Dark Core",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "A shadowy sphere of mysterious energy, pulsating with the dark side of power.",
        cashValue: 5000,
        image: "static/items/crafting/dark_core.png",
    },
    {
        name: "SuperGlue",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Stick to your guns, or anything else, with this miraculous adhesive that keeps your world together.",
        cashValue: 40,
        image: "static/items/crafting/superglue.png",
    },
    {
        name: "Pliers",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "The ultimate grip-and-grab tool, ready to twist, cut, and conquer any crafting challenge.",
        cashValue: 8,
        image: "static/items/crafting/pliers.png",
    },
    {
        name: "Wood Plank",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "Sturdy and steadfast, this basic building block is the foundation of fortresses and the backbone of barricades.",
        cashValue: 16,
        image: "static/items/crafting/wood_plank.png",
    },
    {
        name: "Hammer",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "Not just for nails, this tool's persuasive power can shape metal, break barriers, and occasionally, thumbs.",
        cashValue: 20,
        image: "static/items/crafting/hammer.png",
    },
    {
        name: "Drill",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "For making holes where there weren't any, or for screwing things up in the most literal sense.",
        cashValue: 100,
        image: "static/items/crafting/drill.png",
    },
    {
        name: "Hook",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "Not just for pirates anymore, this hook is perfect for snagging the unreachable and dragging it into reality.",
        cashValue: 24,
        image: "static/items/crafting/hook.png",
    },
    {
        name: "Sharpening Tools",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "These aren't just for keeping your blades keen - they're for honing your edge in life and battle.",
        cashValue: 420,
        image: "static/items/crafting/sharpening_tools.png",
    },
    {
        name: "Mould",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "The first step in turning raw potential into a solid reality, because every masterpiece starts as a moldy idea.",
        cashValue: 220,
        image: "static/items/crafting/mould.png",
    },
    {
        name: "Hacksaw",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "For when you need to cut your problems down to size, or just need to trim a few pesky bolts.",
        cashValue: 240,
        image: "static/items/crafting/hacksaw.png",
    },
    {
        name: "Spring",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Bouncy and capable of more than just mattress support, this spring brings the zing back into your mechanical things.",
        cashValue: 400,
        image: "static/items/crafting/spring.png",
    },
    {
        name: "Toolbox",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "A treasure chest for the mechanically inclined, packed with solutions for just about every bolt, screw, and nail life throws your way.",
        cashValue: 500,
        image: "static/items/crafting/toolbox.png",
    },
    {
        name: "Burner",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Turn up the heat on your creations or your enemies with this fiery tool, perfect for forging or just warming up your lunch.",
        cashValue: 50,
        image: "static/items/crafting/burner.png",
    },
    {
        name: "Scales",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Perfect for measuring the weight of your ingredients or the balance of your destiny, these scales tip the odds in your favor.",
        cashValue: 80,
        image: "static/items/crafting/scales.png",
    },
    {
        name: "Bowl",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "Not just for soup, this culinary cradle holds everything from potions to plans, keeping your components contained and your secrets safe.",
        cashValue: 20,
        image: "static/items/crafting/bowl.png",
    },
    {
        name: "Duct Tape",
        itemType: "crafting",
        rarity: "novice",
        level: 1,
        about: "The universal fixer-upper, capable of mending anything from shattered shields to broken dreams, with a sticky strength that's almost magical.",
        cashValue: 20,
        image: "static/items/crafting/duct_tape.png",
    },
    {
        name: "Solder",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "The little line that binds, transforming a jumble of parts into a circuit of success, ensuring your gadgets have more than just a fighting chance.",
        cashValue: 200,
        image: "static/items/crafting/solder.png",
    },
    {
        name: "Sugar",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "Sweeten your concoctions or your conversations, because a spoonful of sugar helps the diplomacy go down.",
        cashValue: 400,
        image: "static/items/crafting/sugar.png",
    },
    {
        name: "Gishnib Bishlab",
        itemType: "crafting",
        rarity: "military",
        level: 1,
        about: "Unravel the mystery of Gishnib Bishlab: an enigmatic item that seems to do nothing but leaves you wondering what it could possibly be for",
        cashValue: 0,
        image: "static/items/junk/gishnib.png",
    },
    {
        name: "Daily Chest",
        itemType: "special",
        rarity: "enhanced",
        level: 1,
        about: "",
        cashValue: 0,
        image: "static/items/special/dailychest.png",
    },
    {
        name: "Emoji Request",
        itemType: "special",
        rarity: "legendary",
        level: 1,
        about: "If you get this, message @Lancellion on Discord to request a chat emoji.",
        cashValue: 0,
        image: "static/items/special/emojirequest.gif",
    },
    {
        name: "Gang Sigil",
        itemType: "special",
        rarity: "specialist",
        level: 1,
        about: "Used to form your own Gang!",
        cashValue: 60000,
        image: "static/items/special/currency.png",
    },
    {
        name: "Geelfrob",
        itemType: "junk",
        rarity: "legendary",
        level: 1,
        about: "Unravel the mystery of the Geelfrob: an enigmatic item that seems to do nothing but leaves you wondering what it could possibly be for",
        cashValue: 0,
        image: "static/items/junk/gishnib.png",
    },
    {
        name: "Small Weapon Upgrade Core",
        itemType: "upgrade",
        rarity: "standard",
        level: 1,
        about: "Infuse your weapon with this core and witness it transform from a mere toothpick into a legendary dragon slayer!",
        cashValue: 500,
        image: "static/items/crafting/weaponcore.png",
    },
    {
        name: "Medium Weapon Upgrade Core",
        itemType: "upgrade",
        rarity: "enhanced",
        level: 1,
        about: "Infuse your weapon with this core and witness it transform from a mere toothpick into a legendary dragon slayer!",
        cashValue: 1000,
        image: "static/items/crafting/weaponcore.png",
    },
    {
        name: "Large Weapon Upgrade Core",
        itemType: "upgrade",
        rarity: "specialist",
        level: 1,
        about: "Infuse your weapon with this core and witness it transform from a mere toothpick into a legendary dragon slayer!",
        cashValue: 2500,
        image: "static/items/crafting/weaponcore.png",
    },
    {
        name: "Giant Weapon Upgrade Core",
        itemType: "upgrade",
        rarity: "military",
        level: 1,
        about: "Infuse your weapon with this core and witness it transform from a mere toothpick into a legendary dragon slayer!",
        cashValue: 5000,
        image: "static/items/crafting/weaponcore.png",
    },
    {
        name: "Small Armor Upgrade Core",
        itemType: "upgrade",
        rarity: "standard",
        level: 1,
        about: "Fortify your defenses with this core, turning your humble tin can armor into an impenetrable fortress of protection!",
        cashValue: 500,
        image: "static/items/crafting/armorcore.png",
    },
    {
        name: "Medium Armor Upgrade Core",
        itemType: "upgrade",
        rarity: "enhanced",
        level: 1,
        about: "Fortify your defenses with this core, turning your humble tin can armor into an impenetrable fortress of protection!",
        cashValue: 1000,
        image: "static/items/crafting/armorcore.png",
    },
    {
        name: "Large Armor Upgrade Core",
        itemType: "upgrade",
        rarity: "specialist",
        level: 1,
        about: "Fortify your defenses with this core, turning your humble tin can armor into an impenetrable fortress of protection!",
        cashValue: 2500,
        image: "static/items/crafting/armorcore.png",
    },
    {
        name: "Giant Armor Upgrade Core",
        itemType: "upgrade",
        rarity: "military",
        level: 1,
        about: "Fortify your defenses with this core, turning your humble tin can armor into an impenetrable fortress of protection!",
        cashValue: 5000,
        image: "static/items/crafting/armorcore.png",
    },
    {
        name: "Shoes of Quickness",
        itemType: "feet",
        rarity: "enhanced",
        level: 15,
        about: "Slip into these shoes and experience the thrill of outpacing your enemies, escaping danger with unparalleled speed!",
        cashValue: 3000,
        image: "static/items/armor/quickshoes.png",
        itemEffects: [
            {
                effectKey: "FLEECHANCE_PERCENT",
                effectValue: 1.25,
            },
        ],
    },
    {
        name: "AP Storage Booster",
        itemType: "special",
        rarity: "military",
        level: 25,
        about: "Boost your action points to heroic levels, ensuring you have the energy to conquer any challenge that comes your way!",
        cashValue: 5000,
        image: "static/items/special/apbooster.png",
    },
    {
        name: "AP Storage Booster Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 25,
        about: "Boost your action points to heroic levels, ensuring you have the energy to conquer any challenge that comes your way!",
        cashValue: 0,
        image: "static/items/special/apbooster.png",
    },
    {
        name: "Large Armor Upgrade Core Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 20,
        about: "Fortify your defenses with this core, turning your humble tin can armor into an impenetrable fortress of protection!",
        cashValue: 0,
        image: "static/items/crafting/armorcore.png",
        recipeUnlockId: 78,
    },
    {
        name: "Large Weapon Upgrade Core Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 20,
        about: "Infuse your weapon with this core and witness it transform from a mere toothpick into a legendary dragon slayer!",
        cashValue: 0,
        image: "static/items/crafting/weaponcore.png",
        recipeUnlockId: 79,
    },
    {
        name: "Physical Bitcoin Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 24,
        about: "A rare artifact from the time when people thought money could be both digital and physical, a curious relic of speculative dreams.",
        cashValue: 2500,
        image: "static/items/junk/physicalbitcoin.png",
        recipeUnlockId: 14,
    },
    {
        name: "Chest of Fate Recipe",
        itemType: "recipe",
        rarity: "legendary",
        level: 30,
        about: "Clad in this chest armor, every heartbeat echoes the rhythms of destiny, drawing the wearer closer to their inevitable triumph or downfall.",
        cashValue: 5000,
        image: "static/items/armor/chest_of_fate.png",
        recipeUnlockId: 77,
    },
    {
        name: "Life Book Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 15,
        about: "One time use. Lets you revive a selected player from the hospital with full health.",
        cashValue: 1250,
        image: "static/items/special/lifebook.png",
        recipeUnlockId: 16,
    },
    {
        name: "Death Book Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 15,
        about: "One time use. Lets you hospitalise a selected player with a custom hospitalisation reason.",
        cashValue: 1500,
        image: "static/items/special/deathbook.png",
        recipeUnlockId: 15,
    },
    {
        name: "Refined Katana Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 22,
        about: "Like the original, but with more finesse and flair, for slicing enemies in style.",
        cashValue: 1250,
        image: "static/items/weapons/refinedkatana.png",
        damage: 210,
        recipeUnlockId: 22,
    },
    {
        name: "Masterwork Short Sword Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 25,
        about: "A tiny titan of a blade, proof that good things come in small packages—along with lethal cuts.",
        cashValue: 2500,
        image: "static/items/weapons/masterworkshortsword.png",
        damage: 315,
        recipeUnlockId: 23,
    },
    {
        name: "Ice Bow Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 16,
        about: "Why settle for plain arrows when you can freeze your foes in their tracks? It's chill time.",
        cashValue: 900,
        image: "static/items/weapons/icebow.png",
        damage: 265,
        baseAmmo: 3,
        recipeUnlockId: 26,
    },
    {
        name: "Farmers Double-Barrel Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 21,
        about: "This isn't for shooting crows - it's for blasting anything that dares to trespass your field of view.",
        cashValue: 1875,
        image: "static/items/weapons/farmerdb.png",
        damage: 425,
        baseAmmo: 2,
        recipeUnlockId: 27,
    },
    {
        name: "Samurai Helm Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 10,
        about: "Crafted from the finest steel, this helm isn't just for show - it intimidates foes with its mere silhouette, echoing the spirit of ancient warriors.",
        cashValue: 100,
        image: "static/items/armor/samuraihelm.png",
        armour: 26,
        recipeUnlockId: 40,
    },
    {
        name: "Samurai Chestpiece Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 13,
        about: "This chestpiece, adorned with the emblem of the dragon, doesn't just protect your heart - it instills fear into those who dare cross its path.",
        cashValue: 185,
        image: "static/items/armor/samuraichestpiece.png",
        armour: 36,
        recipeUnlockId: 41,
    },
    {
        name: "Samurai Gauntlets Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 11,
        about: "With each fist encased in these gauntlets, your hands become the harbingers of justice, swift and unyielding.",
        cashValue: 120,
        image: "static/items/armor/samuraigauntlets.png",
        armour: 30,
        recipeUnlockId: 42,
    },
    {
        name: "Samurai Trousers Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 12,
        about: "These trousers offer more than agility - they carry the legacy of the samurai, blending stealth and honor in every stride.",
        cashValue: 150,
        image: "static/items/armor/samurai_trousers.png",
        armour: 32,
        recipeUnlockId: 43,
    },
    {
        name: "Samurai Boots Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 11,
        about: "Crafted for the silent warrior, these boots whisper tales of the wind, making each step a testament to the silent strength of the samurai.",
        cashValue: 120,
        image: "static/items/armor/samuraiboots.png",
        armour: 30,
        recipeUnlockId: 44,
    },
    {
        name: "Sleek Shadow Mask Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 13,
        about: "This mask wraps the wearer's face in mystery, casting their identity into the shadows while enhancing their enigmatic presence.",
        cashValue: 185,
        image: "static/items/armor/sleek_shadow_mask.png",
        armour: 36,
        recipeUnlockId: 45,
    },
    {
        name: "Sleek Shadow Suit Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 16,
        about: "Tailored for stealth and agility, this suit blends into the night, making the wearer a whisper in the darkness, unseen but always felt.",
        cashValue: 350,
        image: "static/items/armor/sleek_shadow_suit.png",
        armour: 44,
        recipeUnlockId: 46,
    },
    {
        name: "Sleek Shadow Gloves Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 14,
        about: "These gloves offer precision and grip, allowing the wearer to manipulate their environment with the finesse of a shadow moving against the wall.",
        cashValue: 230,
        image: "static/items/armor/sleek_shadow_gloves.png",
        armour: 38,
        recipeUnlockId: 47,
    },
    {
        name: "Sleek Shadow Trousers Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 15,
        about: "Designed for silent movement, these trousers are the wearer's ally in the dark, ensuring each step is as quiet as a shadow's caress.",
        cashValue: 280,
        image: "static/items/armor/sleek_shadow_trousers.png",
        armour: 42,
        recipeUnlockId: 48,
    },
    {
        name: "Sleek Shadow Shoes Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 14,
        about: "Silent and swift, these shoes are for those who walk the line between seen and unseen, leaving no trace but the memory of their passage.",
        cashValue: 230,
        image: "static/items/armor/sleek_shadow_shoes.png",
        armour: 38,
        recipeUnlockId: 49,
    },
    {
        name: "Dominator Helm Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 24,
        about: "Forging dominance with every glance, this helm encases one's gaze in authority, commanding respect and fear in equal measure.",
        cashValue: 2000,
        image: "static/items/armor/dominator_helm.png",
        armour: 74,
        recipeUnlockId: 55,
    },
    {
        name: "Dominator Chest Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 27,
        about: "This chestpiece isn't just armor - it's a fortress, embodying the unyielding strength and resilience of those destined to rule.",
        cashValue: 3500,
        image: "static/items/armor/dominator_chest.png",
        armour: 86,
        recipeUnlockId: 56,
    },
    {
        name: "Dominator Gloves Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 25,
        about: "These aren't gloves - they're declarations of power, with each grasp and release shaping the fate of battles and bending wills.",
        cashValue: 2375,
        image: "static/items/armor/dominator_gloves.png",
        armour: 78,
        recipeUnlockId: 57,
    },
    {
        name: "Dominator Legs Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 26,
        about: "Encased in this leg armor, each stride becomes a march towards victory, crushing doubts and enemies beneath its weight.",
        cashValue: 2750,
        image: "static/items/armor/dominator_legs.png",
        armour: 82,
        recipeUnlockId: 58,
    },
    {
        name: "Dominator Boots Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 25,
        about: "Crafted for conquerors, these boots stamp authority with every step, declaring the ground they touch as their own.",
        cashValue: 2375,
        image: "static/items/armor/dominator_boots.png",
        armour: 78,
        recipeUnlockId: 59,
    },
    {
        name: "Shield Glove Implant Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 28,
        about: "Why carry a shield when you can wear one? This implant turns your forearm into an instant shield, ready at the flick of your wrist.",
        cashValue: 3375,
        image: "static/items/armor/shield_glove_implant.png",
        armour: 80,
        recipeUnlockId: 63,
    },
    {
        name: "Shotgun Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 25,
        about: "Why target one enemy when you can target everyone in front of you? Talk about efficiency.",
        cashValue: 3125,
        image: "static/items/weapons/shotgun.png",
        damage: 460,
        baseAmmo: 3,
        recipeUnlockId: 28,
    },
    {
        name: "Gemstone Ring Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 12,
        about: "Not just a pretty face, this ring is the ultimate rockstar of accessories, making every outfit a hit.",
        cashValue: 150,
        image: "static/items/armor/gemstonering.png",
        recipeUnlockId: 60,
        itemEffects: [
            {
                effectKey: "DEXTERITY_PERCENT",
                effectValue: 1.06,
            },
        ],
    },
    {
        name: "Amethyst Ring Recipe",
        itemType: "recipe",
        rarity: "enhanced",
        level: 15,
        about: "With this purple powerhouse on your finger, you're not just wearing a ring - you're hosting a royal gem gala.",
        cashValue: 280,
        image: "static/items/armor/amethystring.png",
        recipeUnlockId: 61,
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.08,
            },
        ],
    },
    {
        name: "ArmBlade Implant (Offhand) Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 28,
        about: "Forget about sleeves, this blade is your ace in the arm, ready to slice through trouble at the flick of a wrist.",
        cashValue: 2500,
        image: "static/items/weapons/armblade_implant_offhand.png",
        damage: 150,
        recipeUnlockId: 30,
    },
    {
        name: "Large Battery",
        itemType: "crafting",
        rarity: "military",
        level: 30,
        about: "A hefty powerhouse for your high-octane escapades, ensuring your gear runs as tirelessly as you do.",
        cashValue: 16000,
        image: "static/items/crafting/largebattery.png",
    },
    {
        name: "Small Battery",
        itemType: "crafting",
        rarity: "enhanced",
        level: 20,
        about: "The unsung hero of gadgets, packing a punch in a pint-sized package for your everyday tech needs.",
        cashValue: 2000,
        image: "static/items/crafting/smallbattery.png",
    },
    {
        name: "Small Raw Materials Crate",
        itemType: "special",
        rarity: "standard",
        level: 1,
        about: "Contains 10 Raw Materials",
        cashValue: 100,
        image: "static/items/crafting/toolbox.png",
    },
    {
        name: "Small Tools Crate",
        itemType: "special",
        rarity: "standard",
        level: 1,
        about: "Contains 10 Tools",
        cashValue: 100,
        image: "static/items/crafting/toolbox.png",
    },
    {
        name: "Soulreaver",
        itemType: "weapon",
        rarity: "legendary",
        level: 35,
        cashValue: 1,
        image: "static/items/weapons/soulreaver.png",
        damage: 550,
        itemEffects: [
            {
                effectKey: "STRENGTH_PERCENT",
                effectValue: 1.17,
            },
            {
                effectKey: "LIFESTEAL_PERCENT",
                effectValue: 1.07,
            },
        ],
    },
    {
        name: "Pulsefire",
        itemType: "ranged",
        rarity: "legendary",
        level: 35,
        cashValue: 1,
        image: "static/items/weapons/pulsefire.png",
        damage: 700,
        baseAmmo: 4,
        itemEffects: [
            {
                effectKey: "DEXTERITY_PERCENT",
                effectValue: 1.2,
            },
        ],
    },
    {
        name: "Optical Targeting Goggles",
        itemType: "head",
        rarity: "legendary",
        level: 35,
        cashValue: 1,
        image: "static/items/armor/opticalgoggles.png",
        armour: 25,
        itemEffects: [
            {
                effectKey: "STAMINA_PERCENT",
                effectValue: 1.05,
            },
            {
                effectKey: "DEXTERITY_PERCENT",
                effectValue: 1.1,
            },
        ],
    },
    {
        name: "Titan's Fury Chest",
        itemType: "chest",
        rarity: "legendary",
        level: 35,
        cashValue: 1,
        image: "static/items/armor/titansfurychest.png",
        armour: 150,
        itemEffects: [
            {
                effectKey: "STRENGTH_PERCENT",
                effectValue: 1.1,
            },
        ],
    },
    {
        name: "Chest of the Iron Bastion",
        itemType: "chest",
        rarity: "legendary",
        level: 35,
        cashValue: 1,
        image: "static/items/armor/ironbastionchest.png",
        armour: 250,
        itemEffects: [
            {
                effectKey: "DEFENCE_PERCENT",
                effectValue: 1.07,
            },
        ],
    },
    {
        name: "Sharpshooter Chest",
        itemType: "chest",
        rarity: "legendary",
        level: 35,
        cashValue: 1,
        image: "static/items/armor/sharpshooterchest.png",
        armour: 150,
        itemEffects: [
            {
                effectKey: "DEXTERITY_PERCENT",
                effectValue: 1.1,
            },
        ],
    },
    {
        name: "Inactive Core",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        cashValue: 750,
        image: "static/items/crafting/inactivecore.png",
    },
    {
        name: "Regenerative Serum",
        itemType: "consumable",
        rarity: "specialist",
        level: 1,
        about: "The elixir for bones and bruises – heal like a hero.",
        cashValue: 3000,
        image: "static/items/consumables/regenserum.png",
        itemEffects: [
            {
                effect: "fracture",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "contusion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Emergency Response Kit",
        itemType: "consumable",
        rarity: "military",
        level: 1,
        about: "Your ultimate first-aid BFF",
        cashValue: 10000,
        image: "static/items/consumables/emergencykit.png",
        itemEffects: [
            {
                effect: "bleeding",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "fracture",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "concussion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "contusion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "trauma",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "fatigue",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Cognitive Recovery Capsule",
        itemType: "consumable",
        rarity: "standard",
        level: 1,
        about: "Brain boost and energy in one - a capsule of pure comeback.",
        cashValue: 2500,
        image: "static/items/consumables/capsule.png",
        itemEffects: [
            {
                effect: "concussion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Broken Bowl",
        itemType: "junk",
        rarity: "novice",
        level: 1,
        about: "It's broken.",
        cashValue: 100,
        image: "static/items/junk/brokenbowl.png",
    },
    {
        name: "Broken Plate",
        itemType: "junk",
        rarity: "novice",
        level: 1,
        about: "It's broken.",
        cashValue: 0,
        image: "static/items/junk/brokenplate.png",
    },
    {
        name: "Empty Can",
        itemType: "junk",
        rarity: "novice",
        level: 1,
        about: "It's empty.",
        cashValue: 0,
        image: "static/items/junk/emptycan.png",
    },
    {
        name: "PowerPulse Capsules",
        itemType: "consumable",
        rarity: "specialist",
        level: 1,
        about: "Recharge and repair on the go – these capsules kick bruises to the curb and boost your energy!",
        cashValue: 0,
        image: "static/items/consumables/powerpulse.png",
        itemEffects: [
            {
                effect: "contusion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "fatigue",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "NeuroMend Injector",
        itemType: "consumable",
        rarity: "specialist",
        level: 1,
        about: "A quick shot of clarity and calm – clears your head and your fears.",
        cashValue: 0,
        image: "static/items/consumables/neuromend.png",
        itemEffects: [
            {
                effect: "concussion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "trauma",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Aegis Medkit",
        itemType: "consumable",
        rarity: "specialist",
        level: 1,
        about: "A hero's best friend in a box – staunches bleeding and supports broken bones like a champ!",
        cashValue: 0,
        image: "static/items/consumables/aegis.png",
        itemEffects: [
            {
                effect: "fracture",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "bleeding",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Verdant Life Capsules",
        itemType: "crafting",
        rarity: "standard",
        level: 1,
        about: "A rare essence distilled from enchanted herbs – boosts any healing concoction's potency!",
        cashValue: 100,
        image: "static/items/consumables/lifecapsules.png",
    },
    {
        name: "Nano-Infused Cloth",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "Ultra-light and super-strong",
        cashValue: 200,
        image: "static/items/crafting/nanocloth.png",
    },
    {
        name: "Golden Sun Capsules",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "A vial of concentrated health – the ultimate ingredient for advanced healing serums.",
        cashValue: 200,
        image: "static/items/consumables/suncapsules.png",
    },
    {
        name: "Cyber-Med Gel",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "A futuristic gel that speeds up regeneration – a must-have for any serious healer.",
        cashValue: 200,
        image: "static/items/crafting/medgel.png",
    },
    {
        name: "Emerald Essence Vial",
        itemType: "crafting",
        rarity: "specialist",
        level: 1,
        about: "A vial of concentrated emerald energy – perfect for crafting powerful restorative items.",
        cashValue: 500,
        image: "static/items/crafting/emeraldessence.png",
    },
    {
        name: "PowerPulse Capsules Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 1,
        about: "Recharge and repair on the go – these capsules kick bruises to the curb and boost your energy!",
        cashValue: 0,
        image: "static/items/consumables/powerpulse.png",
        recipeUnlockId: 83,
        itemEffects: [
            {
                effect: "contusion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "fatigue",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "NeuroMend Injector Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 1,
        about: "A quick shot of clarity and calm – clears your head and your fears.",
        cashValue: 0,
        image: "static/items/consumables/neuromend.png",
        recipeUnlockId: 84,
        itemEffects: [
            {
                effect: "concussion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "trauma",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Aegis Medkit Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 1,
        about: "A hero's best friend in a box – staunches bleeding and supports broken bones like a champ!",
        cashValue: 0,
        image: "static/items/consumables/aegis.png",
        recipeUnlockId: 85,
        itemEffects: [
            {
                effect: "fracture",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "bleeding",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Advanced Med-Pack Recipe",
        itemType: "recipe",
        rarity: "standard",
        level: 1,
        about: "The ultimate recovery box, turning near-death experiences into mere anecdotes.",
        cashValue: 2500,
        image: "static/items/consumables/medkit.png",
        recipeUnlockId: 86,
        itemEffects: [
            {
                effect: "bleeding",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "trauma",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Regenerative Serum Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 1,
        about: "The elixir for bones and bruises – heal like a hero.",
        cashValue: 3000,
        image: "static/items/consumables/regenserum.png",
        recipeUnlockId: 87,
        itemEffects: [
            {
                effect: "fracture",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "contusion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Emergency Response Kit Recipe",
        itemType: "recipe",
        rarity: "military",
        level: 1,
        about: "Your ultimate first-aid BFF",
        cashValue: 10000,
        image: "static/items/consumables/emergencykit.png",
        recipeUnlockId: 88,
        itemEffects: [
            {
                effect: "bleeding",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "fracture",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "concussion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "contusion",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "trauma",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
            {
                effect: "fatigue",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Eclipse Serum",
        itemType: "consumable",
        rarity: "specialist",
        level: 1,
        about: "A shadowy serum said to cleanse even the darkest afflictions. Dare to take the injection?",
        cashValue: 1,
        image: "static/items/consumables/zombgon.png",
        itemEffects: [
            {
                effect: "zombified",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Nightshade",
        itemType: "crafting",
        rarity: "enhanced",
        level: 1,
        about: "A seed so dark it makes midnight jealous – handle with care!",
        cashValue: 100,
        image: "static/items/crafting/nightshade.png",
    },
    {
        name: "Eclipse Serum Recipe",
        itemType: "recipe",
        rarity: "specialist",
        level: 1,
        about: "A shadowy serum said to cleanse even the darkest afflictions. Dare to take the injection?",
        cashValue: 1,
        image: "static/items/consumables/zombgon.png",
        recipeUnlockId: 89,
        itemEffects: [
            {
                effect: "zombified",
                source: "consumable_item",
                effectTier: "All",
                effectType: "treatment",
                effectValue: "1",
            },
        ],
    },
    {
        name: "Strength Pills",
        itemType: "consumable",
        rarity: "military",
        level: 1,
        about: "+25% Strength\nLasts 1 Hour\n*Effect does not stack*",
        cashValue: 0,
        image: "static/items/consumables/strpills.png",
        itemEffects: [
            {
                effect: "strength",
                source: "consumable_item",
                duration: "60",
                effectType: "buff",
                effectValue: "0.25",
            },
        ],
    },
    {
        name: "Dexterity Pills",
        itemType: "consumable",
        rarity: "military",
        level: 1,
        about: "+25% Dexterity\nLasts 1 Hour\n*Effect does not stack*",
        cashValue: 0,
        image: "static/items/consumables/dexpills.png",
        itemEffects: [
            {
                effect: "dexterity",
                source: "consumable_item",
                duration: "60",
                effectType: "buff",
                effectValue: "0.25",
            },
        ],
    },
    {
        name: "Defence Pills",
        itemType: "consumable",
        rarity: "military",
        level: 1,
        about: "+25% Defence\nLasts 1 Hour\n*Effect does not stack*",
        cashValue: 0,
        image: "static/items/consumables/defpills.png",
        itemEffects: [
            {
                effect: "defence",
                source: "consumable_item",
                duration: "60",
                effectType: "buff",
                effectValue: "0.25",
            },
        ],
    },
    {
        name: "Vitality Pills",
        itemType: "consumable",
        rarity: "legendary",
        level: 1,
        about: "+100 Max HP (Permanent)",
        cashValue: 0,
        image: "static/items/consumables/vitalitypills.png",
        itemEffects: [
            {
                effect: "health",
                source: "consumable_item",
                duration: "1",
                effectType: "buff",
                effectValue: "100",
            },
        ],
    },
    {
        name: "Stamina Pills",
        itemType: "consumable",
        rarity: "military",
        level: 1,
        about: "+75 Max Stamina\nLasts 1 Hour\n*Effect does not stack*",
        cashValue: 0,
        image: "static/items/consumables/stapills.png",
        itemEffects: [
            {
                effect: "stamina",
                source: "consumable_item",
                duration: "60",
                effectType: "buff",
                effectValue: "75",
            },
        ],
    },
    {
        name: "Monkey Egg",
        itemType: "pet",
        rarity: "military",
        level: 1,
        about: "Munkey",
        cashValue: 100,
        image: "static/Pets/monkey_1.png",
        petUnlockId: 3,
    },
    {
        name: "Small Pet Food",
        itemType: "pet_food",
        rarity: "standard",
        level: 1,
        about: "",
        cashValue: 100,
        image: "static/Pets/monkey_1.png",
    },
    {
        name: "Large Pet Food",
        itemType: "pet_food",
        rarity: "standard",
        level: 1,
        about: "",
        cashValue: 200,
        image: "static/Pets/monkey_1.png",
    },
];

export default items;
