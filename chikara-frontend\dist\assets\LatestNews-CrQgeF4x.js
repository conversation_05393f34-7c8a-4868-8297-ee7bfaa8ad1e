import{e as u,g as f,c as b,y as v,j as e,h as o,o as m,b_ as r,v as j,d0 as g,ae as w,q as N,d1 as n,t as y,r as k,C}from"./index--cEnoMkg.js";import{A as z}from"./arrow-left-CD4tj0GY.js";const L=()=>{const s=u();return{setNewsID:f(b.user.setLastNewsIdRead.mutationOptions({onSuccess:()=>{s.invalidateQueries({queryKey:v.USER.CURRENTUSERINFO})},onError:a=>{console.log(a?.message||"An error occurred")}}))}},p=({text:s,rounded:t=!0})=>e.jsxs("button",{className:o("relative inline-flex h-11 overflow-hidden p-[2px] focus:outline-hidden focus:ring-none focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50",t?"rounded-full":"rounded-md"),children:[e.jsx("span",{className:"absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#E2CBFF_0%,#393BB2_50%,#E2CBFF_100%)]"}),e.jsx("span",{className:o("inline-flex size-full cursor-pointer items-center justify-center bg-slate-900/90 px-3 py-1 font-display text-2xl text-white backdrop-blur-3xl lg:text-3xl",t?"rounded-full":"rounded-md"),children:s})]}),B=({text:s,className:t})=>{const a=g(s,/\{\{(.*?)\}\}/g,(l,i)=>e.jsx("span",{className:r("text-yellow-400",t),children:l},i+s));return e.jsx("span",{children:a})},c=({post:s,className:t})=>{const l={Patch:"text-white text-stroke-sm bg-blue-600/50 group-hover:text-white group-hover:bg-blue-600 font-semibold py-0.5 px-3 text-sm lg:py-[6px] lg:px-[20px] rounded-[8px] tracking-widest lg:text-base",Event:"text-[#a294ff] text-stroke-sm bg-[#8c7aff59]  group-hover:text-white group-hover:bg-[#8c7aff] font-semibold py-0.5 px-3 text-sm lg:py-[6px] lg:px-[20px] rounded-[8px] tracking-widest lg:text-base",News:"text-white text-stroke-sm bg-red-500/75 group-hover:text-white group-hover:bg-red-500 font-semibold py-0.5 px-3 text-sm lg:py-[6px] lg:px-[20px] rounded-[8px] tracking-widest lg:text-base"}[s.tag];return e.jsx("span",{className:r(l,t),children:s.tag})},T=s=>s.hideLink?null:s.externalLink?s.externalLink:s.link?s.link:`/news/${s.href}`,H=({post:s,isLast:t,isFirst:a})=>s?e.jsxs(m,{className:"group relative flex h-min w-full cursor-pointer flex-col flex-nowrap items-center gap-2.5 px-5 font-display no-underline md:flex lg:w-[720px] lg:flex-row lg:p-0",to:T(s),children:[e.jsxs("div",{className:r("relative mt-1.5 hidden h-auto w-[170px] flex-row items-start gap-0 self-stretch p-0 lg:flex",t?"":"-mb-14"),children:[e.jsx("div",{className:"flex flex-col flex-nowrap ",children:e.jsxs("div",{className:"-left-3 relative z-10 flex h-min w-full flex-col flex-nowrap items-start gap-3.5 p-0 will-change-transform",children:[e.jsx("div",{className:"relative size-auto",children:e.jsx("div",{className:"contents",tabIndex:"0",children:e.jsx("div",{className:"relative flex size-min flex-col flex-nowrap items-center gap-2.5 px-5",children:e.jsx("p",{className:"relative whitespace-pre text-left",children:e.jsx(c,{post:s})})})})}),e.jsx("p",{className:"ml-6 relative whitespace-pre text-sm font-body text-stroke-0 text-white/60",children:s.date})]})}),e.jsx(I,{})]}),e.jsx("div",{className:r("relative ms-1 flex h-min flex-1 flex-col flex-nowrap items-start justify-center p-0 lg:ml-0 lg:w-px lg:items-center"),children:e.jsx(E,{isLast:t,isFirst:a,children:e.jsxs("div",{className:r("relative flex h-min w-full flex-col flex-nowrap items-start gap-2.5 p-0"),children:[e.jsxs("div",{className:"relative flex items-center gap-2 lg:hidden",children:[e.jsx("p",{children:e.jsx(c,{post:s,className:"lg:hidden"})}),e.jsx("p",{className:"relative text-sm text-stroke-0 text-white/60",children:s.date})]}),e.jsx("h2",{className:"relative w-full whitespace-pre-wrap break-words font-bold font-display text-2xl text-white leading-tight",children:s.title}),e.jsx("p",{className:"relative w-full overflow-hidden whitespace-pre-wrap break-words text-white/65",children:e.jsx(B,{text:s.description})}),!s.hideLink&&e.jsx("div",{className:"relative h-[26px] lg:w-[120px]",children:e.jsx("div",{className:"contents",tabIndex:"0",children:e.jsxs("div",{className:"relative flex h-[26px] cursor-pointer flex-row flex-nowrap items-center gap-0 p-0 lg:w-[120px]",children:[e.jsx("span",{className:"relative overflow-visible whitespace-pre font-medium text-purple-300",children:s.linkText?s.linkText:"Read more"}),e.jsxs("div",{className:"relative h-3.5 w-[34px] overflow-visible",children:[e.jsx("svg",{className:"-translate-x-1/2 -translate-y-1/2 absolute top-[57.14%] left-[29.41%] h-3 w-[7px]",viewBox:"0 0 7 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M1.5 2L5.5 6L1.5 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})}),e.jsx("div",{className:"-translate-x-1/2 -translate-y-1/2 absolute top-[57.14%] left-[35.29%] size-0.5 bg-purple-300"})]})]})})}),s.image&&e.jsxs("div",{className:r("relative aspect-[2.47/1] w-full overflow-hidden rounded-lg border border-white/10 lg:h-[219px]",s.imageContainerClass),children:[s.version&&e.jsx("div",{className:"-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-20 z-10 ",children:e.jsx(p,{text:s.version})}),e.jsx("img",{src:s.image,alt:"",className:r("size-full rounded-lg object-cover",s.imageClass,s.imageClassLarge)})]})]})})})]}):null,E=({children:s,isLast:t,isFirst:a})=>j()?e.jsxs("div",{className:"relative h-full font-body lg:hidden",children:[e.jsx("div",{className:r(t?"h-full":"h-[160%]",a&&"mt-8","absolute border-gray-200 border-s dark:border-gray-700")}),e.jsxs("div",{className:"ms-8 lg:mb-10",children:[e.jsx("span",{className:"lg:-start-3 -start-2.5 absolute top-10 flex size-[1.35rem] items-center justify-center rounded-full bg-blue-100 ring-8 ring-white lg:size-6 lg:ring-8 dark:bg-blue-900 dark:ring-gray-900",children:e.jsx("svg",{className:"size-2.5 text-blue-800 dark:text-blue-300","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"})})}),s]})]}):s,I=()=>e.jsx("div",{className:"relative hidden h-full border-gray-200 border-s font-body lg:block dark:border-gray-700",children:e.jsx("div",{className:"ms-8 lg:mb-10",children:e.jsx("span",{className:"lg:-start-3 -start-2.5 absolute flex size-[1.35rem] items-center justify-center rounded-full bg-blue-100 ring-8 ring-white lg:size-6 lg:ring-8 dark:bg-blue-900 dark:ring-gray-900",children:e.jsx("svg",{className:"size-2.5 text-blue-800 dark:text-blue-300","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"})})})})}),x=({text:s,className:t="px-1 mx-0.5",bg:a="bg-custom-yellow",textColor:l="text-black"})=>{const i=g(s,/\{\{(.*?)\}\}/g,(d,h)=>e.jsx("span",{className:r("rounded-md font-extrabold text-stroke-0",l,a,t),children:d},h+s));return e.jsx("span",{children:i})},R=({selectedPost:s})=>e.jsxs("div",{className:"modalHeaderBackground vignette-sm flex flex-col gap-4 border-slate-700/50 bg-slate-900/95 bg-blend-overlay lg:rounded-2xl lg:border",children:[e.jsxs("div",{className:"relative size-full",children:[s?.version&&e.jsx("div",{className:"absolute right-0.5 bottom-0.5 z-10 ",children:e.jsx(p,{rounded:!1,text:s?.version})}),e.jsx("img",{src:s?.image,alt:"",className:r("mx-auto size-full rounded-md object-cover object-center group-hover:brightness-105 lg:rounded-2xl 2xl:h-80",s?.imageClassLarge)})]}),e.jsxs("div",{className:"p-4 lg:px-16 lg:pb-8",children:[e.jsx("p",{className:"mb-4 text-center font-bold font-display text-4xl text-custom-yellow uppercase tracking-wide ",children:s?.title}),(s?.description||s?.subDescription)&&e.jsxs("div",{className:"relative",children:[e.jsx("p",{className:"prose prose-slate dark:prose-dark prose-a:relative prose-a:z-10 font-body font-semibold dark:text-slate-300",children:e.jsx(x,{text:s?.description})}),s?.subDescription&&e.jsx("p",{className:"prose mt-4 rounded-lg bg-black/15 p-1 font-medium font-body dark:text-slate-300",children:s?.subDescription})]}),s?.content&&e.jsx("div",{className:"mt-4 flex flex-col gap-4 font-body",children:s?.content.map((t,a)=>e.jsxs("div",{className:"flex flex-col",children:[t.heading&&e.jsx("p",{className:"mt-2 w-full rounded-t-md border-gray-900/50 border-x border-t bg-black/25 px-2 py-1 text-center font-semibold text-black text-xl uppercase dark:text-custom-yellow",children:t.heading}),e.jsxs("div",{className:r("rounded-b-md border-gray-600/50 border-x border-b bg-blue-700/25 px-3 py-2",!t.heading&&"rounded-md pt-3"),children:[t.body&&e.jsx("p",{className:"mb-4 font-medium text-base text-slate-900 dark:text-slate-200",children:e.jsx(x,{className:"",textColor:"text-sky-300 text-stroke-s-sm font-medium",bg:"",text:t.body})}),t.image&&e.jsx("img",{src:t.image,alt:"",className:"my-6 h-auto max-h-120 w-full scale-110 rounded-md object-contain lg:my-0 lg:scale-100"}),t?.textList?.map(l=>e.jsx("p",{className:"prose my-2 font-display text-slate-900 text-sm md:text-base dark:text-slate-200",children:e.jsx(x,{className:"",textColor:"text-sky-300 text-stroke-s-sm font-medium",bg:"",text:l})},l)),e.jsx("ul",{className:"list-inside list-disc space-y-2",children:t?.list?.map(l=>e.jsx("li",{className:"text-slate-900 text-sm md:text-base dark:text-slate-200",children:e.jsx(x,{className:"",textColor:"text-red-400 font-bold text-base text-stroke-s-sm font-medium font-display",bg:"bg-black/25 px-1 rounded-lg",text:l})},l))}),e.jsx("ul",{className:"flex list-inside flex-col gap-4",children:t?.itemList?.map(l=>e.jsxs("li",{className:"flex items-center gap-2 text-base text-slate-900 dark:text-slate-200",children:[e.jsx(w,{item:l.item,height:"h-10!",className:"inline-block"}),e.jsxs("div",{className:"flex-col items-center gap-2 text-sm lg:flex-row lg:text-base",children:[" ",e.jsxs("p",{className:"font-semibold text-yellow-400",children:[l.item.name," - "]}),l.text]})]},l.id))}),t.lowerBody&&e.jsx("p",{className:"mt-4 font-medium text-base text-slate-900 dark:text-slate-200",children:e.jsx(x,{link:!0,className:"",textColor:"text-sky-300 text-stroke-s-sm font-medium",bg:"",text:t.lowerBody})}),t.lowerList&&e.jsx("ul",{className:"my-2 list-inside list-disc space-y-2",children:t?.lowerList?.map(l=>e.jsx("li",{className:"text-slate-900 text-sm md:text-base dark:text-slate-200",children:l},l))}),t?.footerText&&e.jsx("p",{className:"mt-3 font-semibold text-blue-200 text-sm md:text-base",children:t.footerText})]})]},a))})]})]}),D=()=>{const{postID:s}=N(),t=s?n.find(i=>i.href===s):null,{data:a}=y(),{setNewsID:l}=L();return k.useEffect(()=>{a&&a?.lastNewsIDRead<n[0].id&&l.mutate(n[0].id)},[a]),e.jsx("div",{className:"modalHeaderBackground relative rounded-t-lg bg-gray-950/90 bg-blend-overlay 2xl:mt-14",children:e.jsxs("div",{className:"relative mx-auto mt-4 flex max-w-(--breakpoint-lg) flex-col border border-white/10 bg-linear-to-b from-slate-800 to-slate-800/50 pb-6 lg:rounded-xl lg:pb-0",children:[e.jsx("div",{className:"vignette-sm pointer-events-none absolute inset-0 z-5 size-full object-cover opacity-50 lg:rounded-2xl"}),s&&e.jsx(m,{className:"lg:-mb-3 mx-2 my-2.5 lg:z-20 lg:mt-4 lg:mr-0 lg:ml-4",to:-1,children:e.jsx(C,{type:"primary",className:"w-32! ",children:e.jsxs("div",{className:"mr-3 flex items-center gap-x-2",children:[e.jsx(z,{className:"size-5"}),"Back"]})})}),e.jsx("div",{className:"relative mx-auto flex max-w-3xl items-center justify-center sm:pb-12 lg:p-6 lg:px-0",children:s?e.jsx(R,{selectedPost:t}):e.jsx(e.Fragment,{children:e.jsx("div",{className:"mt-4 flex flex-col space-y-12 lg:space-y-14 ",children:n.map((i,d)=>e.jsx("div",{className:"",children:e.jsx(H,{post:i,isLast:d===n.length-1,isFirst:d===0})},i.id))})})})]})})};export{D as default};
