import{B as Q,b as R,c as M,r as d,e as A,g as D,l as y,y as b,j as e,ad as H,ae as z,a6 as O,af as G,v as K,as as W,bH as E,h as S,N as P,o as B,D as U,C as q,t as _,S as V,ac as Y}from"./index--cEnoMkg.js";import{A as $}from"./ag-theme-quartz-2665BHd2.js";import{r as J}from"./rarityColours-Bvm3hwLY.js";import{d as X}from"./differenceInHours-BsBlWOxD.js";function Z({openModal:s,setOpenModal:r,itemToSell:a,setItemToSell:o}){const{ALLOWED_AUCTION_ITEM_TYPES:f,BLACKLISTED_AUCTION_ITEM_IDS:c}=Q();function m(l){return(l?.filter(L=>f?.includes(L.item.itemType))||[])?.filter(L=>!c?.includes(L.item.id))||[]}const{data:h}=R(M.user.tradeableInventory.queryOptions({select:m})),[u,x]=d.useState(1),[g,v]=d.useState(12),[p,w]=d.useState(100),[k,C]=d.useState(!1),j=A(),n={...a?.item};n.rarity==="novice"&&(n.colour="text-stroke-s-sm"),n.rarity==="standard"&&(n.colour="text-green-600"),n.rarity==="enhanced"&&(n.colour="text-blue-600"),n.rarity==="specialist"&&(n.colour="text-indigo-600"),n.rarity==="military"&&(n.colour="text-red-600"),n.rarity==="legendary"&&(n.colour="text-yellow-600");const T=D(M.auctions.createListing.mutationOptions({onSuccess:()=>{N(),y.success(`Successfully listed ${parseInt(u)}x ${n.name} for ¥${parseInt(p)*parseInt(u)}`),j.invalidateQueries({queryKey:b.USER.INVENTORY}),j.invalidateQueries({queryKey:b.USER.CURRENTUSERINFO}),j.invalidateQueries({queryKey:b.AUCTIONS.AUCTIONLIST})},onError:l=>{console.log(l),y.error(l.message)}})),F=()=>{if(!a){y.error("You must select an item to list");return}if(a?.upgradeLevel>0){y.error("You cannot list an upgraded item");return}if(!a?.isTradeable){y.error("You cannot list this item on the market");return}T.mutate({itemId:parseInt(a.item.id),quantity:parseInt(u),buyoutPrice:parseInt(p),auctionLength:parseInt(g),bankFunds:k})},N=()=>{r(!1),x(1),v(12),w(100),C(!1),setTimeout(()=>o(null),500)},I={12:.03,24:.06,48:.09},t=.15,i=l=>{if(!a){y.error("You must select an item to list");return}l>a.count?x(a.count):x(l)};return e.jsx(H,{showClose:!0,open:s,title:"Inventory",iconBackground:"shadow-lg",contentHeight:"max-h-[75dvh] md:max-h-[60dvh]",className:"min-w-[50dvw]! lg:min-w-[35dvw]! mx-auto",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/4AAKgyV.png",alt:"",className:"mt-0.5 h-11 w-auto"}),onOpenChange:N,children:e.jsx("div",{className:"overflow-auto! mt-3 h-full text-center",children:a?e.jsxs("div",{className:"mb-1 flex flex-col",children:[e.jsxs("div",{className:"mb-6 flex flex-col",children:[e.jsx(z,{item:a.item,height:"w-1/4 max-w-36 mx-auto"}),e.jsx("p",{className:"text-gray-200",children:a.item.name}),e.jsxs("p",{className:"mx-auto flex text-gray-200",children:["Sell Value:"," ",e.jsxs("span",{className:"ml-2 inline-flex gap-1 text-yellow-500",children:[e.jsx("img",{src:O,alt:"",className:"my-auto size-4"}),a.item.cashValue]})]})]}),e.jsxs("div",{className:"mx-auto flex w-full flex-col rounded-lg border border-slate-700 bg-slate-800 px-8 py-4 md:w-3/4 2xl:px-20",children:[e.jsxs("div",{className:"mx-auto my-2 flex w-full rounded-md shadow-xs md:mt-1",children:[e.jsxs("label",{className:"my-auto mr-2 block w-1/4 text-center font-medium text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300",htmlFor:"quantity",children:["Price",e.jsx("p",{children:"(per item)"})]}),e.jsxs("div",{className:"mt-1 flex flex-1 rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-500 border-r-0 bg-gray-900 px-3 text-gray-300 text-shadow sm:text-sm",children:"¥"}),e.jsx("input",{type:"number",name:"amount",id:"amount",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-500 px-3 py-2 text-lg placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300",placeholder:"100",min:"100",max:1e6,value:p,onChange:l=>w(l.target.value)})]})]}),e.jsxs("div",{className:"mx-auto my-2 flex w-full rounded-md shadow-xs md:mt-1",children:[e.jsx("label",{className:"my-auto mr-2 block w-1/4 text-center font-medium text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300",htmlFor:"quantity",children:"Quantity"}),e.jsx("input",{type:"number",id:"quantity",name:"quantity",className:"block w-full min-w-0 flex-1 rounded-l-md border-gray-300 px-3 text-center text-lg focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm md:text-xl dark:border-gray-500 dark:bg-gray-900 dark:text-white",placeholder:1,min:1,step:1,max:a.count,value:u,onChange:l=>{i(l.target.value)}}),e.jsxs("span",{className:"inline-flex items-center rounded-r-md border border-gray-300 border-l-0 bg-gray-50 px-3 text-center text-gray-500 text-lg ring-gray-500 sm:text-sm md:text-xl dark:border-gray-500 dark:bg-gray-900 dark:text-white",children:["/ ",a.count]})]}),e.jsxs("div",{className:"mx-auto my-2 flex w-full rounded-md shadow-xs md:mt-1",children:[e.jsx("label",{className:"my-auto mr-2 block w-1/4 text-center font-medium text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300",htmlFor:"quantity",children:"Length"}),e.jsxs("select",{id:"filter-item-name-box",className:"mx-auto w-full flex-1 rounded-md border border-gray-300 text-left font-medium text-gray-700 text-lg shadow-xs focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500 sm:text-sm md:text-base dark:border-gray-500 dark:bg-gray-900 dark:text-white",onChange:l=>v(l.target.value),children:[e.jsx("option",{value:"12",children:"12 Hours"}),e.jsx("option",{value:"24",children:"24 Hours"}),e.jsx("option",{value:"48",children:"48 Hours"})]})]}),e.jsxs("div",{className:"mx-auto my-1 flex items-center",children:[e.jsx("input",{id:"bank_deposit",name:"bank_deposit",type:"checkbox",defaultChecked:k,className:"size-4 rounded-sm border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-blue-600",onChange:l=>C(l.target.checked)}),e.jsxs("label",{htmlFor:"remember_me",className:"ml-2 block text-gray-200 text-sm text-stroke-sm",children:["Deposit funds to bank (",t*100,"% fee)"]})]}),e.jsxs("div",{className:"mx-auto mt-2 flex w-full flex-col rounded-lg border border-gray-600 bg-gray-700 px-2 py-0.5 text-stroke-sm text-white",children:[e.jsx("div",{className:"grid grid-cols-2"}),e.jsxs("p",{className:"text-gray-200",children:["Fee: ",e.jsxs("span",{className:"text-red-500",children:[I[g]*100,"%"]})]}),e.jsxs("p",{className:"text-gray-200",children:["Listing Cost:"," ",e.jsxs("span",{className:"text-red-500",children:["¥",Math.max(p*I[g],50).toFixed(0)]})]}),e.jsxs("p",{className:"mx-auto mt-2 w-fit rounded-lg px-2 text-base text-stroke-sm text-white md:text-lg",children:["Listing"," ",e.jsxs("span",{className:n?.colour,children:[Math.max(u,1),"x ",n?.name]})," ","for"," ",e.jsxs("span",{className:" text-green-600",children:["¥",Math.max(p*u,p)]})," ","Total"]})]})]}),e.jsx("button",{type:"button",className:"darkBlueButtonBGSVG mx-auto mt-2 flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs md:mt-3 md:w-1/2 dark:text-slate-200",onClick:F,children:"List Item"})]}):e.jsx("div",{className:"mt-2 grid grid-cols-3 gap-x-3 gap-y-4 sm:gap-x-6 md:mt-6 lg:grid-cols-6 xl:gap-x-3",children:h?.map(l=>e.jsx(ee,{item:l,setItemToSell:o},l.id))})})})}const ee=({item:s,setItemToSell:r})=>e.jsxs("div",{className:"group cursor-pointer divide-gray-200 rounded-lg shadow-sm",onClick:()=>{r(s)},children:[e.jsx(G,{item:s.item,type:"sell",count:s.count}),e.jsx("div",{className:"-mt-1.5 flex flex-col",children:e.jsx("div",{className:"relative mx-auto flex w-[90%] items-center justify-center rounded-b-md border border-transparent bg-gray-100 pt-3 pb-2 font-medium text-base text-gray-900 ring-2 ring-black group-hover:bg-gray-800 md:text-base dark:border dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 dark:group-hover:border-gray-400",children:"List Item"})})]},s.id),te=s=>{const{value:r}=s;return e.jsxs("div",{className:"relative flex h-full items-center gap-2 py-0.5 md:w-full md:flex-row md:items-start md:gap-4 md:p-1",children:[e.jsx(z,{item:r,className:"size-14 md:h-full md:w-auto"}),e.jsxs("div",{className:"flex flex-1 flex-col gap-1.5 py-1.5 md:my-auto md:gap-0",children:[e.jsx("p",{className:S(r.name.length>15?"md:text-sm! text-[0.65rem]":"text-sm! md:text-base!","leading-none! text-wrap! truncate text-left font-semibold text-custom-yellow md:text-base"),children:r.name}),e.jsxs("p",{className:S(J(r.rarity),"leading-none! text-xs! md:text-sm! text-left font-semibold"),children:[P(r.rarity)," ",e.jsx("span",{className:"text-gray-200",children:P(r.itemType)})]}),e.jsxs(B,{to:`/profile/${s.data.user.id}`,className:"md:hidden! relative mt-1 flex size-full gap-0.5",children:[e.jsx(U,{src:s.data.user,className:" h-4 rounded-full"}),e.jsx("p",{className:"text-xs! text-blue-400",children:s.data.user.username})]})]})]})},ae=s=>{const{value:r}=s;return e.jsx("div",{className:"relative flex size-full py-5",children:e.jsxs(B,{to:`/profile/${r.id}`,className:"flex gap-2",children:[e.jsx(U,{src:r,className:"h-full rounded-full"}),e.jsx("p",{className:"text-wrap! my-auto font-semibold text-blue-400 text-sm",children:r.username})]})})},se=({auctionList:s,setOpenModal:r,currentUser:a,setItemToBuy:o,setOpenPurchaseItemModal:f})=>{const c=d.useRef(null),m=K(),[h,u]=d.useState("All"),x=A(),{marketTablePageSize:g,setMarketTablePageSize:v}=W(),p=t=>{o(t),f(!0)},w=D(M.auctions.cancelListing.mutationOptions({onSuccess:()=>{x.invalidateQueries({queryKey:b.AUCTIONS.AUCTIONLIST}),x.invalidateQueries({queryKey:b.USER.INVENTORY}),x.invalidateQueries({queryKey:b.USER.CURRENTUSERINFO})},onError:t=>{console.error(t),y.error(t.message)}})),k=t=>{window.confirm("Are you sure you want to cancel this listing?")&&w.mutate({auctionItemId:t})},C=t=>{const i=t.data?.buyoutPrice,l=E(new Date(t.data?.endsAt));return e.jsxs("div",{className:"flex size-full flex-col items-center justify-center md:flex-row md:gap-2",children:[a.id!==t.data.user.id?e.jsx(q,{className:"font-semibold! w-[95%] md:w-3/4",type:"primary",onClick:()=>p(t.data),children:e.jsxs("div",{className:"flex items-center gap-1 font-body",children:[e.jsx("img",{className:"size-4",src:O,alt:""}),i]})}):e.jsx("div",{className:"flex size-full flex-col items-center justify-center gap-1",children:e.jsx(q,{className:"font-semibold! w-[95%] md:w-3/4",type:"danger",onClick:()=>k(t.data.id),children:e.jsxs("div",{className:"flex flex-col items-center font-body text-sm md:text-base",children:[e.jsxs("div",{className:"text-xs! -ml-3 mt-0.5 flex gap-1 font-body text-gray-300",children:[e.jsx("img",{"my-auto":!0,className:"my-auto size-3",src:O,alt:""}),i]}),"Cancel Listing"]})})}),e.jsxs("p",{className:"md:hidden! font-body font-semibold text-indigo-500 leading-relaxed",children:[l," left"]})]})};function j(){c?.current?.api.setFilterModel({user:{type:"notEqual",filter:a?.id}})}d.useEffect(()=>{c.current&&c.current.api&&(h==="All"?c.current.api.setFilterModel({user:{type:"notEqual",filter:a?.id}}):h==="CurrentUser"&&c.current.api.setFilterModel({user:{type:"equals",filter:a?.id}}))},[h,a,s]);const[n,T]=d.useState([{headerName:"Item",field:"item",cellRenderer:te,minWidth:m?183:null,sortable:!1,getQuickFilterText:t=>t.data.item.itemType,filter:"agTextColumnFilter",filterValueGetter:t=>t.data.item.name,filterParams:{filterOptions:["contains","equals","notEqual","startsWith","endsWith"],defaultOption:"contains"}},{headerName:m?"Qty":"Quantity",field:"quantity",maxWidth:m?100:125,cellClass:"text-base mt-4 font-semibold font-body",filter:"agNumberColumnFilter",filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Ends In",field:"endsAt",cellClass:"text-base font-semibold text-center mt-4",hide:m,filter:"agNumberColumnFilter",floatingFilter:!0,valueFormatter:t=>{const i=new Date(t.value);return E(i,{roundingMethod:"ceil"})},filterValueGetter:t=>{const i=new Date(t.data.endsAt);return X(i,new Date,{roundingMethod:"ceil"})},filterParams:{filterOptions:[{displayKey:"equals",displayName:"Equals (hours)",predicate:([t],i)=>i==null?!1:i===t}]}},{headerName:"Cost",field:"buyoutPrice",cellRenderer:C,filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Seller",field:"user",cellRenderer:ae,hide:m,filterValueGetter:t=>t.data.user.id,filter:!0,floatingFilter:!1}]),F={flex:1,sortable:!0,filter:!0,resizable:!0,cellClass:"px-1.5! md:px-2! 2xl:px-6!",floatingFilter:!0,suppressHeaderMenuButton:!0,suppressMovable:!0,filterParams:{maxNumConditions:1}},N=[10,30,50],I=t=>{if(t.api&&t.newPageSize===!0){const i=t.api.paginationGetPageSize();N.includes(i)&&i!==g&&v(i)}};return e.jsxs("div",{className:"ag-theme-quartz-dark rounded-t-lg md:p-2",style:{width:"100%",overflow:"auto"},children:[e.jsx(re,{gridRef:c,setOpenModal:r}),e.jsx(le,{setCurrentTab:u,currentTab:h}),e.jsx($,{ref:c,suppressCellFocus:!0,suppressRowHoverHighlight:!0,pagination:!0,rowData:s,columnDefs:n,defaultColDef:F,domLayout:"autoHeight",rowHeight:80,paginationPageSizeSelector:N,paginationPageSize:g||10,onPaginationChanged:t=>I(t),onFirstDataRendered:j})]})},re=({gridRef:s,setOpenModal:r})=>{const a=d.useCallback(()=>{s.current.api.setGridOption("quickFilterText",document.getElementById("filter-item-name-box").value)},[]);return e.jsxs("div",{className:"flex w-full items-center gap-4 text-left",children:[e.jsxs("div",{className:"w-40 pb-2",children:[e.jsx("span",{className:"ml-3 font-medium font-body text-xs",children:"Item Type"}),e.jsxs("select",{id:"filter-item-name-box",className:"mt-1 ml-2 rounded-md border border-gray-300 text-left font-medium text-gray-700 text-sm shadow-xs focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500 sm:text-sm md:w-52 md:text-base dark:border-gray-500 dark:bg-gray-900 dark:text-white",onChange:a,children:[e.jsx("option",{value:"",children:"Any"}),e.jsx("option",{value:"crafting",children:"Crafting Materials"}),e.jsx("option",{value:"upgrade",children:"Upgrade Materials"}),e.jsx("option",{value:"weapon",children:"Melee Weapon"}),e.jsx("option",{value:"ranged",children:"Ranged Weapon"}),e.jsx("option",{value:"offhand",children:"Offhand"}),e.jsx("option",{value:"shield",children:"Shield"}),e.jsx("option",{value:"head",children:"Head"}),e.jsx("option",{value:"chest",children:"Chest"}),e.jsx("option",{value:"hands",children:"Gloves"}),e.jsx("option",{value:"legs",children:"Legs"}),e.jsx("option",{value:"feet",children:"Feet"}),e.jsx("option",{value:"finger",children:"Ring"}),e.jsx("option",{value:"consumable",children:"Consumable"}),e.jsx("option",{value:"recipe",children:"Recipe"}),e.jsx("option",{value:"special",children:"Special"}),e.jsx("option",{value:"pet",children:"Pet"}),e.jsx("option",{value:"Junk",children:"Junk"})]})]}),e.jsx("div",{className:"mr-2 ml-auto flex",children:e.jsx(q,{className:"md:text-base! text-xs! md:min-w-32",variant:"primary",onClick:()=>r(!0),children:"Sell Item"})})]})},le=({currentTab:s,setCurrentTab:r})=>{const a=[{name:"All Listings",value:"All",current:s==="All"},{name:"My Listings",value:"CurrentUser",current:s==="CurrentUser"}];return e.jsx("div",{className:"flex w-full",children:a.map((o,f)=>e.jsxs("button",{"aria-current":o.current?"page":void 0,className:S(o.current?"text-gray-900":"text-gray-500",f===0?"rounded-tl-lg":"rounded-tr-lg",(f===a.length-1,""),"group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-3 text-center font-medium text-lg focus:z-10 dark:bg-gray-900 dark:text-white"),onClick:()=>{r(o.value)},children:[e.jsx("span",{children:o.name}),e.jsx("span",{"aria-hidden":"true",className:S(o.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-[0.15rem]")})]},o.name))})};function me(){const{data:s,isLoading:r}=R(M.auctions.getList.queryOptions()),[a,o]=d.useState(!1),[f,c]=d.useState(!1),[m,h]=d.useState(null),[u,x]=d.useState(null),{data:g}=_();return e.jsx(e.Fragment,{children:e.jsx("section",{className:"mx-auto md:max-w-7xl md:rounded-lg md:py-6",children:e.jsx("div",{className:"mx-auto h-full px-0 md:px-6",children:e.jsxs("div",{className:"mx-auto h-full bg-gray-100 text-center md:rounded-lg md:border md:border-gray-700 dark:bg-slate-800 ",children:[e.jsx("div",{className:"mt-4 md:mx-0",children:r?e.jsx(V,{center:!0}):e.jsx(se,{auctionList:s,setOpenModal:o,currentUser:g,setItemToBuy:x,setOpenPurchaseItemModal:c})}),e.jsx(Z,{openModal:a,setOpenModal:o,itemToSell:m,setItemToSell:h,currentUser:g}),u&&e.jsx("div",{className:"mt-4 md:mx-0",children:e.jsx(Y,{openModal:f,setOpenModal:c,itemToBuy:u})})]})})})})}export{me as default};
