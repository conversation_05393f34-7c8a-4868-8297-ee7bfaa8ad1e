import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { getToday } from "../../utils/dateHelpers.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as ShrineController from "./shrine.controller.js";
import { donateToShrineSchema } from "./shrine.validation.js";
import { z } from "zod";

export const shrineRouter = {
    getGoal: isLoggedInAuth.handler(async () => {
        const today = getToday();
        const response = await ShrineController.getDailyShrineGoal(today);
        return handleResponse({ data: response });
    }),

    getDonations: isLoggedInAuth.handler(async () => {
        const today = getToday();
        const response = await ShrineController.getDailyShrineDonations(today);
        return handleResponse({ data: response });
    }),

    getActiveBuff: isLoggedInAuth.handler(async () => {
        const buffRewards = await ShrineController.isTodaysDonationGoalReached();
        return handleResponse({ data: buffRewards || null });
    }),

    isBuffActive: isLoggedInAuth.input(z.object({ buffName: z.string() })).handler(async ({ input }) => {
        const response = await ShrineController.dailyBuffIsActive(input.buffName);
        return handleResponse({ data: response });
    }),

    donate: canMakeStateChangesAuth.input(donateToShrineSchema).handler(async ({ input, context }) => {
        const response = await ShrineController.donateToShrine(context.user.id, input.amount);
        return handleResponse(response);
    }),
};
