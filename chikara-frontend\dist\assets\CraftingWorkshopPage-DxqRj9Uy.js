import{F as B,b as O,c as b,e as E,g as M,an as I,y as z,j as e,h as u,ae as S,aF as U,r as C,aG as K,aH as H,aI as W,aJ as Y,aK as A,ad as Q,af as J,C as $,N as X,I as Z,ao as R,S as ee,aL as se,a7 as te,aM as re,aN as v}from"./index--cEnoMkg.js";import{i as ae}from"./intervalToDuration-f8TGlnXJ.js";import{g as F}from"./getUserItemCount-CIJt4adY.js";import{C as P}from"./circle-alert-CQS1v2Kc.js";import{H as D}from"./hammer-CpZIr1sG.js";import{C as ne}from"./circle-x-B-7GxCnr.js";import{u as V}from"./useGetInventory-O1uR82rJ.js";import"./differenceInHours-BsBlWOxD.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],ce=B("funnel",le),ie=(s={})=>O(b.crafting.recipeList.queryOptions({...s}));function oe(s){if(!s||s<=0)return"Instant";const a=Math.floor(s/1e3);if(a<60)return`${a}s`;const r=ae({start:0,end:s});return r.hours?`${r.hours}h ${r.minutes||0}m`:r.minutes?`${r.minutes}m ${r.seconds?`${r.seconds}s`:""}`:`${r.seconds}s`}const _=s=>{const a=E();return M(b.crafting.craftItem.mutationOptions({onSuccess:()=>{I.success("Crafting started!"),a.invalidateQueries({queryKey:b.crafting.getQueue.key()}),a.invalidateQueries({queryKey:z.USER.INVENTORY})},onError:r=>{const t=r.message||"Unknown error occurred";console.error("Crafting error:",t),I.error(t)}}))},de=s=>{switch(s){case"fabrication":return"Fabrication";case"electronics":return"Electronics";case"chemistry":return"Chemistry";case"outfitting":return"Outfitting";default:return null}},ue=(s,a=[])=>!s||!s.inputs||!Array.isArray(s.inputs)||!a||!Array.isArray(a)?!1:s.inputs.every(r=>{const t=r.amount||1;return F(a,r.id)>=t});function me({recipe:s,outputItem:a,userInventory:r,isCraftQueueFull:t}){const{mutate:n,isPending:i}=_(),o=ue(s,r),d=!o||i||t,m=()=>{n({recipeId:s.id,amount:1})};return e.jsx("div",{className:u("flex flex-col rounded-lg border transition-colors overflow-hidden","bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/80"),children:e.jsxs("div",{className:"p-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-12 rounded-lg flex items-center justify-center",children:e.jsx(S,{itemTypeFrame:!0,item:a,className:""})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white text-sm font-medium",children:a.name}),e.jsxs("div",{className:"flex items-center gap-1 text-xs",children:[e.jsx("span",{className:"text-purple-300",children:de(s.requiredSkillType)}),e.jsx("span",{className:"text-gray-500",children:"•"}),e.jsxs("span",{className:"text-gray-400 flex items-center gap-1",children:[e.jsx(U,{className:"size-3"}),oe(s.craftTime)]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[!o&&e.jsx("div",{className:"size-6 rounded-full bg-red-900/30 border border-red-700/50 flex items-center justify-center",children:e.jsx(P,{className:"size-4 text-red-400"})}),e.jsxs("button",{disabled:d,className:u("flex items-center justify-center gap-1 px-3 py-1.5 rounded-md text-sm font-medium",d?"bg-gray-700 text-gray-500 cursor-not-allowed":"bg-purple-600 hover:bg-purple-700 text-white"),onClick:m,children:[e.jsx(D,{className:u("size-4",i&&"animate-pulse")}),e.jsx("span",{className:"text-stroke-sm",children:i?"Crafting...":"Craft"})]})]})]}),e.jsx("div",{className:"mt-2 pt-2 border-t border-gray-700/50",children:e.jsx("div",{className:"flex flex-col space-y-1.5",children:s.inputs.map(x=>{const g=x.amount||1,h=F(r||[],x.id),N=h>=g;return e.jsxs("div",{className:"flex items-center justify-between lg:grid lg:gap-4 lg:grid-cols-2 lg:w-fit lg:mx-auto lg:pl-20",children:[e.jsxs("div",{className:"flex items-center gap-1.5 lg:w-40",children:[e.jsx(S,{noBackground:!0,item:x,className:"size-5"}),e.jsx("span",{className:"text-xs text-gray-300",children:x.name})]}),e.jsxs("span",{className:u("text-xs font-medium",N?"text-green-400":"text-red-400"),children:[h,"/",g]})]},x.id)})})})]})})}function xe({name:s,icon:a,isActive:r,onClick:t}){return e.jsxs("button",{className:u("px-3 py-1.5 rounded-lg text-sm whitespace-nowrap transition-colors flex items-center gap-1.5 font-medium font-display",r?"bg-purple-900/30 text-purple-300 border border-purple-900/50":"bg-gray-800/30 text-gray-400 border border-gray-800 hover:text-gray-300"),onClick:t,children:[e.jsx("span",{children:a}),s]})}function ge({activeCategory:s,onCategoryChange:a}){const r=[{id:"all",name:"All",icon:"🔮"},{id:"weapons",name:"Weapons",icon:"⚔️"},{id:"armor",name:"Armor",icon:"🛡️"},{id:"consumables",name:"Consumables",icon:"🧪"},{id:"misc",name:"Misc",icon:"📦"},{id:"quest",name:"Quest",icon:"📜"}];return e.jsx("div",{className:"flex space-x-1 overflow-x-auto pb-1 overflow-y-hidden",children:r.map(t=>e.jsx(xe,{name:t.name,icon:t.icon,isActive:s===t.id,onClick:()=>a(t.id)},t.id))})}const pe=s=>{const a=E();return M(b.crafting.cancelCraft.mutationOptions({onSuccess:()=>{I.success("Crafting cancelled!"),a.invalidateQueries({queryKey:b.crafting.getQueue.key()}),a.invalidateQueries({queryKey:z.USER.INVENTORY})},onError:r=>{const t=r.message||"Unknown error occurred";console.error("Cancel craft error:",t),I.error(t)}}))},fe=s=>{const a=E();return M(b.crafting.completeCraft.mutationOptions({onSuccess:()=>{I.success("Item collected successfully!"),a.invalidateQueries({queryKey:b.crafting.getQueue.key()}),a.invalidateQueries({queryKey:z.USER.INVENTORY})},onError:r=>{const t=r.message||"Unknown error occurred";console.error("Collect craft error:",t),I.error(t)}}))};function he({craftingQueue:s,maxCraftQueue:a,isLoading:r,isError:t}){const[n,i]=C.useState(!0),{mutate:o,isPending:d}=fe(),{mutate:m,isPending:x}=_(),{mutate:g,isPending:h}=pe(),N=l=>W(l?.endsAt),y=l=>{const c=Y(l);if(c===-1)return"Unknown";if(c<=0)return"Ready";const p=Math.floor(c/(1e3*60*60)),k=Math.floor(c%(1e3*60*60)/(1e3*60));return p>0?`${p}h ${k}m`:`${k}m`},w=l=>{const c=A(l?.startedAt),p=A(l?.endsAt);if(c===0||p===0)return 0;const k=Date.now(),j=p-c,G=k-c;return j<=0?100:Math.min(100,Math.max(0,G/j*100))},f=l=>{const c=l?.crafting_recipe.id,p=l?.id;if(!c||!p){console.error("Missing recipe ID or item ID for collect and recraft");return}o({id:p},{onSuccess:()=>{m({recipeId:c,amount:1})}})};return e.jsxs("div",{className:"bg-gray-800/50 rounded-lg border border-purple-900/30 overflow-hidden lg:h-fit lg:w-80",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 text-white",onClick:()=>i(!n),children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(U,{className:"size-5 text-purple-400"}),e.jsx("span",{className:"",children:"Crafting Queue"}),!r&&!t&&e.jsxs("span",{className:"text-sm text-purple-300",children:[e.jsx("span",{className:"font-medium",children:s.length}),e.jsxs("span",{className:"text-gray-500",children:["/",a," slots"]})]}),!r&&!t&&s.some(l=>N(l))&&e.jsx("span",{className:"px-1.5 py-0.5 bg-green-600 text-gray-200 text-xs rounded-full text-stroke-s-sm",children:"Ready"})]}),e.jsx(K,{className:u("size-5 text-gray-400 transition-transform",n&&"rotate-90")})]}),n&&e.jsxs("div",{className:"p-3 pt-0",children:[r&&e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("div",{className:"animate-spin size-8 border-4 border-purple-500 border-t-transparent rounded-full"})}),t&&e.jsx("div",{className:"flex items-center justify-center py-4 text-red-400",children:e.jsx("span",{children:"Failed to load crafting queue"})}),!r&&!t&&s.length>0?e.jsx("div",{className:"space-y-2",children:s.map(l=>{const c=N(l),p=l?.crafting_recipe.outputs[0];return e.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-900/50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:u("size-10 rounded-lg flex items-center justify-center"),children:p?.image?e.jsx(S,{item:p,className:""}):e.jsx("div",{className:"text-xl",children:"🔮"})}),e.jsxs("div",{children:[e.jsxs("h4",{className:"text-white text-sm font-medium",children:[p?.name||"Unknown Item"," ",e.jsxs("span",{className:"text-gray-400",children:["x",p?.amount]})]}),e.jsx("div",{className:"flex items-center gap-1 text-xs",children:c?e.jsx("span",{className:"text-green-400",children:"Ready to collect"}):e.jsxs(e.Fragment,{children:[e.jsx(U,{className:"size-3 text-blue-400"}),e.jsx("span",{className:"text-gray-400",children:y(l?.endsAt)})]})})]})]}),c?e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("button",{disabled:d||x,className:u("px-3 py-1.5 text-white! text-sm rounded-sm transition-colors text-stroke-s-sm",d||x?"bg-green-700 cursor-not-allowed":"bg-green-600 hover:bg-green-700"),onClick:()=>{l?.id&&o({id:l.id})},children:d?"Collecting...":"Collect"}),e.jsx("button",{disabled:d||x,className:u("px-3 py-1.5 flex items-center justify-center gap-1 text-white! text-sm rounded-sm transition-colors text-stroke-s-sm",d||x?"bg-blue-700 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"),onClick:()=>f(l),children:d||x?"Processing...":e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"size-3"})," Collect + Craft Again"]})})]}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-24 h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-blue-600 rounded-full",style:{width:`${w(l)}%`}})}),e.jsx("button",{disabled:h,title:"Cancel crafting",className:u("p-1 text-red-400 hover:text-red-300 rounded-full transition-colors",h&&"opacity-50 cursor-not-allowed"),onClick:()=>{l?.id&&g({id:l.id})},children:e.jsx(ne,{className:"size-5"})})]})]},l?.id)})}):!r&&!t?e.jsxs("div",{className:"flex flex-col items-center justify-center py-4 text-gray-300",children:[e.jsx(D,{className:"size-8 mb-2 opacity-75"}),e.jsx("p",{className:"text-sm",children:"No items in queue"})]}):null]})]})}const je=(s={},a)=>{const r=E();return M(b.items.upgradeItem.mutationOptions({onSuccess:(t,n)=>{t.message==="Upgrade failed"?I.error(t.message):I.success(t.message),r.invalidateQueries({queryKey:b.user.inventory.key()}),r.invalidateQueries({queryKey:b.items.getUpgradeItems.key()});const i=t.newItemInstance||null;n.resetModal(i)},onError:t=>{const n=t.message||"Unknown error occurred";console.error("Upgrade item error:",n),I.error(n)},...s}))};function ye({openModal:s,setOpenModal:a,userInventory:r,setSelectedItem:t}){const n=C.useMemo(()=>{const d=["weapon","ranged","head","chest","hands","legs","feet"];return s==="upgradeItem"?r?.filter(m=>m?.item?.level>22&&d.includes(m?.item?.itemType))||[]:r||[]},[r,s]),i=()=>{a(!1)},o=d=>{t(d),i()};return e.jsx(Q,{showClose:!0,open:s!==!1,title:"Inventory",iconBackground:"shadow-lg",contentHeight:"max-h-[75dvh] md:max-h-[60dvh]",className:"min-w-[50dvw]! lg:min-w-[35dvw]! mx-auto",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/4AAKgyV.png",alt:"",className:"mt-0.5 h-11 w-auto"}),onOpenChange:i,children:e.jsx("div",{className:"overflow-auto! mt-3 h-full text-center md:mt-0",children:e.jsx("div",{className:"mt-2 grid grid-cols-3 gap-x-3 gap-y-4 sm:gap-x-6 md:mt-6 lg:grid-cols-5 xl:gap-x-3",children:n?.map(d=>e.jsx(C.Fragment,{children:e.jsx(Ne,{item:d,handleSelectItem:o},d.id)},d.id))})})})}const Ne=({item:s,handleSelectItem:a})=>e.jsx("div",{className:"group cursor-pointer divide-gray-200 rounded-lg shadow-sm",onClick:()=>{a(s)},children:e.jsx(J,{item:s.item,upgradeLevelCount:s.upgradeLevel})}),ve=(s={})=>O(b.items.getUpgradeItems.queryOptions({staleTime:6e4,...s}));function be({selectedUpgradeCores:s,setSelectedUpgradeCores:a,userInventory:r,disabled:t,selectedItemType:n,successRate:i}){const{data:o,isLoading:d}=ve({staleTime:Number.POSITIVE_INFINITY}),m=n==="weapon"||n==="ranged"?"Weapon Upgrade Core":"Armor Upgrade Core",x=o?.find(f=>f?.name===`Small ${m}`)||null,g=o?.find(f=>f?.name===`Medium ${m}`)||null,h=o?.find(f=>f?.name===`Large ${m}`)||null,N=o?.find(f=>f?.name===`Giant ${m}`)||null,y=C.useMemo(()=>{const f={item:x,count:0},l={item:g,count:0},c={item:h,count:0},p={item:N,count:0};return(r?.filter(j=>j?.item?.name.includes(m)&&j?.item?.itemType==="upgrade")||[])?.forEach(j=>{j?.item?.name===x?.name?f.count+=j?.count:j?.item?.name===g?.name?l.count+=j?.count:j?.item?.name===h?.name?c.count+=j?.count:j?.item?.name===N?.name&&(p.count+=j?.count)}),{smallCores:f,mediumCores:l,largeCores:c,giantCores:p}},[x,g,h,N,r,m]);if(d)return null;const w=Object.values(y).every(f=>f.count===0);return e.jsxs("div",{className:"flex flex-col gap-2.5",children:[e.jsxs("div",{className:u(s?"border-slate-600":"border-yellow-600","flex flex-col items-center gap-2 rounded-lg border border-slate-600 bg-slate-900 px-5 pt-2 pb-4 lg:px-8",t&&"border-slate-600!"),children:[e.jsx("p",{className:u("place-content-end text-center text-sm uppercase"),children:"Upgrade Cores"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(T,{text:"small",disabled:t,coreItem:y.smallCores,selectedUpgradeCores:s,setSelectedUpgradeCores:a}),e.jsx(T,{text:"medium",disabled:t,coreItem:y.mediumCores,selectedUpgradeCores:s,setSelectedUpgradeCores:a}),e.jsx(T,{text:"large",disabled:t,coreItem:y.largeCores,selectedUpgradeCores:s,setSelectedUpgradeCores:a}),e.jsx(T,{text:"giant",disabled:t,coreItem:y.giantCores,selectedUpgradeCores:s,setSelectedUpgradeCores:a})]})]}),e.jsxs("div",{className:"flex items-center justify-end gap-4 lg:items-end",children:[e.jsxs("div",{className:"rounded-lg border-gray-600 bg-slate-900 px-12 pt-1 lg:hidden",children:[e.jsx("p",{className:"text-xs uppercase",children:"Upgrade Chance"}),e.jsxs("p",{className:"text-green-500 text-lg",children:[(i*100).toFixed(0),"%"]})]}),e.jsx($,{type:"danger",className:"text-sm! h-8!",disabled:t||w,onClick:()=>a(null),children:"Reset Cores"})]})]})}const T=({coreItem:s,selectedUpgradeCores:a,setSelectedUpgradeCores:r,disabled:t,text:n})=>{if(!s)return null;const i=s?.item?.id||0,o=s?.count||0,d=a?.[n]?.count||0,m=t||o===0||d>=o,x=()=>{if(o!==0)if(a){const g={...a};if(g[n]){if(g[n]?.count>=o)return;const h=g[n]?.count+1;g[n]={id:i,count:h}}else g[n]={id:i,count:1};r(g)}else r({[n]:{id:i,count:1}})};return e.jsxs("div",{className:u("flex flex-col gap-2 text-sm",t&&"opacity-50"),children:[e.jsx("div",{className:u("gridIcon group gridIconHover col-span-2 flex size-14 cursor-pointer rounded-md bg-slate-900 p-1 text-gray-100 text-stroke-md ring-2 drop-shadow-md hover:bg-slate-600",s!==null&&"gridIconHover hover:bg-slate-600",a?"ring-indigo-600":"ring-yellow-600",t&&"ring-slate-600!"),children:t?e.jsx("div",{className:u("m-auto flex select-none flex-col text-center",t&&"opacity-50")}):e.jsxs("div",{className:"relative flex size-full cursor-pointer",children:[e.jsx("p",{className:"-bottom-1 absolute right-0 z-15 rounded-md bg-black/25 px-1 font-lili text-custom-yellow text-stroke-0",children:o}),e.jsx(S,{noBackground:!0,isClickable:!0,className:"m-auto size-16",item:s.item})]})}),e.jsx("p",{children:X(n)}),e.jsxs("div",{className:"ml-1 flex items-center justify-center",children:[e.jsxs("p",{className:"text-custom-yellow",children:[" ",o===0?0:d]}),e.jsx("div",{className:u(m?"border-gray-600 bg-gray-800 opacity-50":"border-blue-600 bg-blue-900","mx-auto flex size-7 cursor-pointer items-center justify-center rounded-lg border hover:brightness-110"),onClick:()=>x(),children:"+"})]})]})},Ce={small:1,medium:2,large:3,giant:4},we={1:10,2:25,3:40,4:80},Ie={1:16,2:40,3:80,4:128,5:200,6:320,7:520,8:800,9:1120,10:1600},ke={1:24,2:60,3:120,4:192,5:300,6:480,7:780,8:1200,9:1680,10:2400};function Se(s,a,r,t){const i=(t==="Weapon"?ke:Ie)[s];return Math.min(r/i,1)}function Te(s){if(!s)return 0;let a=0;for(const r in s){const t=s[r];if(!t||t.count<1)break;const n=Ce[r];if(!n)break;a+=we[n]*t.count}return a}function Ee({userInventory:s}){const[a,r]=C.useState(!1),[t,n]=C.useState(null),[i,o]=C.useState(null),{mutate:d}=je(),m=l=>{o(null),l&&n(l)},x=()=>{if(t&&i){const{id:l}=t;d({itemId:l,upgradeCores:i,resetModal:m})}},h=["weapon","ranged","offhand"].includes(t?.item.itemType??"")?"Weapon":"Armor",N=i?(t?.upgradeLevel??0)+1:0,y=i?Te(i):0,w=i?Se(N,t?.item.level,y,h):0,f=!w;return e.jsxs("div",{className:"mx-auto flex flex-col items-center justify-center gap-3 px-1 pb-2 text-center text-2xl text-gray-200 md:px-4 2xl:gap-6",children:[e.jsx(ye,{openModal:a,setOpenModal:r,userInventory:s,setSelectedItem:n}),e.jsxs("div",{className:"relative flex gap-24",children:[e.jsxs("div",{className:"flex flex-col items-center gap-2",children:[e.jsx("p",{className:u("place-content-end text-center text-sm uppercase"),children:"Item"}),e.jsx(q,{itemSelected:t,setOpenModal:r,filterType:"upgradeItem"})]}),e.jsxs("div",{className:"flex flex-col items-center gap-2",children:[e.jsx("p",{className:u("place-content-end text-center text-sm uppercase opacity-50"),children:"Upgrade Reagent"}),e.jsx(q,{disabled:!0,filterType:"upgradeCore"})]})]}),f&&e.jsxs("div",{className:"lg:-mt-3 lg:-mb-4 -my-1.5 flex items-center justify-center gap-1 text-sky-400 text-sm xs:text-base lg:text-lg",children:[e.jsx(Z,{}),e.jsx("p",{children:t?"Add upgrade cores":"Select an Item"})]}),e.jsx(be,{selectedUpgradeCores:i,setSelectedUpgradeCores:o,userInventory:s,disabled:!t,selectedItemType:t&&t?.item?.itemType,successRate:w}),t&&e.jsxs("div",{className:"flex w-full flex-col gap-4 border-gray-600 border-t py-2",children:[e.jsxs("div",{className:"flex h-fit justify-center gap-2.5 lg:gap-4",children:[e.jsx("div",{className:"w-[44%]",children:e.jsx(L,{item:t})}),e.jsx("div",{className:"my-auto select-none text-4xl",children:">"}),e.jsx("div",{className:"w-[44%]",children:e.jsx(L,{nextRank:!0,item:t})})]}),e.jsxs("div",{className:"mx-auto hidden rounded-lg border-gray-600 bg-slate-900 px-12 py-1 lg:block 2xl:py-2",children:[e.jsx("p",{className:"text-sm uppercase",children:"Upgrade Chance"}),e.jsxs("p",{className:"text-green-500",children:[(w*100).toFixed(0),"%"]})]}),e.jsx("button",{type:"button",disabled:f,className:u(f?"cursor-default opacity-75 grayscale":"cursor-pointer","darkBlueButtonBGSVG mx-auto flex h-16 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs 2xl:mt-3 dark:text-slate-200"),onClick:x,children:"Upgrade"})]})]})}const L=({item:s,nextRank:a=!1})=>{if(!s)return null;const r=a?s.upgradeLevel+1:s.upgradeLevel,t=R(s.item,null,r,!0),n=a?R(s.item,null,r,!0,!0):null;return e.jsxs("div",{className:"flex size-full flex-col items-center gap-2 rounded-lg border border-gray-600/75 bg-gray-800 px-3 py-1.5 text-gray-300 text-sm text-stroke-md lg:flex-row 2xl:pb-3",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center gap-0.5",children:[e.jsx("div",{className:"mx-auto flex min-w-10 max-w-14 flex-col items-center justify-center lg:flex-row",children:e.jsx(S,{height:"h-10",upgradeLevelOverride:r,item:s})}),e.jsxs("p",{className:u(a&&"text-custom-yellow","flex-1 lg:hidden"),children:[s.item.name," ",r>0&&`+${r}`]})]}),e.jsxs("div",{className:"flex w-full flex-col items-center justify-center gap-0.5 text-sm",children:[e.jsxs("p",{className:u(a&&"text-custom-yellow","hidden flex-1 lg:block"),children:[s.item.name," ",r>0&&`+${r}`]}),t.map((i,o)=>e.jsxs("p",{children:[i," ",a&&n&&e.jsx("span",{className:"ml-1 text-green-500",children:n[o]})]},i))]})]})},q=({itemSelected:s,setOpenModal:a,filterType:r,disabled:t})=>e.jsx("div",{className:u("gridIcon group gridIconHover col-span-2 flex size-20 cursor-pointer rounded-md bg-slate-900 p-1 text-gray-100 text-stroke-md ring-2 drop-shadow-md hover:bg-slate-600",s!==null?"gridIconHover ring-indigo-600 hover:bg-slate-600":"ring-yellow-600",t&&"ring-gray-600! opacity-50"),onClick:()=>t?null:a?.(r),children:s?e.jsxs("div",{className:"relative flex size-full cursor-pointer",children:[s?.upgradeLevel>0&&e.jsxs("p",{className:"absolute top-0 left-1 text-custom-yellow text-sm",children:["+",s?.upgradeLevel]}),e.jsx(S,{noBackground:!0,isClickable:!0,className:"m-auto size-16",item:s})]}):e.jsx("div",{className:u("m-auto flex select-none flex-col text-center",t&&"opacity-50"),children:e.jsx("small",{className:"self-center text-gray-400 text-lg group-hover:text-custom-yellow",children:"Empty"})})});function Me(){const[s,a]=C.useState(!1),{data:r,isLoading:t}=V();return t?e.jsx(ee,{}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"",children:e.jsx($,{variant:"primary",className:"",onClick:()=>a(!0),children:"Upgrade Item"})}),e.jsx(Q,{showClose:!0,open:s,title:"Item Upgrading",iconBackground:"shadow-lg",modalMaxWidth:"max-w-3xl!",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/cgBzeee.png",alt:"",className:"mt-0.5 h-11 w-auto"}),onOpenChange:a,children:e.jsxs("div",{className:"relative mx-auto overflow-hidden rounded-md bg-white pt-4 md:mt-3 md:w-3/4 dark:border dark:border-gray-700 dark:bg-slate-800",children:[e.jsx("div",{className:"vignette-sm pointer-events-none absolute inset-0 z-5 size-full object-cover opacity-50 lg:rounded-2xl"}),r&&e.jsx(Ee,{userInventory:r})]})})]})}function $e(){const{isLoading:s,error:a,data:r}=ie(),{data:t,isLoading:n}=V(),{data:i=[],isLoading:o,isError:d}=se(),[m,x]=C.useState("all"),[g,h]=C.useState(""),y=1+(te("multitasker")?.level||0),w=i.length>=y,f=c=>[v.weapon,v.ranged,v.offhand].includes(c)?"weapons":[v.head,v.chest,v.hands,v.legs,v.feet,v.shield].includes(c)?"armor":c===v.consumable?"consumables":c===v.quest?"quest":"misc",l=C.useMemo(()=>r?r.filter(c=>{if(!c.outputs||c.outputs.length===0)return!1;const p=c.outputs[0],k=f(p.itemType);return!(m!=="all"&&k!==m||g&&!p.name.toLowerCase().includes(g.toLowerCase()))}):[],[r,m,g]);return e.jsxs("div",{className:"gap-4 max-w-(--breakpoint-lg) mx-auto flex flex-col lg:flex-row",children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(he,{craftingQueue:i,maxCraftQueue:y,isLoading:o,isError:d}),e.jsx(Me,{})]}),e.jsxs("div",{className:"space-y-4 flex-1 min-w-0",children:[e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-1/2 -translate-y-1/2 size-4 text-gray-500"}),e.jsx("input",{type:"text",placeholder:"Search recipes...",className:"w-full bg-gray-800/50 border border-purple-900/30 rounded-lg py-2 pl-10 pr-4 text-sm text-white placeholder:text-gray-500 focus:outline-hidden focus:ring-1 focus:ring-purple-500",value:g,onChange:c=>h(c.target.value)})]}),e.jsx(ge,{activeCategory:m,onCategoryChange:x}),(s||n)&&e.jsx("div",{className:"flex flex-col items-center justify-center py-8 text-gray-500",children:e.jsx("div",{className:"animate-spin size-8 border-4 border-purple-500 border-t-transparent rounded-full mb-2"})}),a&&e.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-red-500",children:[e.jsx(P,{className:"size-8 mb-2"}),e.jsx("p",{className:"text-sm",children:"Failed to load recipes"})]}),!s&&!n&&!a&&e.jsx("div",{className:"grid grid-cols-1 gap-3",children:l.map(c=>{const p=c.outputs[0];return e.jsx(me,{recipe:c,outputItem:p,userInventory:t,isCraftQueueFull:w},c.id)})}),!s&&!n&&!a&&l.length===0&&e.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-gray-500",children:[e.jsx(ce,{className:"size-8 mb-2 opacity-50"}),e.jsx("p",{className:"text-sm",children:"No recipes found"})]})]})]})}export{$e as default};
