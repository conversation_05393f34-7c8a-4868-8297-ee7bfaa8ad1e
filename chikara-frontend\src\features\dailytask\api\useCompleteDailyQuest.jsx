import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useCompleteDailyQuest = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.dailyQuest.completeDailyQuest.mutationOptions({
            onSuccess: () => {
                toast.success("Task completed");
                queryClient.invalidateQueries({
                    queryKey: orpc.dailyQuest.getDailyQuests.key(),
                });
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};
