{"version": 3, "sources": ["../../../../node_modules/@orpc/client/dist/index.mjs"], "sourcesContent": ["import { i as isDefinedError } from './shared/client.DHOfWE0c.mjs';\nexport { C as COMMON_ORPC_ERROR_DEFS, O as ORPCError, d as createORPCError<PERSON><PERSON><PERSON>son, a as fallbackORPCErrorMessage, f as fallbackORPCErrorStatus, c as isOR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, b as isORPCErrorStatus, m as mapEventIterator, t as toORPCError } from './shared/client.DHOfWE0c.mjs';\nexport { EventPublisher, onError, onFinish, onStart, onSuccess } from '@orpc/shared';\nexport { ErrorEvent } from '@orpc/standard-server';\n\nasync function safe(promise) {\n  try {\n    const output = await promise;\n    return Object.assign(\n      [null, output, false, true],\n      { error: null, data: output, isDefined: false, isSuccess: true }\n    );\n  } catch (e) {\n    const error = e;\n    if (isDefinedError(error)) {\n      return Object.assign(\n        [error, void 0, true, false],\n        { error, data: void 0, isDefined: true, isSuccess: false }\n      );\n    }\n    return Object.assign(\n      [error, void 0, false, false],\n      { error, data: void 0, isDefined: false, isSuccess: false }\n    );\n  }\n}\nfunction resolveFriendlyClientOptions(options) {\n  return {\n    ...options,\n    context: options.context ?? {}\n    // Context only optional if all fields are optional\n  };\n}\n\nfunction createORPCClient(link, options) {\n  const path = options?.path ?? [];\n  const procedureClient = async (...[input, options2 = {}]) => {\n    return await link.call(path, input, resolveFriendlyClientOptions(options2));\n  };\n  const recursive = new Proxy(procedureClient, {\n    get(target, key) {\n      if (typeof key !== \"string\") {\n        return Reflect.get(target, key);\n      }\n      return createORPCClient(link, {\n        ...options,\n        path: [...path, key]\n      });\n    }\n  });\n  return recursive;\n}\n\nclass DynamicLink {\n  constructor(linkResolver) {\n    this.linkResolver = linkResolver;\n  }\n  async call(path, input, options) {\n    const resolvedLink = await this.linkResolver(options, path, input);\n    const output = await resolvedLink.call(path, input, options);\n    return output;\n  }\n}\n\nexport { DynamicLink, createORPCClient, isDefinedError, resolveFriendlyClientOptions, safe };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAKA,eAAe,KAAK,SAAS;AAC3B,MAAI;AACF,UAAM,SAAS,MAAM;AACrB,WAAO,OAAO;AAAA,MACZ,CAAC,MAAM,QAAQ,OAAO,IAAI;AAAA,MAC1B,EAAE,OAAO,MAAM,MAAM,QAAQ,WAAW,OAAO,WAAW,KAAK;AAAA,IACjE;AAAA,EACF,SAAS,GAAG;AACV,UAAM,QAAQ;AACd,QAAI,eAAe,KAAK,GAAG;AACzB,aAAO,OAAO;AAAA,QACZ,CAAC,OAAO,QAAQ,MAAM,KAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,QAAQ,WAAW,MAAM,WAAW,MAAM;AAAA,MAC3D;AAAA,IACF;AACA,WAAO,OAAO;AAAA,MACZ,CAAC,OAAO,QAAQ,OAAO,KAAK;AAAA,MAC5B,EAAE,OAAO,MAAM,QAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,IAC5D;AAAA,EACF;AACF;AACA,SAAS,6BAA6B,SAAS;AAC7C,SAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS,QAAQ,WAAW,CAAC;AAAA;AAAA,EAE/B;AACF;AAEA,SAAS,iBAAiB,MAAM,SAAS;AACvC,QAAM,OAAO,SAAS,QAAQ,CAAC;AAC/B,QAAM,kBAAkB,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,MAAM;AAC3D,WAAO,MAAM,KAAK,KAAK,MAAM,OAAO,6BAA6B,QAAQ,CAAC;AAAA,EAC5E;AACA,QAAM,YAAY,IAAI,MAAM,iBAAiB;AAAA,IAC3C,IAAI,QAAQ,KAAK;AACf,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,QAAQ,IAAI,QAAQ,GAAG;AAAA,MAChC;AACA,aAAO,iBAAiB,MAAM;AAAA,QAC5B,GAAG;AAAA,QACH,MAAM,CAAC,GAAG,MAAM,GAAG;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,MAAM,KAAK,MAAM,OAAO,SAAS;AAC/B,UAAM,eAAe,MAAM,KAAK,aAAa,SAAS,MAAM,KAAK;AACjE,UAAM,SAAS,MAAM,aAAa,KAAK,MAAM,OAAO,OAAO;AAC3D,WAAO;AAAA,EACT;AACF;", "names": []}