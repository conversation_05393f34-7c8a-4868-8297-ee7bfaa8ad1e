import { getNextSundayMidnight } from "@/helpers/dateHelpers";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { Heart } from "lucide-react";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { CountdownTimer } from "../components/Layout/CountdownTimer";
import { ShopBuyItems } from "../features/shop/components/ShopBuyItems";
import { ShopSellItems } from "../features/shop/components/ShopSellItems";
import SingleShopkeeper from "../features/shop/components/SingleShopkeeper";

function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
}

export default function SingleShop() {
    const { shopID } = useParams();
    const [sellOrBuyTab, setSellOrBuyTab] = useState("Buy");
    const [currentHeartTab, setCurrentHeartTab] = useState(0);
    const { LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT } = useGameConfig("shopConfig");

    const { data: traderRep } = useQuery(
        api.shops.getTraderRep.queryOptions({
            input: { shopId: Number.parseInt(shopID) },
            enabled: !!shopID && !isNaN(Number.parseInt(shopID)) && Number.parseInt(shopID) > 0,
        })
    );
    const { data: shopInfo, isLoading } = useQuery(
        api.shops.shopInfo.queryOptions({
            input: { shopId: Number.parseInt(shopID) },
            enabled: !!shopID && !isNaN(Number.parseInt(shopID)) && Number.parseInt(shopID) > 0,
        })
    );
    const { data: currentUser } = useFetchCurrentUser();
    const repLevel = traderRep?.reputationLevel || 0;

    const allTabFilter = shopInfo?.shop_listing.filter((listing) => listing?.repRequired <= repLevel);

    const filteredListings = shopInfo?.shop_listing.filter((listing) => listing?.repRequired === currentHeartTab);
    const shopData = {
        ...shopInfo,
        shop_listing: currentHeartTab === 0 ? allTabFilter : filteredListings,
    };

    const tabs = [
        {
            heart: false,
            current: currentHeartTab === 0,
            amount: 0,
            disabled: false,
        },
        {
            heart: true,
            current: currentHeartTab === 1,
            amount: 1,
            disabled: !repLevel || repLevel < 1 || shopInfo?.id === 6,
        },
        {
            heart: true,
            current: currentHeartTab === 2,
            amount: 2,
            disabled: !repLevel || repLevel < 2 || shopInfo?.id === 6,
        },
        {
            heart: true,
            current: currentHeartTab === 3,
            amount: 3,
            disabled: !repLevel || repLevel < 3 || shopInfo?.id === 6,
        },
        {
            heart: true,
            current: currentHeartTab === 4,
            amount: 4,
            disabled: true,
        },
    ];

    const buyOrSellTabs = [
        { name: "Buy", current: sellOrBuyTab === "Buy" },
        { name: "Sell", current: sellOrBuyTab === "Sell" },
    ];

    function isSundayShopOpen() {
        if (shopData.id === 6 && shopData.disabled) return false;
        return true;
    }

    if (isLoading) return "Loading..";
    if (shopInfo?.id === 6 && !isSundayShopOpen() && currentUser?.userType !== "admin")
        return <p className="mt-2 text-center text-lg">Shop is closed until 12:00 Sunday</p>;
    return (
        <main className="-mx-4 md:-mt-8 flex-1 pb-8">
            <div className="mt-8">
                <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
                    <div className="mt-2 grid grid-cols-1 py-1 sm:grid-cols-2 md:mt-4 md:py-0 lg:grid-cols-3">
                        <SingleShopkeeper
                            singleShop={shopData}
                            setSellOrBuyTab={setSellOrBuyTab}
                            sellOrBuyTab={sellOrBuyTab}
                            cash={currentUser?.cash.toString()}
                        />
                        <div className="col-span-2 flex flex-col">
                            <div className="h-full border bg-white md:rounded-t-lg dark:border-gray-600 dark:bg-gray-800">
                                {/* BUY OR SELL TABS MOBILE */}
                                <div className="block sm:hidden">
                                    <nav
                                        className="relative z-0 flex divide-x divide-gray-200 overflow-hidden shadow-sm dark:divide-gray-600"
                                        aria-label="Tabs"
                                    >
                                        {buyOrSellTabs.map((tab, tabIdx) => (
                                            <button
                                                key={tab.name}
                                                aria-current={tab.current ? "page" : undefined}
                                                data-testid={
                                                    tab.name === "Buy"
                                                        ? "buy-item-button-mobile"
                                                        : "sell-item-button-mobile"
                                                }
                                                className={classNames(
                                                    tab.current ? "text-gray-900" : "text-gray-500",
                                                    tabIdx === 0 ? "" : "",
                                                    tabIdx === buyOrSellTabs.length - 1 ? "" : "",
                                                    "group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-2.5 text-center font-medium text-xl focus:z-10 dark:bg-gray-900 dark:text-white"
                                                )}
                                                onClick={() => {
                                                    setSellOrBuyTab(tab.name);
                                                }}
                                            >
                                                <span>{tab.name}</span>
                                                <span
                                                    aria-hidden="true"
                                                    className={classNames(
                                                        tab.current ? "bg-indigo-400" : "bg-gray-700",
                                                        "absolute inset-x-0 bottom-0 h-[0.15rem]"
                                                    )}
                                                />
                                            </button>
                                        ))}
                                    </nav>
                                </div>

                                {/* HEART TABS */}
                                <div>
                                    {/* HEART TABS MOBILE */}
                                    <div
                                        className={classNames("md:hidden", sellOrBuyTab === "Buy" ? "block" : "hidden")}
                                    >
                                        <nav
                                            className="relative z-0 flex divide-x divide-gray-200 border-t shadow-sm dark:divide-gray-600 dark:border-gray-700"
                                            aria-label="Tabs"
                                        >
                                            {tabs.map((tab, tabIdx) => (
                                                <button
                                                    key={tab.amount}
                                                    aria-current={tab.current ? "page" : undefined}
                                                    className={classNames(
                                                        tabIdx === 0 ? "" : "",
                                                        tab.disabled
                                                            ? "cursor-default bg-gray-100 text-gray-600 dark:bg-slate-700 dark:text-gray-500"
                                                            : "bg-white text-gray-900 hover:bg-gray-50 dark:bg-slate-800 dark:text-gray-200",
                                                        "group relative flex min-w-0 flex-1 overflow-hidden p-4 text-center font-medium text-sm focus:z-10"
                                                    )}
                                                    onClick={() => {
                                                        if (!tab.disabled) {
                                                            setCurrentHeartTab(tab.amount);
                                                        }
                                                    }}
                                                >
                                                    {tab.heart ? (
                                                        <div className="mx-auto flex flex-row gap-1">
                                                            <span className="text-lg">{tab.amount}</span>
                                                            <Heart
                                                                key={tab.id}
                                                                className={`w-6 stroke-2 drop-shadow-md ${
                                                                    tab.disabled
                                                                        ? `stroke-gray-600 text-gray-600 opacity-50 dark:stroke-gray-800`
                                                                        : `text-pink-600 dark:stroke-black`
                                                                }`}
                                                            />
                                                        </div>
                                                    ) : (
                                                        <span className="mx-auto text-lg">All</span>
                                                    )}

                                                    <span
                                                        aria-hidden="true"
                                                        className={classNames(
                                                            tab.current ? "bg-indigo-500" : "bg-gray-700",
                                                            "absolute inset-x-0 bottom-0 h-1"
                                                        )}
                                                    />
                                                </button>
                                            ))}
                                        </nav>
                                    </div>

                                    <div className="hidden md:block">
                                        {/* HEART TABS DESKTOP */}
                                        <nav
                                            className="relative z-0 flex divide-x divide-gray-200 shadow-sm dark:divide-gray-600"
                                            aria-label="Tabs"
                                        >
                                            {tabs.map((tab, tabIdx) => (
                                                <button
                                                    key={tab.amount}
                                                    aria-current={tab.current ? "page" : undefined}
                                                    className={classNames(
                                                        tab.current ? "" : "",
                                                        tabIdx === 0 ? "rounded-tl-lg" : "",
                                                        tabIdx === tabs.length - 1 ? "rounded-tr-lg" : "",
                                                        tab.disabled
                                                            ? "cursor-default bg-gray-100 dark:bg-slate-700"
                                                            : "bg-white hover:bg-gray-50 dark:bg-slate-800",
                                                        "group relative flex min-w-0 flex-1 overflow-hidden p-4 text-center font-medium text-sm focus:z-10"
                                                    )}
                                                    onClick={() => {
                                                        if (!tab.disabled) {
                                                            setCurrentHeartTab(tab.amount);
                                                        }
                                                    }}
                                                >
                                                    {tab.heart ? (
                                                        <div className="mx-auto flex flex-row">
                                                            {[...Array(tab.amount)].map((i) => (
                                                                <Heart
                                                                    key={tab.id}
                                                                    className={`w-6 stroke-2 drop-shadow-md ${
                                                                        tab.disabled
                                                                            ? `stroke-gray-800 text-gray-600 opacity-50`
                                                                            : `stroke-black text-pink-600`
                                                                    }`}
                                                                />
                                                            ))}
                                                        </div>
                                                    ) : (
                                                        <span className="mx-auto text-gray-200 text-lg text-stroke-sm">
                                                            All
                                                        </span>
                                                    )}

                                                    <span
                                                        aria-hidden="true"
                                                        className={classNames(
                                                            tab.current ? "bg-indigo-500" : "bg-gray-700",
                                                            "absolute inset-x-0 bottom-0 h-1"
                                                        )}
                                                    />
                                                </button>
                                            ))}
                                        </nav>
                                    </div>
                                </div>

                                <div className="flex flex-col pt-1">
                                    {/* Add filters here */}
                                    {shopInfo?.id === 6 && (
                                        <>
                                            {" "}
                                            <div className="flex flex-col">
                                                <p className="mx-auto text-orange-500">
                                                    This shop contains limited stock.
                                                </p>
                                                <p className="mx-auto text-custom-yellow">
                                                    Personal buy limit:{" "}
                                                    <span
                                                        className={cn(
                                                            currentUser?.weeklyBuyLimitRemaining > 0
                                                                ? "text-custom-yellow"
                                                                : "text-red-500"
                                                        )}
                                                    >
                                                        {currentUser?.weeklyBuyLimitRemaining}
                                                    </span>
                                                    /{LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT}
                                                </p>
                                            </div>
                                            <div className="-mb-4 mt-2 mr-3 ml-auto flex flex-row gap-1 text-base text-blue-500 md:mt-0">
                                                <p>Shop closes in:</p>
                                                <CountdownTimer
                                                    showHours
                                                    targetDate={getNextSundayMidnight()}
                                                    showSeconds={false}
                                                />
                                            </div>
                                        </>
                                    )}
                                </div>

                                {/* here */}
                                {sellOrBuyTab === "Buy" ? (
                                    <ShopBuyItems shopData={shopData} currentUser={currentUser} />
                                ) : (
                                    <ShopSellItems currentUser={currentUser} />
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    );
}
