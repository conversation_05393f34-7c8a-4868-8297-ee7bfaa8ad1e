import { useQuery } from "@tanstack/react-query";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { api } from "@/helpers/api";

export const useIsBuffActive = (buffName: string, options: QueryOptions = {}) => {
    return useQuery(
        api.shrine.isBuffActive.queryOptions({
            input: { buffName },
            staleTime: 60000, // 1 minute
            enabled: !!buffName,
            ...options,
        })
    );
};
