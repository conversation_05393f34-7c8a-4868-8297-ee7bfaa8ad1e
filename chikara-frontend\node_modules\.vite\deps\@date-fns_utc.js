import "./chunk-G3PMV62Z.js";

// ../node_modules/@date-fns/utc/date/mini.js
var UTCDateMini = class extends Date {
  constructor() {
    super();
    this.setTime(arguments.length === 0 ? (
      // Enables Sinon's fake timers that override the constructor
      Date.now()
    ) : arguments.length === 1 ? typeof arguments[0] === "string" ? +new Date(arguments[0]) : arguments[0] : Date.UTC(...arguments));
  }
  getTimezoneOffset() {
    return 0;
  }
};
var re = /^(get|set)(?!UTC)/;
Object.getOwnPropertyNames(Date.prototype).forEach((method) => {
  if (re.test(method)) {
    const utcMethod = Date.prototype[method.replace(re, "$1UTC")];
    if (utcMethod) UTCDateMini.prototype[method] = utcMethod;
  }
});

// ../node_modules/@date-fns/utc/date/index.js
var UTCDate = class extends UTCDateMini {
  toString() {
    const date = this.toDateString();
    const time = this.toTimeString();
    return `${date} ${time}`;
  }
  toDateString() {
    const weekday = weekdayFormat.format(this);
    const date = dateFormat.format(this);
    const year = this.getFullYear();
    return `${weekday} ${date} ${year}`;
  }
  toTimeString() {
    const time = timeFormat.format(this);
    return `${time} GMT+0000 (Coordinated Universal Time)`;
  }
  toLocaleString(locales, options) {
    return Date.prototype.toLocaleString.call(this, locales, {
      timeZone: "UTC",
      ...options
    });
  }
  toLocaleDateString(locales, options) {
    return Date.prototype.toLocaleDateString.call(this, locales, {
      timeZone: "UTC",
      ...options
    });
  }
  toLocaleTimeString(locales, options) {
    return Date.prototype.toLocaleTimeString.call(this, locales, {
      timeZone: "UTC",
      ...options
    });
  }
};
var weekdayFormat = new Intl.DateTimeFormat("en-US", {
  weekday: "short",
  timeZone: "UTC"
});
var dateFormat = new Intl.DateTimeFormat("en-US", {
  month: "short",
  day: "numeric",
  timeZone: "UTC"
});
var timeFormat = new Intl.DateTimeFormat("en-GB", {
  hour12: false,
  hour: "numeric",
  minute: "numeric",
  second: "numeric",
  timeZone: "UTC"
});

// ../node_modules/@date-fns/utc/utc/index.js
var utc = (value) => new UTCDate(+new Date(value));
export {
  UTCDate,
  UTCDateMini,
  utc
};
//# sourceMappingURL=@date-fns_utc.js.map
