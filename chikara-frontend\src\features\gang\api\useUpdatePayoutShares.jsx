import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useUpdatePayoutShares = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.updatePayoutShares.mutationOptions({
            onSuccess: () => {
                toast.success(`Payout shares updated successfully!`);
                queryClient.invalidateQueries({
                    queryKey: api.gang.getCurrentGang.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.gang.getMemberShares.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    const updatePayoutShares = (shares) => {
        if (!shares || !Array.isArray(shares)) {
            toast.error("Invalid shares data");
            return;
        }

        mutation.mutate({ shares });
    };

    return {
        updatePayoutShares,
        isLoading: mutation.isPending,
    };
};

export default useUpdatePayoutShares;
