import { api } from "@/helpers/api";
import { APIROUTES } from "@/helpers/apiRoutes";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useStartMission = (options = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.missions.startMission.mutationOptions({
            onSuccess: () => {
                // Invalidate user info to update current mission status
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });

                // Invalidate mission queries
                queryClient.invalidateQueries({
                    queryKey: api.missions.missionList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.missions.currentMission.key(),
                });

                toast.success("Mission Started!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
            ...options,
        })
    );
};
