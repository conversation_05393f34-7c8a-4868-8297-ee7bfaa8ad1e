/* eslint-disable no-else-return */

import http from "node:http";
import https from "node:https";
import { initializeFirebase } from "./config/firebase.js";
import gameConfig from "./config/gameConfig.js";
import { configureGracefulShutdown } from "./config/gracefulShutdown.js";
import logger from "./config/logger.js";
import { redisClient } from "./config/redisClient.js";
import { setupSockets } from "./config/socket.js";
import { initializeGameEventSystem } from "./core/events/index.js";
import { setupAuthRoutes } from "./lib/auth.js";
import { clearAllCache } from "./lib/cache/cacheUtility.js";
import { createContext, orpcLogError } from "./lib/orpc.js";
import errorHandler from "./middleware/errorHandler.js";
import maintenanceMiddleware, { maintenanceState } from "./middleware/maintenanceMiddleware.js";
import globalRateLimit from "./middleware/rateLimitMiddleware.js";
import sessionMiddleware from "./middleware/sessionMiddleware.js";
import { updateLastActivity } from "./middleware/userMiddleware.js";
import { appRouter } from "./routes.js";
import { initializeScheduler } from "./queues/scheduler/scheduler.js";
import { RPCHandler } from "@orpc/server/node";
import { onError } from "@orpc/server";
import cors from "cors";
import express from "express";
import { SimpleCsrfProtectionHandlerPlugin } from "@orpc/server/plugins";

// --- Configuration Constants ---
const IS_DEBUG = process.env.LOG_LEVEL === "debug";
const IS_PROXY_ENABLED = process.env.PROXY_ENABLED === "true";
const IS_LOCALHOST = process.env.NODE_ENV === "development";
const PORT = Number(process.env.PORT) || 3000;
const ALLOWED_ORIGINS = process.env.ALLOWED_ORIGINS;

const HEADERS = {
    "Strict-Transport-Security": "max-age=15552000; includeSubDomains",
    "X-Frame-Options": "SAMEORIGIN",
    "Referrer-Policy": "no-referrer",
    "Cross-Origin-Resource-Policy": "cross-origin",
    "Content-Type": "application/json",
    "Access-Control-Expose-Headers": "x-version",
    "x-version": String(gameConfig.version),
};

// --- Helper Functions ---
const requestProfilingMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
    const correlationId = `[${req.method}][${req.url}]:${Date.now()}`;
    logger.profile(correlationId);
    res.on("finish", () => logger.profile(correlationId));
    next();
};

const globalHeadersMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
    res.set(HEADERS);
    next();
};

// --- Express Application Setup ---
const app = express();

// --- Middleware ---
// Order is important here

app.use(
    cors({
        origin: ALLOWED_ORIGINS ? ALLOWED_ORIGINS.split(",") : true,
        optionsSuccessStatus: 204,
        credentials: true,
    })
);

const handler = new RPCHandler(appRouter, {
    interceptors: [onError(orpcLogError)],
    strictGetMethodPluginEnabled: false,
    plugins: [new SimpleCsrfProtectionHandlerPlugin()],
});
app.use("/rpc{*path}", async (req, res, next) => {
    const { matched } = await handler.handle(req, res, {
        prefix: "/rpc",
        context: await createContext({ req }),
    });
    if (matched) return;
    next();
});

app.disable("x-powered-by"); // Security: Disable X-Powered-By header

if (IS_PROXY_ENABLED) {
    app.set("trust proxy", 1);
}

app.use(globalHeadersMiddleware);
app.use(express.static("public")); // Serve static files

if (IS_DEBUG && process.env.REQUEST_PROFILING === "true") {
    app.use(requestProfilingMiddleware);
}

// Apply standard rate limiting to all requests
if (!IS_LOCALHOST) {
    app.use(globalRateLimit);
}

setupAuthRoutes(app); //Auth routes before body parsing

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.use(sessionMiddleware);

// Conditionally apply maintenance middleware only when it's active
app.use((req, res, next) => {
    if (maintenanceState.isEnabled) {
        return maintenanceMiddleware(req, res, next);
    }
    next();
});

// app.use("/", routes);
app.use((req: express.Request, res: express.Response, next: express.NextFunction) => {
    //put this after all the routes.
    updateLastActivity(req);
    next();
});

initializeFirebase();
app.use(errorHandler as express.ErrorRequestHandler); // Error handler should be last middleware

// --- Health Check Endpoint ---
app.get("/live", (req: express.Request, res: express.Response) => {
    res.send({ uptime: Math.trunc(process.uptime()) });
});

// --- Server Startup ---

async function createServer(): Promise<http.Server | https.Server> {
    if (IS_PROXY_ENABLED) {
        return http.createServer(app);
    } else {
        const { credentials } = await import("./config/getSSLCerts.js");
        return https.createServer(credentials, app);
    }
}

async function startServer(): Promise<void> {
    try {
        logger.profile("startup");

        await Promise.all([initializeScheduler(), redisClient.connect(), initializeGameEventSystem()]);

        // Invalidate all redis model cache on startup
        await clearAllCache().catch((error) => {
            logger.error("Failed to clear Redis cache during initialization: ", error);
        });

        const mainServer = await createServer();
        setupSockets(mainServer);
        mainServer.listen(PORT, () => {
            logger.info(`Server is running on port ${PORT}`);
            logger.profile("startup");
        });

        configureGracefulShutdown(mainServer, IS_LOCALHOST);

        let redirectServer: http.Server | undefined;
        // Separate HTTP server for redirect (only in production/staging and non-proxy)
        if (!IS_LOCALHOST && !IS_PROXY_ENABLED) {
            redirectServer = http.createServer((req, res) => {
                // Simple redirect to HTTPS
                res.writeHead(301, { Location: "https://" + req.headers.host + req.url });
                res.end();
            });
            redirectServer.listen(80, () => {
                logger.info("HTTP server listening on port 80 (redirecting to HTTPS)");
            });

            // Graceful shutdown for redirect server with shorter timeout
            if (redirectServer) {
                configureGracefulShutdown(redirectServer, IS_LOCALHOST, 5000);
            }
        }
    } catch (error: unknown) {
        logger.error("Failed to start server:", error instanceof Error ? error : new Error(String(error)));
        process.exitCode = 1;
    }
}

await startServer();
