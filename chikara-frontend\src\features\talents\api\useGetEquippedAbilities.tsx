import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

export const useGetEquippedAbilities = () => {
    const queryResponse = useQuery(api.talents.getEquippedAbilities.queryOptions());

    const data = [
        queryResponse.data?.talent_equippedAbility1,
        queryResponse.data?.talent_equippedAbility2,
        queryResponse.data?.talent_equippedAbility3,
        queryResponse.data?.talent_equippedAbility4,
    ];

    return { isLoading: queryResponse.isLoading, isError: queryResponse.isError, data };
};

export default useGetEquippedAbilities;
