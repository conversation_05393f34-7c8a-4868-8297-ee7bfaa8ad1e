/*!
 * surveyjs - Survey JavaScript library v2.2.4
 * Copyright (c) 2015-2025 Devsoft Baltic OÜ  - http://surveyjs.io/
 * License: MIT (http://www.opensource.org/licenses/mit-license.php)
 *//*!*********************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].use[2]!./src/default-theme/default.scss ***!
  \*********************************************************************************************************************************************************************************/@font-face{font-family:Open Sans;font-style:normal;font-weight:400;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSKmu1aB.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSumu1aB.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSOmu1aB.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSymu1aB.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS2mu1aB.woff2) format("woff2");unicode-range:U+0590-05FF,U+200C-2010,U+20AA,U+25CC,U+FB1D-FB4F}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSCmu1aB.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSGmu1aB.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-muw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSKmu1aB.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSumu1aB.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSOmu1aB.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSymu1aB.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS2mu1aB.woff2) format("woff2");unicode-range:U+0590-05FF,U+200C-2010,U+20AA,U+25CC,U+FB1D-FB4F}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSCmu1aB.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSGmu1aB.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-muw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSKmu1aB.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSumu1aB.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSOmu1aB.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSymu1aB.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS2mu1aB.woff2) format("woff2");unicode-range:U+0590-05FF,U+200C-2010,U+20AA,U+25CC,U+FB1D-FB4F}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSCmu1aB.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTSGmu1aB.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;font-stretch:100%;src:url(https://fonts.gstatic.com/s/opensans/v34/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-muw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}:root{--font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif}:root{--sjs-transition-duration: .15s}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes changeHeight{0%{height:var(--animation-height-from)}to{height:var(--animation-height-to)}}@keyframes moveInWithOverflow{0%{overflow:hidden;height:var(--animation-height-from);min-height:var(--animation-height-from);margin-top:0;margin-bottom:0;padding-top:0;padding-bottom:0;border-top-width:0;border-bottom-width:0}99%{overflow:hidden;margin-top:var(--animation-margin-top);margin-bottom:var(--animation-margin-bottom);padding-top:var(--animation-padding-top);padding-bottom:var(--animation-padding-bottom);border-top-width:var(--animation-border-top-width);border-bottom-width:var(--animation-border-bottom-width);height:var(--animation-height-to);min-height:var(--animation-height-to)}to{overflow:visible;margin-top:var(--animation-margin-top);margin-bottom:var(--animation-margin-bottom);padding-top:var(--animation-padding-top);padding-bottom:var(--animation-padding-bottom);border-top-width:var(--animation-border-top-width);border-bottom-width:var(--animation-border-bottom-width);height:var(--animation-height-to);min-height:var(--animation-height-to)}}@keyframes moveIn{0%{height:0}to{height:var(--animation-height)}}@keyframes paddingFadeIn{0%{padding-top:0;padding-bottom:0}to{padding-bottom:var(--animation-padding-bottom);padding-top:var(--animation-padding-top)}}.sv-action-bar{display:flex;box-sizing:content-box;position:relative;align-items:center;margin-left:auto;overflow:hidden;white-space:nowrap}.sv-action-bar-separator{display:inline-block;width:1px;height:24px;vertical-align:middle;margin-right:16px;background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sv-action-bar--default-size-mode .sv-action-bar-separator{margin:0 var(--sjs-base-unit, var(--base-unit, 8px))}.sv-action-bar--small-size-mode .sv-action-bar-separator{margin:0 calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-action-bar-item{-webkit-appearance:none;-moz-appearance:none;appearance:none;display:flex;padding:var(--sjs-base-unit, var(--base-unit, 8px));box-sizing:border-box;border:none;border-radius:calc(.5 * (var(--sjs-corner-radius, 4px)));background-color:transparent;color:var(--sjs-general-forecolor, var(--foreground, #161616));cursor:pointer;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));overflow-x:hidden;white-space:nowrap}button.sv-action-bar-item{overflow:hidden}.sv-action-bar--default-size-mode .sv-action-bar-item{height:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));margin:0 var(--sjs-base-unit, var(--base-unit, 8px))}.sv-action-bar--small-size-mode .sv-action-bar-item{height:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));font-size:calc(.75 * (var(--sjs-font-size, 16px)));line-height:var(--sjs-font-size, 16px);margin:0 calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-action:first-of-type .sv-action-bar-item{margin-inline-start:0}.sv-action:last-of-type .sv-action-bar-item{margin-inline-end:0}.sv-action-bar--default-size-mode .sv-action-bar-item__title--with-icon{margin-inline-start:var(--sjs-base-unit, var(--base-unit, 8px))}.sv-action-bar--small-size-mode .sv-action-bar-item__title--with-icon{margin-inline-start:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-action-bar-item__icon svg{display:block}.sv-action-bar-item__icon use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-action-bar-item:hover,.sv-action-bar-item:focus{outline:none;background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sv-action-bar-item:active,.sv-action-bar-item.svc-toolbar__item--pressed{opacity:.5}.sv-action-bar-item.svc-toolbar__item--active{outline:none}.sv-action-bar-item:disabled{opacity:.25;cursor:default}.sv-action-bar-item__title{color:inherit;vertical-align:middle;white-space:nowrap}.sv-action-bar-item--secondary .sv-action-bar-item__icon use{fill:var(--sjs-secondary-backcolor, var(--secondary, #ff9814))}.sv-action-bar-item--active .sv-action-bar-item__icon use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sv-action-bar-item-dropdown{-webkit-appearance:none;-moz-appearance:none;appearance:none;display:flex;height:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:var(--sjs-base-unit, var(--base-unit, 8px));box-sizing:border-box;border:none;border-radius:calc(.5 * (var(--sjs-corner-radius, 4px)));background-color:transparent;cursor:pointer;line-height:calc(1.5 * (var(--sjs-font-size, 16px)));font-size:var(--sjs-font-size, 16px);font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)))}.sv-expand-action:before{content:"";display:inline-block;background-image:url("data:image/svg+xml,%3C%3Fxml version=%271.0%27 encoding=%27utf-8%27%3F%3E%3C%21-- Generator: Adobe Illustrator 21.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0%29 --%3E%3Csvg version=%271.1%27 id=%27Layer_1%27 xmlns=%27http://www.w3.org/2000/svg%27 xmlns:xlink=%27http://www.w3.org/1999/xlink%27 x=%270px%27 y=%270px%27 viewBox=%270 0 10 10%27 style=%27enable-background:new 0 0 10 10;%27 xml:space=%27preserve%27%3E%3Cstyle type=%27text/css%27%3E .st0%7Bfill:%23404040;%7D%0A%3C/style%3E%3Cpolygon class=%27st0%27 points=%272,2 0,4 5,9 10,4 8,2 5,5 %27/%3E%3C/svg%3E%0A");background-repeat:no-repeat;background-position:center center;height:10px;width:12px;margin:auto 8px}.sv-expand-action--expanded:before{transform:rotate(180deg)}.sv-dots{width:48px}.sv-dots__item{width:100%}.sv-dots__item .sv-action-bar-item__icon{margin:auto}.sv-action--hidden{width:0px;height:0px;overflow:hidden;visibility:hidden}.sv-action--hidden .sv-action__content{min-width:fit-content}.sv-action__content{display:flex;flex-direction:row;align-items:center}.sv-action__content>*{flex:0 0 auto}.sv-action--space{margin-left:auto}.sv-action-bar-item--pressed:not(.sv-action-bar-item--active){background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));opacity:50%}.sv-dragged-element-shortcut{height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-width:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:calc(4.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));background-color:var(--sjs-general-backcolor, var(--background, #fff));padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));cursor:grabbing;position:absolute;z-index:10000;box-shadow:0 8px 16px #0000001a;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-size, 16px);padding-left:calc(2.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));line-height:calc(1.5 * (var(--sjs-font-size, 16px)))}.sv-matrixdynamic__drag-icon{padding-top:calc(1.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-matrixdynamic__drag-icon:after{content:" ";display:block;height:calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(2.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));border:1px solid #e7e7e7;box-sizing:border-box;border-radius:calc(1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));cursor:move;margin-top:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-matrixdynamic-dragged-row{cursor:grabbing;position:absolute;z-index:10000;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)))}.sv-matrixdynamic-dragged-row .sd-table__row{box-shadow:var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1));background-color:var(--sjs-general-backcolor, var(--background, #fff));display:flex;flex-grow:0;flex-shrink:0;align-items:center;line-height:0}.sv-matrixdynamic-dragged-row .sd-table__cell.sd-table__cell--drag>div{background-color:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));min-height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-table__cell--header.sd-table__cell--drag,.sd-table__cell.sd-table__cell--drag{padding-right:0;padding-left:0}.sd-question--mobile .sd-table__cell--header.sd-table__cell--drag,.sd-question--mobile .sd-table__cell.sd-table__cell--drag{display:none}.sv-matrix-row--drag-drop-ghost-mod td{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sv-matrix-row--drag-drop-ghost-mod td>*{visibility:hidden}.sv-drag-drop-choices-shortcut{cursor:grabbing;position:absolute;z-index:10000;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));min-width:100px;max-width:400px}.sv-drag-drop-choices-shortcut .sv-ranking-item{height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-drag-drop-choices-shortcut .sv-ranking-item .sv-ranking-item__text .sv-string-viewer,.sv-drag-drop-choices-shortcut .sv-ranking-item .sv-ranking-item__text .sv-string-editor{overflow:hidden;white-space:nowrap}.sv-drag-drop-choices-shortcut__content.sv-drag-drop-choices-shortcut__content{min-width:100px;box-shadow:var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1));background-color:var(--sjs-general-backcolor, var(--background, #fff));border-radius:calc(4.5 * var(--sjs-base-unit, var(--base-unit, 8px)));padding-right:calc(2 * var(--sjs-base-unit, var(--base-unit, 8px)));margin-left:0}.sv-drag-drop-image-picker-shortcut{cursor:grabbing;position:absolute;z-index:10000;box-shadow:var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1));background-color:var(--sjs-general-backcolor, var(--background, #fff));padding:calc(.5 * var(--sjs-base-unit, var(--base-unit, 8px)));border-radius:calc(.5 * var(--sjs-base-unit, var(--base-unit, 8px)))}sv-popup{display:block;position:absolute}.sv-popup{position:fixed;left:0;top:0;width:100vw;outline:none;z-index:2000;height:100vh}.sv-dropdown-popup,.sv-popup.sv-popup-inner{height:0}.sv-popup-inner>.sv-popup__container{margin-top:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-list__item--with-icon .sv-popup-inner>.sv-popup__container{margin-top:calc(-.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-popup--menu-popup>.sv-popup__container{background:var(--lbr-popup-menu-background-color-global, var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3)));border-radius:var(--lbr-popup-menu-corner-radius, var(--sjs-corner-radius, 4px));box-shadow:var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1))}.sv-popup--menu-popup>.sv-popup__container>.sv-popup__body-content{background-color:var(--lbr-popup-menu-background-color, var(--sjs-general-backcolor, var(--background, #fff)));border-radius:var(--lbr-popup-menu-corner-radius, var(--sjs-corner-radius, 4px));height:100%}.sv-popup__container{position:absolute;padding:0}.sv-popup__content{min-width:100%;height:100%;display:flex;flex-direction:column;min-height:0;position:relative}.sv-popup__body-content{width:100%;height:100%;box-sizing:border-box;display:flex;flex-direction:column;max-height:90vh;max-width:100vw}.sv-popup--modal-popup{display:flex;align-items:center;justify-content:center;background-color:var(--lbr-dialog-screen-color, var(--background-semitransparent, rgba(144, 144, 144, .5)));padding:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(15 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(8 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box}.sv-popup--modal-popup>.sv-popup__container{position:static;display:flex;background-color:var(--lbr-dialog-background-color, var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9)));border-radius:var(--lbr-dialog-corner-radius, calc(2 * (var(--sjs-corner-radius, 4px))));box-shadow:var(--lbr-dialog-shadow-2-offset-x, 0px) var(--lbr-dialog-shadow-2-offset-y, 2px) var(--lbr-dialog-shadow-2-blur, 6px) var(--lbr-dialog-shadow-2-spread, 0px) var(--lbr-dialog-shadow-2-color, rgba(0, 0, 0, .1)),var(--lbr-dialog-shadow-1-offset-x, 0px) var(--lbr-dialog-shadow-1-offset-y, 8px) var(--lbr-dialog-shadow-1-blur, 16px) var(--lbr-dialog-shadow-1-spread, 0px) var(--lbr-dialog-shadow-1-color, rgba(0, 0, 0, .1))}.sv-popup--modal-popup>.sv-popup__container>.sv-popup__body-content{padding:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:auto;min-width:452px;gap:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-popup--modal-popup .sv-popup__body-footer .sv-modal-footer-action-bar{overflow:visible}.sv-popup--modal-popup .sv-popup__scrolling-content{padding:2px;margin:-2px}.sd-root-modern--mobile .sv-popup--modal-popup .sv-popup__body-content{min-width:auto}.sv-popup--confirm .sv-popup__body-content .sv-string-viewer{color:var(--sjs-font-editorfont-color, var(--sjs-general-forecolor, rgba(0, 0, 0, .91)));align-self:self-start;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-size, 16px);font-style:normal;font-weight:400;line-height:calc(1.5 * (var(--sjs-font-size, 16px)))}.sv-popup__scrolling-content{height:100%;overflow:auto;display:flex;flex-direction:column}.sv-popup__scrolling-content::-webkit-scrollbar,.sv-popup__scrolling-content *::-webkit-scrollbar{height:6px;width:6px;background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sv-popup__scrolling-content::-webkit-scrollbar-thumb,.sv-popup__scrolling-content *::-webkit-scrollbar-thumb{background:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)))}.sv-popup--show-pointer.sv-popup--top .sv-popup__pointer{transform:translate(calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))) rotate(180deg)}.sv-popup--show-pointer.sv-popup--bottom .sv-popup__pointer{transform:translate(calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px)))),calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px)))))}.sv-popup--show-pointer.sv-popup--right .sv-popup__container{transform:translate(var(--sjs-base-unit, var(--base-unit, 8px)))}.sv-popup--show-pointer.sv-popup--right .sv-popup__container .sv-popup__pointer{transform:translate(-12px,-4px) rotate(-90deg)}.sv-popup--show-pointer.sv-popup--left .sv-popup__container{transform:translate(calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px)))))}.sv-popup--show-pointer.sv-popup--left .sv-popup__container .sv-popup__pointer{transform:translate(-4px,-4px) rotate(90deg)}.sv-popup__pointer{display:block;position:absolute}.sv-popup__pointer:after{content:" ";display:block;width:0;height:0;border-left:var(--sjs-base-unit, var(--base-unit, 8px)) solid transparent;border-right:var(--sjs-base-unit, var(--base-unit, 8px)) solid transparent;border-bottom:var(--sjs-base-unit, var(--base-unit, 8px)) solid var(--sjs-general-backcolor, var(--background, #fff));align-self:center}.sv-popup__body-header{font-family:Open Sans;font-size:calc(1.5 * (var(--sjs-font-size, 16px)));line-height:calc(2 * (var(--sjs-font-size, 16px)));font-style:normal;font-weight:700;color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sv-popup__body-footer{display:flex}.sv-popup__body-footer .sv-action-bar{gap:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));overflow:visible}.sv-popup--menu-phone,.sv-popup--menu-tablet{z-index:2001;padding:0;width:100%;height:var(--sv-popup-overlay-height, 100vh)}.sv-popup--menu-phone .sv-popup__body-footer-item,.sv-popup--menu-tablet .sv-popup__body-footer-item{width:100%}.sv-popup--menu-phone .sv-popup__body-footer .sv-action-bar,.sv-popup--menu-tablet .sv-popup__body-footer .sv-action-bar{width:100%;justify-content:flex-start}.sv-popup--menu-phone .sv-popup__body-footer .sv-action-bar .sv-action,.sv-popup--menu-tablet .sv-popup__body-footer .sv-action-bar .sv-action{flex:0 0 auto}.sv-popup--menu-phone .sv-popup__body-footer,.sv-popup--menu-tablet .sv-popup__body-footer{padding:var(--lbr-popup-menu-footer-padding-top, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))) var(--lbr-popup-menu-footer-padding-right, 0px) var(--lbr-popup-menu-footer-padding-bottom, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))) var(--lbr-popup-menu-footer-padding-left, 0px);border-top:var(--lbr-popup-menu-footer-border-width-top, 1px) solid var(--lbr-popup-menu-footer-border-color, var(--sjs-border-light, var(--border-light, #eaeaea)));background:var(--lbr-popup-menu-footer-background-color, var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9)))}.sv-popup--menu-phone .sv-list__filter,.sv-popup--menu-tablet .sv-list__filter{display:flex;align-items:center;margin-bottom:0;padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-popup--menu-phone .sv-list,.sv-popup--menu-tablet .sv-list{flex-grow:1}.sv-popup--menu-phone .sv-list__filter-icon,.sv-popup--menu-tablet .sv-list__filter-icon{position:static;height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-popup--menu-phone .sv-list__empty-container,.sv-popup--menu-tablet .sv-list__empty-container{display:flex;flex-direction:column;justify-content:center;flex-grow:1}.sv-popup--menu-phone .sv-list__filter-clear-button,.sv-popup--menu-tablet .sv-list__filter-clear-button{height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));appearance:none;border:none;border-radius:100%;background-color:transparent}.sv-popup--menu-phone .sv-list__filter-clear-button svg,.sv-popup--menu-tablet .sv-list__filter-clear-button svg{height:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-popup--menu-phone .sv-list__filter-clear-button svg use,.sv-popup--menu-tablet .sv-list__filter-clear-button svg use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-popup--menu-phone .sv-list__filter-clear-button:hover,.sv-popup--menu-tablet .sv-list__filter-clear-button:hover{border-radius:var(--lbr-popup-menu-search-clear-button-corner-radius, 1024px);background:var(--lbr-popup-menu-search-clear-button-background-color-hovered, var(--sjs-special-red-light, rgba(229, 10, 62, .1)))}.sv-popup--menu-phone .sv-list__filter-clear-button:hover use,.sv-popup--menu-tablet .sv-list__filter-clear-button:hover use{fill:var(--lbr-popup-menu-search-clear-button-icon-color-hovered, var(--sjs-special-red, #E50A3E))}.sv-popup--menu-phone .sv-list__input,.sv-popup--menu-tablet .sv-list__input{color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090));font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) var(--sjs-base-unit, var(--base-unit, 8px))}.sv-popup--menu-phone .sv-list__item:hover .sv-list__item-body,.sv-popup--menu-phone .sv-list__item:focus .sv-list__item-body,.sv-popup--menu-phone .sv-list__item--focused .sv-list__item-body,.sv-popup--menu-tablet .sv-list__item:hover .sv-list__item-body,.sv-popup--menu-tablet .sv-list__item:focus .sv-list__item-body,.sv-popup--menu-tablet .sv-list__item--focused .sv-list__item-body{background:var(--sjs-general-backcolor, var(--background, #fff))}.sv-popup--menu-phone .sv-list__item:hover.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-phone .sv-list__item:focus.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-phone .sv-list__item--focused.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-tablet .sv-list__item:hover.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-tablet .sv-list__item:focus.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-tablet .sv-list__item--focused.sv-list__item--selected .sv-list__item-body{background:var(--sjs-primary-backcolor, var(--primary, #19b394));color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));font-weight:600}.sv-popup--menu-phone.sv-multi-select-list .sv-list__item:hover.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-phone.sv-multi-select-list .sv-list__item:focus.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-phone.sv-multi-select-list .sv-list__item--focused.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-tablet.sv-multi-select-list .sv-list__item:hover.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-tablet.sv-multi-select-list .sv-list__item:focus.sv-list__item--selected .sv-list__item-body,.sv-popup--menu-tablet.sv-multi-select-list .sv-list__item--focused.sv-list__item--selected .sv-list__item-body{background:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));color:var(--sjs-general-forecolor, var(--foreground, #161616));font-weight:400}.sv-popup--menu-phone>.sv-popup__container{width:100%;height:calc(var(--sv-popup-overlay-height, 100vh));max-width:100vw;max-height:calc(var(--sv-popup-overlay-height, 100vh));border:unset;box-shadow:unset;box-sizing:content-box;background:var(--lbr-popup-menu-background-color-global, var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3)))}.sv-popup--menu-phone>.sv-popup__container>.sv-popup__body-content{background-color:var(--lbr-popup-menu-background-color, var(--sjs-general-backcolor, var(--background, #fff)));max-height:var(--sv-popup-overlay-height, 100vh);max-width:100vw;height:calc(var(--sv-popup-overlay-height, 100vh))}.sv-popup--menu-tablet{background:var(--lbr-dialog-screen-color, var(--background-semitransparent, rgba(144, 144, 144, .5)))}.sv-popup--menu-tablet>.sv-popup__container{border:unset;box-sizing:content-box;background:var(--lbr-popup-menu-background-color-global, var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3)));--sv-popup-overlay-max-height: calc(var(--sv-popup-overlay-height, 100vh) - var(--sjs-base-unit, var(--base-unit, 8px)) * 8);--sv-popup-overlay-max-width: calc(100% - var(--sjs-base-unit, var(--base-unit, 8px)) * 8);position:absolute;transform:translate(-50%,-50%);left:50%;top:50%;max-height:var(--sv-popup-overlay-max-height);min-height:min(var(--sv-popup-overlay-max-height),30 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:auto;width:auto;min-width:min(40 * (var(--sjs-base-unit, var(--base-unit, 8px))),var(--sv-popup-overlay-max-width));max-width:var(--sv-popup-overlay-max-width);border-radius:var(--lbr-popup-menu-corner-radius, var(--sjs-corner-radius, 4px));overflow:hidden;box-shadow:var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1))}.sv-popup--menu-tablet>.sv-popup__container>.sv-popup__body-content{background-color:var(--lbr-popup-menu-background-color, var(--sjs-general-backcolor, var(--background, #fff)));max-width:100vw;max-height:calc(var(--sv-popup-overlay-height, 100vh) - var(--sjs-base-unit, var(--base-unit, 8px)) * 8);min-height:min(var(--sv-popup-overlay-max-height),30 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:auto}.sv-popup--menu-tablet .sv-popup__content,.sv-popup--menu-tablet .sv-popup__scrolling-content,.sv-popup--menu-tablet .sv-list__container{flex-grow:1}.sv-popup--visible{opacity:1}.sv-popup--enter{animation-name:fadeIn;animation-fill-mode:forwards;animation-duration:.15s}.sv-popup--modal-popup.sv-popup--enter{animation-timing-function:cubic-bezier(0,0,.58,1);animation-duration:.25s}.sv-popup--leave{animation-direction:reverse;animation-name:fadeIn;animation-fill-mode:forwards;animation-duration:.15s}.sv-popup--modal-popup.sv-popup--leave{animation-timing-function:cubic-bezier(.42,0,1,1);animation-duration:.25s}.sv-popup--hidden{opacity:0}@keyframes modalMoveUp{0%{transform:translateY(64px)}to{transform:translateY(0)}}.sv-popup--modal-popup.sv-popup--leave .sv-popup__container,.sv-popup--modal-popup.sv-popup--enter .sv-popup__container{animation-name:modalMoveUp;animation-timing-function:cubic-bezier(0,0,.58,1);animation-fill-mode:forwards;animation-duration:.25s}.sv-popup--modal-popup.sv-popup--leave .sv-popup__container{animation-direction:reverse;animation-timing-function:cubic-bezier(.42,0,1,1)}.sv-button-group{display:flex;align-items:center;flex-direction:row;font-size:var(--sjs-font-size, 16px);overflow-x:auto;border:1px solid var(--sjs-border-default, var(--border, #d6d6d6))}.sv-button-group__item{display:flex;box-sizing:border-box;flex-direction:row;justify-content:center;align-items:center;appearance:none;flex-grow:1;flex-basis:0;padding:11px calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));line-height:calc(1.5 * (var(--sjs-font-size, 16px)));outline:none;font-size:var(--sjs-font-size, 16px);font-weight:400;background:var(--sjs-general-backcolor, var(--background, #fff));cursor:pointer;color:var(--sjs-general-forecolor, var(--foreground, #161616));position:relative}.sv-button-group__item:not(:last-of-type){border-right:1px solid var(--sjs-border-default, var(--border, #d6d6d6))}.sv-button-group__item--hover:hover{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sv-button-group__item-icon{display:block;height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-button-group__item-icon use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-button-group__item--selected{font-weight:600;color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sv-button-group__item--selected .sv-button-group__item-icon use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sv-button-group__item--selected:hover{background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sv-button-group__item-decorator{display:flex;align-items:center;white-space:nowrap}.sv-button-group__item-caption{display:block}.sv-button-group__item-icon+.sv-button-group__item-caption{margin-left:var(--sjs-base-unit, var(--base-unit, 8px))}.sv-button-group__item--disabled{color:var(--sjs-general-forecolor, var(--foreground, #161616));cursor:default}.sv-button-group__item--disabled .sv-button-group__item-decorator{opacity:.25;font-weight:400}.sv-button-group__item--disabled .sv-button-group__item-icon use{fill:var(--sjs-general-forecolor, var(--foreground, #161616))}.sv-button-group__item--disabled:hover{background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sv-button-group:focus-within{box-shadow:0 0 0 1px var(--sjs-primary-backcolor, var(--primary, #19b394));border-color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sv-visuallyhidden{position:absolute;width:1px;height:1px;overflow:hidden;clip:rect(0 0 0 0)}.sv-hidden{display:none!important}.sv-title-actions{display:flex;align-items:center;width:100%}.sv-title-actions__title{flex-wrap:wrap;max-width:90%;min-width:50%;white-space:initial}.sv-action-title-bar{min-width:56px}.sv-title-actions .sv-title-actions__title{flex-wrap:wrap;flex:0 1 auto;max-width:unset;min-width:unset}.sv-title-actions .sv-action-title-bar{flex:1 1 auto;justify-content:flex-end;min-width:unset}.sv_window{position:fixed;bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:var(--sjs-base-unit, var(--base-unit, 8px));border:1px solid var(--sjs-border-inside, var(--border-inside, rgba(0, 0, 0, .16)));box-shadow:var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1));background-clip:padding-box;z-index:100;max-height:50vh;overflow:auto;box-sizing:border-box;background:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));width:calc(100% - 4 * (var(--sjs-base-unit, var(--base-unit, 8px))))!important}@-moz-document url-prefix(){.sv_window,.sv_window *{scrollbar-width:thin;scrollbar-color:var(--sjs-border-default, var(--border, #d6d6d6)) transparent}}.sv_window::-webkit-scrollbar,.sv_window *::-webkit-scrollbar{width:12px;height:12px;background-color:transparent}.sv_window::-webkit-scrollbar-thumb,.sv_window *::-webkit-scrollbar-thumb{border:4px solid rgba(0,0,0,0);background-clip:padding-box;border-radius:32px;background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sv_window::-webkit-scrollbar-track,.sv_window *::-webkit-scrollbar-track{background:transparent}.sv_window::-webkit-scrollbar-thumb:hover,.sv_window *::-webkit-scrollbar-thumb:hover{border:2px solid rgba(0,0,0,0);background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sv_window_root-content{height:100%}.sv_window--full-screen{top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));left:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));max-height:100%;width:initial!important;max-width:initial!important}.sv_window_header{display:flex;justify-content:flex-end}.sv_window_content{overflow:hidden}.sv_window--collapsed{height:initial}.sv_window--collapsed .sv_window_header{height:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:var(--sjs-base-unit, var(--base-unit, 8px)) var(--sjs-base-unit, var(--base-unit, 8px)) var(--sjs-base-unit, var(--base-unit, 8px)) calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:var(--sjs-base-unit, var(--base-unit, 8px));display:flex;background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));box-sizing:content-box}.sv_window--collapsed .sv_window_content{display:none}.sv_window--collapsed .sv_window_buttons_container{margin-top:0;margin-right:0}.sv_window_header_title_collapsed{color:var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91));font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;font-weight:600;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));flex:1;display:flex;justify-content:flex-start;align-items:center}.sv_window_header_description{color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));font-feature-settings:"salt" on;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.sv_window_buttons_container{position:fixed;margin-top:var(--sjs-base-unit, var(--base-unit, 8px));margin-right:var(--sjs-base-unit, var(--base-unit, 8px));display:flex;gap:var(--sjs-base-unit, var(--base-unit, 8px));z-index:10000}.sv_window_button{display:flex;padding:var(--sjs-base-unit, var(--base-unit, 8px));justify-content:center;align-items:center;border-radius:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));cursor:pointer}.sv_window_button:hover,.sv_window_button:active{background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)))}.sv_window_button:hover svg use,.sv_window_button:hover svg path,.sv_window_button:active svg use,.sv_window_button:active svg path{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sv_window_button:active{opacity:.5}.sv_window_button svg use,.sv_window_button svg path{fill:var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45))}sv-brand-info,.sv-brand-info{z-index:1;position:relative;margin-top:1px}.sv-brand-info{font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));text-align:right;color:#161616;padding:24px 40px}.sv-brand-info a{color:#161616;text-decoration-line:underline}.sd-body--static .sv-brand-info{padding-top:0;margin-top:16px}.sd-body--responsive .sv-brand-info{padding-top:16px;margin-top:-8px}.sd-root-modern--mobile .sv-brand-info{padding:48px 24px 8px;margin-top:0;text-align:center}.sv-brand-info__text{font-weight:600;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));color:#161616}.sv-brand-info__logo{display:inline-block}.sv-brand-info__logo img{width:118px}.sv-brand-info__terms{font-weight:400;font-size:calc(.75 * (var(--sjs-font-size, 16px)));line-height:var(--sjs-font-size, 16px);padding-top:4px}.sv-brand-info__terms a{color:#909090}.sd-body--responsive .sv-brand-info{padding-right:0;padding-left:0}.sv-ranking{outline:none;user-select:none;-webkit-user-select:none}.sv-ranking-item{cursor:pointer;position:relative;opacity:1}.sv-ranking-item:focus .sv-ranking-item__icon--hover{visibility:hidden}.sv-ranking-item:hover:not(:focus) .sv-ranking-item__icon--hover{visibility:visible}.sv-question--disabled .sv-ranking-item:hover .sv-ranking-item__icon--hover{visibility:hidden}.sv-ranking-item:focus{outline:none}.sv-ranking-item:focus .sv-ranking-item__icon--focus{visibility:visible;top:calc(.6 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-item:focus .sv-ranking-item__index{background:var(--sjs-general-backcolor, var(--background, #fff));outline:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid var(--sjs-primary-backcolor, var(--primary, #19b394))}.sv-ranking-item__content.sv-ranking-item__content{display:flex;align-items:center;line-height:1em;padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0px;border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-item__icon-container{position:relative;left:0;bottom:0;flex-shrink:0;width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));align-self:flex-start;padding-left:var(--sjs-base-unit, var(--base-unit, 8px));padding-right:var(--sjs-base-unit, var(--base-unit, 8px));margin-left:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:content-box}.sv-ranking-item--disabled.sv-ranking-item--disabled,.sv-ranking-item--readonly.sv-ranking-item--readonly,.sv-ranking-item--preview.sv-ranking-item--preview{cursor:initial;user-select:initial;-webkit-user-select:initial}.sv-ranking-item--disabled.sv-ranking-item--disabled .sv-ranking-item__icon-container.sv-ranking-item__icon-container .sv-ranking-item__icon.sv-ranking-item__icon,.sv-ranking-item--readonly.sv-ranking-item--readonly .sv-ranking-item__icon-container.sv-ranking-item__icon-container .sv-ranking-item__icon.sv-ranking-item__icon,.sv-ranking-item--preview.sv-ranking-item--preview .sv-ranking-item__icon-container.sv-ranking-item__icon-container .sv-ranking-item__icon.sv-ranking-item__icon{visibility:hidden}.sv-ranking-item__icon.sv-ranking-item__icon{visibility:hidden;fill:var(--sjs-primary-backcolor, var(--primary, #19b394));position:absolute;top:var(--sjs-base-unit, var(--base-unit, 8px));width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-item__index.sv-ranking-item__index{display:flex;flex-shrink:0;align-items:center;justify-content:center;background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));border-radius:100%;border:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid transparent;width:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));line-height:calc(1.5 * (var(--sjs-font-size, 16px)));box-sizing:border-box;font-weight:600;margin-left:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))));transition:outline var(--sjs-transition-duration, .15s),background var(--sjs-transition-duration, .15s);outline:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid transparent;align-self:self-start}.sv-ranking-item__index.sv-ranking-item__index svg{fill:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));width:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));height:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))}.sv-ranking-item__text{display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));margin:0 calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));overflow-wrap:break-word;word-break:normal;align-self:self-start;padding-top:var(--sjs-base-unit, var(--base-unit, 8px));padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sv-ranking-item__text .sv-string-viewer,.sv-ranking-item__text .sv-string-editor{overflow:initial;white-space:pre-line}.sd-ranking--disabled .sv-ranking-item__text{color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));opacity:.25}.sv-ranking-item--disabled .sv-ranking-item__text{color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));opacity:.25}.sv-ranking-item--readonly .sv-ranking-item__index{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sv-ranking-item--preview .sv-ranking-item__index{background-color:transparent;border:1px solid var(--sjs-general-forecolor, var(--foreground, #161616));box-sizing:border-box}.sv-ranking-item__ghost.sv-ranking-item__ghost{display:none;background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(31 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));z-index:1;position:absolute;left:0;top:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}[dir=rtl] .sv-ranking-item__ghost{left:initilal;right:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-item--ghost{height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-item--ghost .sv-ranking-item__text .sv-string-viewer,.sv-ranking-item--ghost .sv-ranking-item__text .sv-string-editor{white-space:unset}.sv-ranking-item--ghost .sv-ranking-item__ghost{display:block}.sv-ranking-item--ghost .sv-ranking-item__content{visibility:hidden}.sv-ranking-item--drag .sv-ranking-item__content{box-shadow:var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1));border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking--drag .sv-ranking-item:hover .sv-ranking-item__icon{visibility:hidden}.sv-ranking-item--drag .sv-ranking-item__icon--hover{visibility:visible}.sv-ranking--mobile .sv-ranking-item__icon--hover{visibility:visible;fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-ranking--mobile.sv-ranking--drag .sv-ranking-item--ghost .sv-ranking-item__icon.sv-ranking-item__icon--hover{visibility:hidden}.sv-ranking--mobile.sv-ranking-shortcut{max-width:80%}.sv-ranking--mobile .sv-ranking-item__index.sv-ranking-item__index,.sv-ranking--mobile .sd-element--with-frame .sv-ranking-item__icon{margin-left:0}.sv-ranking--design-mode .sv-ranking-item:hover .sv-ranking-item__icon{visibility:hidden}.sv-ranking--disabled{opacity:.8}.sv-ranking-shortcut[hidden]{display:none}.sv-ranking-shortcut .sv-ranking-item__icon{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sv-ranking-shortcut .sv-ranking-item__text{margin-right:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-shortcut .sv-ranking-item__icon--hover{visibility:visible}.sv-ranking-shortcut .sv-ranking-item__icon{width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));top:var(--sjs-base-unit, var(--base-unit, 8px))}.sv-ranking-shortcut .sv-ranking-item__content{padding-left:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-shortcut .sv-ranking-item__icon-container{margin-left:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-shortcut{cursor:grabbing;position:absolute;z-index:10000;border-radius:calc(12.5 * var(--sjs-base-unit, var(--base-unit, 8px)));min-width:100px;max-width:400px;box-shadow:var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1));background-color:var(--sjs-general-backcolor, var(--background, #fff));font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)))}.sv-ranking-shortcut .sv-ranking-item{height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking-shortcut .sv-ranking-item .sv-ranking-item__text .sv-string-viewer,.sv-ranking-shortcut .sv-ranking-item .sv-ranking-item__text .sv-string-editor{overflow:hidden;white-space:nowrap}.sv-ranking--select-to-rank{display:flex}.sv-ranking--select-to-rank-vertical{flex-direction:column-reverse}.sv-ranking--select-to-rank-vertical .sv-ranking__containers-divider{margin:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0;height:1px}.sv-ranking--select-to-rank-vertical .sv-ranking__container--empty{padding-top:var(--sjs-base-unit, var(--base-unit, 8px));padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px));display:flex;justify-content:center;align-items:center}.sv-ranking-item--animate-item-removing{animation-name:moveIn,fadeIn;animation-direction:reverse;animation-fill-mode:forwards;animation-timing-function:linear;animation-duration:var(--sjs-ranking-move-out-duration, .15s),var(--sjs-ranking-fade-out-duration, .1s);animation-delay:var(--sjs-ranking-move-out-delay, 0ms),0s}.sv-ranking-item--animate-item-adding{animation-name:moveIn,fadeIn;opacity:0;animation-fill-mode:forwards;animation-timing-function:linear;animation-duration:var(--sjs-ranking-move-in-duration, .15s),var(--sjs-ranking-fade-in-duration, .1s);animation-delay:0s,var(--sjs-ranking-fade-in-delay, .15s)}.sv-ranking-item--animate-item-adding-empty{animation-name:fadeIn;opacity:0;animation-timing-function:linear;animation-duration:var(--sjs-ranking-fade-in-duration, .1s);animation-delay:0}.sv-ranking-item--animate-item-removing-empty{animation-name:fadeIn;animation-direction:reverse;animation-timing-function:linear;animation-duration:var(--sjs-ranking-fade-out-duration, .1s);animation-delay:0}@keyframes sv-animate-item-opacity-reverse-keyframes{0%{opacity:0}to{opacity:1}}@keyframes sv-animate-item-opacity-keyframes{0%{opacity:1}to{opacity:0}}.sv-ranking--select-to-rank-horizontal .sv-ranking__container{max-width:calc(50% - 1px)}.sv-ranking--select-to-rank-horizontal .sv-ranking__containers-divider{width:1px}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--to .sv-ranking-item{left:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--empty.sv-ranking__container--to .sv-ranking-item{left:initial}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--empty.sv-ranking__container--to .sv-ranking__container-placeholder{padding-left:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--empty.sv-ranking__container--from .sv-ranking__container-placeholder{padding-right:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking__container-placeholder{color:var(--sjs-font-questiondescription-color, var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45)));font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));white-space:normal;display:flex;justify-content:center;align-items:center;height:100%;padding-top:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box}.sv-ranking__container{flex:1}.sv-ranking__container--empty{box-sizing:border-box;text-align:center}.sv-ranking__containers-divider{background:var(--sjs-border-default, var(--sjs-border-inside, var(--border-inside, rgba(0, 0, 0, .16))))}.sv-ranking__container--from .sv-ranking-item__icon--focus{display:none}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--to .sv-ranking-item{left:0!important;padding-left:16px}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--to .sv-ranking-item .sv-ranking-item__ghost{left:initial}.sv-ranking--select-to-rank-swap-areas{flex-direction:row-reverse}.sv-ranking--select-to-rank-swap-areas .sv-ranking__container--to .sv-ranking-item{padding-left:0;left:-24px!important}.sv-ranking--select-to-rank-swap-areas .sv-ranking__container--from .sv-ranking-item{padding-left:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));left:0}.sv-ranking--select-to-rank-swap-areas .sv-ranking__container--from .sv-ranking-item__ghost.sv-ranking-item__ghost{left:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking--select-to-rank-swap-areas .sv-ranking__container--empty.sv-ranking__container--to .sv-ranking__container-placeholder{padding-right:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-left:0}.sv-ranking--select-to-rank-swap-areas .sv-ranking__container--empty.sv-ranking__container--to .sv-ranking-item__ghost.sv-ranking-item__ghost{right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking--select-to-rank-swap-areas .sv-ranking__container--empty.sv-ranking__container--from .sv-ranking__container-placeholder{padding-left:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-right:0}.sd-question--mobile .sv-ranking-item__icon-container,.sd-root-modern.sd-root-modern--mobile .sv-ranking-item__icon-container{margin-left:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;justify-content:flex-end;padding:0;width:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-list{margin:0;padding:var(--lbr-popup-menu-padding-top, 4px) var(--lbr-popup-menu-padding-right, 4px) var(--lbr-popup-menu-padding-bottom, 4px) var(--lbr-popup-menu-padding-left, 4px);overflow-y:auto;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));list-style-type:none}.sv-list__empty-container{width:100%;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));box-sizing:border-box;padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;padding:var(--lbr-placeholder-padding-top, 16px) var(--lbr-placeholder-padding-right, 64px) var(--lbr-placeholder-padding-bottom, 16px) var(--lbr-placeholder-padding-left, 64px);flex-direction:column;justify-content:center;align-items:center;gap:var(--lbr-placeholder-gap, 4px);align-self:stretch}.sv-list__empty-text{line-height:calc(1.5 * (var(--sjs-font-size, 16px)));font-size:var(--sjs-font-size, 16px);font-weight:400;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-list__item{width:100%;align-items:center;box-sizing:border-box;color:var(--sjs-general-forecolor, var(--foreground, #161616));cursor:pointer;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.sv-list__item-body{--sjs-list-item-padding-left-default: calc(2 * var(--sjs-base-unit, var(--base-unit, 8px)));--sjs-list-item-padding-left: calc(var(--sjs-list-item-level) * var(--sjs-list-item-padding-left-default));position:relative;width:100%;align-items:center;box-sizing:border-box;padding-block:var(--sjs-base-unit, var(--base-unit, 8px));padding-inline-end:calc(8 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-start:var(--sjs-list-item-padding-left, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))));color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));font-weight:400;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));cursor:pointer;overflow:hidden;text-align:start;text-overflow:ellipsis;white-space:nowrap;transition:background-color var(--sjs-transition-duration, .15s),color var(--sjs-transition-duration, .15s)}.sv-list__item.sv-list__item--focused:not(.sv-list__item--selected){outline:none}.sv-list__item.sv-list__item--focused:not(.sv-list__item--selected) .sv-list__item-body{border:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid var(--sjs-border-light, var(--border-light, #eaeaea));border-radius:var(--sjs-corner-radius, 4px);padding-block:calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-end:calc(7.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-start:calc(1.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-list__item:hover,.sv-list__item:focus{outline:none}.sv-list__item:focus .sv-list__item-body,.sv-list__item--hovered>.sv-list__item-body{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sv-list__item--with-icon.sv-list__item--with-icon{padding:0}.sv-list__item--with-icon.sv-list__item--with-icon>.sv-list__item-body{padding-top:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));gap:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex}.sv-list__item-icon{float:left;flex-shrink:0;width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-list__item-icon svg{display:block}.sv-list__item-icon use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-list-item__marker-icon{position:absolute;right:var(--sjs-base-unit, var(--base-unit, 8px));width:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));flex-shrink:0;padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:content-box}.sv-list-item__marker-icon svg{display:block}.sv-list-item__marker-icon use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}[dir=rtl] .sv-list__item-icon,[style*="direction:rtl"] .sv-list__item-icon,[style*="direction: rtl"] .sv-list__item-icon{float:right}.sv-list__item-separator{margin:var(--sjs-base-unit, var(--base-unit, 8px)) 0;height:1px;background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sv-list--filtering .sv-list__item-separator{display:none}.sv-list__item.sv-list__item--selected>.sv-list__item-body,.sv-list__item.sv-list__item--selected:hover>.sv-list__item-body,.sv-list__item.sv-list__item--selected.sv-list__item--focused>.sv-list__item-body,.sv-multi-select-list .sv-list__item.sv-list__item--selected.sv-list__item--focused>.sv-list__item-body,li:focus .sv-list__item.sv-list__item--selected>.sv-list__item-body{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));font-weight:600}.sv-list__item.sv-list__item--selected .sv-list__item-icon use,.sv-list__item.sv-list__item--selected:hover .sv-list__item-icon use,.sv-list__item.sv-list__item--selected.sv-list__item--focused .sv-list__item-icon use,.sv-multi-select-list .sv-list__item.sv-list__item--selected.sv-list__item--focused .sv-list__item-icon use,li:focus .sv-list__item.sv-list__item--selected .sv-list__item-icon use{fill:var(--sjs-general-backcolor, var(--background, #fff))}.sv-list__item.sv-list__item--selected .sv-list-item__marker-icon use,.sv-list__item.sv-list__item--selected:hover .sv-list-item__marker-icon use,.sv-list__item.sv-list__item--selected.sv-list__item--focused .sv-list-item__marker-icon use,.sv-multi-select-list .sv-list__item.sv-list__item--selected.sv-list__item--focused .sv-list-item__marker-icon use,li:focus .sv-list__item.sv-list__item--selected .sv-list-item__marker-icon use{fill:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sv-multi-select-list .sv-list__item.sv-list__item--selected .sv-list__item-body,.sv-multi-select-list .sv-list__item.sv-list__item--selected:hover .sv-list__item-body{background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));font-weight:400}.sv-list__item--group-selected>.sv-list__item-body{background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));font-weight:400}.sv-list__item--group-selected>.sv-list__item-body use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-list__item.sv-list__item--disabled .sv-list__item-body{cursor:default;color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-list__item span{white-space:nowrap}.sv-list__item-text--wrap span{white-space:normal;word-wrap:break-word}.sv-list__container{position:relative;height:100%;flex-direction:column;display:flex;min-height:0}.sv-list__filter{border-bottom:1px solid var(--sjs-border-inside, var(--border-inside, rgba(0, 0, 0, .16)))}.sv-list__filter-icon{display:block;position:absolute;top:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));inset-inline-start:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-list__filter-icon .sv-svg-icon{width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-list__filter-icon .sv-svg-icon use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-list__input{-webkit-appearance:none;-moz-appearance:none;appearance:none;display:block;background:transparent;box-sizing:border-box;width:100%;min-width:calc(30 * (var(--sjs-base-unit, var(--base-unit, 8px))));outline:none;font-size:var(--sjs-font-size, 16px);color:var(--sjs-general-forecolor, var(--foreground, #161616));padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-start:calc(7 * (var(--sjs-base-unit, var(--base-unit, 8px))));line-height:calc(1.5 * (var(--sjs-font-size, 16px)));border:none}.sv-list__input::placeholder{color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-list__input:disabled,.sv-list__input:disabled::placeholder{color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv-list__loading-indicator{pointer-events:none}.sv-list__loading-indicator .sv-list__item-body{background-color:transparent}.sv-scroll__wrapper{position:relative;height:100%;display:flex;flex-direction:column}.sv-scroll__scroller{box-sizing:border-box;flex-grow:1;overflow-y:auto;overflow-x:hidden;display:flex;flex-direction:column;-ms-overflow-style:none;scrollbar-width:none}.sv-scroll__scroller::-webkit-scrollbar{display:none}.sv-scroll__scrollbar{position:absolute;top:0;bottom:0;overflow-y:scroll;overflow-x:hidden;width:fit-content;margin-inline-start:-1px;visibility:hidden;z-index:30;inset-inline-end:0;inset-inline-start:initial}@-moz-document url-prefix(){.sv-scroll__scrollbar{scrollbar-width:thin;scrollbar-color:var(--ctr-scrollbar-background-color, var(--sjs-border-25-overlay, rgba(0, 0, 0, .1490196078))) transparent}}.sv-scroll__wrapper:hover .sv-scroll__scrollbar{visibility:visible}.sv-scroll__scrollbar-sizer{width:1px}.sv-scroll__container{width:100%;flex-grow:1;display:flex;flex-direction:column}.sv-save-data_root{position:fixed;left:50%;bottom:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));background:var(--sjs-general-backcolor, var(--background, #fff));opacity:0;padding:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-shadow:var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1));border-radius:calc(2 * (var(--sjs-corner-radius, 4px)));color:var(--sjs-general-forecolor, var(--foreground, #161616));min-width:calc(30 * (var(--sjs-base-unit, var(--base-unit, 8px))));text-align:center;z-index:1600;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));display:flex;flex-direction:row;justify-content:center;align-items:center;transform:translate(-50%) translateY(calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px)))));transition-timing-function:ease-in;transition-property:transform,opacity;transition-delay:.25s;transition:.5s}.sv-save-data_root.sv-save-data_root--shown{transition-timing-function:ease-out;transition-property:transform,opacity;transform:translate(-50%) translateY(0);transition-delay:.25s;opacity:.75}.sv-save-data_root span{display:flex;flex-grow:1}.sv-save-data_root .sv-action-bar{display:flex;flex-grow:0;flex-shrink:0}.sv-save-data_root--shown.sv-save-data_success,.sv-save-data_root--shown.sv-save-data_error{opacity:1}.sv-save-data_root.sv-save-data_root--with-buttons{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-save-data_root.sv-save-data_error{background-color:var(--sjs-special-red, var(--red, #e60a3e));color:var(--sjs-general-backcolor, var(--background, #fff));font-weight:600;gap:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-save-data_root.sv-save-data_error .sv-save-data_button{font-weight:600;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));height:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));color:#fff;background-color:var(--sjs-special-red, var(--red, #e60a3e));border:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid #ffffff;border-radius:calc(1.5 * (var(--sjs-corner-radius, 4px)));padding:var(--sjs-base-unit, var(--base-unit, 8px)) calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;align-items:center}.sv-save-data_root.sv-save-data_error .sv-save-data_button:hover,.sv-save-data_root.sv-save-data_error .sv-save-data_button:focus{color:var(--sjs-special-red, var(--red, #e60a3e));background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sv-save-data_root.sv-save-data_success{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));color:#fff;font-weight:600}.sv-string-viewer.sv-string-viewer--multiline{white-space:pre-wrap;word-break:break-word}.sd-element{padding-left:var(--sv-element-add-padding-left, 0px);padding-right:var(--sv-element-add-padding-right, 0px)}.sd-element__title{outline:none}.sd-element__title.sd-element__title--disabled{opacity:.25}.sd-root--readonly .sd-element__title.sd-element__title--disabled{opacity:1}.sd-element--invisible{opacity:.35}.sd-title.sd-element__title{font-family:var(--sjs-font-questiontitle-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-questiontitle-weight, 600);color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-element__header .sv-string-editor{max-width:100%;white-space:normal}.sd-element__title{font-size:0;line-height:0;position:static;margin:0}.sd-element__title .sd-element__num{font-size:calc(.75 * (var(--sjs-font-size, 16px)));line-height:var(--sjs-font-size, 16px);color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-element__title span{font-size:var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px))))}.sd-element__title .sv-title-actions__title{font-size:0;line-height:0}.sd-element__title .sv-title-actions__title{white-space:nowrap}.sd-element__title .sv-string-viewer{white-space:normal}.sd-element__title .sv-string-viewer.sv-string-viewer--multiline{white-space:pre-wrap}.sd-element__title.sd-element__title--singleinput span{--page-title-font-size: var(--sjs-font-pagetitle-size, calc(1.5 * (var(--sjs-font-size, 16px))));font-family:var(--sjs-font-pagetitle-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-pagetitle-weight, 700);font-size:var(--page-title-font-size);color:var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91)));position:static;line-height:calc(1.33 * (var(--page-title-font-size)));margin:0 0 calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0px}.sd-element__num{float:left;padding-top:calc(.625 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:calc(.375 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-start:0;padding-inline-end:var(--sjs-base-unit, var(--base-unit, 8px));width:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));font-size:calc(.75 * (var(--sjs-font-size, 16px)));line-height:var(--sjs-font-size, 16px);color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090));margin-inline-start:calc(-5 * (var(--sjs-base-unit, var(--base-unit, 8px))));text-align:end;box-sizing:border-box;white-space:nowrap;flex-shrink:0}.sd-page__num+span,.sd-element__num+span{float:left;width:0}[dir=rtl] .sd-element__num,[style*="direction:rtl"] .sd-element__num,[style*="direction: rtl"] .sd-element__num{float:right}[dir=rtl] .sd-element__title--collapsed .sd-element__title-expandable-svg,[style*="direction:rtl"] .sd-element__title--collapsed .sd-element__title-expandable-svg,[style*="direction: rtl"] .sd-element__title--collapsed .sd-element__title-expandable-svg{transform:rotate(180deg)}.sd-element__title--num-inline .sd-element__num{float:none;margin-inline-start:0;width:auto;padding-inline-start:0;padding-inline-end:0}.sd-element__title--num-inline .sd-element__num+span{float:none;width:auto}.sd-element__title--expandable.sd-element__title--expandable{position:relative;display:block}.sd-element__title-expandable-svg{display:inline-block;width:var(--lbr-question-panel-expand-button-icon-width, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-question-panel-expand-button-icon-height, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))));position:absolute;inset-inline-start:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));top:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-element__title-expandable-svg use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-element--with-frame{border-radius:var(--sjs-questionpanel-cornerRadius, var(--sjs-corner-radius, 4px));box-sizing:border-box;padding-top:var(--sd-base-padding);padding-left:calc(var(--sd-base-padding) + var(--sv-element-add-padding-left, 0px));padding-right:calc(var(--sd-base-padding) + var(--sv-element-add-padding-right, 0px));padding-bottom:var(--sd-base-padding);background:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));box-shadow:var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15))}.sd-element--with-frame.sd-element--compact{border:1px solid var(--sjs-border-default, var(--border, #d6d6d6));border-radius:var(--sjs-questionpanel-cornerRadius, var(--sjs-corner-radius, 4px));box-shadow:none;background-color:transparent}@keyframes elementMoveIn{0%{flex-basis:0;flex-grow:0;max-width:0;min-width:0;width:0;height:0;padding-left:0;overflow:hidden}to{height:var(--animation-height);flex-basis:var(--animation-width);width:var(--animation-width);min-width:var(--animation-width);padding-left:var(--animation-padding-left);overflow:visible}}.sd-element-wrapper--enter{animation-name:elementMoveIn,fadeIn;animation-timing-function:cubic-bezier(0,0,.58,1);animation-fill-mode:forwards;animation-duration:var(--sjs-element-move-in-duration, .25s),var(--sjs-element-fade-in-duration, .5s);animation-delay:0s,var(--sjs-element-fade-in-delay, .1s);opacity:0}.sd-element-wrapper--leave{animation-name:elementMoveIn,fadeIn;animation-timing-function:cubic-bezier(.42,0,1,1);animation-fill-mode:forwards;animation-direction:reverse;animation-duration:var(--sjs-element-move-out-duration, .25s),var(--sjs-element-fade-out-duration, .15s);animation-delay:var(--sjs-element-move-out-delay, 0ms),0s}.sd-element__content{box-sizing:border-box}.sd-element__content--enter,.sd-element__content--leave{--animation-padding-top: 0;--animation-padding-bottom: 0}.sd-element__content--enter{animation-name:fadeIn,moveInWithOverflow;min-height:0!important;opacity:0;animation-fill-mode:forwards;animation-timing-function:cubic-bezier(0,0,.58,1);animation-duration:var(--sjs-expand-fade-in-duration, .5s),var(--sjs-expand-move-in-duration, .15s);animation-delay:var(--sjs-expand-fade-in-delay, .15s),0s,0s}.sd-element__content--leave{animation-name:fadeIn,moveInWithOverflow;min-height:0!important;animation-direction:reverse;animation-fill-mode:forwards;animation-timing-function:cubic-bezier(.42,0,1,1);animation-duration:var(--sjs-collapse-fade-out-duration, .15s),var(--sjs-collapse-move-out-duration, .25s);animation-delay:0s,var(--sjs-collapse-move-out-delay, .1s),var(--sjs-collapse-move-out-delay, .1s)}.sd-element--expandable.sd-elemenet--expandable--animating>.sd-element__header:focus-within,.sd-element--expandable.sd-elemenet--expandable--animating>.sd-element__header:hover{background-color:transparent}.sd-elemenet--expandable--animating.sd-element--expandable{transition-property:padding-top,padding-bottom}.sd-elemenet--expandable--animating.sd-element--expandable>.sd-element__header{transition-property:padding-top,padding-bottom}.sd-elemenet--expandable--animating.sd-element--expandable.sd-element--expanded{transition-timing-function:cubic-bezier(0,0,.58,1);transition-duration:var(--sjs-expand-move-in-duration, .15s)}.sd-elemenet--expandable--animating.sd-element--expandable.sd-element--expanded>.sd-element__header{transition-timing-function:cubic-bezier(0,0,.58,1);transition-duration:var(--sjs-expand-move-in-duration, .15s)}.sd-elemenet--expandable--animating.sd-element--expandable.sd-element--expanded>.sd-element__header .sd-element__title:before{transition-duration:var(--sjs-expand-move-in-duration, .15s)}.sd-elemenet--expandable--animating.sd-element--expandable.sd-element--collapsed{transition-timing-function:cubic-bezier(0,0,.58,1);transition-duration:var(--sjs-collapse-move-out-duration, .25s);transition-delay:var(--sjs-collapse-move-out-delay, .1s)}.sd-elemenet--expandable--animating.sd-element--expandable.sd-element--collapsed>.sd-element__header{transition-timing-function:cubic-bezier(0,0,.58,1);transition-duration:var(--sjs-collapse-move-out-duration, .25s);transition-delay:var(--sjs-collapse-move-out-delay, .1s)}.sd-elemenet--expandable--animating.sd-element--expandable.sd-element--collapsed>.sd-element__header .sd-element__title:before{transition-duration:var(--sjs-collapse-move-out-duration, .25s);transition-delay:var(--sjs-collapse-move-out-delay, .1s)}.sd-elemenet--expandable--animating.sd-element--expandable.sd-element--complex:not(.sd-question--empty)>.sd-element__header--location-top:after{display:block;opacity:0;height:0;--animation-height: 1px;animation-name:fadeIn,moveIn;animation-fill-mode:forwards;animation-timing-function:cubic-bezier(0,0,.58,1);animation-delay:var(--sjs-expand-fade-in-delay, .15s),0s;animation-duration:var(--sjs-expand-fade-in-duration, .5s),var(--sjs-expand-move-in-duration, .15s)}.sd-elemenet--expandable--animating.sd-element--expandable.sd-element--complex:not(.sd-question--empty).sd-element--collapsed .sd-element__header--location-top:after{animation-direction:reverse;opacity:1;height:1px;animation-timing-function:cubic-bezier(.42,0,1,1);animation-delay:0s,var(--sjs-collapse-move-out-delay, .1s);animation-duration:var(--sjs-collapse-fade-out-duration, .15s),var(--sjs-collapse-move-out-duration, .25s)}.sd-question{position:relative}.sd-question--no-pointer-events .sd-selectbase label,.sd-question--no-pointer-events .sd-rating label{pointer-events:none}.sd-element__erbox--above-element{margin-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-question__erbox--below-question{margin-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-question__content--support-container-queries{container-type:inline-size}.sd-question--title-top>.sd-question__erbox--above-question{margin-bottom:calc(.5 * var(--sd-base-vertical-padding))}.sd-question--description-under-input>.sd-question__erbox--below-question,.sd-question--title-bottom>.sd-question__erbox--below-question{margin-top:calc(.25 * var(--sd-base-vertical-padding) + .5 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-element--with-frame>.sd-element__erbox--above-element{margin-bottom:var(--sd-base-padding);border-radius:var(--sjs-corner-radius, 4px) var(--sjs-corner-radius, 4px) 0 0}.sd-question--left>.sd-element__erbox--above-element{margin-bottom:0}.sd-element--with-frame.sd-question--left>.sd-element__erbox--above-element{margin-bottom:calc(1 * var(--sd-base-vertical-padding))}.sd-element--with-frame>.sd-question__erbox--below-question{margin-top:auto;border-radius:0 0 var(--sjs-corner-radius, 4px) var(--sjs-corner-radius, 4px)}.sd-element--with-frame.sd-question--title-top>.sd-question__erbox--above-question{margin-bottom:calc(.5 * var(--sd-base-vertical-padding) + var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-element--with-frame.sd-question--description-under-input>.sd-question__erbox--below-question,.sd-element--with-frame.sd-question--title-bottom>.sd-question__erbox--below-question{margin-top:calc(.5 * var(--sd-base-vertical-padding) + var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-question__header{width:100%}.sd-question__header--location-top{padding-bottom:calc(.5 * var(--sd-base-vertical-padding))}.sd-question__header--location--bottom{padding-top:calc(.375 * var(--sd-base-vertical-padding))}.sd-element--with-frame.sd-question--title-top{padding-top:var(--sd-base-vertical-padding)}.sd-element--with-frame.sd-question--error-top{padding-top:0}.sd-element--with-frame.sd-question--error-bottom{padding-bottom:0;display:flex;flex-direction:column}.sd-element--with-frame.sd-question--error-bottom>.sd-question__content{margin-bottom:var(--sd-base-padding)}.sd-element--with-frame>.sd-element__erbox{margin-left:calc(-1 * var(--sd-base-padding));margin-right:calc(-1 * var(--sd-base-padding));width:calc(100% + 2 * var(--sd-base-padding));position:sticky;left:calc(-1 * var(--sd-base-padding))}.sd-scrollable .sd-question__content{overflow-x:auto;padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-question__header--location--left{display:inline-block;width:auto;vertical-align:top;margin-top:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--left{display:flex;flex-wrap:wrap;flex-direction:row;column-gap:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));row-gap:calc(.25 * var(--sd-base-vertical-padding))}.sd-question__content--left{display:inline-block;flex:1;margin-top:calc(.25 * var(--sd-base-vertical-padding)) 0;max-width:100%}.sd-element--with-frame>.sd-question__content--left{margin:0}.sd-question__required-text{color:var(--sjs-special-red, var(--red, #e60a3e));vertical-align:top}.sd-question__comment-area{font-size:var(--sjs-font-size, 16px);margin-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;flex-direction:column;gap:var(--sjs-base-unit, var(--base-unit, 8px));color:var(--sjs-general-forecolor, var(--foreground, #161616));white-space:normal}.sd-question__placeholder{display:flex;flex-direction:column;align-items:center;text-align:center;gap:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));justify-content:center;min-height:calc(24 * (var(--sjs-base-unit, var(--base-unit, 8px))));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)))}.sd-question__placeholder>div .sv-string-viewer,.sd-question__placeholder>span .sv-string-viewer{white-space:pre-line}.sd-scrollable-container:not(.sd-scrollable-container--compact){width:max-content;overflow:visible;max-width:100%}.sd-question__title--empty .sv-string-viewer{display:inline-block;height:calc(1.5 * (var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px))))}.sd-question__content{box-sizing:border-box}.sd-singleinput__title{font-family:var(--sjs-font-pagetitle-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-pagetitle-weight, 700);font-size:var(--page-title-font-size);color:var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91)));position:static;line-height:calc(1.33 * (var(--page-title-font-size)));margin:0 0 calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0px}.sd-error{display:block;padding:var(--sjs-base-unit, var(--base-unit, 8px)) calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:var(--sjs-corner-radius, 4px);line-height:var(--sjs-font-size, 16px);font-size:calc(.75 * (var(--sjs-font-size, 16px)));font-weight:600;text-align:left;color:var(--sjs-special-red, var(--red, #e60a3e));white-space:normal;width:100%;background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)));box-sizing:border-box}.sd-element--expanded>.sd-element__header,.sd-element--collapsed>.sd-element__header{cursor:pointer}.sd-element--collapsed>.sd-element__header{padding:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px)))) var(--sd-base-padding);box-sizing:border-box;background-color:var(--sjs-general-backcolor, var(--background, #fff));margin-inline-start:calc(-1 * var(--sd-base-padding));width:calc(100% + 2 * var(--sd-base-padding))}.sd-element--collapsed.sd-element--with-frame{padding-top:0;padding-bottom:0}.sd-element--collapsed.sd-element--with-frame>.sd-element__header{padding-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:var(--sjs-questionpanel-cornerRadius, var(--sjs-corner-radius, 4px))}.sd-element--collapsed>.sd-element__header:focus-within,.sd-element--collapsed>.sd-element__header:hover{background-color:var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))}.sd-element--collapsed.sd-element--nested>.sd-element__header:hover,.sd-element--collapsed.sd-element--nested>.sd-element__header:focus-within{box-shadow:0 calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0 0 var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9)),0 calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0 var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))}.sd-element--complex.sd-element--with-frame{padding-top:0}.sd-element--complex.sd-element--nested-with-borders>.sd-element__erbox,.sd-element--complex.sd-element--with-frame>.sd-element__erbox{margin-top:0;margin-bottom:0}.sd-element--complex>.sd-element__header:after{content:" ";display:block;height:1px;position:relative;background:var(--sjs-border-light, var(--border-light, #eaeaea));bottom:0}.sd-element--complex.sd-element--with-frame>.sd-element__header{padding-top:var(--sd-base-vertical-padding);padding-bottom:var(--sd-base-vertical-padding)}.sd-element--collapsed.sd-element--complex>.sd-element__header{padding-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-element--nested.sd-element--complex>.sd-element__header--location-top:after{bottom:calc(-.5 * var(--sd-base-vertical-padding))}.sd-element--nested-with-borders{border:1px solid var(--sjs-border-light, var(--border-light, #eaeaea));border-radius:calc(var(--sjs-corner-radius, 4px) - 4px);box-sizing:border-box;padding-left:var(--sd-base-padding);padding-right:var(--sd-base-padding)}.sd-element--nested-with-borders>.sd-element__header--location-top{padding-top:calc(.5 * var(--sd-base-vertical-padding));padding-bottom:calc(.5 * var(--sd-base-vertical-padding))}.sd-element--nested-with-borders>.sd-element__erbox{margin-left:calc(-1 * var(--sd-base-padding));margin-right:calc(-1 * var(--sd-base-padding));width:calc(100% + 2 * var(--sd-base-padding))}.sd-element--nested-with-borders>.sd-question__erbox--below-question{bottom:0;margin-top:0}.sd-element--nested-with-borders.sd-element--collapsed>.sd-element__header--location-top:hover,.sd-element--nested-with-borders.sd-element--collapsed>.sd-element__header--location-top:focus-within{box-shadow:none}.sd-element--nested-with-borders>.sd-element__header--location-top:after,.sd-element--complex.sd-element--with-frame>.sd-element__header--location-top:after{bottom:calc(-1 * var(--sd-base-vertical-padding));inset-inline-start:calc(-1 * var(--sd-base-padding));width:calc(100% + 2 * var(--sd-base-padding))}.sd-element--collapsed.sd-element--complex>.sd-element__header--location-top:after{display:none}.sd-question--empty.sd-question--complex>.sd-question__content{padding-top:0;padding-bottom:0}.sd-question--empty.sd-question--complex>.sd-question__content:first-of-type{padding-top:var(--sd-base-padding)}.sd-question--empty.sd-question--complex>.sd-question__header--location-top{padding-bottom:calc(.5 * var(--sd-base-vertical-padding))}.sd-question--empty.sd-question--complex>.sd-question__header--location-top:after{display:none}.sd-input{-webkit-appearance:none;-moz-appearance:none;appearance:none;position:static;width:100%;box-sizing:border-box;padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));font-family:var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-editorfont-weight, 400);color:var(--sjs-font-editorfont-color, var(--sjs-general-forecolor, rgba(0, 0, 0, .91)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));background-color:var(--sjs-editorpanel-backcolor, var(--sjs-editor-background, var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))));border:none;border-radius:var(--sjs-editorpanel-cornerRadius, var(--sjs-corner-radius, 4px));text-align:start;box-shadow:var(--sjs-shadow-inner, inset 0px 1px 2px 0px rgba(0, 0, 0, .15)),0 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394));transition:box-shadow var(--sjs-transition-duration, .15s);display:block}.sd-input:focus{box-shadow:var(--sjs-shadow-inner-reset, inset 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-input.sd-input:focus{outline:none}input.sd-input:disabled:not(.sd-input--disabled),textarea.sd-input:disabled:not(.sd-input--disabled){opacity:1}.sd-input--disabled{background-color:var(--sjs-editorpanel-backcolor, var(--sjs-editor-background, var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))))}.sd-input--readonly{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-input.sd-input--preview{background:none;box-shadow:none;transition:none;border-bottom:1px solid var(--sjs-general-forecolor, var(--foreground, #161616));border-radius:0;padding-left:0;padding-right:0}.sd-input::placeholder{color:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)));-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.sd-input.sd-input--disabled,.sd-input.sd-input--disabled::placeholder{color:var(--sjs-general-forecolor, var(--foreground, #161616));opacity:.25}.sd-input.sd-input--readonly{color:var(--sjs-general-forecolor, var(--foreground, #161616));box-shadow:none;transition:none}.sd-input.sd-input--readonly::placeholder{color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-root--readonly .sd-input--disabled,.sd-root--readonly .sd-input--disabled::placeholder{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-input--error{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))}.sd-text__content{position:relative}.sd-text__character-counter{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.sd-text__character-counter:focus-within{padding-inline-end:calc(8 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-text__character-counter.sd-text__character-counter--big:focus-within{padding-inline-end:calc(11 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-remaining-character-counter{display:none;flex-direction:row;justify-content:flex-end;align-items:flex-end;padding:0;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));line-height:calc(1.5 * (var(--sjs-font-size, 16px)));font-size:var(--sjs-font-size, 16px);color:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)));position:absolute;inset-inline-end:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));inset-block-end:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-text__content:focus-within .sd-remaining-character-counter,.sd-comment__content:focus-within .sd-remaining-character-counter,.sd-multipletext__content:focus-within .sd-remaining-character-counter,.sd-matrix__question-wrapper:focus-within .sd-remaining-character-counter{display:flex}.sd-input[type=time],.sd-input[type=date],.sd-input[type=datetime-local],.sd-input[type=week],.sd-input[type=month],.sd-input[type=tel],.sd-input[type=password],.sd-input[type=url],.sd-input[type=email],.sd-input[type=color],.sd-input[type=range]{box-sizing:content-box;width:calc(100% - 4 * var(--sjs-base-unit, var(--base-unit, 8px)));height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))))}.sd-input[type=time].sd-text__character-counter:focus-within,.sd-input[type=date].sd-text__character-counter:focus-within,.sd-input[type=datetime-local].sd-text__character-counter:focus-within,.sd-input[type=week].sd-text__character-counter:focus-within,.sd-input[type=month].sd-text__character-counter:focus-within,.sd-input[type=tel].sd-text__character-counter:focus-within,.sd-input[type=password].sd-text__character-counter:focus-within,.sd-input[type=url].sd-text__character-counter:focus-within,.sd-input[type=email].sd-text__character-counter:focus-within,.sd-input[type=color].sd-text__character-counter:focus-within,.sd-input[type=range].sd-text__character-counter:focus-within{width:calc(100% - 10 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-input[type=range]::-webkit-slider-runnable-track{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));border-radius:var(--sjs-base-unit, var(--base-unit, 8px));height:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-input[type=range]::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;margin-top:calc(-.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:100%;border:2px solid var(--sjs-primary-backcolor, var(--primary, #19b394));box-shadow:inset 0 0 0 2px var(--background, #fff);background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-input[type=range]::-moz-range-track{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));border-radius:var(--sjs-base-unit, var(--base-unit, 8px));height:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-input[type=range]::-moz-range-thumb{-webkit-appearance:none;appearance:none;margin-top:calc(-.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:100%;border:2px solid var(--sjs-primary-backcolor, var(--primary, #19b394));box-shadow:inset 0 0 0 2px var(--background, #fff);background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-comment{display:block;height:auto;min-width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));max-width:100%}.sd-comment__content,.sd-panel{position:relative}.sd-panel.sd-panel--as-page>.sd-panel__header.sd-panel__header{padding-top:0;padding-bottom:calc(.5 * var(--sd-base-vertical-padding) + var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-panel.sd-panel--as-page>.sd-panel__header.sd-panel__header:after{content:none}.sd-panel.sd-panel--as-page>.sd-panel__header.sd-panel__header .sd-panel__title{--page-title-font-size: var(--sjs-font-pagetitle-size, calc(1.5 * (var(--sjs-font-size, 16px))));font-family:var(--sjs-font-pagetitle-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-pagetitle-weight, 700);font-size:var(--page-title-font-size);color:var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91)));position:static;line-height:calc(1.33 * (var(--page-title-font-size)));margin:0 0 calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0px}.sd-panel.sd-panel--as-page>.sd-panel__header.sd-panel__header .sd-panel__title span{font-family:inherit;font-size:inherit;font-weight:inherit;color:inherit;line-height:inherit}.sd-panel.sd-panel--as-page>.sd-panel__header.sd-panel__header .sd-element__num{padding:initial;margin:initial;float:initial;width:initial}.sd-panel.sd-panel--as-page>.sd-panel__header.sd-panel__header .sd-panel__description{font-family:var(--sjs-font-pagedescription-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-pagedescription-weight, 400);font-size:var(--sjs-font-pagedescription-size, var(--sjs-font-size, 16px));color:var(--sjs-font-pagedescription-color, var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45)));position:static;line-height:calc(1.5 * (var(--sjs-font-pagedescription-size, var(--sjs-font-size, 16px))));margin:0 0 calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-panel.sd-panel--as-page>.sd-panel__header.sd-panel__header .sd-panel__required-text{display:none}.sd-panel.sd-panel--as-page>.sd-panel__errbox{margin:0 0 calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0;padding:calc(.5 * var(--sd-base-vertical-padding) + var(--sjs-base-unit, var(--base-unit, 8px))) var(--sd-base-padding)}.sd-panel.sd-panel--as-page>.sd-panel__content{padding-top:0}.sd-root--compact .sd-panel--as-page>.sd-panel__errbox{margin:0 0 var(--sd-base-vertical-padding) 0}.sd-row~.sd-row .sd-panel--as-page{padding-top:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-panel__required-text{color:var(--sjs-special-red, var(--red, #e60a3e))}.sd-panel__footer{box-sizing:border-box;padding-left:calc(var(--sd-base-padding) - 3 * var(--sjs-base-unit, var(--base-unit, 8px)));margin-left:calc(-1 * var(--sd-base-padding));width:calc(100% + 2 * var(--sd-base-padding));margin-bottom:calc(-1 * var(--sd-base-padding) + .5 * var(--sd-base-vertical-padding));margin-top:calc(.5 * var(--sd-base-vertical-padding));border-top:1px solid var(--sjs-border-light, var(--border-light, #eaeaea));padding-top:calc(.5 * var(--sd-base-vertical-padding))}.sd-panel--as-page .sd-panel__footer{padding:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0 0;margin:0;border:none;width:initial}.sd-panel__content{padding-top:var(--sd-base-padding)}.sd-panel.sd-element--nested>.sd-panel__content{padding-bottom:var(--sd-base-padding);--animation-padding-top: var(--sd-base-padding)}.sd-panel__content{--animation-padding-top: var(--sd-base-padding)}.sjs_sp_placeholder{color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));display:flex;align-items:center;justify-content:center;position:absolute;z-index:1;-webkit-user-select:none;user-select:none;pointer-events:none;width:100%;height:100%}.sjs_sp_container{position:relative;max-width:100%;border:1px dashed var(--sjs-border-default, var(--border, #d6d6d6));box-sizing:content-box}.sjs_sp_container>div>canvas:focus{outline:none}.sd-question--readonly .sjs_sp_container,.sd-question--preview .sjs_sp_container{border:none}.sd-question--readonly .sjs_sp_placeholder,.sd-question--preview .sjs_sp_placeholder{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sjs_sp_controls{position:absolute;left:0;bottom:0}.sjs_sp_controls>button{-webkit-user-select:none;user-select:none}.sjs_sp_controls.sd-signaturepad__controls{right:var(--sjs-base-unit, var(--base-unit, 8px));top:var(--sjs-base-unit, var(--base-unit, 8px));left:auto;bottom:auto}.sd-question--signature.sd-question--error .sjs_sp_placeholder{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))}.sd-signaturepad__background-image{position:absolute;top:0;left:0;object-fit:cover}.sd-signaturepad__loading-indicator{width:100%;height:100%;position:absolute;left:0;top:0}.sd-signaturepad__loading-indicator .sd-loading-indicator{position:absolute;right:var(--sjs-base-unit, var(--base-unit, 8px));top:var(--sjs-base-unit, var(--base-unit, 8px))}.sjs_sp_canvas{position:relative;max-width:100%;display:block}.sjs_sp__background-image{position:absolute;top:0;left:0;object-fit:cover;max-width:100%;width:100%;height:100%}.sd-checkbox__decorator{border-radius:calc(.5 * (var(--sjs-corner-radius, 4px)))}.sd-checkbox__svg{display:block;width:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-checkbox__svg use{fill:transparent;transition:fill var(--sjs-transition-duration, .15s)}.sd-checkbox--checked .sd-checkbox__svg use{fill:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sd-checkbox--checked.sd-checkbox--disabled .sd-checkbox__svg use{fill:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-checkbox--checked .sd-checkbox__control:focus+.sd-checkbox__decorator .sd-checkbox__svg use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-checkbox--checked.sd-checkbox--readonly .sd-checkbox__svg use{fill:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-checkbox--checked.sd-checkbox--preview .sd-checkbox__svg use{fill:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-matrixdynamic__btn.sd-matrixdynamic__add-btn{position:sticky;left:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-left:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));z-index:12}.sd-question--mobile .sd-matrixdynamic__footer:before{content:" ";display:block;position:relative;height:1px;background-color:var(--sjs-border-light, var(--border-light, #eaeaea));left:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));top:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(100% + 6 * var(--sjs-base-unit, var(--base-unit, 8px)));z-index:12}.sd-matrixdynamic__footer:first-child{padding-bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrixdynamic__footer:first-child:before{display:none}.sd-matrixdynamic__footer{padding-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-action.sd-matrixdynamic__remove-btn{opacity:.5}.sd-action.sd-matrixdynamic__remove-btn .sd-action__icon{width:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))))}.sd-matrixdynamic__btn{appearance:none;background:transparent;border:none;line-height:calc(1.5 * (var(--sjs-font-size, 16px)));font-size:var(--sjs-font-size, 16px);font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:600;padding:var(--sjs-base-unit, var(--base-unit, 8px)) 0}.sd-matrixdynamic__drag-element{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrixdynamic__drag-element:hover{background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sd-matrixdynamic__drag-element:after{content:" ";display:block;height:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));border:1px solid var(--sjs-border-default, var(--border, #d6d6d6));box-sizing:border-box;border-radius:calc(1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrixdynamic__placeholder .sd-matrixdynamic__add-btn{margin-left:0}.sd-table__row:hover .sd-drag-element__svg{visibility:visible}.sd-table__cell.sd-table__cell--drag>div{display:flex;justify-content:flex-end;align-items:center;margin-left:calc(-4 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));background-color:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));min-height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-drag-element__svg{width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:block;cursor:pointer;visibility:hidden}.sd-drag-element__svg use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}@keyframes borderAnimation{0%{border-width:0px}to{border-width:8px}}@keyframes paddingAnimation{0%{padding-top:0;padding-bottom:0}to{padding-top:24px;padding-bottom:32px}}@keyframes empty{}.sd-table__row--leave,.sd-table__row--enter{animation-name:empty;--move-whole-animation-duration: calc(var(--move-animation-duration) + var(--move-animation-delay));--fade-whole-animation-duration: calc(var(--fade-animation-duration) + var(--fade-animation-delay));animation-duration:max(var(--fade-whole-animation-duration),var(--move-whole-animation-duration))}.sd-table__row--leave>td,.sd-table__row--enter>td{animation-name:borderAnimation;animation-direction:var(--animation-direction);animation-timing-function:var(--animation-timing-function);animation-duration:var(--move-animation-duration);animation-fill-mode:forwards;animation-delay:var(--move-animation-delay)}.sd-table__row--leave>td>div,.sd-table__row--enter>td>div{animation-name:fadeIn,moveInWithOverflow;opacity:0;animation-direction:var(--animation-direction);animation-timing-function:var(--animation-timing-function);animation-fill-mode:forwards;animation-duration:var(--fade-animation-duration),var(--move-animation-duration);animation-delay:var(--fade-animation-delay),var(--move-animation-delay)}.sd-table__row--enter{--move-animation-delay: 0s;--move-animation-duration: var(--sjs-matrix-row-move-in-duration, .15s);--fade-animation-duration: var(--sjs-matrix-row-fade-in-duration, .25s);--fade-animation-delay: var(--sjs-matrix-row-fade-in-delay, .15s);--animation-direction: normal;--animation-timing-function: cubic-bezier(0, 0, .58, 1)}.sd-table__row--leave{--move-animation-delay: var(--sjs-matrix-row-move-out-delay, .1s);--move-animation-duration: var(--sjs-matrix-row-move-out-duration, .25s);--fade-animation-duration: var(--sjs-matrix-row-fade-out-duration, .1s);--fade-animation-delay: 0s;--animation-direction: reverse;--animation-timing-function: cubic-bezier(.42, 0, 1, 1)}.sd-table__row--detail.sd-table__row--enter>td,.sd-table__row--detail.sd-table__row--leave>td{animation-name:borderAnimation,paddingAnimation;animation-duration:var(--move-animation-duration);animation-fill-mode:forwards;animation-direction:var(--animation-direction);animation-timing-function:var(--animation-timing-function)}.sd-table__row--detail.sd-table__row--enter{--move-animation-delay: 0s;--move-animation-duration: var(--sjs-matrix-detail-row-move-in-duration, .15s);--fade-animation-duration: var(--sjs-matrix-detail-row-fade-in-duration, .5s);--fade-animation-delay: var(--sjs-matrix-detail-row-fade-in-delay, .15s);--animation-direction: normal;--animation-timing-function: cubic-bezier(0, 0, .58, 1)}.sd-table__row--detail.sd-table__row--leave{--move-animation-delay: var(--sjs-matrix-detail-row-move-out-delay, .1s);--move-animation-duration: var(--sjs-matrix-detail-row-move-out-duration, .25s);--fade-animation-duration: var(--sjs-matrix-detail-row-fade-out-duration, .15s);--fade-animation-delay: 0s;--animation-direction: reverse;--animation-timing-function: cubic-bezier(.42, 0, 1, 1)}.sd-table{width:100%;background:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));border-collapse:separate;border-spacing:0;white-space:normal}.sd-table>thead>tr>th{border-top:0;border-bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid transparent}.sd-table__cell{border-top:var(--sjs-base-unit, var(--base-unit, 8px)) solid transparent;border-bottom:var(--sjs-base-unit, var(--base-unit, 8px)) solid transparent;border-left:none;border-right:none;background-clip:padding-box;box-sizing:content-box}.sd-table.sd-table--columnsautowidth .sd-table__cell:not(.sd-table__cell--actions):not(.sd-table__cell--action):not(.sd-table__cell--empty.sd-table__cell--error){width:10000px}.sd-table__row:first-of-type>.sd-table__cell{border-top:0}.sd-table__row:last-of-type>.sd-table__cell{border-bottom:0}.sd-table--align-top .sd-table__cell{vertical-align:top}.sd-table--no-header{padding-top:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content .sd-table--no-header{padding-top:0}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content .sd-question--table .sd-question__content{padding-bottom:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-bottom:calc(-.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-top:var(--sjs-base-unit, var(--base-unit, 8px));margin-top:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content .sd-question--table>.sd-question__header~.sd-question__content{padding-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));--animation-padding-top: calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content .sd-question--table>.sd-question__header~.sd-question__content .sd-table--no-header{padding-top:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content .sd-question--table>.sd-question__content .sd-table-wrapper .sd-table:not(.sd-table--no-header){margin-top:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content .sd-question--table.sd-question--error-top>.sd-question__content .sd-table-wrapper .sd-table:not(.sd-table--no-header){margin-top:0}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content>.sd-row:not(:first-of-type) .sd-question--table>.sd-question__content .sd-table-wrapper .sd-table:not(.sd-table--no-header){margin-top:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content>.sd-row:not(:first-of-type) .sd-question--table.sd-question--error-top>.sd-question__content .sd-table-wrapper .sd-table:not(.sd-table--no-header){margin-top:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content .sd-question--table>.sd-question__header~.sd-question__content .sd-table-wrapper .sd-table:not(.sd-table--no-header){margin-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-panel:not(.sd-panel--as-page)>.sd-panel__content>.sd-row:not(:first-of-type) .sd-question--table>.sd-question__header~.sd-question__content .sd-table-wrapper .sd-table:not(.sd-table--no-header){margin-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows{margin-left:var(--sjs-base-unit, var(--base-unit, 8px));margin-right:var(--sjs-base-unit, var(--base-unit, 8px));width:calc(100% - 2 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__cell:not(.sd-table__cell--header):first-of-type{padding-left:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__cell:last-of-type{padding-right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:first-of-type>.sd-table__cell{border-top:var(--sjs-base-unit, var(--base-unit, 8px)) solid transparent}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:last-of-type>.sd-table__cell{border-bottom:var(--sjs-base-unit, var(--base-unit, 8px)) solid transparent}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd)>td.sd-table__cell:not(.sd-table__cell--actions),.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd) td:first-of-type{border-top-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)));border-bottom-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)));background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd)>td.sd-table__cell:not(.sd-table__cell--actions).sd-matrix__text--checked,.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd) td:first-of-type.sd-matrix__text--checked{background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd)>td.sd-table__cell:not(.sd-table__cell--actions) .sd-input,.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd) td:first-of-type .sd-input{background-color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd)>td.sd-table__cell:not(.sd-table__cell--actions) .sd-item:not(.sd-item--error) .sd-item__decorator,.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd) td:first-of-type .sd-item:not(.sd-item--error) .sd-item__decorator{background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd)>td.sd-table__cell:not(.sd-table__cell--actions) .sd-item:not(.sd-item--error).sd-item--checked .sd-item__decorator,.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd) td:first-of-type .sd-item:not(.sd-item--error).sd-item--checked .sd-item__decorator{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd)>td.sd-table__cell:not(.sd-table__cell--actions) .sd-item:not(.sd-item--error).sd-item--checked .sd-item__control:focus+.sd-item__decorator,.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd) td:first-of-type .sd-item:not(.sd-item--error).sd-item--checked .sd-item__control:focus+.sd-item__decorator{background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd)>td.sd-table__cell:not(.sd-table__cell--actions) .sd-item:not(.sd-item--error).sd-item--readonly.sd-item--checked .sd-item__decorator,.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd) td:first-of-type .sd-item:not(.sd-item--error).sd-item--readonly.sd-item--checked .sd-item__decorator{background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd)>td.sd-table__cell:not(.sd-table__cell--actions) .sd-item:not(.sd-item--error).sd-item--preview.sd-item--preview .sd-item__decorator,.sd-question:not(.sd-question--mobile) .sd-table--alternate-rows .sd-table__row:nth-of-type(odd) td:first-of-type .sd-item:not(.sd-item--error).sd-item--preview.sd-item--preview .sd-item__decorator{background-color:transparent}.sd-table__cell{font-weight:400;font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));padding:0 var(--sjs-base-unit, var(--base-unit, 8px));color:var(--sjs-general-forecolor, var(--foreground, #161616));text-align:center}.sd-table__cell:not(.sd-table__cell--empty):not(.sd-table__cell--actions):not(:empty){min-width:calc(15 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-table__cell .sd-item{text-align:initial}.sd-table__cell--error{border:none;padding:0 var(--sjs-base-unit, var(--base-unit, 8px))}.sd-table__cell--error .sd-question__erbox{margin:0}.sd-table__cell--error-top{vertical-align:bottom;padding-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-table__cell--error-bottom{vertical-align:top;padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-table__cell--item .sd-selectbase__item{text-align:center;justify-content:center}.sd-table__cell--item .sd-selectbase__label{justify-content:center}.sd-question--disabled .sd-table__cell{opacity:.25}.sd-root--readonly .sd-question--disabled .sd-table__cell{opacity:1}.sd-table__cell--header .sv-vue-title-additional-div,.sd-table__cell--header{font-size:0;line-height:0}.sd-table__cell--header span{font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));font-weight:var(--sjs-font-questiontitle-weight, 600)}.sd-table__cell--header{font-weight:600;color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));vertical-align:top;padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) var(--sjs-base-unit, var(--base-unit, 8px))}.sd-table__cell--header:not(.sd-table__cell--empty){min-width:calc(15 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrixdropdown .sd-table__cell--header.sd-table__cell--empty{min-width:calc(15 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrixdropdown .sd-table__cell--header.sd-table__cell--action{min-width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrixdropdown .sd-table__cell--header:not(.sd-table__cell--empty){min-width:calc(15 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrixdropdown .sd-table__cell--header:not(.sd-table__cell--empty).sd-table__cell--dropdown,.sd-matrixdropdown .sd-table__cell--header:not(.sd-table__cell--empty).sd-table__cell--rating{min-width:calc(22 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrixdropdown .sd-table__cell--header:not(.sd-table__cell--empty).sd-table__cell--boolean{min-width:calc(18 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-table__cell--footer{text-align:end;padding-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-table__cell--footer-total{font-weight:600;text-align:start}.sd-table__cell--detail-panel{border-top:var(--sjs-base-unit, var(--base-unit, 8px)) solid transparent;padding:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px)))) var(--sjs-base-unit, var(--base-unit, 8px)) calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-bottom:var(--sjs-base-unit, var(--base-unit, 8px)) solid transparent}.sd-table__cell--actions .sv-action-bar,.sd-matrixdynamic__add-btn .sv-action-bar{overflow:visible}.sd-table__cell--actions:not(.sd-table__cell--vertical){width:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-table__cell--actions:not(.sd-table__cell--vertical):not(.sd-table__cell--drag):first-of-type{padding-left:0;width:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-width:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-table__cell--detail-button{border:none;background:transparent;border-radius:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:var(--sjs-base-unit, var(--base-unit, 8px));transition:background var(--sjs-transition-duration, .15s)}.sd-table__cell--detail-button svg{display:block;width:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));height:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));fill:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));transition:fill var(--sjs-transition-duration, .15s)}.sd-table__cell--detail-button:hover,.sd-table__cell--detail-button:focus{background:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));outline:none}.sd-table__cell--detail-button:hover svg,.sd-table__cell--detail-button:focus svg{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-table__cell--actions{white-space:nowrap}.sd-table__cell--actions.sd-table__cell--vertical .sd-action-bar{justify-content:center}.sd-table__cell--row-text{font-weight:var(--sjs-font-questiontitle-weight, 600);color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));text-align:start;min-width:calc(12 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) var(--sjs-base-unit, var(--base-unit, 8px))}.sd-matrix__question-wrapper{position:relative}.sd-table__question-wrapper:not(:focus-within):hover{position:relative}.sd-table__cell--actions:not(.sd-table__cell--vertical),.sd-table__cell--empty,.sd-table__cell--row-text,.sd-table__cell--footer-total,.sd-matrix__cell:first-of-type,.sd-matrix tr>td:first-of-type{position:sticky;background-color:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));z-index:12}.sd-table__cell--actions:not(.sd-table__cell--vertical):first-of-type,.sd-table__cell--empty:first-of-type,.sd-table__cell--row-text:first-of-type,.sd-table__cell--footer-total:first-of-type,.sd-matrix__cell:first-of-type:first-of-type,.sd-matrix tr>td:first-of-type:first-of-type{left:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-table__cell--actions:not(.sd-table__cell--vertical):last-of-type,.sd-table__cell--empty:last-of-type,.sd-table__cell--row-text:last-of-type,.sd-table__cell--footer-total:last-of-type,.sd-matrix__cell:first-of-type:last-of-type,.sd-matrix tr>td:first-of-type:last-of-type{right:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-table__cell--actions:not(.sd-table__cell--vertical):last-child .sd-action-bar{margin-right:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));justify-content:flex-end;background:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))))}.sd-question.sd-question--table{position:relative;overflow-x:auto}.sd-question--table.sd-element--collapsed,.sd-question--table.sd-element--nested{overflow-x:visible}.sd-question--table .sd-question__header--location--left{z-index:12}.sd-table-wrapper{display:flex;margin:0 calc(-1 * var(--sd-base-padding));width:fit-content;min-width:calc(100% + 2 * var(--sd-base-padding))}.sd-table-wrapper:before,.sd-table-wrapper:after{content:"";display:block;position:sticky;min-height:100%;width:calc(var(--sd-base-padding) - var(--sjs-base-unit, var(--base-unit, 8px)));flex-shrink:0;background:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));z-index:11}.sd-table-wrapper:before{left:calc(-1 * var(--sd-base-padding))}.sd-table-wrapper:after{right:calc(-1 * var(--sd-base-padding))}.sd-table-wrapper>*{flex-basis:100%}.sd-element--with-frame.sd-element--compact .sd-table{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sd-element--with-frame.sd-element--compact .sd-table-wrapper:before,.sd-element--with-frame.sd-element--compact .sd-table-wrapper:after{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sd-element--with-frame.sd-element--compact .sd-table__cell--actions:not(.sd-table__cell--vertical):last-child .sd-action-bar{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sd-element--with-frame.sd-element--compact .sd-table__cell--actions:not(.sd-table__cell--vertical),.sd-element--with-frame.sd-element--compact .sd-table__cell--empty,.sd-element--with-frame.sd-element--compact .sd-table__cell--row-text,.sd-element--with-frame.sd-element--compact .sd-table__cell--footer-total,.sd-element--with-frame.sd-element--compact .sd-matrix__cell:first-of-type,.sd-element--with-frame.sd-element--compact .sd-matrix tr>td:first-of-type{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sd-question--table>.sd-question__header,.sd-question--table .sd-question__description--under-input .sv-string-viewer{position:sticky;left:0}.sd-question--table>.sd-question__content{padding-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));--animation-padding-top: calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-width:min-content}.sd-question--table.sd-element--nested>.sd-question__content{min-width:auto;overflow-x:auto}.sd-question--table.sd-element--nested>.sd-question__content,.sd-question--table:not(.sd-element--with-frame):not(.sd-element--nested){padding-right:var(--sd-base-padding);margin-right:calc(-1 * var(--sd-base-padding));padding-left:var(--sd-base-padding);margin-left:calc(-1 * var(--sd-base-padding));box-sizing:content-box}.sd-row--multiple .sd-question--table.sd-element--nested>.sd-question__content{padding-right:calc(var(--sd-base-padding) + 2px)}.sd-question--scroll{overflow-x:scroll}.sd-table__row-disabled>.sd-table__cell{opacity:.25}.sd-question--mobile.sd-question.sd-question--table>.sd-question__content{padding-top:0;--animation-padding-top: 0}.sd-question--mobile.sd-question--table,.sd-question--mobile.sd-question--scroll{overflow-x:visible}.sd-question--mobile>.sd-question__content{min-width:auto}.sd-question--mobile .sd-table-wrapper{width:auto}.sd-question--mobile .sd-table{display:block;width:calc(100% - 2 * var(--sd-base-padding) + 2 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-question--mobile .sd-table>tbody{display:block}.sd-question--mobile .sd-table>tfoot{display:block}.sd-question--mobile .sd-table__cell.sd-matrix__cell{display:flex;align-items:flex-start;border-top:none;border-bottom:none}.sd-question--mobile .sd-table__cell.sd-matrix__cell .sd-matrix__responsive-title{margin-left:var(--sjs-base-unit, var(--base-unit, 8px));text-align:start}.sd-question--mobile .sd-table__cell.sd-matrix__cell:first-of-type{padding-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-question--mobile .sd-table thead{display:none}.sd-question--mobile .sd-table tr{display:flex;flex-direction:column}.sd-question--mobile .sd-matrix__table .sd-table__row{padding-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-question--mobile .sd-table__row{padding-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table__row--expanded .sd-table__cell-action--show-detail-mobile{display:none}.sd-question--mobile .sd-table:not(.sd-matrix__table) .sd-table__row{padding-bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table:not(.sd-matrix__table) .sd-table__row.sd-table__row--has-end-actions{padding-bottom:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table:not(.sd-matrix__table) tr:not(.sd-table__row--has-end-actions){padding-bottom:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table:not(.sd-matrix__table) tr:not(.sd-table__row--has-end-actions):not(:last-of-type){padding-bottom:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table:not(.sd-matrix__table) tr:not(.sd-table__row--has-end-actions):not(:last-of-type):after{bottom:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table:not(.sd-matrix__table) tfoot tr:before,.sd-question--mobile .sd-table:not(.sd-matrix__table) tr:not(.sd-table__row--expanded):after{content:" ";display:block;position:relative;height:1px;background-color:var(--sjs-border-light, var(--border-light, #eaeaea));left:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(100% + 4 * var(--sjs-base-unit, var(--base-unit, 8px)));z-index:12}.sd-question--mobile .sd-table:not(.sd-matrix__table) tr:not(.sd-table__row--expanded):after{bottom:0}.sd-question--mobile .sd-table:not(.sd-matrix__table) tr:last-of-type:after{display:none}.sd-question--mobile .sd-table:not(.sd-matrix__table) tfoot tr{padding-top:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table:not(.sd-matrix__table) tfoot tr:before{background-color:transparent}.sd-question--mobile .sd-table:not(.sd-table--has-footer) .sd-table__row:last-of-type .sd-table__cell-action--show-detail-mobile,.sd-question--mobile .sd-table:not(.sd-table--has-footer) .sd-table__row:last-of-type .sd-table__cell-action--remove-row{margin-bottom:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-matrix__label{justify-content:flex-start}.sd-question--mobile .sd-table__cell{border-top:none;border-bottom:none;display:block;padding-top:0;padding-bottom:0;text-align:start}.sd-question--mobile .sd-table__cell{margin-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table__cell--error{margin-top:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table__cell--error.sd-table__cell--error-bottom .sd-question__erbox{margin-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-question--mobile .sd-table__cell:first-of-type,.sd-question--mobile .sd-matrix__cell{margin-top:0}.sd-question--mobile .sd-table__cell--footer:not(.sd-question--answered){display:none}.sd-question--mobile .sd-table__responsive-title{padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px));font-weight:600;display:block;text-align:start}.sd-question--mobile .sd-table__responsive-title .sv-string-viewer{white-space:normal}.sd-question--mobile .sd-table--no-header{padding-top:0}.sd-question--mobile .sd-table--no-header .sd-table__responsive-title{display:none}.sd-question--mobile .sd-table__cell--detail-panel{padding-top:0;padding-bottom:0;border-top:0;border-bottom:0}.sd-question--mobile .sd-table__cell--detail-panel .sd-panel__content{padding-top:0}.sd-question--mobile .sd-table__cell.sd-table__cell--actions{width:auto;margin-top:var(--sjs-base-unit, var(--base-unit, 8px));margin-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-question--mobile .sd-table__cell.sd-table__cell--actions .sd-action-bar{margin-right:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-left:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));background:var(--sjs-general-backcolor, var(--background, #fff))}.sd-question--mobile .sd-table__cell.sd-table__cell--actions #show-detail-mobile{flex-grow:1}.sd-question--mobile .sd-action.sd-action.sd-matrixdynamic__remove-btn{opacity:1;padding:var(--sjs-base-unit, var(--base-unit, 8px)) calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-action.sd-action.sd-matrixdynamic__remove-btn .sd-action__icon{display:none}.sd-question--mobile .sd-action.sd-action.sd-matrixdynamic__remove-btn:after{content:attr(title)}.sd-question--mobile .sd-matrixdynamic__footer{padding-top:var(--sjs-base-unit, var(--base-unit, 8px));margin-bottom:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table__cell--footer-total:not(.sd-matrix__cell),.sd-question--mobile .sd-table__cell--row-text:not(.sd-matrix__cell){color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-question--mobile .sd-matrixdropdown.sd-table tr:after{bottom:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-matrixdropdown.sd-table tr:last-child:after{content:none}.sd-question--mobile .sd-table__cell.sd-table__cell--error-top{margin-top:0}.sd-question--mobile .sd-table__cell--error-top:first-of-type~.sd-table__cell:nth-of-type(2){margin-top:0}.sd-question--mobile .sd-table__cell--error-top .sd-question__erbox{margin-top:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-bottom:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-question--mobile .sd-table__cell--error-top:first-of-type .sd-question__erbox{margin-top:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-question--mobile .sd-table__question-wrapper .sd-boolean-root{margin:initial}.sd-table__cell--detail-panel .sd-panel__content{padding-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-table__question-wrapper .sd-boolean-root{margin:auto}.sd-table__cell--footer .sd-table__question-wrapper--expression .sd-expression{padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-bottom:1px solid var(--sjs-border-light, var(--border-light, #eaeaea));font-size:var(--sjs-font-size, 16px);font-weight:600;line-height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-table__cell--footer .sd-table__question-wrapper--left{text-align:start}.sd-table__cell--footer .sd-table__question-wrapper--center{text-align:center}.sd-table__cell--footer .sd-table__question-wrapper--right{text-align:end}.sd-table.sd-matrixdynamic{table-layout:auto}.sd-page{position:relative;display:flex;flex-direction:column;align-items:flex-start;padding:0 calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:100%;box-sizing:border-box}.sd-root-modern--mobile .sd-page{padding:0}.sd-page .sd-page__title{--page-title-font-size: var(--sjs-font-pagetitle-size, calc(1.5 * (var(--sjs-font-size, 16px))));font-family:var(--sjs-font-pagetitle-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-pagetitle-weight, 700);font-size:var(--page-title-font-size);color:var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91)));position:static;line-height:calc(1.33 * (var(--page-title-font-size)));margin:0 0 calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0px}.sd-page .sd-page__description{font-family:var(--sjs-font-pagedescription-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-pagedescription-weight, 400);font-size:var(--sjs-font-pagedescription-size, var(--sjs-font-size, 16px));color:var(--sjs-font-pagedescription-color, var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45)));position:static;line-height:calc(1.5 * (var(--sjs-font-pagedescription-size, var(--sjs-font-size, 16px))));margin:0 0 calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-page__errbox{padding:calc(.5 * var(--sd-base-vertical-padding) + var(--sjs-base-unit, var(--base-unit, 8px))) var(--sd-base-padding)}.sd-page__title~.sd-page__errbox,.sd-page__description~.sd-page__errbox{margin-top:calc(.5 * var(--sd-base-vertical-padding) + var(--sjs-base-unit, var(--base-unit, 8px)));margin-bottom:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-root--compact .sd-page__title~.sd-page__errbox,.sd-root--compact .sd-page__description~.sd-page__errbox{margin-bottom:0}.sd-row{display:flex;flex-direction:row;width:100%;box-sizing:border-box;margin-top:var(--sd-base-vertical-padding)}.sd-row.sd-page__row{margin-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-page__row.sd-row--compact{margin-top:var(--sd-base-vertical-padding)}.sd-row:first-of-type{margin-top:0}.sd-page__title~.sd-row.sd-page__row:not(.sd-row--compact),.sd-page__description~.sd-row.sd-page__row:not(.sd-row--compact){margin-top:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-page__title~.sd-page__row.sd-row--compact,.sd-page__description~.sd-page__row.sd-row--compact{margin-top:var(--sd-base-vertical-padding)}.sd-page__title~.sd-page__row.sd-row.sd-row--enter.sd-row-delayed-enter,.sd-page__description~.sd-page__row.sd-row.sd-row--enter.sd-row-delayed-enter{margin-top:0}.sd-row.sd-page__row:not(.sd-row--compact)~.sd-row.sd-page__row:not(.sd-row--compact){margin-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-row.sd-page__row:not(.sd-row--compact)~.sd-page__row.sd-row.sd-row--enter.sd-row-delayed-enter{margin-top:0}.sd-row--multiple{row-gap:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-left:calc(-1 * var(--sd-base-padding));width:calc(100% + var(--sd-base-padding));flex-wrap:wrap}.sd-row--multiple>div{box-sizing:border-box;--animation-padding-left: calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-left:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;align-items:stretch}.sd-panel.sd-panel--as-page>.sd-panel__content>.sd-row--multiple:not(.sd-row--compact){margin-left:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(100% + var(--base-unit) * 2)}.sd-panel:not(.sd-panel--as-page) .sd-row--multiple{row-gap:var(--sd-base-vertical-padding)}.sd-panel:not(.sd-panel--as-page) .sd-row--multiple>div{--animation-padding-left: var(--sd-base-padding);padding-left:var(--sd-base-padding)}.sd-row--multiple.sd-row--compact>div{--animation-padding-left: var(--sd-base-padding);padding-left:var(--sd-base-padding)}.sd-page__row.sd-row--multiple{margin-left:calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(100% + 2 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-page__row.sd-row--multiple.sd-row--compact{padding:0;row-gap:var(--sd-base-vertical-padding);margin-left:calc(-1 * var(--sd-base-padding));width:calc(100% + var(--sd-base-padding))}.sd-row__panel{box-sizing:border-box;width:100%}.sd-row__question{box-sizing:border-box;width:100%;white-space:nowrap}.sd-row.sd-row--enter{margin-top:0}.sd-row--enter{animation-fill-mode:forwards;animation-name:fadeIn,moveInWithOverflow;min-height:0!important;opacity:0;height:0;animation-timing-function:cubic-bezier(0,0,.58,1);animation-delay:var(--sjs-row-fade-in-delay, .15s),0s,0s;animation-duration:var(--sjs-row-fade-in-duration, .5s),var(--sjs-row-move-in-duration, .15s),var(--sjs-row-move-in-duration, .15s)}.sd-row--delayed-enter{animation-delay:calc(var(--sjs-row-fade-in-delay, .15s) + var(--sjs-row-fade-in-animation-delay, .4s)),var(--sjs-row-fade-in-animation-delay, .4s),var(--sjs-row-fade-in-animation-delay, .4s)}.sd-row--leave{animation-name:fadeIn,moveInWithOverflow;animation-timing-function:cubic-bezier(.42,0,1,1);animation-fill-mode:forwards;animation-direction:reverse;min-height:0!important;animation-delay:0s,var(--sjs-row-move-out-delay, .1s),var(--sjs-row-move-out-delay, .1s);animation-duration:var(--sjs-row-fade-out-duration, .15s),var(--sjs-row-move-out-duration, .25s),var(--sjs-row-move-out-duration, .25s)}.sd-row--enter .sd-element-wrapper--enter,.sd-row--leave .sd-element-wrapper--leave{animation:none}.sv-skeleton-element{min-height:50px}.sd-title{display:block;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;color:var(--sjs-general-forecolor, var(--foreground, #161616));flex-direction:row;white-space:normal}.sd-title.sd-container-modern__title{display:flex;align-items:center;padding:var(--sd-page-vertical-padding) var(--sd-page-vertical-padding);font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));gap:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-shadow:0 2px 0 var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-title.sd-container-modern__title .sd-logo.sv-logo--right{margin-left:auto}.sd-title.sd-container-modern__title .sd-logo__image{margin-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-header__text{display:flex;flex-direction:column;gap:var(--sjs-base-unit, var(--base-unit, 8px));flex-grow:1}.sd-header__text .sd-title{--survey-title-font-size: var(--sjs-font-surveytitle-size, calc(2 * (var(--sjs-font-size, 16px))));font-size:var(--survey-title-font-size);line-height:calc(1.25 * (var(--survey-title-font-size)));color:var(--sjs-font-surveytitle-color, var(--sjs-primary-backcolor, var(--primary, #19b394)));font-family:var(--sjs-font-surveytitle-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-surveytitle-weight, 700)}.sd-header__text h3{line-height:calc(2.5 * (var(--sjs-font-size, 16px)))}.sd-header__text h5{font-size:var(--sjs-font-size, 16px);font-weight:400;line-height:calc(1.5 * (var(--sjs-font-size, 16px)))}.sd-header__text .sd-description{--survey-description-font-size: var(--sjs-font-surveydescription-size, var(--sjs-font-size, 16px));font-size:var(--survey-description-font-size);line-height:calc(1.5 * (var(--survey-description-font-size)));color:var(--sjs-font-surveydescription-color, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)));font-family:var(--sjs-font-surveydescription-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-surveydescription-weight, 400)}.sd-title .sv-title-actions{width:100%;justify-content:space-between}.sd-root-modern .sd-container-modern__title{background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sd-root-modern .sd-container-modern__title .sd-header__text h3{margin:0}.sd-root-modern .sd-container-modern__title .sd-description{margin:0;color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-title .sv-title-actions{align-items:flex-start;width:calc(100% + 3 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-title .sv-title-actions .sv-title-actions__title{flex-wrap:wrap;flex:0 1 auto;max-width:calc(100% - 3 * var(--sjs-base-unit, var(--base-unit, 8px)));white-space:initial;min-width:unset}.sd-root--compact .sd-title .sv-title-actions{width:100%}.sd-action-title-bar{flex:1 9 auto;min-width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));justify-content:flex-end;margin:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-action-title-bar .sv-action{flex:0 0 auto}.sd-action-title-bar.sd-action-bar--empty{min-width:0}.sd-description{font-style:normal;font-family:var(--sjs-font-questiondescription-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-questiondescription-weight, 400);color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));font-size:var(--sjs-font-questiondescription-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-questiondescription-size, var(--sjs-font-size, 16px))));white-space:normal}.sd-description.sd-question__description--under-input{padding-top:calc(.375 * var(--sd-base-vertical-padding))}.sd-element__header .sd-description{margin-top:calc(.25 * var(--sd-base-vertical-padding) - .5 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-item{display:flex;padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-item--disabled.sd-item--disabled .sd-item__decorator,.sd-item__decorator{display:flex;align-items:center;justify-content:center;width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box;background-color:var(--sjs-editorpanel-backcolor, var(--sjs-editor-background, var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))));border:none;flex-shrink:0;margin-top:calc((1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))) - 3 * (var(--sjs-base-unit, var(--base-unit, 8px)))) / 2);box-shadow:var(--sjs-shadow-inner, inset 0px 1px 2px 0px rgba(0, 0, 0, .15)),0 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394));transition:box-shadow var(--sjs-transition-duration, .15s),background var(--sjs-transition-duration, .15s)}.sd-item--readonly.sd-item--readonly.sd-item:not(.sd-item--checked) .sd-item__control:focus+.sd-item__decorator,.sd-item--readonly.sd-item--readonly .sd-item__decorator{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)));box-shadow:none;transition:none}.sd-item--preview.sd-item--preview .sd-item__decorator{background-color:transparent;box-shadow:none;transition:none}.sd-item--checked .sd-item__decorator{box-shadow:none}.sd-item__control:focus+.sd-item__decorator{background:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));outline:none;box-shadow:var(--sjs-shadow-inner-reset, inset 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-item--allowhover:not(.sd-item--readonly) .sd-selectbase__label:hover .sd-item__decorator{background:var(--sjs-editorpanel-hovercolor, var(--sjs-general-backcolor-dim-dark, rgb(243, 243, 243)));outline:none}.sd-item--checked .sd-item__decorator{background:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-item__control-label{font-family:var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-style:normal;font-weight:var(--sjs-font-editorfont-weight, 400);line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));white-space:normal;width:100%;text-align:start;min-width:0}.sd-item__control-label .sv-string-viewer{max-width:100%;overflow:hidden;text-overflow:ellipsis;display:block}.sd-item--disabled .sd-item__control-label{color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));opacity:.25}.sd-root--readonly .sd-item--disabled .sd-item__control-label{color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-item--error .sd-item__decorator{background:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))}.sd-selectbase{border:none;margin:0;padding:0;min-inline-size:0;min-width:0}.sd-selectbase--row{border:none;margin:0;padding:0;display:flex;flex-wrap:wrap;column-gap:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-selectbase--multi-column{display:flex;flex:1 1 0px;flex-wrap:nowrap;overflow:auto;padding:0 2px;margin:0 -2px}.sd-selectbase__label{display:inline-flex;position:relative;gap:var(--sjs-base-unit, var(--base-unit, 8px));vertical-align:top;max-width:100%}.sd-selectbase__column{vertical-align:top}.sd-selectbase__column{display:block;box-sizing:border-box;flex:1 1 0px;max-width:100%}.sd-selectbase__column:not(:last-child){padding-right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-selectbase__column.sv-q-column-1{width:100%}.sd-item--enter,.sd-item--leave{animation-name:moveInWithOverflow,fadeIn;opacity:0;animation-fill-mode:forwards;animation-timing-function:linear;animation-direction:var(--animation-direction);animation-duration:var(--move-animation-duration),var(--fade-animation-duration);animation-delay:var(--move-animation-delay),var(--fade-animation-delay)}.sd-item--enter{--animation-direction: normal;--move-animation-duration: var(--sjs-ranking-move-in-duration, .15s);--move-animation-delay: 0s;--fade-animation-duration: var(--sjs-ranking-fade-in-duration, .1s);--fade-animation-delay: var(--sjs-ranking-fade-in-delay, .15s)}.sd-item--leave{--animation-direction: reverse;--move-animation-duration: var(--sjs-ranking-move-out-duration, .15s);--move-animation-delay: var(--sjs-ranking-move-out-delay, 0ms);--fade-animation-duration: var(--sjs-ranking-fade-out-duration, .1s);--fade-animation-delay: 0s}div[class*=sv-q-column-]:not(:first-of-type) .sd-item--enter{--move-animation-duration: 0s;--fade-animation-delay: 0s}.sd-radio__decorator{border-radius:50%}.sd-radio__decorator:after{content:" ";display:block;width:var(--sjs-base-unit, var(--base-unit, 8px));height:var(--sjs-base-unit, var(--base-unit, 8px));border-radius:50%;background-color:transparent;transition:background-color var(--sjs-transition-duration, .15s)}.sd-radio--checked .sd-radio__decorator:after{content:" ";display:block;width:var(--sjs-base-unit, var(--base-unit, 8px));height:var(--sjs-base-unit, var(--base-unit, 8px));border-radius:50%;background-color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sd-radio--checked.sd-radio--disabled .sd-radio__decorator:after{background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-radio--checked.sd-radio--readonly .sd-radio__decorator:after{background-color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-radio--checked.sd-radio--preview .sd-radio__decorator:after{display:none}.sd-radio--checked.sd-radio--preview .sd-radio__decorator .sd-radio__svg{fill:var(--sjs-general-forecolor, var(--foreground, #161616));display:block;width:calc(2.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(2.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-radio--checked .sd-radio__control:focus+.sd-radio__decorator:after{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-radio__svg{display:none}.sd-visuallyhidden{position:absolute;height:1px;width:1px;overflow:hidden;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.sd-matrix fieldset{border:none;padding:0;margin:0}.sd-matrix__label{display:flex;position:relative;justify-content:center}.sd-matrix__text{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrix__text--checked{background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-matrix__cell:first-of-type{font-weight:var(--sjs-font-questiontitle-weight, 600);text-align:start}:root{--sd-rating-bad-color: var(--sjs-special-red, var(--red, #e60a3e));--sd-rating-normal-color: var(--sjs-special-yellow, var(--yellow, #ff9814));--sd-rating-good-color: var(--sjs-special-green, var(--green, #19b394));--sd-rating-bad-color-light: var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)));--sd-rating-normal-color-light: var(--sjs-special-yellow-light, var(--yellow-light, rgba(255, 152, 20, .1)));--sd-rating-good-color-light: var(--sjs-special-green-light, var(--green-light, rgba(25, 179, 148, .1)))}.sd-rating{overflow-x:auto;min-height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating fieldset{display:flex;border:none;padding:0 0 2px;flex-wrap:nowrap;gap:var(--sjs-base-unit, var(--base-unit, 8px));margin-inline-start:0;align-items:center}.sd-rating.sd-rating--wrappable fieldset{flex-wrap:wrap;min-width:0}.sd-rating.sd-rating--labels-top fieldset{padding-top:calc(4.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));position:relative}.sd-rating.sd-rating--labels-top fieldset .sd-rating__min-text{position:absolute;margin:0;left:0;top:0;border:0}.sd-rating.sd-rating--labels-top fieldset .sd-rating__max-text{position:absolute;margin:0;right:0;top:0;border:0}.sd-rating.sd-rating--labels-bottom fieldset{padding-bottom:calc(4.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));position:relative}.sd-rating.sd-rating--labels-bottom fieldset .sd-rating__min-text{position:absolute;margin:0;left:0;bottom:0;border:0}.sd-rating.sd-rating--labels-bottom fieldset .sd-rating__max-text{position:absolute;margin:0;right:0;bottom:0;border:0}.sd-rating.sd-rating--labels-diagonal fieldset{padding-top:calc(4.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:calc(4.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));position:relative}.sd-rating.sd-rating--labels-diagonal fieldset .sd-rating__min-text{position:absolute;margin:0;left:0;top:0;border:0}.sd-rating.sd-rating--labels-diagonal fieldset .sd-rating__max-text{position:absolute;margin:0;right:0;bottom:0;border:0}.sd-rating--small{min-height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin:auto}.sd-rating--small fieldset{padding:0;gap:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-rating__item{position:relative;background:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));white-space:nowrap;padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(2.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;justify-content:center;align-items:center;box-sizing:border-box;min-width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));text-align:center;border:0px solid transparent;color:var(--sjs-general-forecolor, var(--foreground, #161616));fill:var(--sjs-general-forecolor, var(--foreground, #161616));font-size:var(--sjs-font-size, 16px);box-shadow:var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15)),inset 0 0 0 0 var(--sjs-general-backcolor, var(--background, #fff));transition:box-shadow var(--sjs-transition-duration, .15s),background-color var(--sjs-transition-duration, .15s)}.sd-rating__item--fixed-size{width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:0}legend+.sd-rating__item,legend+sv-ng-rating-item-smiley+.sd-rating__item-smiley,legend+sv-ng-rating-item+.sd-rating__item{margin-inline-start:2px}.sd-rating__item--error{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)));box-shadow:0 1px 2px transparent;border:none}.sd-rating__item.sd-rating__item--disabled{color:var(--sjs-general-forecolor, var(--foreground, #161616));fill:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-rating__item.sd-rating__item--selected.sd-rating__item--disabled{color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));fill:var(--sjs-general-forecolor, var(--foreground, #161616));background-color:var(--lbr-dialog-screen-color, var(--background-semitransparent, rgba(144, 144, 144, .5)));border:none}.sd-rating__item.sd-rating__item--readonly{fill:transparent;background-color:transparent;border:2px solid var(--sjs-border-inside, var(--border-inside, rgba(0, 0, 0, .16)));box-shadow:none;transform:none}.sd-rating__item.sd-rating__item--readonly .sd-rating__item-text.sd-rating__item-text{color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-rating__item.sd-rating__item--selected.sd-rating__item--readonly{border-color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-rating__item.sd-rating__item--selected.sd-rating__item--readonly .sd-rating__item-text.sd-rating__item-text{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-rating__item.sd-rating__item--preview{fill:transparent;background-color:transparent;border:1px solid transparent;box-shadow:none;transform:none}.sd-rating__item.sd-rating__item--preview:focus-within{box-shadow:none}.sd-rating__item.sd-rating__item--preview .sd-rating__item-text.sd-rating__item-text{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-rating__item.sd-rating__item--selected.sd-rating__item--preview{border-color:var(--sjs-general-forecolor, var(--foreground, #161616));border-width:1px}.sd-rating__item.sd-rating__item--selected.sd-rating__item--preview .sd-rating__item-text.sd-rating__item-text{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-question--disabled .sd-rating__item-text{opacity:.25}.sd-rating__item--allowhover:hover{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-rating__item:focus-within{box-shadow:0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-rating__item--selected{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));font-weight:600;box-shadow:0 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-rating__item--selected:focus-within{box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),inset 0 0 0 4px var(--sjs-general-backcolor, var(--background, #fff)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-rating__item-smiley{position:relative;border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));white-space:nowrap;padding:calc(1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box;min-width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;justify-content:center;align-items:center;text-align:center;border:2px solid var(--sjs-border-default, var(--border, #d6d6d6));color:var(--sjs-general-forecolor, var(--foreground, #161616));fill:var(--sjs-border-default, var(--border, #d6d6d6));box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),inset 0 0 0 0 var(--sjs-general-backcolor, var(--background, #fff));transition:box-shadow var(--sjs-transition-duration, .15s),background-color var(--sjs-transition-duration, .15s)}.sd-rating__item-smiley svg{display:block;width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating__item-smiley--small{padding:calc(.625 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-width:1px}.sd-rating__item-smiley--small svg{width:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating__item-smiley--small:not(.sd-rating__item-smiley--selected):focus-within{box-shadow:0 0 0 1px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-rating__item-smiley--colored.sd-rating__item-smiley--small:not(.sd-rating__item-smiley--selected):focus-within{box-shadow:0 0 0 1px var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)))}.sd-rating__item-smiley--small.sd-rating__item-smiley--selected:focus-within{box-shadow:inset 0 0 0 2px var(--sjs-general-backcolor, var(--background, #fff)),0 0 0 1px var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)))}legend+.sd-rating__item-smiley,legend+sv-ng-rating-item+.sd-rating__item-smiley{margin-inline-start:2px}.sd-rating__item-smiley--scale-colored{border-color:var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)));fill:var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)));transition:box-shadow var(--sjs-transition-duration, .15s),opacity var(--sjs-transition-duration, .15s),background-color var(--sjs-transition-duration, .15s)}.sd-rating__item-smiley--error{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)));border-color:transparent;fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-rating__item-smiley--error.sd-rating__item-smiley--scale-colored:hover{fill:var(--sd-rating-item-color, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)))}.sd-rating__item-smiley--error.sd-rating__item-smiley--scale-colored:not(.sd-rating__item-smiley--selected){opacity:initial}.sd-rating__item-smiley.sd-rating__item-smiley--disabled{opacity:.5}.sd-rating__item-smiley.sd-rating__item-smiley--selected.sd-rating__item-smiley--disabled{opacity:initial;fill:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sd-rating__item-smiley.sd-rating__item-smiley--readonly{fill:var(--sjs-border-default, var(--border, #d6d6d6));border-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-rating__item-smiley.sd-rating__item-smiley--selected.sd-rating__item-smiley--readonly{fill:var(--sjs-general-forecolor, var(--foreground, #161616));border-color:var(--sjs-general-forecolor, var(--foreground, #161616));background-color:unset}.sd-rating__item-smiley.sd-rating__item-smiley--preview.sd-rating__item-smiley--preview.sd-rating__item-smiley--preview{border:1px solid var(--sjs-general-forecolor, var(--foreground, #161616));fill:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-rating__item-smiley.sd-rating__item-smiley--preview.sd-rating__item-smiley--preview.sd-rating__item-smiley--preview:focus-within{box-shadow:none}.sd-rating__item-smiley.sd-rating__item-smiley--preview.sd-rating__item-smiley--preview.sd-rating__item-smiley--preview svg{margin:1px}.sd-rating__item-smiley.sd-rating__item-smiley--selected.sd-rating__item-smiley--preview.sd-rating__item-smiley--preview{fill:var(--sjs-general-backcolor, var(--background, #fff));background-color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-rating__item-smiley--allowhover:hover{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)));border-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-rating__item-smiley:focus-within{border:none;box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-rating__item-smiley--selected{background-color:var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)));border-color:var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)));fill:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));font-weight:600}.sd-rating__item-smiley--selected:focus-within{border:0px solid var(--sjs-general-backcolor, var(--background, #fff));box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),inset 0 0 0 4px var(--sjs-general-backcolor, var(--background, #fff)),0 0 0 2px var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)))}.sd-rating__item-smiley--scale-colored:not(.sd-rating__item-smiley--selected){opacity:.25}.sd-rating__item-smiley--scale-colored:not(.sd-rating__item-smiley--selected).sd-rating__item-smiley--allowhover:hover{background-color:var(--sd-rating-item-color-light, var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248))));border-color:var(--sd-rating-item-color, var(--sjs-border-default, var(--border, #d6d6d6)));opacity:.5}.sd-rating__item-smiley--rate-colored:not(.sd-rating__item-smiley--selected).sd-rating__item-smiley--allowhover:hover{opacity:.5}.sd-rating__item-smiley--scale-colored:not(.sd-rating__item-smiley--selected):focus-within{opacity:1;box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)))}.sd-rating__item-smiley--scale-colored.sd-rating__item-smiley--small:not(.sd-rating__item-smiley--selected):focus-within{opacity:1;box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 1px var(--sd-rating-item-color, var(--sjs-primary-backcolor, var(--primary, #19b394)))}.sd-rating__item-smiley--scale-colored.sd-rating__item-smiley--selected,.sd-rating__item-smiley--scale-colored.sd-rating__item-smiley--readonly,.sd-rating__item-smiley--scale-colored.sd-rating__item-smiley--preview{opacity:1}.sd-rating__item-star{position:relative;width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:content-box}.sd-rating__item-star:not(:first-of-type){padding-left:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-left:calc(-.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating__item-star:not(:last-of-type){padding-right:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-right:calc(-.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating__item-star svg{stroke:var(--sjs-border-default, var(--border, #d6d6d6));stroke-width:2px;fill:transparent;width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:block;position:absolute;transition:stroke var(--sjs-transition-duration, .15s),opacity var(--sjs-transition-duration, .15s),fill var(--sjs-transition-duration, .15s)}.sd-rating__item-star .sv-star{opacity:1}.sd-rating__item-star .sv-star-2{opacity:0}.sd-rating__item-star--small{width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating__item-star--small svg{width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating__item-star--small.sd-rating__item-star--selected svg{stroke-width:1px}.sd-rating__item-star--selected svg{stroke:transparent;fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-rating__item-star--selected:nth-child(1) svg{transition-delay:0ms}.sd-rating__item-star--selected:nth-child(2) svg{transition-delay:25ms}.sd-rating__item-star--selected:nth-child(3) svg{transition-delay:50ms}.sd-rating__item-star--selected:nth-child(4) svg{transition-delay:75ms}.sd-rating__item-star--selected:nth-child(5) svg{transition-delay:.1s}.sd-rating__item-star--selected:nth-child(6) svg{transition-delay:125ms}.sd-rating__item-star--selected:nth-child(7) svg{transition-delay:.15s}.sd-rating__item-star--selected:nth-child(8) svg{transition-delay:175ms}.sd-rating__item-star--selected:nth-child(9) svg{transition-delay:.2s}.sd-rating__item-star--selected:nth-child(10) svg{transition-delay:225ms}.sd-rating__item-star--selected:nth-child(11) svg{transition-delay:.25s}.sd-rating__item-star--selected:nth-child(12) svg{transition-delay:275ms}.sd-rating__item-star--selected:nth-child(13) svg{transition-delay:.3s}.sd-rating__item-star--selected:nth-child(14) svg{transition-delay:325ms}.sd-rating__item-star--selected:nth-child(15) svg{transition-delay:.35s}.sd-rating__item-star--selected:nth-child(16) svg{transition-delay:375ms}.sd-rating__item-star--selected:nth-child(17) svg{transition-delay:.4s}.sd-rating__item-star--selected:nth-child(18) svg{transition-delay:425ms}.sd-rating__item-star--selected:nth-child(19) svg{transition-delay:.45s}.sd-rating__item-star--selected:nth-child(20) svg{transition-delay:475ms}.sd-rating__item-star--selected:nth-child(21) svg{transition-delay:.5s}.sd-rating__item-star--selected:nth-child(22) svg{transition-delay:525ms}.sd-rating__item-star--selected:nth-child(23) svg{transition-delay:.55s}.sd-rating__item-star--selected:nth-child(24) svg{transition-delay:575ms}.sd-rating__item-star--selected:nth-child(25) svg{transition-delay:.6s}.sd-rating__item-star--error svg{stroke:none;fill:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))}.sd-rating__item-star--disabled{opacity:.5}.sd-rating__item-star--disabled svg{stroke:var(--sjs-border-default, var(--border, #d6d6d6));fill:none}.sd-rating__item-star--selected.sd-rating__item-star--disabled svg{stroke:none;fill:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-rating__item-star--readonly svg{stroke:var(--sjs-border-default, var(--border, #d6d6d6));fill:none}.sd-rating__item-star--selected.sd-rating__item-star--readonly svg{stroke:none;fill:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-rating__item-star--preview svg{stroke:var(--sjs-general-forecolor, var(--foreground, #161616));stroke-width:1px;fill:none}.sd-rating__item-star--selected.sd-rating__item-star--preview svg{stroke:none;fill:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-rating__item-star:not(.sd-rating__item-star--preview):focus-within svg{stroke:var(--sjs-primary-backcolor, var(--primary, #19b394));fill:transparent}.sd-rating__item-star--unhighlighted svg{stroke:transparent;fill:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-rating__item-star--highlighted svg{stroke:var(--sjs-border-default, var(--border, #d6d6d6));fill:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-rating__item-star--selected:not(.sd-rating__item-star--preview).sd-rating__item-star--unhighlighted:focus-within svg{stroke:var(--sjs-border-default, var(--border, #d6d6d6));fill:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-rating__item-star--selected:not(.sd-rating__item-star--preview):focus-within svg{stroke:var(--sjs-primary-backcolor, var(--primary, #19b394));fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-rating__item-star--selected:not(.sd-rating__item-star--preview):focus-within .sv-star{opacity:0}.sd-rating__item-star--selected:not(.sd-rating__item-star--preview):focus-within .sv-star-2{opacity:1}.sd-rating__item-text.sd-rating__item-text{font-family:var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-editorfont-weight, 400);color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));display:inline-block;box-sizing:border-box;transition:color var(--sjs-transition-duration, .15s)}.sd-rating__item-text.sd-rating__item-text.sd-rating__min-text,.sd-rating__item-text.sd-rating__item-text.sd-rating__max-text{margin-top:calc(1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-bottom:calc(1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));border:2px solid rgba(0,0,0,0)}.sd-rating__item-text.sd-rating__item-text.sd-rating__min-text{margin-right:var(--sjs-base-unit, var(--base-unit, 8px));border-left:0px}.sd-rating__item-text.sd-rating__item-text.sd-rating__max-text{margin-right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-left:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-rating__item-text.sd-rating__item-text .sv-string-editor{white-space:nowrap}.sd-rating__item-text.sd-rating__item-text.sd-rating__item--fixed-size{min-width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating__item-text.sd-rating__item-text:after{display:block;content:attr(data-text);font-weight:600;height:0;color:transparent;overflow:hidden;visibility:hidden}.sd-rating--wrappable .sd-rating__item-text{max-width:100%}.sd-rating--wrappable .sd-rating__item-text .sv-string-viewer{max-width:100%;text-overflow:ellipsis;overflow:hidden;display:block}.sd-rating__item:focus-within .sd-rating__item-text.sd-rating__item-text{border:none}.sd-rating__item--selected .sd-rating__item-text.sd-rating__item-text{color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));font-weight:inherit;border:none}.sd-rating-dropdown-item{display:flex;width:100%;justify-content:space-between;align-items:center;gap:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-rating-dropdown-item_description{color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090));font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;font-weight:400;font-size:calc(.75 * (var(--sjs-font-size, 16px)));line-height:var(--sjs-font-size, 16px)}.sv-list__item.sv-list__item--selected .sd-rating-dropdown-item_description{color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sv-ranking-item--error .sv-ranking-item__index{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)));box-shadow:0 1px 2px transparent;border-color:transparent}.sd-element--with-frame .sv-ranking-item{left:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking.sd-ranking--readonly{user-select:initial;-webkit-user-select:initial}.sv-ranking.sd-ranking--readonly.sv-ranking--select-to-rank-empty-value .sv-ranking__containers-divider,.sv-ranking.sd-ranking--readonly .sv-ranking__container--empty{visibility:hidden}.sv-ranking.sd-ranking--preview{user-select:initial;-webkit-user-select:initial}.sv-ranking.sd-ranking--preview.sv-ranking--select-to-rank-empty-value .sv-ranking__containers-divider,.sv-ranking.sd-ranking--preview .sv-ranking__container--empty{visibility:hidden}@container (max-width: 496px){.sv-ranking--select-to-rank-horizontal{flex-direction:column-reverse}.sv-ranking--select-to-rank-horizontal .sv-ranking__containers-divider{margin:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0;height:1px;width:initial}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--empty{padding-top:var(--sjs-base-unit, var(--base-unit, 8px));padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px));display:flex;justify-content:center;align-items:center}.sv-ranking--select-to-rank-horizontal .sv-ranking__container{max-width:initial}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--to .sv-ranking-item,.sv-ranking--select-to-rank-horizontal .sv-ranking__container--empty.sv-ranking__container--to .sv-ranking-item{left:initial}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--empty.sv-ranking__container--to .sv-ranking__container-placeholder{padding-left:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-right:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-ranking--select-to-rank-horizontal .sv-ranking__container--empty.sv-ranking__container--from .sv-ranking__container-placeholder{padding-right:initial}}@container (max-width: 176px){.sv-ranking__container--empty.sv-ranking__container--to .sv-ranking__container-placeholder{margin-left:calc(-5 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-right:calc(-5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}}.sd-element--with-frame .sv-ranking--mobile .sv-ranking-item__icon-container{margin-left:0;margin-right:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-element--with-frame .sv-ranking--mobile .sv-ranking-item{left:0}.sv-ranking-item__content.sd-ranking-item__content{line-height:calc(1.5 * (var(--sjs-font-size, 16px)))}.sv-dragdrop-movedown{transform:translate(0);animation:svdragdropmovedown .1s;animation-timing-function:ease-in-out}@keyframes svdragdropmovedown{0%{transform:translateY(-50px)}to{transform:translate(0)}}.sv-dragdrop-moveup{transform:translate(0);animation:svdragdropmoveup .1s;animation-timing-function:ease-in-out}@keyframes svdragdropmoveup{0%{transform:translateY(50px)}to{transform:translate(0)}}.sv-dropdown_select-wrapper{position:relative}.sv-dropdown_select-wrapper use{fill:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)))}.sd-dropdown{-webkit-appearance:none;-moz-appearance:none;appearance:none;padding-inline-end:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-start:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-block:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));opacity:1;display:flex;justify-content:space-between;word-spacing:normal}.sd-dropdown[disabled]{pointer-events:none}select.sd-dropdown{padding-inline-start:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-end:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-block:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-dropdown--empty:not(.sd-input--disabled),.sd-dropdown--empty:not(.sd-input--disabled) .sd-dropdown__value{color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-dropdown__input-field-component{height:auto}.sd-dropdown option{color:var(--sjs-general-forecolor, var(--foreground, #161616));font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-size, 16px)}.sd-dropdown input[readonly]{pointer-events:none}.sd-dropdown__value{width:100%;min-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));font-family:var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-editorfont-weight, 400);color:var(--sjs-font-editorfont-color, var(--sjs-general-forecolor, rgba(0, 0, 0, .91)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));position:relative;margin-block:var(--sjs-base-unit, var(--base-unit, 8px));margin-inline:0}.sd-dropdown-action-bar{overflow:unset;gap:calc(.25 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));margin-inline-start:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-editor-button-item{-webkit-appearance:none;-moz-appearance:none;appearance:none;border:none;outline:none;background:transparent;display:flex;padding:var(--sjs-base-unit, var(--base-unit, 8px));justify-content:center;align-items:center;align-self:stretch;border-radius:calc(.125 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));cursor:pointer}.sd-editor-button-item:hover{background:var(--lbr-editor-button-background-color-hovered, var(--sjs-general-backcolor-dim-dark, #f3f3f3));transition:background var(--sjs-transition-duration, .15s)}.sd-input--readonly .sd-editor-button-item:hover,.sd-input--readonly .sd-editor-button-item:focus{background:transparent}.sd-editor-button-item--pressed{opacity:var(--lbr-editor-button-icon-opacity-pressed, .5);background:var(--lbr-editor-button-background-color-hovered, var(--sjs-general-backcolor-dim-dark, #f3f3f3));transition-property:opacity,background-color;transition-duration:var(--sjs-transition-duration, .15s)}.sv-editor-button-item__icon{width:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))))}.sv-editor-button-item__icon use{pointer-events:none}.sd-question--readonly .sd-editor-button-item{opacity:var(--lbr-editor-button-icon-opacity-disabled, .25);cursor:default}.sd-dropdown_chevron-button{position:absolute;width:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));top:0;bottom:0;inset-inline-end:0;display:flex;justify-content:center;align-items:center;padding-inline-end:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:content-box;cursor:pointer}.sd-dropdown_chevron-button-svg{width:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))))}.sd-dropdown_chevron-button-svg use{pointer-events:none}.sd-question--readonly .sd-dropdown_chevron-button use{opacity:.24}.sd-input.sd-dropdown:focus-within{box-shadow:var(--sjs-shadow-inner-reset, inset 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-input.sd-dropdown.sd-input--readonly{box-shadow:none;transition:none}.sd-dropdown__filter-string-input{position:absolute;left:0;top:0;bottom:0;width:100%;max-width:100%;border:none;outline:none;padding:0;font-family:var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-editorfont-weight, 400);color:var(--sjs-font-editorfont-color, var(--sjs-general-forecolor, rgba(0, 0, 0, .91)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));background-color:transparent;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:inline-block;appearance:none}.sd-dropdown--empty:not(.sd-input--disabled) .sd-dropdown__filter-string-input::placeholder{color:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)))}.sd-dropdown__filter-string-input::placeholder{color:var(--sjs-general-forecolor, var(--foreground, #161616));width:100%;max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:inline-block;appearance:none}.sd-dropdown__hint-prefix{color:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)));display:flex;align-items:center}.sd-dropdown__hint-prefix span{white-space:pre}.sd-dropdown__hint-suffix{display:flex;color:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)))}.sd-dropdown__hint-suffix span{white-space:pre}.sd-dropdown.sd-input--disabled .sv-string-viewer,.sd-dropdown.sd-input--readonly .sv-string-viewer,.sd-dropdown.sd-input--preview .sv-string-viewer{width:100%;max-width:100%;overflow:hidden;text-overflow:ellipsis}.sv-dropdown-popup .sd-list__item-body{line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));font-weight:var(--sjs-font-editorfont-weight, 400);font-family:var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));padding-inline-end:calc(1.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-block:calc(1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-start:calc(1.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));border:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid transparent;border-radius:var(--sjs-corner-radius, 4px);transition:border-color var(--sjs-transition-duration, .15s)}.sv-dropdown-popup .sv-list__item.sv-list__item--focused:not(.sv-list__item--selected) .sv-list__item-body{border:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid var(--sjs-border-light, var(--border-light, #eaeaea));padding-inline-end:calc(1.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-block:calc(1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-start:calc(1.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-dropdown-popup.sv-popup--menu-tablet .sd-list__item-body,.sv-dropdown-popup.sv-popup--menu-phone .sd-list__item-body{font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));font-weight:400}.sv-dropdown-popup.sv-single-select-list.sv-popup--leave .sd-list__item.sv-list__item--selected .sv-list__item-body{font-weight:400;color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));background-color:transparent}.sv-dropdown-popup.sv-popup--menu-popup.sv-popup--top .sv-popup__container{transform:translateY(-2px)}.sv-dropdown-popup.sv-popup--menu-popup.sv-popup--bottom .sv-popup__container{transform:translateY(2px)}[dir=rtl] .sd-dropdown,[style*="direction:rtl"] .sd-dropdown,[style*="direction: rtl"] .sd-dropdown{background-position:left calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) top 50%,0 0}.sv-list-item--custom-value .sv-list__item-body{font-weight:700;color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-input.sd-tagbox:not(.sd-tagbox--empty).sd-input--editable{height:auto;padding-inline-start:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-input.sd-tagbox:not(.sd-tagbox--empty).sd-input--editable .sd-tagbox__value{padding:0}.sd-tagbox.sd-input--disabled .sd-tagbox__value,.sd-tagbox.sd-input--readonly .sd-tagbox__value,.sd-tagbox.sd-input--preview .sd-tagbox__value{white-space:normal}.sd-tagbox_clean-button{height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));padding:calc(.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));margin:auto 0}.sd-input.sd-tagbox:focus-within{box-shadow:var(--sjs-shadow-inner-reset, inset 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sv-tagbox__item{position:relative;display:flex;align-items:center;padding:var(--sjs-base-unit, var(--base-unit, 8px)) calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));border-radius:calc(.5 * (var(--sjs-corner-radius, 4px)));max-width:calc(100% - var(--sjs-base-unit, var(--base-unit, 8px)));box-sizing:border-box}.sv-tagbox__item-text{color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));min-width:calc(5.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));text-align:center;font-weight:600}.sv-tagbox__item-text span{display:block;text-overflow:ellipsis;overflow:hidden}.sv-tagbox__item:hover .sd-tagbox-item_clean-button,.sv-tagbox__item:focus .sd-tagbox-item_clean-button,.sv-tagbox__item:focus-within .sd-tagbox-item_clean-button{align-self:center;opacity:1}.sd-tagbox-item_clean-button{display:flex;position:absolute;inset-inline-end:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:0;padding-inline-start:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));background:linear-gradient(270deg,var(--sjs-primary-backcolor, var(--primary, #19b394)) 53.12%,rgba(25,179,148,0) 100%);opacity:0;transition:opacity var(--sjs-transition-duration, .15s)}.sd-tagbox-item_clean-button-svg{display:block;padding:calc(.25 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));width:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));height:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));box-sizing:content-box}.sd-tagbox-item_clean-button-svg:hover{border-radius:100px;background:var(--sjs-primary-forecolor-light, var(--primary-foreground-disabled, rgba(255, 255, 255, .25)))}.sd-tagbox-item_clean-button-svg use{fill:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sd-tagbox__value.sd-dropdown__value{position:relative;gap:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;flex-wrap:wrap;flex-grow:1;align-content:center;padding-inline:unset;margin-inline:unset;margin-block:unset}.sd-tagbox__filter-string-input{width:100%;height:100%;display:flex;flex-grow:1}.sd-tagbox__placeholder{position:absolute;top:0;left:0;max-width:100%;width:auto;height:100%;text-align:start;cursor:text;pointer-events:none;color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}[dir=rtl] .sd-tagbox-item_clean-button,[style*="direction:rtl"] .sd-tagbox-item_clean-button,[style*="direction: rtl"] .sd-tagbox-item_clean-button{background:linear-gradient(90deg,var(--sjs-primary-backcolor, var(--primary, #19b394)) 53.12%,rgba(25,179,148,0) 100%)}.sd-tagbox.sd-tagbox--empty .sd-tagbox__hint,.sd-tagbox.sd-tagbox--empty .sd-tagbox__hint-suffix-wrapper,.sd-tagbox.sd-tagbox--empty .sd-tagbox__filter-string-input{width:100%;height:100%}.sd-tagbox__hint{display:flex;flex-grow:1;max-width:100%}.sd-tagbox__hint-suffix-wrapper{position:relative;width:100%}.sd-dropdown__hint-suffix.sd-tagbox__hint-suffix{line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));height:100%;display:flex;align-items:center}.sd-dropdown__hint-prefix.sd-tagbox__hint-prefix{line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));height:100%;display:flex;align-items:center;max-width:50%;justify-content:flex-end}.sd-imagepicker{padding:0;border:none;gap:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:100%;margin:0}.sd-imagepicker--static{display:flex;flex-wrap:wrap}.sd-imagepicker--responsive{display:grid}.sd-imagepicker--column{align-items:flex-start;flex-direction:column}@supports not (aspect-ratio: 1/1){.sd-imagepicker>div{margin-right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}}.sd-imagepicker__item img,.sd-imagepicker__item .sd-imagepicker__image-container>div{border-radius:var(--sjs-corner-radius, 4px);background-color:var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))}.sd-imagepicker__item-decorator{position:relative;display:flex;flex-direction:column;align-items:center}.sd-imagepicker__label{position:relative}.sd-imagepicker__label .sd-visuallyhidden{height:100%;margin:0}.sd-imagepicker__image-container{position:relative}.sd-imagepicker__check-decorator{display:block;opacity:0;position:absolute;top:var(--sjs-base-unit, var(--base-unit, 8px));right:var(--sjs-base-unit, var(--base-unit, 8px));padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box;border-radius:100%;background-color:var(--sjs-general-backcolor, var(--background, #fff));z-index:1;transition:opacity var(--sjs-transition-duration, .15s)}.sd-imagepicker__check-icon{display:block;width:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-imagepicker__item--checked .sd-imagepicker__check-decorator{opacity:1}.sd-imagepicker__item--error .sd-imagepicker__image-container:before{display:block;position:absolute;content:" ";left:0;top:0;width:100%;height:100%;background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)));border-radius:var(--sjs-corner-radius, 4px);background:linear-gradient(0deg,var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1))),var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1))))}.sd-imagepicker__item:focus-within .sd-imagepicker__image .sd-imagepicker__image:hover,.sd-imagepicker__item--allowhover:not(.sd-imagepicker__item--readonly,.sd-imagepicker__item--preview) .sd-imagepicker__image:hover{opacity:.5}.sd-imagepicker__image{display:block;box-sizing:border-box;max-width:100%;transition:opacity var(--sjs-transition-duration, .15s)}.sd-imagepicker__text{font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));margin-top:var(--sjs-base-unit, var(--base-unit, 8px));color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-imagepicker__no-image{display:flex;background-color:var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9));inset-block-start:0}.sd-imagepicker__no-image-svg{height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin:auto}.sd-imagepicker__no-image-svg use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090));opacity:.5}.sd-imagepicker__column{display:flex;flex-direction:column;gap:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));align-items:flex-start;padding-right:0}.sd-imagepicker__column.sd-selectbase__column{min-width:0}.sd-imagepicker__column .sd-imagepicker__item{width:100%}.sd-imagepicker__column .sd-imagepicker__text{width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;text-align:center}.sd-selectbase__column.sd-imagepicker__column:not(:last-child){padding-right:0}.sd-imagepicker__item--readonly .sd-imagepicker__check-icon{fill:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-imagepicker__item--preview .sd-imagepicker__check-decorator{display:none}.sd-imagepicker__item--preview .sd-imagepicker__image{-webkit-filter:grayscale(100%);filter:grayscale(100%);opacity:.25}.sd-imagepicker__item--preview.sd-imagepicker__item--checked .sd-imagepicker__image{-webkit-filter:grayscale(0%);filter:grayscale(0%);opacity:1}.sd-image__image{display:block;max-width:100%;border-radius:var(--sjs-corner-radius, 4px)}.sd-image__image--adaptive{width:100%;height:auto;max-width:calc(80 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-image__no-image{background:var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9));min-width:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-height:calc(27.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:100%;height:100%;position:relative;display:flex;align-items:center;justify-content:center}.sd-image__no-image use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090));opacity:.5}.sd-question--image{width:100%}.sd-html{white-space:initial}.sd-html{font-size:var(--sjs-article-font-default-fontSize, var(--sjs-font-size, 16px));text-decoration:var(--sjs-article-font-default-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-default-fontWeight, 400);font-style:var(--sjs-article-font-default-fontStyle, "normal");font-stretch:var(--sjs-article-font-default-fontStretch, "normal");letter-spacing:var(--sjs-article-font-default-letterSpacing, 0);line-height:var(--sjs-article-font-default-lineHeight, 28px);text-indent:var(--sjs-article-font-default-paragraphIndent, 0px);text-transform:var(--sjs-article-font-default-textCase, "none");color:var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91)))}.sd-html h1{font-size:var(--sjs-article-font-xx-large-fontSize, calc(4 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-xx-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-xx-large-fontWeight, 700);font-style:var(--sjs-article-font-xx-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-xx-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-xx-large-letterSpacing, 0);line-height:var(--sjs-article-font-xx-large-lineHeight, 64px);text-indent:var(--sjs-article-font-xx-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-xx-large-textCase, "none")}.sd-html h2{font-size:var(--sjs-article-font-x-large-fontSize, calc(3 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-x-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-x-large-fontWeight, 700);font-style:var(--sjs-article-font-x-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-x-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-x-large-letterSpacing, 0);line-height:var(--sjs-article-font-x-large-lineHeight, 56px);text-indent:var(--sjs-article-font-x-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-x-large-textCase, "none")}.sd-html h3{font-size:var(--sjs-article-font-large-fontSize, calc(2 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-large-fontWeight, 700);font-style:var(--sjs-article-font-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-large-letterSpacing, 0);line-height:var(--sjs-article-font-large-lineHeight, 40px);text-indent:var(--sjs-article-font-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-large-textCase, "none")}.sd-html h4,.sd-html h5,.sd-html h6{font-size:var(--sjs-article-font-medium-fontSize, calc(1.5 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-medium-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-medium-fontWeight, 700);font-style:var(--sjs-article-font-medium-fontStyle, "normal");font-stretch:var(--sjs-article-font-medium-fontStretch, "normal");letter-spacing:var(--sjs-article-font-medium-letterSpacing, 0);line-height:var(--sjs-article-font-medium-lineHeight, 32px);text-indent:var(--sjs-article-font-medium-paragraphIndent, 0px);text-transform:var(--sjs-article-font-medium-textCase, "none")}.sd-html td,.sd-html span,.sd-html div,.sd-html p{font-size:var(--sjs-article-font-default-fontSize, var(--sjs-font-size, 16px));text-decoration:var(--sjs-article-font-default-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-default-fontWeight, 400);font-style:var(--sjs-article-font-default-fontStyle, "normal");font-stretch:var(--sjs-article-font-default-fontStretch, "normal");letter-spacing:var(--sjs-article-font-default-letterSpacing, 0);line-height:var(--sjs-article-font-default-lineHeight, 28px);text-indent:var(--sjs-article-font-default-paragraphIndent, 0px);text-transform:var(--sjs-article-font-default-textCase, "none")}.sd-html a{color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-html button{display:flex;align-items:center;padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));vertical-align:baseline;text-align:center;background-color:var(--sjs-general-backcolor, var(--background, #fff));border:none;border-radius:var(--sjs-corner-radius, 4px);cursor:pointer;-webkit-user-select:none;user-select:none;outline:solid calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) transparent;color:var(--sjs-primary-backcolor, var(--primary, #19b394));font-weight:600;font-style:normal;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px))));box-shadow:var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15)),0 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394));transition:box-shadow var(--sjs-transition-duration, .15s)}.sd-html button:hover{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-html button:focus{box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-html button span{display:flex;align-items:center;flex-grow:1;justify-content:center}.sd-html--nested{color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-expression{color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));font-size:var(--sjs-font-size, 16px);white-space:break-spaces}.sd-question__content--left .sd-expression{line-height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-progress{height:2px;background-color:var(--sjs-border-light, var(--border-light, #eaeaea));position:relative}.sd-progress__bar{position:relative;height:100%;background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));overflow:hidden}.sd-progress__text{position:absolute;margin-top:var(--sjs-base-unit, var(--base-unit, 8px));padding:var(--sjs-base-unit, var(--base-unit, 8px)) calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));right:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));color:var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45));font-size:calc(.75 * (var(--sjs-font-size, 16px)));line-height:var(--sjs-font-size, 16px);font-weight:600}@media only screen and (min-width: 1000px){.sd-progress__text{margin-left:5%}}@media only screen and (max-width: 1000px){.sd-progress__text{margin-left:10px}}.sd-body__progress--top{margin-bottom:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));position:sticky;top:0;z-index:50}.sd-body__progress--bottom .sd-progress__text{margin-top:calc(-3.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-root--sticky-top .sd-progress__text{border-radius:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));color:var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91));opacity:.75;background:var(--sjs-general-backcolor, var(--background, #fff));box-shadow:var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15))}.sd-boolean{display:flex;width:max-content;position:relative;gap:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));background-color:var(--sjs-editorpanel-backcolor, var(--sjs-editor-background, var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))));border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-shadow:var(--sjs-shadow-inner, inset 0px 1px 2px 0px rgba(0, 0, 0, .15)),0 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394));transition:box-shadow var(--sjs-transition-duration, .15s)}.sd-boolean.sd-boolean--allowhover:focus-within{box-shadow:var(--sjs-shadow-inner-reset, inset 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-boolean__thumb,.sd-boolean__label{display:block;font-family:var(--sjs-font-editorfont-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-editorfont-weight, 400);font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));color:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));padding:var(--sjs-base-unit, var(--base-unit, 8px)) calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-boolean__switch{display:flex;padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));align-items:center;position:absolute;left:0;top:0;width:100%;height:100%;box-sizing:border-box;border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));overflow:hidden}.sd-boolean.sd-boolean--checked .sd-boolean__label--true,.sd-boolean:not(.sd-boolean--checked):not(sd-boolean--indeterminate) .sd-boolean__label--false{color:transparent;transition-duration:.15s;transition-property:color;transition-timing-function:linear}.sd-boolean.sd-boolean--indeterminate .sd-boolean__switch,.sd-boolean--indeterminate .sd-boolean__thumb{display:none}.sd-boolean__thumb{position:absolute;left:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));transform:translate(0);background-color:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));box-shadow:var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15));border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));transition-duration:.2s;transition-property:transform,left;transition-timing-function:linear;color:var(--sjs-primary-backcolor, var(--primary, #19b394));font-weight:600;z-index:2}.sd-boolean__thumb .sv-string-viewer.sv-string-viewer--multiline{white-space:nowrap}.sd-boolean--checked:not(.sd-boolean--exchanged) .sd-boolean__thumb,.sd-boolean--exchanged:not(.sd-boolean--checked) .sd-boolean__thumb{left:calc(100% - .5 * var(--sjs-base-unit, var(--base-unit, 8px)));transform:translate(-100%)}[dir=rtl] .sd-boolean__thumb,[style*="direction:rtl"] .sd-boolean__thumb,[style*="direction: rtl"] .sd-boolean__thumb{left:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));right:unset;transform:translate(100%)}[dir=rtl] .sd-boolean--checked:not(.sd-boolean--exchanged) .sd-boolean__thumb,[dir=rtl] .sd-boolean--exchanged:not(.sd-boolean--checked) .sd-boolean__thumb,[style*="direction:rtl"] .sd-boolean--checked:not(.sd-boolean--exchanged) .sd-boolean__thumb,[style*="direction:rtl"] .sd-boolean--exchanged:not(.sd-boolean--checked) .sd-boolean__thumb,[style*="direction: rtl"] .sd-boolean--checked:not(.sd-boolean--exchanged) .sd-boolean__thumb,[style*="direction: rtl"] .sd-boolean--exchanged:not(.sd-boolean--checked) .sd-boolean__thumb{left:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));right:unset;transform:translate(0)}.sd-boolean--exchanged.sd-boolean:not(.sd-boolean--checked):not(sd-boolean--indeterminate) .sd-boolean__label--false,.sd-boolean--exchanged.sd-boolean.sd-boolean--checked .sd-boolean__label--true{color:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)))}.sd-boolean--disabled{pointer-events:none}.sd-boolean--disabled .sd-boolean__thumb,.sd-boolean--disabled .sd-boolean__label{color:var(--sjs-general-forecolor, var(--foreground, #161616));opacity:.25}.sd-boolean--readonly{pointer-events:none;box-shadow:none;transition:none;background:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-boolean--readonly .sd-boolean__thumb{box-shadow:inset 0 0 0 2px var(--sjs-general-forecolor, var(--foreground, #161616));transition:none;color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-boolean--preview{pointer-events:none;box-shadow:none;transition:none;background:transparent}.sd-boolean--preview .sd-boolean__thumb{border:1px solid var(--sjs-general-forecolor, var(--foreground, #161616));box-shadow:none;transition:none;color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-boolean--preview.sd-boolean--checked .sd-boolean__thumb,.sd-boolean--preview.sd-boolean--indeterminate .sd-boolean__thumb{margin-left:auto}.sd-boolean--preview .sd-checkbox__label--preview{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-boolean__thumb-ghost{z-index:1;border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));background-color:transparent;transition:background-color var(--sjs-transition-duration, .15s)}.sd-boolean.sd-boolean--allowhover .sd-boolean__thumb-ghost:hover{background-color:var(--sjs-editorpanel-hovercolor, var(--sjs-general-backcolor-dim-dark, rgb(243, 243, 243)))}.sd-boolean--error{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))}:root{--sjs-postcss-fix-slider-path-height: var(--lbr-slider-path-height, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));--sjs-postcss-fix-slider-path-color: var(--lbr-slider-path-color, var(--sjs-border-light, var(--border-light, #eaeaea)));--sjs-postcss-fix-slider-path-color-filled: var(--lbr-slider-path-color-filled, var(--sjs-primary-backcolor, var(--primary, #19b394)));--sjs-postcss-fix-slider-thumb-width: var(--lbr-slider-thumb-width, calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))));--sjs-postcss-fix-slider-thumb-height: var(--lbr-slider-thumb-height, calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))));--sjs-postcss-fix-slider-thumb-color: var(--lbr-slider-thumb-color, var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff)))));--sjs-postcss-fix-slider-tooltip-pointer-width: var(--lbr-tooltip-pointer-width, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));--sjs-postcss-fix-slider-tooltip-pointer-height: var(--lbr-tooltip-pointer-height, calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px)))));--sjs-postcss-fix-slider-path-height-preview: var(--lbr-slider-path-height-preview, calc(.125 * (var(--sjs-base-unit, var(--base-unit, 8px)))))}.sd-slider{position:relative;display:flex;width:100%;margin-top:var(--lbr-slider-margin-top, var(--sjs-base-unit, var(--base-unit, 8px)));flex-direction:column;gap:var(--lbr-slider-gap, var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-slider-container{position:relative;min-height:var(--sjs-postcss-fix-slider-thumb-height);cursor:pointer;margin-left:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-right:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-slider-container__slider{position:relative}.sd-slider__track{position:absolute;height:var(--sjs-postcss-fix-slider-path-height);top:calc(var(--sjs-postcss-fix-slider-thumb-height) / 2 - var(--sjs-postcss-fix-slider-path-height) / 2)}.sd-slider__inverse-track{background:var(--sjs-postcss-fix-slider-path-color)}.sd-slider__inverse-track:after,.sd-slider__inverse-track:before{background:var(--sjs-postcss-fix-slider-path-color)}.sd-slider__inverse-track--left{left:0}.sd-slider__inverse-track--left:before{position:absolute;content:"";height:var(--sjs-postcss-fix-slider-path-height);width:var(--sjs-base-unit, var(--base-unit, 8px));left:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-top-left-radius:10px;border-bottom-left-radius:10px}.sd-slider__inverse-track--right{right:0}.sd-slider__inverse-track--right:after{position:absolute;content:"";height:var(--sjs-postcss-fix-slider-path-height);width:var(--sjs-base-unit, var(--base-unit, 8px));right:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-top-right-radius:10px;border-bottom-right-radius:10px}.sd-slider__range-track{left:0;background:var(--sjs-postcss-fix-slider-path-color-filled)}.sd-slider__range-track:before{position:absolute;content:"";height:var(--sjs-postcss-fix-slider-path-height);width:var(--sjs-base-unit, var(--base-unit, 8px));left:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-top-left-radius:10px;border-bottom-left-radius:10px;background:var(--sjs-postcss-fix-slider-path-color-filled)}.sd-slider__range-track:after{position:absolute;content:"";height:var(--sjs-postcss-fix-slider-path-height);width:var(--sjs-base-unit, var(--base-unit, 8px));right:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-top-right-radius:10px;border-bottom-right-radius:10px;background:var(--sjs-postcss-fix-slider-path-color-filled)}.sd-slider__thumb-container{position:absolute;margin-left:calc(var(--sjs-postcss-fix-slider-thumb-width) / -2);z-index:2;cursor:pointer;display:flex;justify-content:center;align-items:center;flex-shrink:0;width:var(--sjs-postcss-fix-slider-thumb-width);height:var(--sjs-postcss-fix-slider-thumb-height);border-radius:var(--lbr-slider-thumb-corner-radius, 1024px)}.sd-slider__input:hover+.sd-slider__thumb-container .sd-slider__tooltip--on-hover{opacity:1}.sd-slider__input:hover+.sd-slider__thumb-container .sd-slider__thumb-dot{width:var(--lbr-slider-thumb-dot-width-hovering, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-slider-thumb-dot-height-hovering, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))))}.sd-slider__thumb-container--indeterminate .sd-slider__thumb-dot{background:var(--lbr-slider-thumb-dot-color-undefined, transparent)}.sd-slider__thumb-container--focused .sd-slider__thumb{box-shadow:none;border:var(--lbr-slider-thumb-border-width-focused, 2px) solid var(--lbr-slider-thumb-border-color-focused, var(--sjs-primary-backcolor, var(--primary, #19b394)))}.sd-slider__thumb-container--focused .sd-slider__tooltip--on-hover{opacity:1}.sd-slider__thumb{display:flex;padding:var(--lbr-slider-thumb-padding-top, calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))) var(--lbr-slider-thumb-padding-right, calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))) var(--lbr-slider-thumb-padding-bottom, calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))) var(--lbr-slider-thumb-padding-left, calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px)))));justify-content:center;align-items:center;border-radius:var(--lbr-slider-thumb-corner-radius, 1024px);background:var(--sjs-postcss-fix-slider-thumb-color);box-shadow:var(--lbr-slider-thumb-shadow-offset-x, 0px) var(--lbr-slider-thumb-shadow-offset-y, 1px) var(--lbr-slider-thumb-shadow-blur, 2px) var(--lbr-slider-thumb-shadow-spread, 0px) var(--lbr-slider-thumb-shadow-color, rgba(0, 0, 0, .15))}.sd-slider__thumb-dot{display:flex;border-radius:var(--lbr-slider-thumb-dot-corner-radius, 1024px);background:var(--lbr-slider-thumb-dot-color, var(--sjs-primary-backcolor, var(--primary, #19b394)));width:var(--lbr-slider-thumb-dot-width, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-slider-thumb-dot-height, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));transition:width var(--sjs-transition-duration, .15s),height var(--sjs-transition-duration, .15s)}.sd-slider__tooltip{opacity:1;position:absolute;bottom:calc(100% + var(--sjs-postcss-fix-slider-tooltip-pointer-height));box-shadow:var(--lbr-tooltip-shadow-2-offset-x, 0px) var(--lbr-tooltip-shadow-2-offset-y, 1px) var(--lbr-tooltip-shadow-2-blur, 2px) var(--lbr-tooltip-shadow-2-spread, 0px) var(--lbr-tooltip-shadow-2-color, rgba(0, 0, 0, .15)),var(--lbr-tooltip-shadow-1-offset-x, 0px) var(--lbr-tooltip-shadow-1-offset-y, 2px) var(--lbr-tooltip-shadow-1-blur, 6px) var(--lbr-tooltip-shadow-1-spread, 0px) var(--lbr-tooltip-shadow-1-color, rgba(0, 0, 0, .1));display:flex;flex-direction:column;-webkit-user-select:none;user-select:none;border-radius:var(--lbr-tooltip-corder-radius, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));transition:opacity var(--sjs-transition-duration, .15s)}.sd-slider__tooltip-panel{padding:var(--lbr-tooltip-padding-top, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-tooltip-padding-right, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))) var(--lbr-tooltip-padding-bottom, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-tooltip-padding-left, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))));border-radius:var(--lbr-tooltip-corder-radius, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));background:var(--lbr-tooltip-background-color, var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff)))));display:flex;justify-content:center;align-items:center}.sd-slider__tooltip-panel:after{content:" ";width:calc(var(--sjs-postcss-fix-slider-tooltip-pointer-height) * 1.41);height:calc(var(--sjs-postcss-fix-slider-tooltip-pointer-height) * 1.41);background:var(--lbr-tooltip-background-color, var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff)))));position:absolute;box-shadow:var(--ctr-tooltip-shadow-1-offset-x, 0px) var(--ctr-tooltip-shadow-1-offset-y, 2px) var(--ctr-tooltip-shadow-1-blur, 8px) var(--ctr-tooltip-shadow-1-spread, 0px) var(--ctr-tooltip-shadow-1-color, var(--sjs-special-glow, rgba(0, 76, 68, .1019607843))),var(--ctr-tooltip-shadow-2-offset-x, 0px) var(--ctr-tooltip-shadow-2-offset-y, 1px) var(--ctr-tooltip-shadow-2-blur, 2px) var(--ctr-tooltip-shadow-2-spread, 0px) var(--ctr-tooltip-shadow-2-color, var(--sjs-special-shadow, rgba(0, 0, 0, .2509803922)));bottom:calc(var(--sjs-postcss-fix-slider-tooltip-pointer-height) * 1.41 / -1);left:calc(50% - var(--sjs-postcss-fix-slider-tooltip-pointer-height) * 1.41);clip-path:polygon(-100% -100%,-100% 200%,200% 200%);transform:translate(calc(var(--sjs-postcss-fix-slider-tooltip-pointer-height) / 1.41),calc(var(--sjs-postcss-fix-slider-tooltip-pointer-height) / -1.41)) rotate(-45deg)}.sd-slider__tooltip-value{font-family:var(--lbr-font-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-size:var(--lbr-font-small-size, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));font-weight:600;line-height:var(--lbr-font-small-line-height, size(2));color:var(--lbr-tooltip-text-color, var(--sjs-primary-backcolor, var(--primary, #19b394)))}.sd-slider__tooltip--on-hover{opacity:0}.sd-slider-labels-container{-webkit-user-select:none;user-select:none;margin-left:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-right:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-slider-labels-container>div{-webkit-user-select:none;user-select:none;position:relative;display:flex;align-items:center;min-height:var(--sjs-postcss-fix-slider-thumb-height)}.sd-slider__label{-webkit-user-select:none;user-select:none;cursor:pointer;color:var(--sjs-general-forecolor, var(--foreground, #161616));position:absolute;width:var(--sjs-postcss-fix-slider-thumb-width);display:flex;flex-direction:column;align-items:center;gap:var(--lbr-slider-label-gap, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));margin-left:calc(var(--sjs-postcss-fix-slider-thumb-width) / -2)}.sd-slider__label--long:first-child{align-items:flex-start}.sd-slider__label--long:last-child{align-items:flex-end}.sd-slider__label-tick{display:flex;width:var(--lbr-slider-label-tick-width, calc(.125 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-slider-label-tick-height, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));flex-direction:column;align-items:flex-start;background:var(--lbr-slider-label-tick-color, var(--sjs-border-default, var(--border, #d6d6d6)))}.sd-slider__label-text{color:var(--lbr-slider-label-text-color, var(--sjs-font-editorfont-color, var(--sjs-general-forecolor, rgba(0, 0, 0, .91))));text-align:center;font-family:var(--lbr-font-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-size:var(--lbr-font-default-size, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))));font-style:normal;font-weight:400;line-height:var(--lbr-font-default-line-height, calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px)))))}input[type=range].sd-slider__input{position:absolute;pointer-events:none;-webkit-appearance:none;z-index:4;height:var(--sjs-postcss-fix-slider-thumb-height);top:0;width:100%;opacity:0;margin:0}input[type=range][name=range-input].sd-slider__input{z-index:3}input[type=range][name=range-input].sd-slider__input:hover+.sd-slider-container .sd-slider__tooltip--on-hover{opacity:1}input[type=range].sd-slider__input:focus::-webkit-slider-runnable-track{background:transparent;border:transparent}input[type=range].sd-slider__input::-webkit-slider-runnable-track{margin:0 calc(var(--sjs-postcss-fix-slider-thumb-width) / -2);height:var(--sjs-postcss-fix-slider-path-height)}input[type=range].sd-slider__input::-moz-range-track{margin:0 calc(var(--sjs-postcss-fix-slider-thumb-width) / -2);height:var(--sjs-postcss-fix-slider-path-height)}input[type=range].sd-slider__input:focus{outline:none}input[type=range].sd-slider__input::-webkit-slider-thumb{pointer-events:all;height:67px;width:var(--sjs-postcss-fix-slider-thumb-width);border-radius:0;border:0 none;background:var(--sjs-primary-backcolor, var(--primary, #19b394));-webkit-appearance:none;cursor:pointer;top:-50px;position:relative}input[type=range][name=range-input].sd-slider__input::-webkit-slider-thumb{height:var(--sjs-postcss-fix-slider-thumb-height);top:0}input[type=range].sd-slider__input::-moz-range-thumb{pointer-events:all;height:75px;width:var(--sjs-postcss-fix-slider-thumb-width);border-radius:0;border:0 none;background:var(--sjs-primary-backcolor, var(--primary, #19b394));-webkit-appearance:none;cursor:pointer;top:-20px;position:relative}input[type=range][name=range-input].sd-slider__input::-moz-range-thumb{height:var(--sjs-postcss-fix-slider-thumb-height);top:0}input[type=range].sd-slider__input::-ms-fill-lower{background:transparent;border:0 none}input[type=range].sd-slider__input::-ms-fill-upper{background:transparent;border:0 none}input[type=range].sd-slider__input::-ms-tooltip{display:none}:root{--sjs-range-slider-range-input-thumb-width: 0px;--sjs-range-slider-range-input-thumb-left: 0;--sjs-range-slider-range-input-thumb-position: absolute}input[type=range][name=range-input].sd-slider__input::-webkit-slider-thumb{width:var(--sjs-range-slider-range-input-thumb-width, 0px);left:var(--sjs-range-slider-range-input-thumb-left, 0);position:var(--sjs-range-slider-range-input-thumb-position, relative)}input[type=range][name=range-input].sd-slider__input::-moz-range-thumb{width:var(--sjs-range-slider-range-input-thumb-width, 0px);left:var(--sjs-range-slider-range-input-thumb-left, 0);position:var(--sjs-range-slider-range-input-thumb-position, relative)}.sd-question--readonly .sd-slider__tooltip,.sd-question--preview .sd-slider__tooltip{display:none}.sd-question--readonly .sd-slider__input,.sd-question--readonly .sd-slider-container,.sd-question--readonly .sd-slider__thumb-container,.sd-question--readonly .sd-slider__label{cursor:default}.sd-question--readonly input[type=range].sd-slider__input::-webkit-slider-thumb{cursor:default}.sd-question--readonly input[type=range].sd-slider__input::-moz-range-thumb{cursor:default}.sd-question--readonly .sd-slider__input:hover+.sd-slider__thumb-container .sd-slider__thumb-dot{width:var(--lbr-slider-thumb-dot-width, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-slider-thumb-dot-height, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))))}.sd-question--readonly .sd-slider__thumb-container--focused .sd-slider__thumb,.sd-question--readonly .sd-slider__thumb{box-shadow:none;border:var(--lbr-slider-thumb-border-width-read-only, 2px) solid var(--lbr-slider-thumb-border-color-read-only, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-question--readonly .sd-slider__thumb .sd-slider__thumb-dot{background:transparent}.sd-question--readonly .sd-slider__inverse-track{background:var(--lbr-slider-path-color-read-only, var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248))))}.sd-question--readonly .sd-slider__inverse-track:after,.sd-question--readonly .sd-slider__inverse-track:before{background:var(--lbr-slider-path-color-read-only, var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248))))}.sd-question--readonly .sd-slider__range-track{background:var(--lbr-slider-path-color-filled-read-only, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-question--readonly .sd-slider__range-track:before{background:var(--lbr-slider-path-color-filled-read-only, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-question--readonly .sd-slider__range-track:after{background:var(--lbr-slider-path-color-filled-read-only, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-question--readonly .sd-slider__label-tick{background:var(--lbr-slider-label-tick-color, var(--sjs-border-light, var(--border-light, #eaeaea)))}.sd-question--preview .sd-slider--single .sd-slider__inverse-track--left:before{background:var(--lbr-slider-path-color-filled-preview, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-question--preview .sd-slider__track{top:calc(var(--sjs-postcss-fix-slider-thumb-height) / 2 - var(--sjs-postcss-fix-slider-path-height-preview) / 2)}.sd-question--preview .sd-slider__input:hover+.sd-slider__thumb-container .sd-slider__thumb-dot{width:var(--lbr-slider-thumb-dot-width-preivew, calc(.125 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-slider-thumb-dot-height-preview, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))))}.sd-question--preview .sd-slider__thumb-container--focused .sd-slider__thumb{box-shadow:none;border:none}.sd-question--preview .sd-slider__thumb{box-shadow:none;padding:0}.sd-question--preview .sd-slider__thumb .sd-slider__thumb-dot{width:var(--lbr-slider-thumb-dot-width-preivew, calc(.125 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-slider-thumb-dot-height-preview, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))));background:var(--lbr-slider-thumb-dot-color-preview, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91)))}.sd-question--preview .sd-slider__inverse-track{height:var(--sjs-postcss-fix-slider-path-height-preview);background:var(--lbr-slider-path-color-preview, var(--sjs-border-light, var(--border-light, #eaeaea)))}.sd-question--preview .sd-slider__inverse-track:after,.sd-question--preview .sd-slider__inverse-track:before{height:var(--sjs-postcss-fix-slider-path-height-preview);background:var(--lbr-slider-path-color-preview, var(--sjs-border-light, var(--border-light, #eaeaea)))}.sd-question--preview .sd-slider__range-track{height:var(--sjs-postcss-fix-slider-path-height-preview);background:var(--lbr-slider-path-color-filled-preview, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-question--preview .sd-slider__range-track:before{height:var(--sjs-postcss-fix-slider-path-height-preview);display:none}.sd-question--preview .sd-slider__range-track:after{height:var(--sjs-postcss-fix-slider-path-height-preview);display:none}.sd-question--preview .sd-slider--negative-scale .sd-slider__inverse-track--left:before{background:var(--lbr-slider-path-color-preview, var(--sjs-border-light, var(--border-light, #eaeaea)))}.sd-question--preview .sd-slider__label-tick{background:var(--lbr-slider-label-tick-color-preview, var(--sjs-general-forecolor, var(--foreground, #161616)))}.sd-question--error .sd-slider__inverse-track{background:var(--lbr-slider-path-color-error, var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1))))}.sd-question--error .sd-slider__inverse-track:after,.sd-question--error .sd-slider__inverse-track:before{background:var(--lbr-slider-path-color-error, var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1))))}.sd-question--error .sd-slider__range-track{background:var(--lbr-slider-path-color-filled-error, var(--sjs-special-red, var(--red, #e60a3e)))}.sd-question--error .sd-slider__range-track:before{background:var(--lbr-slider-path-color-filled-error, var(--sjs-special-red, var(--red, #e60a3e)))}.sd-question--error .sd-slider__range-track:after{background:var(--lbr-slider-path-color-filled-error, var(--sjs-special-red, var(--red, #e60a3e)))}.sd-question--error .sd-slider__thumb-dot{background:var(--lbr-slider-thumb-dot-color-error, var(--sjs-special-red, var(--red, #e60a3e)))}.sd-slider--design-mode .sd-slider__input,.sd-slider--design-mode .sd-slider-container,.sd-slider--design-mode .sd-slider__thumb-container,.sd-slider--design-mode .sd-slider__label{cursor:default}.sd-slider--design-mode input[type=range].sd-slider__input::-webkit-slider-thumb{cursor:default}.sd-slider--design-mode input[type=range].sd-slider__input::-moz-range-thumb{cursor:default}.sd-slider--design-mode .sd-slider__input:hover+.sd-slider__thumb-container .sd-slider__tooltip--on-hover{opacity:0}.sd-slider--design-mode .sd-slider__input:hover+.sd-slider__thumb-container .sd-slider__thumb-dot{width:var(--lbr-slider-thumb-dot-width-hovering, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-slider-thumb-dot-height-hovering, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))))}.sd-slider--design-mode input[type=range][name=range-input].sd-slider__input:hover+.sd-slider-container .sd-slider__tooltip--on-hover{opacity:0}.sd-slider--negative-scale .sd-slider__range-track:before{display:none}.sd-slider--negative-scale .sd-slider__range-track:after{display:none}.sd-slider--animated-thumb-mode .sd-slider__thumb-container{transition:left var(--sjs-transition-duration, .15s)}.sd-slider--animated-thumb-mode .sd-slider__track{transition:left var(--sjs-transition-duration, .15s),right var(--sjs-transition-duration, .15s),width var(--sjs-transition-duration, .15s)}[dir=rtl] .sd-slider,[dir=rtl] .sd-slider__label-text,[dir=rtl] .sd-slider__input,[dir=rtl] .sd-slider__tooltip-value,[style*="direction:rtl"] .sd-slider,[style*="direction:rtl"] .sd-slider__label-text,[style*="direction:rtl"] .sd-slider__input,[style*="direction:rtl"] .sd-slider__tooltip-value,[style*="direction: rtl"] .sd-slider,[style*="direction: rtl"] .sd-slider__label-text,[style*="direction: rtl"] .sd-slider__input,[style*="direction: rtl"] .sd-slider__tooltip-value{transform:scaleX(-1)}[dir=rtl] .sd-slider__label-text,[dir=rtl] .sd-slider__tooltip-value,[style*="direction:rtl"] .sd-slider__label-text,[style*="direction:rtl"] .sd-slider__tooltip-value,[style*="direction: rtl"] .sd-slider__label-text,[style*="direction: rtl"] .sd-slider__tooltip-value{direction:ltr}.sd-paneldynamic .sd-progress{position:absolute;left:0;right:0;height:2px;z-index:2;transform:translateY(-1px)}.sd-paneldynamic>.sd-panel{padding-top:1px;padding-bottom:calc(.5 * var(--sd-base-vertical-padding))}.sd-paneldynamic .sd-paneldynamic__panel-wrapper>.sd-panel>.sd-panel__header{padding-top:var(--sd-base-vertical-padding);padding-bottom:0}.sd-paneldynamic .sd-paneldynamic__panel-wrapper>.sd-panel>.sd-panel__header:after{display:none}.sd-paneldynamic .sd-paneldynamic__panel-wrapper>.sd-panel>.sd-panel__header>.sd-panel__title{color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sd-question--readonly .sd-paneldynamic .sd-question__placeholder,.sd-question--preview .sd-paneldynamic .sd-question__placeholder{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-paneldynamic__separator{display:none}.sd-paneldynamic__panel-wrapper{box-sizing:border-box;padding-bottom:calc(1 * var(--sd-base-padding))}.sd-paneldynamic__panel-wrapper:after{display:table;clear:both;content:" "}.sd-paneldynamic__footer .sd-paneldynamic__separator,.sd-paneldynamic__panel-wrapper--list~.sd-paneldynamic__panel-wrapper--list:before{content:" ";display:block;position:absolute;left:0;right:0;margin:0;border-color:var(--sjs-border-light, var(--border-light, #eaeaea));background:var(--sjs-border-light, var(--border-light, #eaeaea));height:1px;border:none}.sd-paneldynamic__separator:only-child{display:none}.sd-paneldynamic__panel-wrapper--in-row{display:flex;flex-direction:row;align-items:center}.sd-paneldynamic__footer{clear:both}.sd-paneldynamic__footer .sd-paneldynamic__prev-btn,.sd-paneldynamic__footer .sd-paneldynamic__next-btn{display:block}.sd-paneldynamic__footer .sd-paneldynamic__prev-btn svg,.sd-paneldynamic__footer .sd-paneldynamic__next-btn svg{width:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-paneldynamic__footer .sd-paneldynamic__add-btn,.sd-paneldynamic__footer .sd-paneldynamic__progress-text,.sd-paneldynamic__footer .sd-paneldynamic__progress--bottom{display:initial}.sd-paneldynamic__buttons-container{display:flex;align-items:center;padding:calc(var(--sd-base-vertical-padding) - var(--sjs-base-unit, var(--base-unit, 8px))) 0}.sd-paneldynamic__progress-container{display:flex;align-items:center;margin-left:auto;margin-right:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-paneldynamic__progress-text{font-size:var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px))));color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));margin:0;margin-right:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-paneldynamic__prev-btn,.sd-paneldynamic__next-btn{width:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-paneldynamic__prev-btn .sv-svg-icon,.sd-paneldynamic__next-btn .sv-svg-icon{display:block}.sd-paneldynamic__prev-btn{margin-right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-paneldynamic__next-btn{margin-left:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));transform:rotate(180deg)}.sd-paneldynamic__placeholder .sd-paneldynamic__add-btn{display:initial;margin-left:0}.sd-question--empty.sd-question--paneldynamic>.sd-question__content{padding-bottom:var(--sd-base-padding);--animation-padding-bottom: var(--sd-base-padding)}.sd-question--paneldynamic:not(.sd-question--empty)>.sd-question__content>.sd-question__comment-area{padding-bottom:var(--sd-base-padding)}.sd-paneldynamic__buttons-container .sd-action-bar{width:100%;margin:0 calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(100% + 6 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-paneldynamic__panel-footer{border-top:none;position:relative;top:calc(.5 * var(--sd-base-vertical-padding));margin-right:calc(-3 * (var(--sjs-base-unit, var(--base-unit, 8px))));justify-content:flex-end}.sd-paneldynamic__panel-footer .sv-action:not(.sv-action--hidden){margin-bottom:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-paneldynamic__tabs-container{padding-top:var(--sd-base-padding)}.sd-paneldynamic__tabs-container .sd-tabs-toolbar{margin:0 0 -1px calc(-2 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(100% + 4 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-paneldynamic__tabs-container:after{content:" ";display:block;height:1px;position:relative;background:var(--sjs-border-light, var(--border-light, #eaeaea));bottom:0;inset-inline-start:calc(-1 * var(--sd-base-padding));width:calc(100% + 2 * var(--sd-base-padding))}.sd-paneldynamic__tabs-container--with-header{padding-top:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-tabs-toolbar.sv-action-bar{align-items:flex-start;z-index:1}.sd-tabs-toolbar.sd-tabs-toolbar--left{justify-content:flex-start}.sd-tabs-toolbar.sd-tabs-toolbar--right{justify-content:flex-end}.sd-tabs-toolbar.sd-tabs-toolbar--center{justify-content:center}.sd-tab-item{margin:0 calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-tab-item{padding:var(--sjs-base-unit, var(--base-unit, 8px)) 0 calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));-webkit-appearance:none;-moz-appearance:none;appearance:none;display:flex;box-sizing:border-box;border:none;background-color:transparent;cursor:pointer;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));overflow-x:hidden;white-space:nowrap;box-shadow:inset 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394));transition:box-shadow var(--sjs-transition-duration, .15s)}.sd-tab-item:hover,.sd-tab-item:focus-visible{outline:none;box-shadow:inset 0 -2px 0 var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-tab-item.sd-tab-item--pressed{color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));box-shadow:inset 0 -2px 0 var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-tab-item--icon{padding:var(--sjs-base-unit, var(--base-unit, 8px));width:auto;border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-tab-item--icon use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090));opacity:.5}.sd-tab-item--icon:hover,.sd-tab-item--icon.sd-tab-item--pressed{background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));box-shadow:none}.sd-tab-item--icon:hover use,.sd-tab-item--icon.sd-tab-item--pressed use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-tab-item--icon:hover use{opacity:1}.sd-tabs-toolbar .sv-dots{width:auto}.sd-tabs-toolbar .sv-dots.sv-action--hidden{width:0}.sd-tab-item__title{line-height:calc(1.5 * (var(--sjs-font-size, 16px)));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;align-items:center}.sd-question__title~.sd-tabs-toolbar{margin-top:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-paneldynamic__header.sd-element__header.sd-paneldynamic__header-tab{padding-bottom:0}.sd-element--collapsed .sd-paneldynamic__header.sd-element__header.sd-paneldynamic__header-tab{padding-bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-paneldynamic__header.sd-element__header.sd-paneldynamic__header-tab:after{content:none}.sd-question--paneldynamic.sd-element--with-frame{padding-bottom:0}.sd-paneldynamic__panels-container{position:relative;overflow:hidden;margin-left:calc(-1 * var(--sd-base-padding));padding-left:var(--sd-base-padding);margin-right:calc(-1 * var(--sd-base-padding));padding-right:var(--sd-base-padding)}.sd-paneldynamic__panel-wrapper{box-sizing:border-box}@keyframes movePanel{0%{transform:translate(var(--sjs-pd-tab-move-margin))}to{transform:translate(0)}}.sd-paneldynamic__panel-wrapper--enter.sv-pd-animation-left,.sd-paneldynamic__panel-wrapper--enter.sv-pd-animation-right{--sjs-pd-tab-animation-delay: 0ms;animation-name:movePanel,changeHeight,paddingFadeIn,fadeIn;animation-duration:var(--sjs-pd-tab-move-in-duration, .25s),var(--sjs-pd-tab-height-change-duration, .25s),var(--sjs-pd-tab-height-change-duration, .25s),var(--sjs-pd-tab-fade-in-duration, .25s);animation-delay:calc(var(--sjs-pd-tab-move-in-delay, .1s) + var(--sjs-pd-tab-animation-delay)),calc(var(--sjs-pd-tab-height-change-delay, 0ms) + var(--sjs-pd-tab-animation-delay)),calc(var(--sjs-pd-tab-height-change-delay, 0ms) + var(--sjs-pd-tab-animation-delay)),calc(var(--sjs-pd-tab-fade-in-delay, .1s) + var(--sjs-pd-tab-animation-delay));animation-timing-function:cubic-bezier(0,0,.58,1);animation-fill-mode:forwards;opacity:0;padding-bottom:0;transform:translate(var(--sjs-pd-tab-move-margin));height:var(--animation-height-from);--animation-padding-top: 0;--animation-padding-bottom: calc(1 * var(--sd-base-padding))}.sd-paneldynamic__panel-wrapper--enter.sv-pd-animation-left{--sjs-pd-tab-move-margin: calc(1 * var(--sjs-pd-tab-move-in-margin, 50%))}.sd-paneldynamic__panel-wrapper--enter.sv-pd-animation-right{--sjs-pd-tab-move-margin: calc(-1 * var(--sjs-pd-tab-move-in-margin, 50%))}.sd-paneldynamic__panel-wrapper--leave.sv-pd-animation-left,.sd-paneldynamic__panel-wrapper--leave.sv-pd-animation-right{animation-name:fadeIn,movePanel;animation-duration:var(--sjs-pd-tab-fade-out-duration, .25s),var(--sjs-pd-tab-move-out-duration, .25s);animation-delay:var(--sjs-pd-tab-fade-out-delay, 0ms),var(--sjs-pd-tab-move-out-delay, 0ms);animation-timing-function:cubic-bezier(.42,0,1,1);animation-direction:reverse;animation-fill-mode:forwards;position:absolute;left:var(--sd-base-padding);top:0;width:calc(100% - 2 * var(--sd-base-padding))}.sd-paneldynamic__panel-wrapper--leave.sv-pd-animation-left{--sjs-pd-tab-move-margin: calc(-1 * var(--sjs-pd-tab-move-out-margin, 50%))}.sd-paneldynamic__panel-wrapper--leave.sv-pd-animation-right{--sjs-pd-tab-move-margin: calc(1 * var(--sjs-pd-tab-move-out-margin, 50%))}.sd-paneldynamic__panel-wrapper--enter.sv-pd-animation-adding{animation-name:fadeIn,changeHeight,paddingFadeIn;animation-duration:var(--sjs-pd-tab-add-fade-in-duration, .5s),var(--sjs-pd-tab-height-change-duration, .25s),var(--sjs-pd-tab-height-change-duration, .25s);animation-delay:calc(var(--sjs-pd-tab-add-fade-in-delay, .25s) + var(--sjs-pd-tab-animation-delay)),calc(var(--sjs-pd-tab-height-change-delay, 0ms) + var(--sjs-pd-tab-animation-delay)),calc(var(--sjs-pd-tab-height-change-delay, 0ms) + var(--sjs-pd-tab-animation-delay));animation-timing-function:cubic-bezier(0,0,.58,1);animation-fill-mode:forwards;transform:translate(0)}.sd-paneldynamic__panel-wrapper--enter.sv-pd-animation-removing{--sjs-pd-tab-animation-delay: var(--sjs-pd-tab-remove-fade-in-delay, .15s)}.sd-paneldynamic__panel-wrapper--leave.sv-pd-animation-removing{animation-name:fadeIn;animation-duration:var(--sjs-pd-tab-remove-fade-out-duration, .15s);animation-delay:var(--sjs-pd-tab-remove-fade-out-delay, 0ms);animation-timing-function:cubic-bezier(.42,0,1,1);animation-direction:reverse;animation-fill-mode:forwards;position:absolute;left:var(--sd-base-padding);top:0;width:calc(100% - 2 * var(--sd-base-padding))}.sd-paneldynamic__panel-wrapper--enter,.sd-paneldynamic__panel-wrapper--leave{animation-name:fadeIn,moveInWithOverflow;animation-fill-mode:forwards;--animation-padding-top: 0;--animation-padding-bottom: calc(1 * var(--sd-base-padding));min-height:0!important}.sd-paneldynamic__panel-wrapper--enter{opacity:0;animation-timing-function:cubic-bezier(0,0,.58,1);animation-duration:var(--sjs-pd-list-fade-in-duration, .5s),var(--sjs-pd-list-move-in-duration, .25s),var(--sjs-pd-list-move-in-duration, .25s);animation-delay:var(--sjs-pd-list-fade-in-delay, .25s),0s,0s}.sd-paneldynamic__panel-wrapper--leave{animation-direction:reverse;animation-timing-function:cubic-bezier(.42,0,1,1);animation-duration:var(--sjs-pd-list-fade-out-duration, .15s),var(--sjs-pd-list-move-out-duration, .25s),var(--sjs-pd-list-move-out-duration, .25s);animation-delay:0s,var(--sjs-pd-list-move-out-delay, .1s),var(--sjs-pd-list-move-out-delay, .1s);--animation-padding-bottom: calc(1 * var(--sd-base-padding))}.sd-file{position:relative;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));min-height:calc(36 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box;padding:0 calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-file .sv-action-bar{padding:var(--sjs-base-unit, var(--base-unit, 8px)) 0;justify-content:center;position:absolute;width:100%;left:0;bottom:0}.sd-file .sv-action-bar .sv-action-bar-item{height:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));color:var(--sjs-general-forecolor-light, var(--foreground-light, #909090));border-radius:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-file .sv-action-bar #fileIndex .sv-action-bar-item{padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0;font-weight:600}.sd-file .sv-action-bar #fileIndex .sv-action-bar-item:hover{background-color:var(--sjs-general-backcolor, var(--background, #fff))}.sd-file .sv-action-bar #fileIndex .sv-action-bar-item:disabled{opacity:initial}.sd-file__decorator{display:flex;flex-direction:column;position:absolute;left:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:100%;height:100%;box-sizing:border-box;justify-content:center;align-items:center;border:1px dashed var(--sjs-border-default, var(--border, #d6d6d6));container-type:inline-size}.sd-file--answered .sd-file__decorator{container-type:unset}.sd-file__decorator--drag{z-index:1;border:1px solid var(--sjs-primary-backcolor, var(--primary, #19b394));background:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));box-shadow:inset 0 0 0 1px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-file__drag-area-placeholder{text-align:center;white-space:normal;color:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));padding:0 calc(8 * (var(--sjs-base-unit, var(--base-unit, 8px))))}@container (max-width: 496px){.sd-file__drag-area-placeholder{padding:0 var(--sjs-base-unit, var(--base-unit, 8px))}}@container (max-width: 176px){.sd-file__drag-area-placeholder{display:none}.sd-file__decorator .sd-action{padding:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-file__decorator .sd-action .sv-svg-icon{margin-left:0}.sd-file__decorator .sd-file__actions-container{flex-direction:column}.sd-file__decorator .sd-file__actions-container span{display:none}}.sd-root-modern--mobile .sd-file__decorator{padding:0 calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-file__choose-btn--text{display:block;font-weight:600;color:var(--sjs-primary-backcolor, var(--primary, #19b394));cursor:pointer}.sd-file__choose-btn--text .sv-svg-icon{margin-left:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-file__choose-btn--icon>span{display:none}.sd-file__wrapper{width:max-content;max-width:100%}.sd-file__actions-container{margin-top:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));flex-wrap:wrap;justify-content:center}.sd-file__actions-container .sv-action--hidden{display:none}.sd-file--answered .sd-file__actions-container{z-index:2;margin-top:0;gap:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-file--answered .sd-file__actions-container{top:var(--sjs-base-unit, var(--base-unit, 8px));right:var(--sjs-base-unit, var(--base-unit, 8px));position:absolute}.sd-file--chose-btn--as .sd-file--answered .sd-file__actions-container{inset-inline-end:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))) + 5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-file__list{display:flex;position:relative;overflow:hidden;box-sizing:content-box;flex-direction:row;padding:calc(10.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0;min-height:calc(15 * (var(--sjs-base-unit, var(--base-unit, 8px))));max-height:calc(15 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:100%}.sd-file__page{display:flex;left:0;align-items:stretch;justify-content:center;gap:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(100% - 21 * var(--sjs-base-unit, var(--base-unit, 8px)));width:100%;position:absolute}@keyframes file-page-to-right{0%{opacity:1;left:0}to{opacity:0;left:100%}}@keyframes file-page-from-right{0%{opacity:0;left:100%}to{opacity:1;left:0}}@keyframes file-page-from-left{0%{opacity:0;left:-100%}to{opacity:1;left:0}}@keyframes file-page-to-left{0%{opacity:1;left:0}to{opacity:0;left:-100%}}.sd-file__page--leave-to-right,.sd-file__page--enter-from-right,.sd-file__page--leave-to-left,.sd-file__page--enter-from-left{animation-duration:.5s;animation-fill-mode:forwards}.sd-file__page--leave-to-right{animation-name:file-page-to-right}.sd-file__page--enter-from-right{animation-name:file-page-from-right}.sd-file__page--leave-to-left{animation-name:file-page-to-left}.sd-file__page--enter-from-left{animation-name:file-page-from-left}.sd-file__preview-item{position:relative;display:flex;align-items:stretch;flex-direction:column;min-height:100%;width:calc(12 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin:0;cursor:pointer}.sd-file__preview-item .sd-file__default-image{width:calc(9 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(9 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-file__preview-item .sd-file__default-image use{fill:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)))}.sd-file__preview-item:hover .sd-file__remove-file-button{opacity:1}.sd-file__preview-item:hover .sd-file__sign a{text-decoration:underline}.sd-file__sign{margin-top:var(--sjs-base-unit, var(--base-unit, 8px));text-align:center;font-size:calc(.75 * (var(--sjs-font-size, 16px)));line-height:var(--sjs-font-size, 16px)}.sd-file__sign a{display:block;text-decoration:none;color:var(--sjs-font-questiontitle-color, var(--sjs-general-forecolor, var(--foreground, #161616)));white-space:normal;word-break:break-all;width:calc(12 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-width:100%;max-width:100%;overflow:hidden;max-height:calc(3 * (var(--sjs-font-size, 16px)));text-overflow:ellipsis;line-height:var(--sjs-font-size, 16px);display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical}.sd-file__remove-file-button{display:block;opacity:0;position:absolute;left:100%;top:0;transform:translate(-50%,-50%);transition:opacity var(--sjs-transition-duration, .15s)}.sd-file__image-wrapper--default-image .sd-file__remove-file-button{left:50%;top:50%;transform:translate(calc(3.5 * var(--sjs-base-unit, var(--base-unit, 8px)) - 50% - 1.5px),calc(-4.25 * var(--sjs-base-unit, var(--base-unit, 8px)) - 50% + 1.5px))}.sd-file__decorator--error{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))}.sd-file__image-wrapper{position:relative;text-align:center;display:flex;align-items:center;justify-content:center;width:calc(12 * (var(--sjs-base-unit, var(--base-unit, 8px))));min-height:calc(12 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(12 * (var(--sjs-base-unit, var(--base-unit, 8px))));background:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sd-file__image-wrapper img:not(.sd-file__default-image){display:block;max-width:100%;max-height:100%;width:calc(12 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(12 * (var(--sjs-base-unit, var(--base-unit, 8px))));object-fit:contain}.sd-file__image-wrapper--default-image{background:transparent}.sd-file--single .sd-file__preview-item .sd-file__remove-file-button{display:none}.sd-file--single-image{height:calc(36 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-file--single-image .sd-file__page{height:100%}.sd-file--single-image .sd-file__preview-item{width:100%;margin:0}.sd-file--single-image .sd-file__list{position:absolute;left:0;padding:0;height:100%;max-height:100%;width:100%}.sd-file--single-image .sd-file__image-wrapper{background-color:var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))}.sd-file--single-image .sd-file__image-wrapper{min-height:100%;min-width:100%}.sd-file--single-image .sd-file__image-wrapper img{width:100%;height:100%}.sd-file--single-image .sd-file__sign{position:absolute;margin:0;width:100%;min-height:100%}.sd-file--single-image .sd-file__sign a{color:transparent;width:100%;min-width:100%;max-width:100%;height:100%;outline:none}.sd-file__image-wrapper.sd-file__image-wrapper--default-image{background:transparent}.sd-file>input:focus+.sd-file__decorator .sd-file__choose-btn.sd-file__choose-btn--icon use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-file>input:focus+.sd-file__decorator .sd-file__choose-btn:not(.sd-file__choose-btn--icon){background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)))}.sd-file__drag-area{position:static;width:100%}.sd-file__change-camera-button{position:absolute;z-index:2;left:var(--sjs-base-unit, var(--base-unit, 8px));top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-file__close-camera-button{position:absolute;z-index:2;right:var(--sjs-base-unit, var(--base-unit, 8px));top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-context-btn.sd-file__take-picture-button{background-color:var(--sjs-special-red, var(--red, #e60a3e));position:absolute;bottom:16px;left:50%;transform:translate(-50%);padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-context-btn.sd-file__take-picture-button:hover,.sd-context-btn.sd-file__take-picture-button:focus{box-shadow:0 0 0 2px var(--sjs-special-red, var(--red, #e60a3e))}.sd-context-btn.sd-file__take-picture-button .sv-svg-icon{height:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-context-btn.sd-file__take-picture-button .sv-svg-icon use{fill:var(--sjs-general-backcolor, var(--background, #fff))}.sd-file__video-container{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)));position:absolute;top:0;left:0;width:100%;height:100%}.sd-file__loading-indicator{width:100%;height:100%;box-sizing:border-box;position:absolute;border:1px dashed var(--sjs-border-default, var(--border, #d6d6d6));left:0}.sd-file__loading-indicator .sd-loading-indicator{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.sd-file__choose-file-btn--disabled{opacity:.25;cursor:default}.sd-file--readonly .sd-context-btn{display:none}.sd-file--readonly .sd-file__decorator{border-color:transparent}.sd-file--readonly .sd-file__actions-container{display:none}.sd-file--readonly .sd-file__image-wrapper{background:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-file--readonly .sd-file__drag-area-placeholder{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-file--preview .sd-context-btn{display:none}.sd-file--preview .sd-file__decorator{border-color:transparent}.sd-file--preview .sd-file__image-wrapper{background:transparent}.sd-file--preview .sd-file__actions-container{display:none}.sd-file--preview .sd-file__drag-area-placeholder{color:var(--sjs-general-forecolor, var(--foreground, #161616))}.sd-hidden{display:none!important}.sd-body__navigation .sd-btn{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(8 * (var(--sjs-base-unit, var(--base-unit, 8px))));font-size:var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px))))}.sd-root--compact .sd-body__navigation .sd-btn:not(.sd-btn--action){background-color:var(--sjs-general-backcolor-dim-light, var(--background-dim-light, #f9f9f9))}.sd-root--compact .sd-body__navigation .sd-btn:not(.sd-btn--action):hover{background-color:var(--sjs-editorpanel-hovercolor, var(--sjs-general-backcolor-dim-dark, rgb(243, 243, 243)))}.sd-root-modern--mobile .sd-body__navigation .sv-action:not(.sv-action--hidden),.sd-root-modern--mobile .sd-body__navigation .sd-btn{flex-grow:1}.sd-root-modern--mobile .sd-body__navigation .sd-btn{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-body__navigation .sv-action--hidden{display:none}.sd-btn{appearance:none;-webkit-appearance:none;-moz-appearance:none;padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));background:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));border-radius:var(--sjs-corner-radius, 4px);cursor:pointer;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;font-weight:600;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)));text-align:center;color:var(--sjs-primary-backcolor, var(--primary, #19b394));border:none;outline:none;box-shadow:var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15)),0 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394));transition:box-shadow var(--sjs-transition-duration, .15s),background var(--sjs-transition-duration, .15s)}.sd-btn--small{flex-grow:1;padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-btn:hover{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-btn:focus-visible{box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-btn:disabled{color:var(--sjs-general-forecolor, var(--foreground, #161616));opacity:.25;pointer-events:none}.sd-btn--action{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sd-btn--action:hover{color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));background-color:var(--sjs-primary-backcolor-dark, rgb(20, 164, 139))}.sd-btn--action:disabled{color:var(--sjs-primary-forecolor-light, var(--primary-foreground-disabled, rgba(255, 255, 255, .25)));pointer-events:none}.sd-btn--danger,.sd-btn--danger:hover{background-color:var(--sjs-special-red, var(--red, #e60a3e));color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sd-btn--danger:disabled{color:var(--sjs-special-red-forecolor, #ffffff);pointer-events:none}.sd-body{width:100%;box-sizing:border-box}.sd-body .sd-body__page{min-width:min(100%,300px)}.sd-body .sd-body__timer{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0;box-sizing:border-box}.sd-body.sd-body--static{max-width:calc(90 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-left:auto;margin-right:auto;padding-top:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:calc(10 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-body.sd-body--static .sd-body__timer,.sd-body.sd-body--static .sd-body__navigation,.sd-body.sd-body--static .sd-body__page{margin-left:0;margin-right:0}.sd-body.sd-body--static .sd-body__navigation{padding-top:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-body.sd-body--static .sd-body__navigation.sd-action-bar{padding-left:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-right:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-body.sd-body--responsive{max-width:initial;padding:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box}.sd-body.sd-body--responsive .sd-page{padding:0}.sd-body.sd-body--responsive .sd-body__timer,.sd-body.sd-body--responsive .sd-body__navigation{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-body.sd-body--responsive .sd-body__navigation{padding-top:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-body.sd-body--responsive.sd-body--with-timer{max-width:calc(100% + var(--sd-timer-size) * -1.4444444444 + 6 * var(--sjs-base-unit, var(--base-unit, 8px)));margin-left:auto;margin-right:auto}.sd-root-modern--mobile .sd-body.sd-body--with-timer.sd-body--responsive{max-width:initial;margin-left:0;margin-right:0}.sd-root-modern--mobile .sd-body.sd-body--responsive,.sd-root-modern--mobile .sd-body.sd-body--static{padding:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-root-modern--mobile .sd-body.sd-body--responsive .sd-body__navigation,.sd-root-modern--mobile .sd-body.sd-body--static .sd-body__navigation{padding-left:0;padding-right:0;padding-bottom:0}.sd-root--compact .sd-body.sd-body--responsive .sd-body__navigation,.sd-root--compact .sd-body.sd-body--static .sd-body__navigation{padding-top:calc(7 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-body__navigation.sd-action-bar{box-sizing:border-box;padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));flex-wrap:wrap;gap:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-body--empty{min-height:400px;text-align:center;padding-top:180px;box-sizing:border-box}.sd-body--empty,.sd-body--loading{font-size:var(--sjs-article-font-default-fontSize, var(--sjs-font-size, 16px));text-decoration:var(--sjs-article-font-default-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-default-fontWeight, 400);font-style:var(--sjs-article-font-default-fontStyle, "normal");font-stretch:var(--sjs-article-font-default-fontStretch, "normal");letter-spacing:var(--sjs-article-font-default-letterSpacing, 0);line-height:var(--sjs-article-font-default-lineHeight, 28px);text-indent:var(--sjs-article-font-default-paragraphIndent, 0px);text-transform:var(--sjs-article-font-default-textCase, "none");color:var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91)))}.sd-body--empty h1,.sd-body--loading h1{font-size:var(--sjs-article-font-xx-large-fontSize, calc(4 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-xx-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-xx-large-fontWeight, 700);font-style:var(--sjs-article-font-xx-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-xx-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-xx-large-letterSpacing, 0);line-height:var(--sjs-article-font-xx-large-lineHeight, 64px);text-indent:var(--sjs-article-font-xx-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-xx-large-textCase, "none")}.sd-body--empty h2,.sd-body--loading h2{font-size:var(--sjs-article-font-x-large-fontSize, calc(3 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-x-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-x-large-fontWeight, 700);font-style:var(--sjs-article-font-x-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-x-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-x-large-letterSpacing, 0);line-height:var(--sjs-article-font-x-large-lineHeight, 56px);text-indent:var(--sjs-article-font-x-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-x-large-textCase, "none")}.sd-body--empty h3,.sd-body--loading h3{font-size:var(--sjs-article-font-large-fontSize, calc(2 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-large-fontWeight, 700);font-style:var(--sjs-article-font-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-large-letterSpacing, 0);line-height:var(--sjs-article-font-large-lineHeight, 40px);text-indent:var(--sjs-article-font-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-large-textCase, "none")}.sd-body--empty h4,.sd-body--empty h5,.sd-body--empty h6,.sd-body--loading h4,.sd-body--loading h5,.sd-body--loading h6{font-size:var(--sjs-article-font-medium-fontSize, calc(1.5 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-medium-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-medium-fontWeight, 700);font-style:var(--sjs-article-font-medium-fontStyle, "normal");font-stretch:var(--sjs-article-font-medium-fontStretch, "normal");letter-spacing:var(--sjs-article-font-medium-letterSpacing, 0);line-height:var(--sjs-article-font-medium-lineHeight, 32px);text-indent:var(--sjs-article-font-medium-paragraphIndent, 0px);text-transform:var(--sjs-article-font-medium-textCase, "none")}.sd-body--empty td,.sd-body--empty span,.sd-body--empty div,.sd-body--empty p,.sd-body--loading td,.sd-body--loading span,.sd-body--loading div,.sd-body--loading p{font-size:var(--sjs-article-font-default-fontSize, var(--sjs-font-size, 16px));text-decoration:var(--sjs-article-font-default-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-default-fontWeight, 400);font-style:var(--sjs-article-font-default-fontStyle, "normal");font-stretch:var(--sjs-article-font-default-fontStretch, "normal");letter-spacing:var(--sjs-article-font-default-letterSpacing, 0);line-height:var(--sjs-article-font-default-lineHeight, 28px);text-indent:var(--sjs-article-font-default-paragraphIndent, 0px);text-transform:var(--sjs-article-font-default-textCase, "none")}.sd-body--empty a,.sd-body--loading a{color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-body--empty button,.sd-body--loading button{display:flex;align-items:center;padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));vertical-align:baseline;text-align:center;background-color:var(--sjs-general-backcolor, var(--background, #fff));border:none;border-radius:var(--sjs-corner-radius, 4px);cursor:pointer;-webkit-user-select:none;user-select:none;outline:solid calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) transparent;color:var(--sjs-primary-backcolor, var(--primary, #19b394));font-weight:600;font-style:normal;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px))));box-shadow:var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15)),0 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394));transition:box-shadow var(--sjs-transition-duration, .15s)}.sd-body--empty button:hover,.sd-body--loading button:hover{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-body--empty button:focus,.sd-body--loading button:focus{box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-body--empty button span,.sd-body--loading button span{display:flex;align-items:center;flex-grow:1;justify-content:center}.sd-root_background-image{background-position-x:center;position:absolute;inset:0}.sd-multipletext{width:100%;table-layout:fixed;border-spacing:0;height:1px}.sd-multipletext__cell{height:100%}.sd-multipletext__cell:not(:first-of-type){padding-left:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-multipletext__item-container.sd-input:focus-within{box-shadow:var(--sjs-shadow-inner-reset, inset 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-multipletext__item-container{display:flex;align-items:flex-start;height:100%;padding-top:0;padding-bottom:0}.sd-multipletext__item-container .sd-input{padding-top:0;padding-right:0;padding-bottom:0;border:none;box-shadow:none}.sd-multipletext__item-container .sd-input,.sd-multipletext__item-title{margin-top:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-bottom:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-multipletext__item-title{font-size:0;line-height:0}.sd-multipletext__item-title span{font-size:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))))}.sd-multipletext__item-title{height:calc(100% - var(--sjs-base-unit, var(--base-unit, 8px)) * 3);max-width:30%;padding-right:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-right:1px solid var(--sjs-border-default, var(--border, #d6d6d6));white-space:normal;color:var(--sjs-font-editorfont-placeholdercolor, var(--sjs-general-forecolor-light, var(--foreground-light, #909090)));box-sizing:content-box}.sd-question--preview .sd-multipletext__item-title{border:none}.sd-multipletext__item{flex-grow:1}.sd-multipletext__content .sd-multipletext__item-container{position:relative}.sd-multipletext__item-container--error{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))}.sd-multipletext__item-container--error .sd-input--error{background-color:transparent}.sd-multipletext-item__character-counter{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.sd-question__content:focus-within .sd-multipletext-item__character-counter{padding-inline-end:calc(8 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-multipletext__cell{padding-left:0;padding-right:0;padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px));padding-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-multipletext__cell--error-bottom,.sd-multipletext__row:first-of-type .sd-multipletext__cell{padding-top:0}.sd-multipletext__cell--error-top,.sd-multipletext__row:last-of-type .sd-multipletext__cell{padding-bottom:0}.sd-multipletext__cell--error .sd-question__erbox{margin:0}.sd-multipletext .sd-input .sd-input{background:transparent}.sd-action{appearance:none;border:none;display:flex;align-items:center;gap:var(--sjs-base-unit, var(--base-unit, 8px));background:transparent;padding:var(--sjs-base-unit, var(--base-unit, 8px)) calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));color:var(--sjs-primary-backcolor, var(--primary, #19b394));border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));font-weight:600;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;font-size:var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px))));outline:none;transition:background-color var(--sjs-transition-duration, .15s);box-sizing:content-box}.sd-action--negative{color:var(--sjs-special-red, var(--red, #e60a3e))}.sd-action--icon{padding:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-action__icon{margin-left:calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-action__icon use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-action--icon .sd-action__icon{margin-left:0}.sd-action--icon use{fill:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));transition:fill var(--sjs-transition-duration, .15s)}svg.sd-action--icon{fill:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)))}.sd-action:disabled,.sd-action--disabled{color:var(--sjs-general-forecolor, var(--foreground, #161616));cursor:default;opacity:.25;pointer-events:none}.sd-action:disabled use,.sd-action--disabled use{fill:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)))}.sd-action:not(.sd-action--pressed):hover,.sd-action:not(.sd-action--pressed):focus{outline:none;background-color:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));cursor:pointer;opacity:1}.sd-action:not(.sd-action--pressed):hover.sd-action--icon,.sd-action:not(.sd-action--pressed):focus.sd-action--icon{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sd-action:not(.sd-action--pressed):hover.sd-action--negative,.sd-action:not(.sd-action--pressed):focus.sd-action--negative{background-color:var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))}.sd-action:not(.sd-action--pressed):hover.sd-action--negative.sd-action--icon use,.sd-action:not(.sd-action--pressed):focus.sd-action--negative.sd-action--icon use{fill:var(--sjs-special-red, var(--red, #e60a3e))}.sd-action:not(.sd-action--pressed):hover:active,.sd-action:not(.sd-action--pressed):focus:active{opacity:.5}.sd-action__icon{display:block;width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-action--pressed:not(.sd-action--active){background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));opacity:.5}.sd-action-bar>.sv-dots{width:auto}.sd-action-bar>.sv-action--hidden{width:0}.sd-action.sv-dots__item{width:auto}.sd-action-bar{display:flex;align-items:center}.sd-action-bar .sv-action:not(:last-child) .sv-action__content{padding-right:0}.sd-context-btn{background-color:var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff))));padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:calc(12.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));border:none;outline:none;cursor:pointer}.sd-context-btn .sv-svg-icon{margin:0}.sd-context-btn svg{display:block;width:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))));height:calc(1.5 * (var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))))}.sd-context-btn use{fill:var(--sjs-font-questiondescription-color, var(--sjs-general-forecolor-light, rgba(0, 0, 0, .45)));transition:fill var(--sjs-transition-duration, .15s)}.sd-context-btn:hover use,.sd-context-btn:focus use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-context-btn:hover.sd-context-btn--negative use,.sd-context-btn:focus.sd-context-btn--negative use{fill:var(--sjs-special-red, var(--red, #e60a3e))}.sd-context-btn:disabled{opacity:.25}.sd-context-btn--small{padding:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-context-btn--small svg{width:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px));height:var(--sjs-font-editorfont-size, var(--sjs-font-size, 16px))}.sd-context-btn--with-border{--box-shadow-color: var(--sjs-border-default, var(--border, #d6d6d6));box-shadow:0 0 0 1px var(--box-shadow-color)}.sd-context-btn--colorful use{fill:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-context-btn--colorful.sd-context-btn--negative use{fill:var(--sjs-special-red, var(--red, #e60a3e))}.sd-context-btn--colorful:focus,.sd-context-btn--colorful:hover{background:linear-gradient(var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1))),var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)))),linear-gradient(var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff)))),var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff)))))}.sd-context-btn--colorful:focus.sd-context-btn--negative,.sd-context-btn--colorful:hover.sd-context-btn--negative{background:linear-gradient(var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1))),var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1)))),linear-gradient(var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff)))),var(--sjs-questionpanel-backcolor, var(--sjs-question-background, var(--sjs-general-backcolor, var(--background, #fff)))))}.sd-context-btn--colorful:focus.sd-context-btn--with-border,.sd-context-btn--colorful:hover.sd-context-btn--with-border{--box-shadow-color: var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-context-btn--colorful:focus.sd-context-btn--with-border.sd-context-btn--negative,.sd-context-btn--colorful:hover.sd-context-btn--with-border.sd-context-btn--negative{--box-shadow-color: var(--sjs-special-red, var(--red, #e60a3e))}.sd-completedpage,.sd-body--loading,.sd-completed-before-page{align-items:center;font-weight:700;box-sizing:border-box;text-align:center;height:auto;font-size:var(--sjs-article-font-default-fontSize, var(--sjs-font-size, 16px));text-decoration:var(--sjs-article-font-default-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-default-fontWeight, 400);font-style:var(--sjs-article-font-default-fontStyle, "normal");font-stretch:var(--sjs-article-font-default-fontStretch, "normal");letter-spacing:var(--sjs-article-font-default-letterSpacing, 0);line-height:var(--sjs-article-font-default-lineHeight, 28px);text-indent:var(--sjs-article-font-default-paragraphIndent, 0px);text-transform:var(--sjs-article-font-default-textCase, "none");color:var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91)))}.sd-completedpage h1,.sd-body--loading h1,.sd-completed-before-page h1{font-size:var(--sjs-article-font-xx-large-fontSize, calc(4 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-xx-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-xx-large-fontWeight, 700);font-style:var(--sjs-article-font-xx-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-xx-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-xx-large-letterSpacing, 0);line-height:var(--sjs-article-font-xx-large-lineHeight, 64px);text-indent:var(--sjs-article-font-xx-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-xx-large-textCase, "none")}.sd-completedpage h2,.sd-body--loading h2,.sd-completed-before-page h2{font-size:var(--sjs-article-font-x-large-fontSize, calc(3 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-x-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-x-large-fontWeight, 700);font-style:var(--sjs-article-font-x-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-x-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-x-large-letterSpacing, 0);line-height:var(--sjs-article-font-x-large-lineHeight, 56px);text-indent:var(--sjs-article-font-x-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-x-large-textCase, "none")}.sd-completedpage h3,.sd-body--loading h3,.sd-completed-before-page h3{font-size:var(--sjs-article-font-large-fontSize, calc(2 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-large-fontWeight, 700);font-style:var(--sjs-article-font-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-large-letterSpacing, 0);line-height:var(--sjs-article-font-large-lineHeight, 40px);text-indent:var(--sjs-article-font-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-large-textCase, "none")}.sd-completedpage h4,.sd-completedpage h5,.sd-completedpage h6,.sd-body--loading h4,.sd-body--loading h5,.sd-body--loading h6,.sd-completed-before-page h4,.sd-completed-before-page h5,.sd-completed-before-page h6{font-size:var(--sjs-article-font-medium-fontSize, calc(1.5 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-medium-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-medium-fontWeight, 700);font-style:var(--sjs-article-font-medium-fontStyle, "normal");font-stretch:var(--sjs-article-font-medium-fontStretch, "normal");letter-spacing:var(--sjs-article-font-medium-letterSpacing, 0);line-height:var(--sjs-article-font-medium-lineHeight, 32px);text-indent:var(--sjs-article-font-medium-paragraphIndent, 0px);text-transform:var(--sjs-article-font-medium-textCase, "none")}.sd-completedpage td,.sd-completedpage span,.sd-completedpage div,.sd-completedpage p,.sd-body--loading td,.sd-body--loading span,.sd-body--loading div,.sd-body--loading p,.sd-completed-before-page td,.sd-completed-before-page span,.sd-completed-before-page div,.sd-completed-before-page p{font-size:var(--sjs-article-font-default-fontSize, var(--sjs-font-size, 16px));text-decoration:var(--sjs-article-font-default-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-default-fontWeight, 400);font-style:var(--sjs-article-font-default-fontStyle, "normal");font-stretch:var(--sjs-article-font-default-fontStretch, "normal");letter-spacing:var(--sjs-article-font-default-letterSpacing, 0);line-height:var(--sjs-article-font-default-lineHeight, 28px);text-indent:var(--sjs-article-font-default-paragraphIndent, 0px);text-transform:var(--sjs-article-font-default-textCase, "none")}.sd-completedpage a,.sd-body--loading a,.sd-completed-before-page a{color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-completedpage button,.sd-body--loading button,.sd-completed-before-page button{display:flex;align-items:center;padding:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));vertical-align:baseline;text-align:center;background-color:var(--sjs-general-backcolor, var(--background, #fff));border:none;border-radius:var(--sjs-corner-radius, 4px);cursor:pointer;-webkit-user-select:none;user-select:none;outline:solid calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px)))) transparent;color:var(--sjs-primary-backcolor, var(--primary, #19b394));font-weight:600;font-style:normal;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-size:var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px));line-height:calc(1.5 * (var(--sjs-font-questiontitle-size, var(--sjs-font-size, 16px))));box-shadow:var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15)),0 0 0 0 var(--sjs-primary-backcolor, var(--primary, #19b394));transition:box-shadow var(--sjs-transition-duration, .15s)}.sd-completedpage button:hover,.sd-body--loading button:hover,.sd-completed-before-page button:hover{background-color:var(--sjs-questionpanel-hovercolor, var(--sjs-general-backcolor-dark, rgb(248, 248, 248)))}.sd-completedpage button:focus,.sd-body--loading button:focus,.sd-completed-before-page button:focus{box-shadow:var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, .15)),0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-completedpage button span,.sd-body--loading button span,.sd-completed-before-page button span{display:flex;align-items:center;flex-grow:1;justify-content:center}.sd-completedpage:not(:has(>*)),.sd-body--loading:not(:has(>*)),.sd-completed-before-page:not(:has(>*)){font-size:var(--sjs-article-font-large-fontSize, calc(2 * (var(--sjs-font-size, 16px))));text-decoration:var(--sjs-article-font-large-textDecoration, "none");font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-weight:var(--sjs-article-font-large-fontWeight, 700);font-style:var(--sjs-article-font-large-fontStyle, "normal");font-stretch:var(--sjs-article-font-large-fontStretch, "normal");letter-spacing:var(--sjs-article-font-large-letterSpacing, 0);line-height:var(--sjs-article-font-large-lineHeight, 40px);text-indent:var(--sjs-article-font-large-paragraphIndent, 0px);text-transform:var(--sjs-article-font-large-textCase, "none")}.sd-completedpage{padding:calc(17 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0 calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-completedpage:has(>*){padding:calc(13 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0 calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-body--loading,.sd-completed-before-page{padding:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0 calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))) 0}.sd-body--loading:has(>*),.sd-completed-before-page:has(>*){padding:0}.sd-progress-buttons__image-button-left,.sd-progress-buttons__image-button-right{display:none}.sd-progress-buttons__image-button--hidden{visibility:hidden}.sd-progress-buttons__page-description{display:none}.sd-progress-buttons{padding:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));display:flex;flex-direction:column}.sd-progress-buttons__list-container{display:flex;overflow:hidden;margin:0 calc(-.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons__connector{display:none}.sd-progress-buttons__list{display:inline-flex;flex-direction:row;flex-grow:1;margin:0 auto;padding:0}.sd-progress-buttons__list li{display:flex;flex-grow:1;flex-shrink:1;flex-basis:0;position:relative;flex-wrap:nowrap;text-align:center;flex-direction:column;cursor:pointer}.sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector{display:block;content:"";height:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));background-color:var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45));opacity:.5;position:absolute;bottom:calc(1.125 * (var(--sjs-base-unit, var(--base-unit, 8px))));right:calc(50% + 10px);width:calc(100% - 20px);pointer-events:none}.sd-progress-buttons__list li .sd-progress-buttons__button{position:relative;display:flex;content:attr(data-page-number);width:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(0 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin:calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));border:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid transparent;border-radius:50%;align-self:center;z-index:1;font-size:calc(.75 * (var(--sjs-font-size, 16px)));font-weight:600;line-height:var(--sjs-font-size, 16px);justify-content:center;color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));box-sizing:content-box}.sd-progress-buttons__list li .sd-progress-buttons__button .sd-progress-buttons__button-background{position:absolute;width:calc(2.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));top:calc(-.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));left:calc(-1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));z-index:-2}.sd-progress-buttons__list li .sd-progress-buttons__button .sd-progress-buttons__button-content{position:absolute;width:100%;height:100%;top:calc(-.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));left:calc(-.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));background-color:var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45));opacity:.5;z-index:-1;border:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid transparent;border-radius:50%;box-sizing:content-box}.sd-progress-buttons__list li:hover .sd-progress-buttons__button{color:var(--sjs-primary-backcolor, var(--primary, #19b394));padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px))));border:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid var(--sjs-primary-backcolor, var(--primary, #19b394));background-color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff))}.sd-progress-buttons__list li:hover .sd-progress-buttons__button .sd-progress-buttons__button-background{left:calc(-.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons__list li:hover .sd-progress-buttons__button .sd-progress-buttons__button-content{top:calc(-.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));left:calc(-.75 * (var(--sjs-base-unit, var(--base-unit, 8px))));border:calc(.75 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid var(--sjs-primary-backcolor, var(--primary, #19b394));background-color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));opacity:1}.sd-progress-buttons__list .sd-progress-buttons__list-element--passed:not(:first-child)>.sd-progress-buttons__connector{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));opacity:1}.sd-progress-buttons__list .sd-progress-buttons__list-element--passed .sd-progress-buttons__button{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394))}.sd-progress-buttons__list .sd-progress-buttons__list-element--passed .sd-progress-buttons__button .sd-progress-buttons__button-content{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));opacity:1}.sd-progress-buttons__list .sd-progress-buttons__list-element--current:not(:first-child)>.sd-progress-buttons__connector{background-color:var(--sjs-primary-backcolor, var(--primary, #19b394));opacity:1}.sd-progress-buttons__list .sd-progress-buttons__list-element--current .sd-progress-buttons__button{border:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid var(--sjs-primary-backcolor, var(--primary, #19b394));background-color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));color:var(--sjs-primary-backcolor, var(--primary, #19b394));padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin:calc(.25 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons__list .sd-progress-buttons__list-element--current .sd-progress-buttons__button .sd-progress-buttons__button-background{left:calc(-.75 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons__list .sd-progress-buttons__list-element--current .sd-progress-buttons__button .sd-progress-buttons__button-content{border:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) solid var(--sjs-primary-backcolor, var(--primary, #19b394));background-color:var(--sjs-primary-forecolor, var(--primary-foreground, #fff));opacity:1}.sd-progress-buttons__page-title{display:flex;justify-content:center;align-items:center;font-size:calc(.75 * (var(--sjs-font-size, 16px)));font-weight:600;line-height:var(--sjs-font-size, 16px);flex-grow:1;text-align:center;padding:0 calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));color:var(--lbr-step-progress-bar-step-title-text-color, var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91))))}.sd-progress-buttons__header .sd-progress-buttons__page-title{margin-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-progress-buttons__footer .sd-progress-buttons__page-title{margin-top:var(--sjs-base-unit, var(--base-unit, 8px));margin-bottom:0;justify-content:flex-end;padding:0;color:var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45))}.sd-progress-buttons--bottom{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));flex-direction:column-reverse}.sd-progress-buttons--bottom .sd-progress-buttons__list li{flex-direction:column-reverse}.sd-progress-buttons--bottom .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector{top:calc(1.125 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons--bottom .sd-progress-buttons__page-title{margin-top:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-bottom:0}.sd-progress-buttons--bottom .sd-progress-buttons__header .sd-progress-buttons__page-title{margin-top:var(--sjs-base-unit, var(--base-unit, 8px));margin-bottom:0}.sd-progress-buttons--bottom .sd-progress-buttons__footer .sd-progress-buttons__page-title{margin-top:0;margin-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-progress-buttons--numbered .sd-progress-buttons__list-container{margin:0 calc(-1 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons--numbered .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector{bottom:calc(2.175 * (var(--sjs-base-unit, var(--base-unit, 8px))));right:calc(50% + 18px);width:calc(100% - 36px)}.sd-progress-buttons--numbered .sd-progress-buttons__list li .sd-progress-buttons__button{width:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons--numbered .sd-progress-buttons__list li .sd-progress-buttons__button .sd-progress-buttons__button-background{height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(4.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons--numbered .sd-progress-buttons__list li:hover .sd-progress-buttons__button{padding:calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons--with-titles .sd-progress-buttons__list-container{margin:0}.sd-root-modern--mobile .sd-progress-buttons__list,.sd-progress-buttons--no-titles .sd-progress-buttons__list{justify-content:space-between;width:100%}.sd-root-modern--mobile .sd-progress-buttons__list li,.sd-progress-buttons--no-titles .sd-progress-buttons__list li{flex-grow:0}.sd-progress-buttons--bottom.sd-progress-buttons--numbered .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector{top:calc(2.175 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-root-modern--mobile .sd-progress-buttons{padding:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))) calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-progress-buttons--fit-survey-width{max-width:calc(90 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box;width:100%;margin:auto}[dir=rtl] .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector,[style*="direction:rtl"] .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector,[style*="direction: rtl"] .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector{right:unset;left:calc(50% + 10px)}[dir=rtl] .sd-progress-buttons--numbered .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector,[style*="direction:rtl"] .sd-progress-buttons--numbered .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector,[style*="direction: rtl"] .sd-progress-buttons--numbered .sd-progress-buttons__list li:not(:first-child)>.sd-progress-buttons__connector{right:unset;left:calc(50% + 20px)}.sv_progress-toc{padding:var(--lbr-toc-padding-top, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-toc-padding-right, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-toc-padding-bottom, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-toc-padding-left, var(--sjs-base-unit, var(--base-unit, 8px)));background:var(--lbr-toc-background-color, var(--sjs-general-backcolor, var(--background, #fff)));min-width:calc(32 * (var(--sjs-base-unit, var(--base-unit, 8px))));max-width:calc(42 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:100%;box-sizing:border-box}.sv_progress-toc .sv-list{padding:0}.sv_progress-toc .sv-list__item.sv-list__item--selected .sv-list__item-body{background:var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1)));color:var(--sjs-general-forecolor, var(--foreground, #161616));font-weight:400}.sv_progress-toc .sv-list__item span{white-space:break-spaces}.sv_progress-toc .sv-list__item-body{padding-inline-start:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-inline-end:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:var(--sjs-corner-radius, 4px);padding-top:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));padding-bottom:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv_progress-toc use{fill:var(--sjs-general-forecolor-light, var(--foreground-light, #909090))}.sv_progress-toc--left{border-right:var(--lbr-toc-border-width-right, 1px) solid var(--lbr-toc-border-color, var(--sjs-border-default, var(--border, #d6d6d6)))}.sv_progress-toc--right{border-left:var(--lbr-toc-border-width-right, 1px) solid var(--lbr-toc-border-color, var(--sjs-border-default, var(--border, #d6d6d6)))}.sv_progress-toc--mobile{position:fixed;top:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));right:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:auto;min-width:auto;height:auto;background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));z-index:15;border-radius:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv_progress-toc--mobile>div{width:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv_progress-toc--mobile:hover{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3))}.sd-title+.sv-components-row>.sv-components-column .sv_progress-toc:not(.sv_progress-toc--mobile),.sd-title~.sv-components-row>.sv-components-column .sv_progress-toc:not(.sv_progress-toc--mobile){margin-top:2px}.sv_progress-toc.sv_progress-toc--sticky{position:sticky;height:auto;overflow-y:auto;top:0}.sd-list__item-body{padding-block:calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))));border-radius:var(--sjs-corner-radius, 4px);font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)))}.sd-list__item.sd-list__item--focused:not(.sd-list__item--selected){outline:none}.sd-list__item.sd-list__item--focused:not(.sd-list__item--selected) .sd-list__item-body{padding-block:calc(1.25 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-timer{z-index:2;position:fixed;bottom:calc(var(--sd-timer-size) / 144 * 32);right:var(--sjs-base-unit, var(--base-unit, 8px));background:#fff;width:var(--sd-timer-size);height:var(--sd-timer-size);margin-right:calc(var(--sd-timer-size) / 144 * 32);display:flex;border-radius:100%;padding:calc(var(--sd-timer-size) / 144 * 8);box-shadow:var(--sjs-shadow-large, 0px 8px 16px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1));box-sizing:border-box}.sd-timer--top{top:calc(var(--sd-timer-size) / 144 * 32);margin-top:calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-bottom:calc(-1 * var(--sd-timer-size))}.sd-timer--bottom{bottom:calc(var(--sd-timer-size) / 144 * 32);margin-top:calc(var(--sd-timer-size) * -1.2222222222)}.sd-timer__progress{--sd-timer-stroke-background-color: var(--background-dim, #f3f3f3);--sd-timer-stroke-background-width: 2px;stroke-linecap:round;height:100%;width:100%;transform:rotate(-90deg);stroke:var(--sjs-primary-backcolor, var(--primary, #19b394));stroke-dashoffset:0;fill:none;stroke-width:4px}.sd-timer__progress--animation{stroke-dashoffset:0;transition:stroke-dashoffset 1s linear}.sd-timer__text-container{display:flex;flex-direction:column;align-items:center;position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);padding:var(--sjs-base-unit, var(--base-unit, 8px));color:var(--sjs-primary-backcolor, var(--primary, #19b394));font-weight:700;font-size:calc(var(--sd-timer-size) / 144 * 32)}.sd-timer__text--major{color:var(--sjs-primary-backcolor, var(--primary, #19b394));font-weight:700;font-size:calc(var(--sd-timer-size) / 144 * 32)}.sd-timer__text--minor{color:var(--lbr-timer-text-color-secondary, var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45)));font-size:var(--lbr-font-default-size, var(--sjs-font-size, 16px));font-style:normal;font-weight:600;line-height:var(--lbr-font-default-line-height, calc(1.5 * (var(--sjs-font-size, 16px))));margin-top:calc(-.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-header{position:relative;background-color:var(--sjs-header-backcolor, transparent)}.sv-header__background-color--accent{background-color:var(--sjs-header-backcolor, var(--sjs-primary-backcolor, var(--primary, #19b394)))}.sv-header__overlap{padding-bottom:calc(8 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:content-box}.sv-header__overlap~div .sd-body,.sv-header__overlap~div .sv-body{margin-top:calc(-14 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-root-modern--mobile .sv-header__overlap{padding-bottom:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-root-modern--mobile .sv-header__overlap~div .sd-body,.sd-root-modern--mobile .sv-header__overlap~div .sv-body{margin-top:calc(-5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-header__overlap.sv-header__without-background{margin-bottom:0;padding-bottom:0}.sv-header__overlap.sv-header__without-background~div .sd-body,.sv-header__overlap.sv-header__without-background~div .sv-body{margin-top:0}.sv-header__without-background .sv-header--mobile,.sv-header__without-background .sv-header__content{padding-bottom:0}.sd-body .sv-header__content{padding-top:0;padding-bottom:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-header__content{padding:calc(5 * (var(--sjs-base-unit, var(--base-unit, 8px))));box-sizing:border-box;height:100%;position:relative;display:grid;grid-template-columns:1fr 1fr 1fr;grid-template-rows:1fr 1fr 1fr;row-gap:var(--lbr-cover-row-gap, var(--lbr-spacing-x150, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))));column-gap:var(--lbr-cover-column-gap, var(--lbr-spacing-x6, calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))))}.sv-header--height-auto .sv-header__content{grid-template-rows:auto;height:auto}.sv-header--height-auto .sv-header__cell:not(.sv-header__cell--empty) .sv-header__cell-content{position:relative;min-height:100%;min-width:100%}.sv-header--height-auto .sv-header__cell--right .sv-header__cell-content{left:100%;transform:translate(-100%)}.sv-header__content--static{max-width:calc(90 * (var(--sjs-base-unit, var(--base-unit, 8px))));margin-left:auto;margin-right:auto}.sv-header__background-image{width:100%;height:100%;position:absolute;top:0;left:0;right:0;border:0;background-position-x:center}.sv-header__background-image--contain{background-repeat:no-repeat}.sv-header__cell{position:relative}.sv-header__cell-content{display:flex;flex-direction:column;position:absolute;width:max-content;max-width:300%;top:0;bottom:0}.sv-header__cell--left .sv-header__cell-content{left:0}.sv-header__cell--center .sv-header__cell-content{min-width:100%;left:50%;transform:translate(-50%)}.sv-header__cell--right .sv-header__cell-content{right:0}.sv-header__logo{display:flex}.sv-header__logo img{display:block}.sv-header__title{display:flex}.sv-header__title .sd-title{--header-title-font-size: var(--sjs-font-headertitle-size, calc(2 * (var(--sjs-font-size, 16px))));font-size:var(--header-title-font-size);line-height:calc(1.25 * (var(--header-title-font-size)));color:var(--sjs-font-headertitle-color, var(--sjs-font-pagetitle-color, var(--sjs-general-dim-forecolor, rgba(0, 0, 0, .91))));font-family:var(--sjs-font-headertitle-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-headertitle-weight, 700);margin:0}.sv-header__logo~.sv-header__title{margin-top:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-header__logo~.sv-header__description{margin-top:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sv-header__title~.sv-header__description{margin-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sv-header__description{display:flex}.sv-header__description .sd-description{--header-description-font-size: var(--sjs-font-headerdescription-size, 20px);font-size:var(--header-description-font-size);line-height:calc(1.5 * (var(--header-description-font-size)));color:var(--sjs-font-headerdescription-color, var(--sjs-font-pagedescription-color, var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45))));font-family:var(--sjs-font-headerdescription-family, var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family))));font-weight:var(--sjs-font-headerdescription-weight, 400);margin:0}.sv-header__background-color--accent .sv-header__title .sd-title{color:var(--sjs-font-headertitle-color, var(--sjs-primary-forecolor, var(--primary-foreground, #fff)))}.sv-header__background-color--accent .sv-header__description .sd-description{color:var(--sjs-font-headerdescription-color, var(--sjs-primary-forecolor, var(--primary-foreground, #fff)))}.sv-header__content .sd-header__text h3{margin:0}.sv-header--mobile{padding:calc(3 * (var(--sjs-base-unit, var(--base-unit, 8px))));position:relative;z-index:1}.sv-header--mobile .sv-header__logo img{max-width:100%}@keyframes rotationAnimation{0%{rotate:0deg}to{rotate:360deg}}.sd-loading-indicator{height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-loading-indicator .sv-svg-icon{height:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));width:calc(6 * (var(--sjs-base-unit, var(--base-unit, 8px))));animation-name:rotationAnimation;animation-timing-function:linear;animation-iteration-count:infinite;animation-duration:1s}.sd-loading-indicator .sv-svg-icon use{fill:var(--lbr-placeholder-loading-circle-color-spin, var(--sjs-primary-backcolor, var(--primary, #19b394)))}sv-components-container,.sd-components-container{display:flex}.sv-components-row{display:flex;flex-direction:row;width:100%}.sv-components-column{display:flex;flex-direction:column}.sv-components-column--expandable{flex-grow:1}.sv-components-row>.sv-components-column--expandable{width:1px}.sd-breadcrumbs{display:flex;align-items:center;align-content:center;gap:var(--lbr-breadcrumbs-gap, var(--sjs-base-unit, var(--base-unit, 8px)));align-self:stretch;flex-wrap:wrap;padding-bottom:var(--lbr-page-header-breadcrumbs-margin-bottom, 20px)}.sd-breadcrumbs-item{color:var(--lbr-breadcrumbs-item-text-color, var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45)));font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)))}.sd-breadcrumbs-item__button{-webkit-appearance:none;-moz-appearance:none;appearance:none;display:flex;padding:0;box-sizing:border-box;border:none;background-color:transparent;cursor:pointer;color:unset;font-size:unset;font-family:unset;overflow-x:hidden;white-space:nowrap}.sd-breadcrumbs-separator{display:flex;width:var(--lbr-breadcrumbs-item-separator-icon-width, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));height:var(--lbr-breadcrumbs-item-separator-icon-height, calc(1.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));justify-content:center;align-items:center}.sd-breadcrumbs-separator use{fill:var(--lbr-breadcrumbs-item-separator-color, var(--sjs-general-dim-forecolor-light, rgba(0, 0, 0, .45)))}.sd-summary{border-radius:var(--lbr-data-table-corner-radius, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px)))));background:var(--lbr-data-table-background-color, var(--sjs-general-backcolor, var(--background, #fff)));box-shadow:var(--lbr-data-table-shadow-offset-x, 0px) var(--lbr-data-table-shadow-offset-y, 1px) var(--lbr-data-table-shadow-blur, 2px) var(--lbr-data-table-shadow-spread, 0px) var(--lbr-data-table-shadow-color, rgba(0, 0, 0, .15));overflow:hidden}.sd-summary-row{display:flex;padding:var(--lbr-data-table-row-padding-top, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-data-table-row-padding-right, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))) var(--lbr-data-table-row-padding-bottom, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-data-table-row-padding-left, calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px)))));align-items:flex-start;gap:var(--lbr-data-table-row-gap, calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))));flex:1 0 0;align-self:stretch;border-bottom:var(--lbr-data-table-row-border-width-bottom, 1px) solid var(--lbr-data-table-row-border-color, var(--sjs-border-light, var(--border-light, #eaeaea)));background:var(--lbr-data-table-row-background-color, var(--sjs-general-backcolor, var(--background, #fff)))}.sd-summary-row__content{display:flex;padding:var(--lbr-data-table-row-text-margin-top, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))) 0px var(--lbr-data-table-row-text-margin-bottom, calc(.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))) 0px;align-items:flex-start;gap:var(--lbr-data-table-row-gap, calc(4 * (var(--sjs-base-unit, var(--base-unit, 8px)))));flex:1 0 0;color:var(--lbr-data-table-row-text-color-title, var(--sjs-general-forecolor, var(--foreground, #161616)));font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));font-style:normal;font-size:var(--sjs-font-size, 16px);line-height:calc(1.5 * (var(--sjs-font-size, 16px)))}.sd-summary-row__actions{display:flex;align-items:flex-start;gap:var(--lbr-data-table-actions-gap, var(--sjs-base-unit, var(--base-unit, 8px)));opacity:0;transition:opacity var(--sjs-transition-duration, .15s)}.sd-summary-row:hover .sd-summary-row__actions,.sd-question--mobile .sd-summary-row__actions{opacity:1}.sd-summary-row-action{-webkit-appearance:none;-moz-appearance:none;appearance:none;padding:0;box-sizing:border-box;border:none;background-color:transparent;cursor:pointer;color:unset;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));overflow-x:hidden;white-space:nowrap;display:flex;padding:var(--lbr-action-button-padding-top, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-action-button-padding-right-icon-only, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-action-button-padding-bottom, var(--sjs-base-unit, var(--base-unit, 8px))) var(--lbr-action-button-padding-left-icon-only, var(--sjs-base-unit, var(--base-unit, 8px)));justify-content:center;align-items:center;gap:var(--lbr-action-button-gap, var(--sjs-base-unit, var(--base-unit, 8px)));border-radius:var(--lbr-action-button-corner-radius, 1024px);transition:background-color var(--sjs-transition-duration, .15s)}.sd-summary-row-action:hover,.sd-summary-row-action:focus{outline:none;background-color:var(--lbr-action-button-background-color-hovered-positive, var(--sjs-primary-backcolor-light, var(--primary-light, rgba(25, 179, 148, .1))))}.sd-summary-row-action:active,.sd-summary-row-action.svc-toolbar__item--pressed{opacity:.5}.sd-summary-row-action.svc-toolbar__item--active{outline:none}.sd-summary-row-action:disabled{opacity:.25;cursor:default}.sd-summary-row-action .sv-svg-icon{width:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))));height:calc(2 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-summary-row-action--edit use{fill:var(--lbr-action-button-icon-color-positive, var(--sjs-primary-backcolor, var(--primary, #19b394)))}.sd-summary-row-action--delete:hover,.sd-summary-row-action--delete:focus{outline:none;background-color:var(--lbr-action-button-background-color-hovered-negative, var(--sjs-special-red-light, var(--red-light, rgba(230, 10, 62, .1))))}.sd-summary-row-action--delete use{fill:var(--lbr-action-button-icon-color-negative, var(--sjs-special-red, var(--red, #e60a3e)))}.sd-root-modern,.sd-container-modern{--sd-base-padding: calc(5 * var(--sjs-base-unit, var(--base-unit, 8px)));--sd-base-vertical-padding: calc(4 * var(--sjs-base-unit, var(--base-unit, 8px)));--sd-page-vertical-padding: calc(3 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-root-modern.sd-root-modern--mobile,.sd-root-modern--mobile .sd-container-modern{--sd-base-padding: calc(3 * var(--sjs-base-unit, var(--base-unit, 8px)));--sd-base-vertical-padding: calc(2 * var(--sjs-base-unit, var(--base-unit, 8px)));--sd-page-vertical-padding: calc(2 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-root-modern.sd-root-modern--mobile .sd-element__num{float:none;margin-inline-start:0;width:auto;padding-inline-start:0;padding-inline-end:0}.sd-root-modern.sd-root-modern--mobile .sd-element__num+span{float:none;width:auto}.sd-root-modern.sd-root-modern--mobile .sd-element__title-expandable-svg{inset-inline-start:calc(-2.5 * (var(--sjs-base-unit, var(--base-unit, 8px))))}.sd-root-modern.sd-root-modern--mobile .sd-title.sd-container-modern__title{flex-direction:column}.sd-root-modern.sd-root-modern--mobile .sd-header__text{min-width:100%}.sd-multipletext--mobile .sd-multipletext__cell{display:block}.sd-multipletext--mobile .sd-multipletext__cell:not(:first-of-type){padding-left:0;padding-top:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-multipletext--mobile .sd-multipletext__cell :not(:last-of-type){padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-multipletext--mobile .sd-multipletext__item-container{padding-top:var(--sjs-base-unit, var(--base-unit, 8px));padding-bottom:var(--sjs-base-unit, var(--base-unit, 8px))}.sd-multipletext--mobile .sd-multipletext__item-title{max-width:none;border-right:none;width:100%;padding:var(--sjs-base-unit, var(--base-unit, 8px)) 0;margin:0}.sd-multipletext--mobile .sd-multipletext__item{flex-basis:0;min-width:0}.sd-multipletext--mobile .sd-multipletext__item .sd-input{padding:0;margin:0}.sd-multipletext--mobile .sd-multipletext__item-container--answered,.sd-multipletext--mobile .sd-multipletext__item-container--allow-focus:focus-within{flex-direction:column}.sd-multipletext--mobile .sd-multipletext__item-container--answered .sd-multipletext__item-title,.sd-multipletext--mobile .sd-multipletext__item-container--allow-focus:focus-within .sd-multipletext__item-title{padding:0}.sd-multipletext--mobile .sd-multipletext__item-container--answered .sd-multipletext__item-title span,.sd-multipletext--mobile .sd-multipletext__item-container--allow-focus:focus-within .sd-multipletext__item-title span{font-size:calc(.75 * (var(--sjs-font-size, 16px)));line-height:var(--sjs-font-size, 16px)}.sd-multipletext--mobile .sd-multipletext__item-container--answered .sd-multipletext__item,.sd-multipletext--mobile .sd-multipletext__item-container--allow-focus:focus-within .sd-multipletext__item{flex-basis:auto;min-width:auto;width:100%}.sd-multipletext--mobile .sd-multipletext__item-container--answered .sd-multipletext__item .sd-input,.sd-multipletext--mobile .sd-multipletext__item-container--allow-focus:focus-within .sd-multipletext__item .sd-input{width:100%}.sd-selectbase--mobile .sd-selectbase--multi-column{flex-direction:column}.sd-selectbase--mobile .sd-selectbase--multi-column .sd-selectbase__column:not(:last-child){padding-right:0}body{--sv-defaultV2-mark: true}.sd-root-modern{-webkit-font-smoothing:antialiased;-webkit-tap-highlight-color:transparent;--sd-mobile-width: 600px;--sd-timer-size: calc(18 * var(--sjs-base-unit, var(--base-unit, 8px)));width:100%;font-family:var(--sjs-font-family, var(--font-family, var(--sjs-default-font-family)));background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));position:relative}@-moz-document url-prefix(){.sd-root-modern,.sd-root-modern *{scrollbar-width:thin;scrollbar-color:var(--sjs-border-default, var(--border, #d6d6d6)) transparent}}.sd-root-modern::-webkit-scrollbar,.sd-root-modern *::-webkit-scrollbar{width:12px;height:12px;background-color:transparent}.sd-root-modern::-webkit-scrollbar-thumb,.sd-root-modern *::-webkit-scrollbar-thumb{border:4px solid rgba(0,0,0,0);background-clip:padding-box;border-radius:32px;background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-root-modern::-webkit-scrollbar-track,.sd-root-modern *::-webkit-scrollbar-track{background:transparent}.sd-root-modern::-webkit-scrollbar-thumb:hover,.sd-root-modern *::-webkit-scrollbar-thumb:hover{border:2px solid rgba(0,0,0,0);background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sd-root-modern form{position:relative}.sd-root-modern--animation-disabled *{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;transition:none!important}.sd-root-modern.sd-root-modern--full-container{width:100%;height:100%;overflow:auto;position:relative}.sd-root-modern--mobile{--sd-timer-size: calc(9 * var(--sjs-base-unit, var(--base-unit, 8px)))}.sd-root-modern__wrapper{position:relative}.sd-root-modern__wrapper--has-image{min-height:100%}.sd-root-modern--full-container .sd-root-modern__wrapper--fixed{position:static;width:100%;height:100%}.sd-root-modern--full-container .sd-root-modern__wrapper--fixed form{width:100%;height:100%;max-height:100%}.sv-popup .sv-popup__scrolling-content{box-sizing:content-box}@-moz-document url-prefix(){.sv-popup .sv-popup__scrolling-content,.sv-popup .sv-popup__scrolling-content *{scrollbar-width:thin;scrollbar-color:var(--sjs-border-default, var(--border, #d6d6d6)) transparent}}.sv-popup .sv-popup__scrolling-content::-webkit-scrollbar,.sv-popup .sv-popup__scrolling-content *::-webkit-scrollbar{width:12px;height:12px;background-color:transparent}.sv-popup .sv-popup__scrolling-content::-webkit-scrollbar-thumb,.sv-popup .sv-popup__scrolling-content *::-webkit-scrollbar-thumb{border:4px solid rgba(0,0,0,0);background-clip:padding-box;border-radius:32px;background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sv-popup .sv-popup__scrolling-content::-webkit-scrollbar-track,.sv-popup .sv-popup__scrolling-content *::-webkit-scrollbar-track{background:transparent}.sv-popup .sv-popup__scrolling-content::-webkit-scrollbar-thumb:hover,.sv-popup .sv-popup__scrolling-content *::-webkit-scrollbar-thumb:hover{border:2px solid rgba(0,0,0,0);background-color:var(--sjs-border-default, var(--border, #d6d6d6))}.sv-components-container-center{position:sticky;top:0}.sv-root--sticky-top .sv-components-container-center{z-index:15}.sv-root--sticky-top.sd-progress--pages .sv-components-container-center,.sv-root--sticky-top.sd-progress--buttons .sv-components-container-center{background-color:var(--sjs-general-backcolor-dim, var(--background-dim, #f3f3f3));box-shadow:var(--sjs-shadow-medium, 0px 2px 6px 0px rgba(0, 0, 0, .1)),var(--sjs-shadow-small, 0px 1px 2px 0px rgba(0, 0, 0, .15))}.sv-components-container-right,.sv-components-container-left{width:fit-content}
