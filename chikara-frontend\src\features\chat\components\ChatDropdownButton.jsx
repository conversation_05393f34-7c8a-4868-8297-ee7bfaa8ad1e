import { useSessionStore, useSocketStore } from "@/app/store/stores";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useQuery } from "@tanstack/react-query";
import { MessageSquare } from "lucide-react";
import { useEffect } from "react";
import { api } from "@/helpers/api";

const classroomChatboxes = {
    Mizu: { id: 3, name: "mizu" },
    Honoo: { id: 4, name: "honoo" },
    Kaze: { id: 5, name: "kaze" },
    Tsuchi: { id: 6, name: "tsuchi" },
};

function ChatDropdownButton({ chatRoom, currentUser }) {
    const { setMainChatRoom } = useSessionStore();
    const availableRooms = [{ displayName: "Global Chat", room: { id: 1, name: "global" } }];
    const { data: currentGang } = useQuery(api.gang.currentGangInfo.queryOptions({ enabled: !!currentUser?.gangId }));

    if (currentGang?.chat_room) {
        availableRooms.push({ displayName: "Gang Chat", room: currentGang?.chat_room });
    }

    if (currentUser?.class) {
        const classChatroom = classroomChatboxes[currentUser?.class];
        availableRooms.push({ displayName: "Class Chat", room: classChatroom });
    }

    const handleChange = (room) => {
        setMainChatRoom(room);
    };

    const { socket } = useSocketStore();

    useEffect(() => {
        if (socket) {
            if (chatRoom) {
                socket.emit("join room", chatRoom?.room?.id);
            }
            return () => {
                if (chatRoom) {
                    socket.emit("leave room", chatRoom?.room?.id);
                }
            };
        }
    }, [chatRoom, socket]);

    return (
        <DropdownMenu className="dark">
            <DropdownMenuTrigger
                asChild
                className="z-10 rounded-lg border-[#1F1F2D] border-b shadow-[0_1px_0_0_#303045_inset,0_2px_2px_0_rgba(0,0,0,0.25)] data-[state=open]:text-blue-500 data-[state=open]:ring-3"
            >
                <button
                    id="dropdownRadioBgHoverButton"
                    className="inline-flex h-9 w-fit items-center rounded-md bg-[#28283C] px-2 text-center font-medium text-sm text-white hover:brightness-110 focus:outline-hidden focus:ring-4 focus:ring-blue-800"
                    type="button"
                >
                    <MessageSquare size={13} className="my-auto mr-2 md:ml-1 dark:text-gray-300" />
                    {chatRoom?.displayName}
                    <svg
                        className="ms-3 size-2.5"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 10 6"
                    >
                        <path
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="m1 1 4 4 4-4"
                        />
                    </svg>
                </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="dark w-56">
                <DropdownMenuGroup>
                    {availableRooms.map((room, i) => (
                        <DropdownMenuItem key={i}>
                            <div className="flex items-center rounded-sm p-2" onClick={() => handleChange(room)}>
                                <input
                                    readOnly // Make the input read-only since we are controlling it from the div
                                    id={`chatroom` + room?.room?.id}
                                    type="radio"
                                    checked={chatRoom?.room?.id === room?.room?.id}
                                    name={`chatroom` + room?.room?.id}
                                    className="size-4 cursor-pointer border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-600 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700"
                                />

                                <label
                                    htmlFor={`chatroom` + room?.room?.id}
                                    className="ms-2 w-full cursor-pointer select-none rounded-sm font-medium text-gray-900 text-sm dark:text-gray-300"
                                >
                                    {room?.displayName}
                                </label>
                            </div>
                        </DropdownMenuItem>
                    ))}
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

export default ChatDropdownButton;
