import { z } from "zod";

export const depositSchema = z.object({
    amount: z.number().int().positive(),
});

export const withdrawSchema = z.object({
    amount: z.number().int().positive(),
});

export const transferSchema = z.object({
    recipientId: z.number().int().positive(),
    transferAmount: z.number().int().positive(),
});

// Export types inferred from the schemas
export type DepositSchemaType = z.infer<typeof depositSchema>;
export type WithdrawSchemaType = z.infer<typeof withdrawSchema>;
export type TransferSchemaType = z.infer<typeof transferSchema>;
