import { defaultMockAuctionItem, defaultMockItem, defaultMockUser } from "../../../__tests__/prismaModelMocks.js";
import * as AuctionRepository from "../../../repositories/auction.repository.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import { db } from "../../../lib/db.js";
import { AuctionItemStatus } from "@prisma/client";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock the user repository
vi.mock("../../../repositories/user.repository.js", () => ({
    getUserById: vi.fn(),
    updateUserCash: vi.fn(),
}));

describe("Auction Repository", () => {
    const mockUser = { ...defaultMockUser, id: 1, cash: 1000 };
    const mockItem = { ...defaultMockItem, id: 10 };
    const mockAuctionItem = {
        ...defaultMockAuctionItem,
        id: 100,
        sellerId: mockUser.id,
        itemId: mockItem.id,
        buyoutPrice: 500,
        quantity: 2,
        status: "in_progress" as AuctionItemStatus,
        item: { id: mockItem.id, name: "Test Weapon" },
        user: {
            id: mockUser.id,
            username: "testuser",
            avatar: "avatar.png",
        },
    };

    beforeEach(() => {
        vi.resetAllMocks();
    });

    describe("findAuctionItems", () => {
        it("should return auction items", async () => {
            const mockDate = new Date();
            const mockAuctionItems = [mockAuctionItem];

            vi.mocked(db.auction_item.findMany).mockResolvedValue(mockAuctionItems);

            const result = await AuctionRepository.findAuctionItems(mockDate);

            expect(db.auction_item.findMany).toHaveBeenCalledWith({
                where: {
                    status: "in_progress",
                    endsAt: { gt: mockDate },
                },
                include: {
                    item: true,
                    user: {
                        select: {
                            id: true,
                            username: true,
                            avatar: true,
                        },
                    },
                },
                orderBy: {
                    endsAt: "asc",
                },
            });
            expect(result).toEqual(mockAuctionItems);
        });
    });

    describe("createAuctionItem", () => {
        it("should create an auction item", async () => {
            const auctionData = {
                itemId: mockItem.id,
                sellerId: mockUser.id,
                quantity: 1,
                deposit: 0,
                buyoutPrice: 500,
                endsAt: new Date(),
                bankFunds: false,
            };

            vi.mocked(db.auction_item.create).mockResolvedValue({ ...mockAuctionItem, ...auctionData });

            const result = await AuctionRepository.createAuctionItem(auctionData);

            expect(db.auction_item.create).toHaveBeenCalledWith({
                data: {
                    ...auctionData,
                    status: "in_progress",
                },
            });
            expect(result).toEqual({ ...mockAuctionItem, ...auctionData });
        });
    });

    describe("findAuctionItemWithDetails", () => {
        it("should return an auction item with details", async () => {
            vi.mocked(db.auction_item.findFirst).mockResolvedValue(mockAuctionItem);

            const result = await AuctionRepository.findAuctionItemWithDetails(mockAuctionItem.id);

            expect(db.auction_item.findFirst).toHaveBeenCalledWith({
                where: {
                    id: mockAuctionItem.id,
                    status: "in_progress",
                },
                include: {
                    item: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    user: {
                        select: {
                            id: true,
                            username: true,
                        },
                    },
                },
            });
            expect(result).toEqual(mockAuctionItem);
        });
    });

    describe("updateAuctionItemStatus", () => {
        it("should update an auction item's status", async () => {
            const newStatus = "cancelled" as AuctionItemStatus;
            vi.mocked(db.auction_item.update).mockResolvedValue({ ...mockAuctionItem, status: newStatus });

            const result = await AuctionRepository.updateAuctionItemStatus(mockAuctionItem.id, newStatus);

            expect(db.auction_item.update).toHaveBeenCalledWith({
                where: { id: mockAuctionItem.id },
                data: { status: newStatus },
            });
            expect(result).toEqual({ ...mockAuctionItem, status: newStatus });
        });
    });

    describe("updateAuctionItemQuantity", () => {
        it("should update an auction item's quantity", async () => {
            const newQuantity = 1;
            vi.mocked(db.auction_item.update).mockResolvedValue({ ...mockAuctionItem, quantity: newQuantity });

            const result = await AuctionRepository.updateAuctionItemQuantity(mockAuctionItem.id, newQuantity);

            expect(db.auction_item.update).toHaveBeenCalledWith({
                where: { id: mockAuctionItem.id },
                data: { quantity: newQuantity },
            });
            expect(result).toEqual({ ...mockAuctionItem, quantity: newQuantity });
        });
    });

    describe("Transaction methods", () => {
        // Create mock transaction client
        const mockTx = {
            user: {
                update: vi.fn(),
                findUnique: vi.fn(),
            },
            auction_item: {
                update: vi.fn(),
            },
        };

        // Define mockBuyer to fix the reference error
        const mockBuyer = { ...defaultMockUser, id: 2, cash: 2000 };

        beforeEach(() => {
            vi.resetAllMocks();
        });

        describe("updateBuyerCashTransaction", () => {
            it("should update a buyer's cash within a transaction", async () => {
                const newCash = 1500;
                mockTx.user.update.mockResolvedValue({ ...mockUser, cash: newCash });

                const result = await AuctionRepository.updateBuyerCashTransaction(mockTx as any, mockUser.id, newCash);

                expect(mockTx.user.update).toHaveBeenCalledWith({
                    where: { id: mockUser.id },
                    data: { cash: newCash },
                });
                expect(result).toEqual({ ...mockUser, cash: newCash });
            });
        });

        describe("findSellerTransaction", () => {
            it("should find a seller within a transaction", async () => {
                mockTx.user.findUnique.mockResolvedValue(mockUser);

                const result = await AuctionRepository.findSellerTransaction(mockTx as any, mockUser.id);

                expect(mockTx.user.findUnique).toHaveBeenCalledWith({
                    where: { id: mockUser.id },
                });
                expect(result).toEqual(mockUser);
            });
        });

        describe("updateUserCashTransaction", () => {
            it("should update a user's cash within a transaction", async () => {
                const newCash = 1500;
                mockTx.user.update.mockResolvedValue({ ...mockUser, cash: newCash });

                const result = await AuctionRepository.updateUserCashTransaction(mockTx as any, mockUser.id, newCash);

                expect(mockTx.user.update).toHaveBeenCalledWith({
                    where: { id: mockUser.id },
                    data: { cash: newCash },
                });
                expect(result).toEqual({ ...mockUser, cash: newCash });
            });
        });

        describe("updateUserBankBalanceTransaction", () => {
            it("should update a user's bank balance within a transaction", async () => {
                const newBankBalance = 1500;
                mockTx.user.update.mockResolvedValue({ ...mockUser, bank_balance: newBankBalance });

                const result = await AuctionRepository.updateUserBankBalanceTransaction(
                    mockTx as any,
                    mockUser.id,
                    newBankBalance
                );

                expect(mockTx.user.update).toHaveBeenCalledWith({
                    where: { id: mockUser.id },
                    data: { bank_balance: newBankBalance },
                });
                expect(result).toEqual({ ...mockUser, bank_balance: newBankBalance });
            });
        });

        describe("updateAuctionItemTransaction", () => {
            it("should update an auction item within a transaction", async () => {
                const newQuantity = 1;
                const newStatus = "in_progress" as AuctionItemStatus;
                mockTx.auction_item.update.mockResolvedValue({
                    ...mockAuctionItem,
                    quantity: newQuantity,
                    status: newStatus,
                });

                const result = await AuctionRepository.updateAuctionItemTransaction(
                    mockTx as any,
                    mockAuctionItem.id,
                    newQuantity,
                    newStatus
                );

                expect(mockTx.auction_item.update).toHaveBeenCalledWith({
                    where: { id: mockAuctionItem.id },
                    data: {
                        quantity: newQuantity,
                        status: newStatus,
                    },
                    include: {
                        item: true,
                        user: true,
                    },
                });
                expect(result).toEqual({
                    ...mockAuctionItem,
                    quantity: newQuantity,
                    status: newStatus,
                });
            });
        });

        describe("executeAuctionTransaction", () => {
            it("should execute all transaction steps for an auction buyout", async () => {
                // Setup mock implementations
                mockTx.user.update.mockImplementation((params) => {
                    if (params.where.id === mockUser.id) {
                        return Promise.resolve({ ...mockUser, cash: params.data.cash });
                    } else {
                        return Promise.resolve({ ...mockBuyer, cash: params.data.cash });
                    }
                });

                const newBuyerCash = 1500;
                const newSellerCash = 1500;
                const newQuantity = 1;
                const newStatus = "in_progress" as AuctionItemStatus;

                mockTx.auction_item.update.mockResolvedValue({
                    ...mockAuctionItem,
                    quantity: newQuantity,
                    status: newStatus,
                });

                const result = await AuctionRepository.executeAuctionTransaction(
                    mockTx as any,
                    mockBuyer.id,
                    newBuyerCash,
                    mockUser.id,
                    newSellerCash,
                    mockAuctionItem.id,
                    newQuantity,
                    newStatus,
                    false
                );

                // Buyer cash should be updated
                expect(mockTx.user.update).toHaveBeenCalledWith({
                    where: { id: mockBuyer.id },
                    data: { cash: newBuyerCash },
                });

                // Seller cash should be updated
                expect(mockTx.user.update).toHaveBeenCalledWith({
                    where: { id: mockUser.id },
                    data: { cash: newSellerCash },
                });

                // Auction item should be updated
                expect(mockTx.auction_item.update).toHaveBeenCalledWith({
                    where: { id: mockAuctionItem.id },
                    data: {
                        quantity: newQuantity,
                        status: newStatus,
                    },
                    include: {
                        item: true,
                        user: true,
                    },
                });

                expect(result).toEqual({
                    ...mockAuctionItem,
                    quantity: newQuantity,
                    status: newStatus,
                });
            });

            it("should handle bank funds correctly", async () => {
                // Setup mock implementations
                mockTx.user.update.mockImplementation((params) => {
                    if (params.where.id === mockUser.id) {
                        if (params.data.bank_balance) {
                            return Promise.resolve({ ...mockUser, bank_balance: params.data.bank_balance });
                        } else {
                            return Promise.resolve({ ...mockUser, cash: params.data.cash });
                        }
                    } else {
                        return Promise.resolve({ ...mockBuyer, cash: params.data.cash });
                    }
                });

                const newBuyerCash = 1500;
                const newSellerCash = 1500;
                const newQuantity = 1;
                const newStatus = "in_progress" as AuctionItemStatus;
                const bankFunds = true;
                const sellerBankBalance = 2000;

                mockTx.auction_item.update.mockResolvedValue({
                    ...mockAuctionItem,
                    quantity: newQuantity,
                    status: newStatus,
                    bankFunds,
                });

                const result = await AuctionRepository.executeAuctionTransaction(
                    mockTx as any,
                    mockBuyer.id,
                    newBuyerCash,
                    mockUser.id,
                    newSellerCash,
                    mockAuctionItem.id,
                    newQuantity,
                    newStatus,
                    bankFunds,
                    sellerBankBalance
                );

                // Buyer cash should be updated
                expect(mockTx.user.update).toHaveBeenCalledWith({
                    where: { id: mockBuyer.id },
                    data: { cash: newBuyerCash },
                });

                // Seller bank balance should be updated
                expect(mockTx.user.update).toHaveBeenCalledWith({
                    where: { id: mockUser.id },
                    data: { bank_balance: sellerBankBalance },
                });

                // Auction item should be updated
                expect(mockTx.auction_item.update).toHaveBeenCalledWith({
                    where: { id: mockAuctionItem.id },
                    data: {
                        quantity: newQuantity,
                        status: newStatus,
                    },
                    include: {
                        item: true,
                        user: true,
                    },
                });

                expect(result).toEqual({
                    ...mockAuctionItem,
                    quantity: newQuantity,
                    status: newStatus,
                    bankFunds,
                });
            });
        });
    });
});
