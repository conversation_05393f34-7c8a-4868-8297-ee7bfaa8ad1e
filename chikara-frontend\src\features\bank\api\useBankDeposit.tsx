import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBankDeposit = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.bank.deposit.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.BANK.BANKTRANSACTIONS,
                });
            },
        })
    );
};
