import { PrismaClient, SkillType } from "@prisma/client";
import { parse } from "date-fns";

import defaultPets from "./default_seeds/default_pets";
import defaultStaticNodes from "./default_seeds/default_static_nodes";
import defaultAccounts from "./latest/accounts.json" assert { type: "json" };
import bounties from "./latest/bounties.json" assert { type: "json" };
import chatMessages from "./latest/chat_messages.json" assert { type: "json" };
import chatRooms from "./latest/chat_rooms.json" assert { type: "json" };
import defaultCraftingRecipes from "./latest/crafting_recipes.json" assert { type: "json" };
import defaultCreatures from "./latest/creatures.json" assert { type: "json" };
import defaultDropChances from "./latest/drop_chances.json" assert { type: "json" };
// import equippedItems from "../src/config/sequelize/seeders/json/latest/equipped_items_new.json";
import gangMembers from "./latest/gang_members.json" assert { type: "json" };
import defaultGangs from "./latest/gangs.json" assert { type: "json" };
import statusEffects from "./latest/injuries.json" assert { type: "json" };
import defaultItems from "./latest/items.json" assert { type: "json" };
import defaultJobs from "./latest/jobs.json" assert { type: "json" };
import pollResponses from "./latest/poll_responses.json" assert { type: "json" };
import polls from "./latest/polls.json" assert { type: "json" };
import privateMessages from "./latest/private_messages.json" assert { type: "json" };
import profileComments from "./latest/profile_comments.json" assert { type: "json" };
import defaultProperty from "./latest/property.json" assert { type: "json" };
import questObjectiveProgress from "./latest/quest_objective_progress.json";
import questObjectives from "./latest/quest_objectives.json";
import questProgress from "./latest/quest_progresses.json" assert { type: "json" };
import questRewards from "./latest/quest_rewards.json";
import defaultQuests from "./latest/quests.json" assert { type: "json" };
import defaultRecipeItems from "./latest/recipe_items.json" assert { type: "json" };
import defaultShopListings from "./latest/shop_listings.json" assert { type: "json" };
import defaultShops from "./latest/shops.json" assert { type: "json" };
import suggestionComments from "./latest/suggestion_comments.json" assert { type: "json" };
import suggestionVotes from "./latest/suggestion_votes.json" assert { type: "json" };
import suggestions from "./latest/suggestions.json" assert { type: "json" };
import defaultTalents from "./default_seeds/default_talents.js";
import traderReps from "./latest/trader_reps.json" assert { type: "json" };
import userAchievements from "./latest/user_achievements.json" assert { type: "json" };
// import userDebuffs from "../src/config/sequelize/seeders/json/latest/user_debuffs.json";
// import userEffects from "../src/config/sequelize/seeders/json/latest/user_effects.json";
import userItems from "./latest/user_items.json" assert { type: "json" };
import userRecipes from "./latest/user_recipes.json" assert { type: "json" };
import userTalents from "./latest/user_talents.json" assert { type: "json" };
import defaultUsers from "./latest/users.json" assert { type: "json" };
import migratedUserSkills from "./latest/user_skills.json" assert { type: "json" };
import defaultStorySeasons from "./default_seeds/default_story_seasons";
import defaultStoryChapters from "./default_seeds/default_story_chapters";
import defaultStoryEpisodes from "./default_seeds/default_story_episodes";
import defaultStoryQuests from "./default_seeds/default_story_quests";
import defaultStoryQuestObjectives from "./default_seeds/default_story_quest_objectives";

const prisma = new PrismaClient();

// Helper functions
const convertIntToBool = (value: number | boolean): boolean =>
    value === 0 || value === 1 ? value === 1 : Boolean(value);

const convertIntsToBools = (item: Record<string, any>, keys: string[]): Record<string, any> => {
    const newItem = { ...item };
    keys.forEach((key) => {
        if (typeof newItem[key] === "number") {
            newItem[key] = convertIntToBool(item[key]);
        }
    });
    return newItem;
};

const dateFields = {
    createdAt: "dd/MM/yyyy HH:mm:ss",
    updatedAt: "dd/MM/yyyy HH:mm:ss",
    last_activity: "dd/MM/yyyy HH:mm:ss",
    endsAt: "dd/MM/yyyy HH:mm:ss",
    dailyQuestsRewardClaimed: "dd/MM/yyyy",
};

const booleanFields = [
    "isUnlockable",
    "disabled",
    "boss",
    "pushNotificationsEnabled",
    "blockNextJobPayout",
    "isTradeable",
    "hidden",
    "ended",
    "showResults",
    "active",
    "read",
    "isGlobal",
    "victory",
];

const propsToDelete = [
    "statModifiers",
    "dailyRewardClaimed",
    "equippedWeaponId",
    "equippedRangedWeaponId",
    "equippedHeadId",
    "equippedChestId",
    "equippedHandsId",
    "equippedLegsId",
    "equippedFeetId",
    "equippedFingerId",
    "equippedOffhandId",
    "authMethod",
    "resetToken",
    "tokenValidUntil",
    "ipHistory",
    "completedCourses",
    "battleWins",
    "npcBattleWins",
    "craftsCompleted",
    "marketItemsSold",
    "marketMoneyMade",
    "totalMuggingGain",
    "totalMuggingLoss",
    "totalCasinoProfitLoss",
    "questsCompleted",
    "dailyQuestsCompleted",
    "coursesCompleted",
    "roguelikeNodesCompleted",
    "roguelikeMapsCompleted",
    "examsCompleted",
    "totalBountyRewards",
    "totalMissionHours",
    "courseIndex",
    "dailyBossesDefeated",
    "craftingRecipe1Started",
    "craftingRecipe2Started",
    "craftingRecipe3Started",
    "craftingRecipe1Ends",
    "craftingRecipe2Ends",
    "craftingRecipe3Ends",
    "craftingRecipe1Id",
    "craftingRecipe2Id",
    "craftingRecipe3Id",
    "battleValidUntil",
    "battleAggressor",
    "npcEnemyDetails",
    "battleOpponentId",
    "battleStatusEffects",
    "isZombie",
    "questProgressId",
    "charismaFormula",
    "charisma",
    "currentStamina",
    "strength",
    "endurance",
    "dexterity",
    "intelligence",
    "defence",
    "bannedUntil",
    "upgradeBenchConstructed",
    "equippedAbility1Id",
    "equippedAbility2Id",
    "equippedAbility3Id",
    "equippedAbility4Id",
    "classAssigned",
    "storyContext",
];

const passwordValue =
    "deaa8f96e0344570770234e5cea14b7e:d195eb621c724878381f3ead255a382a8b79622fc1e4eec8f0d88f9750bac075e7656c48fc5bbb786aa4907f9f0bd2308507a5b0136a88614314b819dd87817c";

const tableMapper = (item: Record<string, any>): Record<string, any> => {
    let output: Record<string, any> = {};

    // Handle date fields conversion
    Object.entries(dateFields).forEach(([field, format]) => {
        if (item[field] && (field !== "endsAt" || typeof item[field] === "string")) {
            output[field] = parse(item[field], format, new Date());
        }
    });

    // Handle postgres boolean conversions and sqlite
    const convertedBools = convertIntsToBools(item, booleanFields);
    if ((convertedBools.hidden === true || convertedBools.hidden === false) && item.userId === 0) {
        output.userId = 1;
    }
    output = { ...convertedBools, ...output };

    if (item.password) {
        output.password = passwordValue;
    }

    // Change chat msg with userId 0 to 2
    if ((item.parentMessageId || item.parentMessageId === null) && item.userId === 0) {
        output.userId = 2;
    }

    if (item.buffs) {
        output.buffs = JSON.stringify(item.buffs);
        output.createdAt = new Date();
        output.updatedAt = new Date();
    }

    return output;
};

async function wipeData() {
    try {
        // Use deleteMany in reverse order of dependencies
        await prisma.user_property.deleteMany(); // Foreign keys cascade.  Must be first
        await prisma.user_crafting_queue.deleteMany();
        await prisma.user_achievements.deleteMany();
        await prisma.user_talent.deleteMany();
        await prisma.user_completed_course.deleteMany();
        await prisma.user_recipe.deleteMany();
        await prisma.user_item.deleteMany();
        await prisma.user_status_effect.deleteMany();
        await prisma.friend_request.deleteMany();
        await prisma.friend.deleteMany();
        await prisma.rival.deleteMany();
        await prisma.status_effect.deleteMany();
        await prisma.verification.deleteMany();
        await prisma.account.deleteMany();
        await prisma.session.deleteMany();
        await prisma.registration_code.deleteMany();
        await prisma.quest_objective_progress.deleteMany();
        await prisma.quest_progress.deleteMany();
        await prisma.quest_objective.deleteMany();
        await prisma.quest.deleteMany();
        await prisma.push_token.deleteMany();
        await prisma.profile_comment.deleteMany();
        await prisma.private_message.deleteMany();
        await prisma.poll_response.deleteMany();
        await prisma.poll.deleteMany();
        await prisma.notification.deleteMany();
        await prisma.lottery_entry.deleteMany();
        await prisma.lottery.deleteMany();
        await prisma.job.deleteMany();
        await prisma.suggestion_vote.deleteMany();
        await prisma.suggestion_comment.deleteMany();
        await prisma.suggestion.deleteMany();
        await prisma.shrine_donation.deleteMany();
        await prisma.shrine_goal.deleteMany();
        await prisma.shop_listing.deleteMany();
        await prisma.trader_rep.deleteMany();
        await prisma.shop.deleteMany();
        await prisma.talent.deleteMany();
        await prisma.recipe_item.deleteMany();
        await prisma.daily_quest.deleteMany();
        await prisma.daily_mission.deleteMany();
        await prisma.drop_chance.deleteMany();
        await prisma.creature.deleteMany();
        await prisma.crafting_recipe.deleteMany();
        await prisma.chat_room.deleteMany();
        await prisma.chat_message.deleteMany();
        await prisma.bounty.deleteMany();
        await prisma.battle_log.deleteMany();
        await prisma.bank_transaction.deleteMany();
        await prisma.auction_item.deleteMany();
        await prisma.equipped_item.deleteMany();
        await prisma.gang_log.deleteMany();
        await prisma.gang_invite.deleteMany();
        await prisma.gang_member.deleteMany();
        await prisma.gang.deleteMany();
        await prisma.game_stats.deleteMany();
        await prisma.user_skill.deleteMany();
        await prisma.item.deleteMany();
        await prisma.action_log.deleteMany();
        await prisma.user.deleteMany();
        await prisma.property.deleteMany(); //must be last due to foreign key cascade

        console.log("All data wiped from tables.");
    } catch (error) {
        console.error("Error wiping data:", error);
        throw error;
    }
}

async function seed() {
    // Helper function to insert data
    const insertData = async <T>(model: any, data: any[]): Promise<void> => {
        if (data.length > 0) {
            const mappedData = data.map((item) => {
                const cleanedItem = { ...item };
                propsToDelete.forEach((prop) => delete cleanedItem[prop]);

                return {
                    ...cleanedItem,
                    ...tableMapper(cleanedItem),
                };
            });

            // Use createMany for bulk insert
            try {
                await model.createMany({
                    data: mappedData,
                    skipDuplicates: true,
                });
            } catch (error) {
                console.error(`Error inserting data into ${model.name}:`, error);
                throw error;
            }
        }
    };

    try {
        // await wipeData();

        // PETS
        await insertData(prisma.pet, defaultPets);

        // ITEMS
        await insertData(prisma.crafting_recipe, defaultCraftingRecipes);
        await insertData(prisma.item, defaultItems);
        await insertData(prisma.recipe_item, defaultRecipeItems);

        // SHOPS
        await insertData(prisma.shop, defaultShops);

        // SHOP LISTINGS
        await insertData(prisma.shop_listing, defaultShopListings);

        // JOBS
        await insertData(prisma.job, defaultJobs);

        // CREATURES
        await insertData(prisma.creature, defaultCreatures);

        // PROPERTY
        await insertData(prisma.property, defaultProperty);

        // TALENTS
        await insertData(prisma.talent, defaultTalents);

        // DROPCHANCES
        await insertData(prisma.drop_chance, defaultDropChances);

        // QUESTS
        await insertData(prisma.quest, defaultQuests);

        await insertData(prisma.quest_objective, questObjectives);

        await insertData(prisma.quest_reward, questRewards);

        // STATUS EFFECTS
        await insertData(prisma.status_effect, statusEffects);

        // Create temporary users without gangId (to solve circular dependency)
        const tempUsers = defaultUsers.map((user) => {
            const { gangId, ...userWithoutGangId } = user;
            return userWithoutGangId;
        });

        // USERS (without gang references initially)
        await insertData(prisma.user, tempUsers);

        // GANGS (now users exist for owner references)
        await insertData(prisma.gang, defaultGangs);

        // Update users with their gangId (now that gangs exist)
        for (const user of defaultUsers) {
            if (user.gangId) {
                await prisma.user.update({
                    where: { id: user.id },
                    data: { gangId: user.gangId },
                });
            }
        }

        // USER SKILLS
        await insertData(prisma.user_skill, migratedUserSkills);

        // GANG MEMBERS
        await insertData(prisma.gang_member, gangMembers);

        await insertData(prisma.account, defaultAccounts);
        await insertData(prisma.user_item, userItems);
        // await insertData(prisma.equipped_item, equippedItems);

        await insertData(prisma.user_talent, userTalents);
        await insertData(prisma.user_recipe, userRecipes);
        await insertData(prisma.quest_progress, questProgress);
        await insertData(prisma.quest_objective_progress, questObjectiveProgress);
        await insertData(prisma.trader_rep, traderReps);
        await insertData(prisma.user_achievements, userAchievements);

        const userSkills: SkillType[] = [
            "mining",
            "scavenging",
            "foraging",
            "fabrication",
            "outfitting",
            "chemistry",
            "electronics",
            "vitality",
        ];

        // Create user_skill_levels for each existing user
        const users = await prisma.user.findMany({
            select: { id: true },
        });

        if (users.length > 0) {
            for (const user of users) {
                for (const skillType of userSkills) {
                    await prisma.user_skill.create({
                        data: {
                            userId: user.id,
                            skillType,
                        },
                    });
                }
            }

            console.log(`Created skill levels for ${users.length} users`);
        }

        // BOUNTIES
        await insertData(prisma.bounty, bounties);

        // SUGGESTIONS/POLLS
        await insertData(prisma.suggestion, suggestions);
        await insertData(prisma.suggestion_comment, suggestionComments);
        await insertData(prisma.suggestion_vote, suggestionVotes);
        await insertData(prisma.poll, polls);
        await insertData(prisma.poll_response, pollResponses);

        // CHAT ROOMS
        await insertData(prisma.chat_room, chatRooms);

        // PRIVATE MESSAGES
        await insertData(prisma.private_message, privateMessages);

        // PROFILE COMMENTS
        await insertData(prisma.profile_comment, profileComments);

        // CHAT MESSAGES
        await insertData(prisma.chat_message, chatMessages);

        // STATIC NODES
        await insertData(prisma.explore_static_node, defaultStaticNodes);

        // Story Mode Content
        await insertData(prisma.story_season, defaultStorySeasons);
        await insertData(prisma.story_chapter, defaultStoryChapters);

        // Story Quests (separate from regular quests)
        await insertData(prisma.quest, defaultStoryQuests);
        await insertData(prisma.quest_objective, defaultStoryQuestObjectives);

        await insertData(prisma.story_episode, defaultStoryEpisodes);

        console.log("Alpha seed completed successfully.");
    } catch (error) {
        console.error("Error seeding data:", error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

seed()
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
