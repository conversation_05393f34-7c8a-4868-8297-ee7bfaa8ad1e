import { vi } from "vitest";

// This is a mock module that will replace ../config/redisClient.js in tests
// Mock Redis client with a proxy to handle any method calls

const mockConnect = vi.fn().mockResolvedValue();
const mockDisconnect = vi.fn().mockResolvedValue();

// Create a mock Redis client that handles all method calls
const redisClient = {
    // Connection methods
    connect: mockConnect,
    disconnect: mockDisconnect,
    close: vi.fn().mockResolvedValue("OK"),

    // Flag to pretend we're connected
    isOpen: true,
    isReady: true,

    // Data methods
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue("OK"),
    setEx: vi.fn().mockResolvedValue("OK"),
    del: vi.fn().mockResolvedValue(1),
    keys: vi.fn().mockResolvedValue([]),

    // Pub/Sub methods
    publish: vi.fn().mockResolvedValue(0),
    subscribe: vi.fn().mockResolvedValue(),
    unsubscribe: vi.fn().mockResolvedValue(),

    // Event handlers - they should be synchronous functions
    on: vi.fn(),
    off: vi.fn(),

    // Special commands
    EXISTS: vi.fn().mockResolvedValue(0),
    PING: vi.fn().mockResolvedValue("PONG"),
    HGETALL: vi.fn().mockResolvedValue({}),
    HSET: vi.fn().mockResolvedValue(1),

    // Hash methods
    hGet: vi.fn().mockResolvedValue(null),
    hGetAll: vi.fn().mockResolvedValue({}),
    hSet: vi.fn().mockResolvedValue(1),
    hDel: vi.fn().mockResolvedValue(1),

    // Command tracking for debugging
    _commandsExecuted: {},
};

const redisOptions = {
    host: "localhost",
    port: 6379,
};

export { redisClient, redisOptions, mockConnect, mockDisconnect };
