import { useMutation, useQueryClient } from "@tanstack/react-query";
import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";

const useProcessSwing = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.mining.processSwing.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.MINING.SESSION });
            },
        })
    );
};

export default useProcessSwing;
