import{F as I,e as z,g as P,c as f,r as j,j as e,ad as E,I as k,f as J,an as b,h as y,b as S,y as q,a as B,B as F,t as D,a7 as M,O,aF as H}from"./index--cEnoMkg.js";import{B as R}from"./banknote-CGu40TCx.js";import{C as A}from"./circle-check-big-DkHAMB3v.js";import{C as L}from"./circle-x-B-7GxCnr.js";import{g as Q,B as w}from"./getJobImage-CkPZ6zcB.js";import{T as G}from"./trending-up-Bh6mAcwG.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],X=I("award",U),Y=t=>{const l=z();return P({...f.jobs.changePayoutTime.mutationOptions(),onSuccess:i=>{i.success&&(l.invalidateQueries({queryKey:f.jobs.currentJobInfo.key()}),t?.onSuccess&&t.onSuccess())},onError:i=>{console.error("Job payout time change error:",i),t?.onError&&t.onError(i)}})};function V({currentUser:t,open:l,setOpen:i}){const[u,s]=j.useState(null),o=Y({onSuccess:()=>{t.jobPayoutHour?b.success("Payout time changed successfully!"):b.success("Payout time set successfully!"),i(!1),s(null)},onError:n=>{b.error(n?.response?.data||"An error occurred")}}),a=n=>{const d=new Date;return d.setUTCHours(n,0,0,0),`${J(d,"h a")} Local Time`},h=n=>{t?.jobPayoutHour||b.error("Payout time not set. Change it on your job page."),i(n),s(null)},x=async()=>{u&&o.mutate({time:u})};return e.jsx(E,{open:l,showClose:t?.jobPayoutHour,title:"Job Payout Time",iconBackground:"shadow-lg",modalMaxWidth:"max-w-3xl!",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/iSaQJS3.png",alt:"",className:"mt-1 h-6 w-auto"}),onOpenChange:h,children:e.jsxs("div",{className:"flex flex-col md:mx-8",children:[e.jsx("h3",{className:"mb-2 font-medium text-gray-900 text-stroke-sm dark:text-white",children:"Payout Time (UTC)"}),e.jsxs("ul",{className:"w-full items-center rounded-lg border border-gray-200 bg-white font-medium text-gray-900 text-lg sm:flex dark:border-gray-600 dark:bg-gray-800 dark:text-white",children:[e.jsxs("li",{className:"w-full border-gray-200 border-b py-1 sm:border-r sm:border-b-0 dark:border-gray-600",children:[e.jsxs("div",{className:"flex items-center ps-5",children:[e.jsx("input",{id:"horizontal-list-radio-license",type:"radio",value:"2",name:"list-radio",className:"size-5 border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-blue-600 dark:focus:ring-offset-gray-700",onChange:()=>s(2)}),e.jsxs("label",{htmlFor:"horizontal-list-radio-license",className:"ms-2 w-full py-3 font-medium text-gray-900 text-lg dark:text-white",children:["2 AM",e.jsx("span",{className:"ml-12 text-center text-base text-blue-500 md:hidden",children:a(2)})]})]}),e.jsx("p",{className:"-mt-2.5 mb-1 hidden text-center text-blue-500 text-sm leading-0 md:block",children:a(2)})]}),e.jsxs("li",{className:"w-full border-gray-200 border-b py-1 sm:border-r sm:border-b-0 dark:border-gray-600",children:[e.jsxs("div",{className:"flex items-center ps-5",children:[e.jsx("input",{id:"horizontal-list-radio-id",type:"radio",value:"8",name:"list-radio",className:"size-5 border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-blue-600 dark:focus:ring-offset-gray-700",onChange:()=>s(8)}),e.jsxs("label",{htmlFor:"horizontal-list-radio-id",className:"ms-2 w-full py-3 font-medium text-gray-900 text-lg dark:text-white",children:["8 AM",e.jsx("span",{className:"ml-12 text-center text-base text-blue-500 md:hidden",children:a(8)})]})]}),e.jsx("p",{className:"-mt-2.5 mb-1 hidden text-center text-blue-500 text-sm leading-0 md:block",children:a(8)})]}),e.jsxs("li",{className:"w-full border-gray-200 border-b py-1 sm:border-r sm:border-b-0 dark:border-gray-600",children:[e.jsxs("div",{className:"flex items-center ps-5",children:[e.jsx("input",{id:"horizontal-list-radio-military",type:"radio",value:"14",name:"list-radio",className:"size-5 border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-blue-600 dark:focus:ring-offset-gray-700",onChange:()=>s(14)}),e.jsxs("label",{htmlFor:"horizontal-list-radio-military",className:"ms-2 w-full py-3 font-medium text-gray-900 text-lg dark:text-white",children:["2 PM",e.jsx("span",{className:"ml-12 text-center text-base text-blue-500 md:hidden",children:a(14)})]})]}),e.jsx("p",{className:"-mt-2.5 mb-1 hidden text-center text-blue-500 text-sm leading-0 md:block",children:a(14)})]}),e.jsxs("li",{className:"w-full py-1 dark:border-gray-600",children:[e.jsxs("div",{className:"flex items-center ps-5",children:[e.jsx("input",{id:"horizontal-list-radio-passport",type:"radio",value:"20",name:"list-radio",className:"size-5 border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-blue-600 dark:focus:ring-offset-gray-700",onChange:()=>s(20)}),e.jsxs("label",{htmlFor:"horizontal-list-radio-passport",className:"ms-2 w-full py-3 font-medium text-gray-900 text-lg dark:text-white",children:["8 PM",e.jsx("span",{className:"ml-12 text-center text-base text-blue-500 md:hidden",children:a(20)})]})]}),e.jsx("p",{className:"-mt-2.5 mb-1 hidden text-center text-blue-500 text-sm leading-0 md:block",children:a(20)})]})]}),t?.jobPayoutHour?e.jsxs("p",{className:"-mb-2.5 mt-2.5 flex w-full justify-center text-center text-amber-500 text-lg md:gap-2",children:[e.jsxs("span",{className:"md:-ml-3 mt-1.5 ml-0 text-amber-400 md:my-auto",children:[" ",e.jsx(k,{})]}),t?.jobPayoutHour,"Changing your payout time will skip your next payment!"]}):e.jsxs("p",{className:"-mb-2.5 -ml-3 mt-2.5 flex w-full justify-center text-center text-custom-yellow text-lg md:gap-2",children:[e.jsxs("span",{className:"md:-ml-3 mt-1.5 ml-4 text-amber-400 md:my-auto",children:[" ",e.jsx(k,{})]}),"Set a daily payout time for your job earnings."]}),e.jsx("button",{type:"button",className:"darkBlueButtonBGSVG mx-auto mt-5 flex h-14 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:grayscale md:mt-6 dark:text-slate-200",onClick:()=>x(),children:"Save"})]})})}function v({rank:t,title:l,level:i,salary:u,accent:s="current",getModifiedSalary:o,getStatRequirements:a,checkIfMeetsStatReqs:h}){const x=a(),n={current:"from-slate-700 to-slate-900 border-slate-600",next:"from-indigo-900/50 to-slate-900 border-indigo-600",future:"from-purple-900/50 to-slate-900 border-purple-600"},d={current:"text-slate-400",next:"text-indigo-400",future:"text-purple-400"};return e.jsxs("div",{className:y("relative overflow-hidden rounded-xl border bg-gradient-to-b p-6 shadow-lg transition-all hover:shadow-xl",n[s]),children:[e.jsx("div",{className:"absolute inset-0 opacity-5",children:e.jsx("div",{className:"h-full w-full",style:{backgroundImage:"repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.1) 35px, rgba(255,255,255,.1) 70px)"}})}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(X,{className:y("size-8",d[s])}),e.jsx("span",{className:y("rounded-full px-3 py-1 text-xs font-semibold uppercase tracking-wider",s==="current"&&"bg-slate-700 text-slate-300",s==="next"&&"bg-indigo-900/50 text-indigo-300",s==="future"&&"bg-purple-900/50 text-purple-300"),children:l})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white",children:["Rank ",i]}),e.jsxs("div",{className:"mt-2 flex items-center text-custom-yellow",children:[e.jsx(R,{className:"mr-2 size-5"}),e.jsxs("span",{className:"text-xl font-semibold",children:["¥",o(u),"/day"]})]})]}),t&&Object.keys(t).length>0&&e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("p",{className:"text-xs font-semibold uppercase tracking-wider text-gray-400",children:"Requirements"}),e.jsx("div",{className:"space-y-2",children:x.map((c,p)=>{const m=t[c.type],g=h(c.type,m);return e.jsxs("div",{className:y("flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors",g?"bg-emerald-900/20 text-emerald-400":"bg-red-900/20 text-red-400"),children:[e.jsx("span",{children:c.name}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:m}),g?e.jsx(A,{className:"size-4"}):e.jsx(L,{className:"size-4"})]})]},p)})})]})]})]})}const _=(t={})=>S(f.jobs.currentJobInfo.queryOptions({staleTime:3e4,...t})),C=({level:t,jobId:l,options:i={}})=>S(f.jobs.getJobLvlReqs.queryOptions({input:{level:t,jobId:l},...i})),K=()=>{const t=z();return P(f.jobs.applyForPromotion.mutationOptions({onSuccess:l=>{if(!l){b.error("You do not meet the requirements for a promotion!");return}t.invalidateQueries({queryKey:q.JOBS.CURRENTJOBINFO}),b.success("You were successfully promoted!")},onError:l=>{console.error("Job promotion error:",l)}}))};function ae(){const[t,l]=j.useState(!1),i=B(),u=F(),{data:s,isLoading:o}=_(),{data:a}=D(),{mutate:h}=K(),x=M("investor");j.useEffect(()=>{!o&&!s&&i("/joblistings")},[o,s,i]);const{data:n}=C({level:s?.level?s.level+1:1,jobId:s?.id||1,options:{enabled:!!(s?.id&&s?.level)}}),{data:d}=C({level:s?.level?s.level+2:1,jobId:s?.id||1,options:{enabled:!!(s?.id&&s?.level)}}),c=n&&s?{...n,salary:s.salary+100,level:s.level?s.level+1:1,payment:s.salary+100}:{level:1,payment:0},p=d&&s?{...d,salary:s.salary+200,level:s.level?s.level+2:2,payment:s.salary+200}:{level:2,payment:0},m=r=>x?Math.round(r*(x.modifier||1)):r;if(j.useEffect(()=>{a?.jobId&&!a?.jobPayoutHour&&l(!0)},[a?.jobId,a?.jobPayoutHour]),u?.JOBS_DISABLED)return e.jsx("div",{className:"mt-10 flex flex-col dark:text-slate-200",children:e.jsxs("div",{className:"mx-auto text-center",children:[e.jsx("h2",{className:"text-xl",children:"Jobs currently Disabled"}),e.jsx("p",{children:"Please return later."})]})});const g=(r,T)=>a?a[r]>=T:!1,N=()=>{const r=[];switch(s?.name){case"Nemar Ramen":r.push({type:"strength",name:"STR"}),r.push({type:"defence",name:"DEF"});break;case"Robo-San Construction":r.push({type:"dexterity",name:"DEX"}),r.push({type:"endurance",name:"END"});break;case"Costar Cafe":r.push({type:"strength",name:"STR"}),r.push({type:"dexterity",name:"DEX"});break;case"PGS Corp":r.push({type:"strength",name:"STR"}),r.push({type:"dexterity",name:"DEX"}),r.push({type:"defence",name:"DEF"});break;case"5-11 Convenience Store":r.push({type:"intelligence",name:"INT"}),r.push({type:"endurance",name:"END"});break;case"Envydia Computers":r.push({type:"intelligence",name:"INT"});break}return r};return o?e.jsx("div",{className:"flex h-screen items-center justify-center",children:e.jsx("div",{className:"text-lg text-gray-400",children:"Loading job information..."})}):s?e.jsxs("div",{className:"min-h-screen bg-gradient-to-b from-slate-900 to-black p-4 md:p-8",children:[e.jsx(V,{currentUser:a,open:t,setOpen:l}),e.jsxs("div",{className:"mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-8 overflow-hidden rounded-2xl bg-gradient-to-br from-slate-800 via-slate-850 to-slate-900 shadow-2xl border border-slate-700/50",children:[e.jsx("div",{className:"absolute inset-0 opacity-5",children:e.jsx("div",{className:"h-full w-full",style:{backgroundImage:"radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%)"}})}),e.jsx("div",{className:"relative z-10",children:e.jsxs("div",{className:"flex flex-col lg:flex-row",children:[e.jsx("div",{className:"flex-1 p-5 lg:p-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 lg:gap-5",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsxs("div",{className:"relative",children:[e.jsx("img",{className:"size-16 sm:size-18 lg:size-20 rounded-xl shadow-2xl ring-2 ring-slate-600/30",src:Q(s.name),alt:s.name}),e.jsx("div",{className:"absolute -bottom-1 -right-1 bg-slate-700 rounded-full p-1 shadow-lg",children:e.jsx(w,{className:"size-3 text-slate-300"})})]})}),e.jsxs("div",{className:"flex-1 space-y-3",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl lg:text-3xl font-bold text-white mb-1 tracking-tight",children:s.name}),e.jsx("p",{className:"text-slate-400 text-xs lg:text-sm",children:"Your current workplace"})]}),e.jsxs("div",{className:"flex flex-wrap gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/40 rounded-lg px-3 py-2 backdrop-blur-sm",children:[e.jsx("div",{className:"bg-blue-500/20 rounded-md p-1.5",children:e.jsx(O,{className:"size-3 text-blue-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-[10px] text-slate-400 uppercase tracking-wider font-medium",children:"Rank"}),e.jsx("p",{className:"text-white font-semibold text-sm",children:s.level})]})]}),e.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/40 rounded-lg px-3 py-2 backdrop-blur-sm",children:[e.jsx("div",{className:"bg-green-500/20 rounded-md p-1.5",children:e.jsx(R,{className:"size-3 text-green-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-[10px] text-slate-400 uppercase tracking-wider font-medium",children:"Daily Salary"}),e.jsxs("p",{className:"text-white font-semibold text-sm",children:["¥",m(s.salary)]})]})]})]})]})]})}),e.jsx("div",{className:"lg:w-64 border-t lg:border-t-0 lg:border-l border-slate-700/50 bg-slate-800/30 backdrop-blur-sm",children:e.jsxs("div",{className:"p-4 lg:p-5 h-full flex flex-col justify-center",children:[e.jsx("h3",{className:"text-sm font-semibold text-white mb-3",children:"Quick Actions"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("button",{className:"w-full flex items-center justify-between gap-2 rounded-lg bg-slate-700/60 hover:bg-slate-700 px-3 py-2.5 font-medium text-white transition-all duration-200 hover:shadow-lg group",onClick:()=>l(!0),children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(H,{className:"size-3.5 text-slate-400 group-hover:text-slate-300"}),e.jsx("span",{className:"text-xs",children:"Payout Time"})]}),e.jsx("div",{className:"size-1.5 rounded-full bg-slate-500 group-hover:bg-slate-400"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between gap-2 rounded-lg bg-slate-700/60 hover:bg-slate-700 px-3 py-2.5 font-medium text-white transition-all duration-200 hover:shadow-lg group",onClick:()=>i("/joblistings"),children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{className:"size-3.5 text-slate-400 group-hover:text-slate-300"}),e.jsx("span",{className:"text-xs",children:"Find New Job"})]}),e.jsx("div",{className:"size-1.5 rounded-full bg-slate-500 group-hover:bg-slate-400"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between gap-2 rounded-lg bg-gradient-to-r from-blue-600/80 to-indigo-600/80 hover:from-blue-600 hover:to-indigo-600 px-3 py-2.5 font-medium text-white shadow-lg transition-all duration-200 hover:shadow-xl group",onClick:()=>h({}),children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(G,{className:"size-3.5 text-blue-200 group-hover:text-white"}),e.jsx("span",{className:"text-xs",children:"Request Promotion"})]}),e.jsx("div",{className:"size-1.5 rounded-full bg-blue-300 group-hover:bg-white"})]})]})]})})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-center text-2xl font-bold text-white",children:"Career Progression"}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-3",children:[e.jsx(v,{rank:{},title:"Current",level:s.level||0,salary:s.salary||0,accent:"current",getModifiedSalary:m,getStatRequirements:N,checkIfMeetsStatReqs:g}),e.jsx(v,{rank:c,title:"Next",level:c.level,salary:c.payment,accent:"next",getModifiedSalary:m,getStatRequirements:N,checkIfMeetsStatReqs:g}),e.jsx(v,{rank:p,title:"Future",level:p.level,salary:p.payment,accent:"future",getModifiedSalary:m,getStatRequirements:N,checkIfMeetsStatReqs:g})]}),e.jsxs("div",{className:"relative mt-8 hidden md:block",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"h-1 w-full bg-gradient-to-r from-slate-600 via-indigo-600 to-purple-600 rounded-full"})}),e.jsxs("div",{className:"relative flex justify-between",children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-slate-700 ring-4 ring-slate-900",children:e.jsx("div",{className:"h-6 w-6 rounded-full bg-slate-400"})}),e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-indigo-700 ring-4 ring-slate-900",children:e.jsx("div",{className:"h-6 w-6 rounded-full bg-indigo-400"})}),e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-purple-700 ring-4 ring-slate-900",children:e.jsx("div",{className:"h-6 w-6 rounded-full bg-purple-400"})})]})]})]})]})]}):null}export{ae as default};
