{"version": 3, "sources": ["../../../../node_modules/react-tracked/dist/createContainer.js", "../../../../node_modules/use-context-selector/dist/index.js", "../../../../node_modules/react-tracked/dist/createTrackedSelector.js", "../../../../node_modules/proxy-compare/dist/index.js", "../../../../node_modules/react-tracked/dist/utils.js"], "sourcesContent": ["import { createContext as createContext<PERSON>rig, createElement, useCallback, useContext as useContextOrig, useDebugValue, } from 'react';\nimport { createContext, useContextSelector, useContextUpdate, } from 'use-context-selector';\nimport { createTrackedSelector } from './createTrackedSelector.js';\nconst hasGlobalProcess = typeof process === 'object';\nexport const createContainer = (useValue, options) => {\n    if (typeof options === 'boolean') {\n        // eslint-disable-next-line no-console\n        console.warn('boolean option is deprecated, please specify { concurrentMode: true }');\n        options = { concurrentMode: options };\n    }\n    const { stateContextName = 'StateContainer', updateContextName = 'UpdateContainer', concurrentMode, } = options || {};\n    const StateContext = createContext(options === null || options === void 0 ? void 0 : options.defaultState);\n    const UpdateContext = createContextOrig(options === null || options === void 0 ? void 0 : options.defaultUpdate);\n    StateContext.displayName = stateContextName;\n    UpdateContext.displayName = updateContextName;\n    const Provider = (props) => {\n        const [state, update] = useValue(props);\n        return createElement(UpdateContext.Provider, { value: update }, createElement(StateContext.Provider, { value: state }, props.children));\n    };\n    const useSelector = (selector) => {\n        if (hasGlobalProcess && process.env.NODE_ENV !== 'production') {\n            const selectorOrig = selector;\n            selector = (state) => {\n                if (state === undefined) {\n                    throw new Error('Please use <Provider>');\n                }\n                return selectorOrig(state);\n            };\n        }\n        const selected = useContextSelector(StateContext, selector);\n        useDebugValue(selected);\n        return selected;\n    };\n    const useTrackedState = createTrackedSelector(useSelector);\n    const useUpdate = concurrentMode\n        ? () => {\n            if (hasGlobalProcess &&\n                process.env.NODE_ENV !== 'production' &&\n                useContextOrig(UpdateContext) === undefined) {\n                throw new Error('Please use <Provider>');\n            }\n            const contextUpdate = useContextUpdate(StateContext);\n            const update = useContextOrig(UpdateContext);\n            return useCallback((...args) => {\n                let result;\n                contextUpdate(() => {\n                    result = update(...args);\n                });\n                return result;\n            }, [contextUpdate, update]);\n        }\n        : // not concurrentMode\n            () => {\n                if (typeof process === 'object' &&\n                    process.env.NODE_ENV !== 'production' &&\n                    useContextOrig(UpdateContext) === undefined) {\n                    throw new Error('Please use <Provider>');\n                }\n                return useContextOrig(UpdateContext);\n            };\n    const useTracked = () => [useTrackedState(), useUpdate()];\n    return {\n        Provider,\n        useTrackedState,\n        useTracked,\n        useUpdate,\n        useSelector,\n    };\n};\n", "import { createElement, createContext as createContextOrig, useContext as useContextOrig, useEffect, useLayoutEffect, useReducer, useRef, useState, } from 'react';\nimport { unstable_NormalPriority as NormalPriority, unstable_runWithPriority as runWithPriority, } from 'scheduler';\nconst CONTEXT_VALUE = Symbol();\nconst ORIGINAL_PROVIDER = Symbol();\nconst isSSR = typeof window === 'undefined' ||\n    /ServerSideRendering/.test(window.navigator && window.navigator.userAgent);\nconst useIsomorphicLayoutEffect = isSSR ? useEffect : useLayoutEffect;\n// for preact that doesn't have runWithPriority\nconst runWithNormalPriority = runWithPriority\n    ? (fn) => {\n        try {\n            runWithPriority(NormalPriority, fn);\n        }\n        catch (e) {\n            if (e.message === 'Not implemented.') {\n                fn();\n            }\n            else {\n                throw e;\n            }\n        }\n    }\n    : (fn) => fn();\nconst createProvider = (ProviderOrig) => {\n    const ContextProvider = ({ value, children, }) => {\n        const valueRef = useRef(value);\n        const versionRef = useRef(0);\n        const [resolve, setResolve] = useState(null);\n        if (resolve) {\n            resolve(value);\n            setResolve(null);\n        }\n        const contextValue = useRef();\n        if (!contextValue.current) {\n            const listeners = new Set();\n            const update = (fn, options) => {\n                versionRef.current += 1;\n                const action = {\n                    n: versionRef.current,\n                };\n                if (options === null || options === void 0 ? void 0 : options.suspense) {\n                    action.n *= -1; // this is intentional to make it temporary version\n                    action.p = new Promise((r) => {\n                        setResolve(() => (v) => {\n                            action.v = v;\n                            delete action.p;\n                            r(v);\n                        });\n                    });\n                }\n                listeners.forEach((listener) => listener(action));\n                fn();\n            };\n            contextValue.current = {\n                [CONTEXT_VALUE]: {\n                    /* \"v\"alue     */ v: valueRef,\n                    /* versio\"n\"   */ n: versionRef,\n                    /* \"l\"isteners */ l: listeners,\n                    /* \"u\"pdate    */ u: update,\n                },\n            };\n        }\n        useIsomorphicLayoutEffect(() => {\n            valueRef.current = value;\n            versionRef.current += 1;\n            runWithNormalPriority(() => {\n                contextValue.current[CONTEXT_VALUE].l.forEach((listener) => {\n                    listener({ n: versionRef.current, v: value });\n                });\n            });\n        }, [value]);\n        return createElement(ProviderOrig, { value: contextValue.current }, children);\n    };\n    return ContextProvider;\n};\nconst identity = (x) => x;\n/**\n * This creates a special context for `useContextSelector`.\n *\n * @example\n * import { createContext } from 'use-context-selector';\n *\n * const PersonContext = createContext({ firstName: '', familyName: '' });\n */\nexport function createContext(defaultValue) {\n    const context = createContextOrig({\n        [CONTEXT_VALUE]: {\n            /* \"v\"alue     */ v: { current: defaultValue },\n            /* versio\"n\"   */ n: { current: -1 },\n            /* \"l\"isteners */ l: new Set(),\n            /* \"u\"pdate    */ u: (f) => f(),\n        },\n    });\n    context[ORIGINAL_PROVIDER] = context.Provider;\n    context.Provider = createProvider(context.Provider);\n    delete context.Consumer; // no support for Consumer\n    return context;\n}\n/**\n * This hook returns context selected value by selector.\n *\n * It will only accept context created by `createContext`.\n * It will trigger re-render if only the selected value is referentially changed.\n *\n * The selector should return referentially equal result for same input for better performance.\n *\n * @example\n * import { useContextSelector } from 'use-context-selector';\n *\n * const firstName = useContextSelector(PersonContext, (state) => state.firstName);\n */\nexport function useContextSelector(context, selector) {\n    const contextValue = useContextOrig(context)[CONTEXT_VALUE];\n    if (typeof process === 'object' && process.env.NODE_ENV !== 'production') {\n        if (!contextValue) {\n            throw new Error('useContextSelector requires special context');\n        }\n    }\n    const { \n    /* \"v\"alue     */ v: { current: value }, \n    /* versio\"n\"   */ n: { current: version }, \n    /* \"l\"isteners */ l: listeners, } = contextValue;\n    const selected = selector(value);\n    const [state, dispatch] = useReducer((prev, action) => {\n        if (!action) {\n            // case for `dispatch()` below\n            return [value, selected];\n        }\n        if ('p' in action) {\n            throw action.p;\n        }\n        if (action.n === version) {\n            if (Object.is(prev[1], selected)) {\n                return prev; // bail out\n            }\n            return [value, selected];\n        }\n        try {\n            if ('v' in action) {\n                if (Object.is(prev[0], action.v)) {\n                    return prev; // do not update\n                }\n                const nextSelected = selector(action.v);\n                if (Object.is(prev[1], nextSelected)) {\n                    return prev; // do not update\n                }\n                return [action.v, nextSelected];\n            }\n        }\n        catch (_e) {\n            // ignored (stale props or some other reason)\n        }\n        return [...prev]; // schedule update\n    }, [value, selected]);\n    if (!Object.is(state[1], selected)) {\n        // schedule re-render\n        // this is safe because it's self contained\n        dispatch();\n    }\n    useIsomorphicLayoutEffect(() => {\n        listeners.add(dispatch);\n        return () => {\n            listeners.delete(dispatch);\n        };\n    }, [listeners]);\n    return state[1];\n}\n/**\n * This hook returns the entire context value.\n * Use this instead of React.useContext for consistent behavior.\n *\n * @example\n * import { useContext } from 'use-context-selector';\n *\n * const person = useContext(PersonContext);\n */\nexport function useContext(context) {\n    return useContextSelector(context, identity);\n}\n/**\n * This hook returns an update function to wrap an updating function\n *\n * Use this for a function that will change a value in\n * concurrent rendering in React 18.\n * Otherwise, there's no need to use this hook.\n *\n * @example\n * import { useContextUpdate } from 'use-context-selector';\n *\n * const update = useContextUpdate();\n *\n * // Wrap set state function\n * update(() => setState(...));\n *\n * // Experimental suspense mode\n * update(() => setState(...), { suspense: true });\n */\nexport function useContextUpdate(context) {\n    const contextValue = useContextOrig(context)[CONTEXT_VALUE];\n    if (typeof process === 'object' && process.env.NODE_ENV !== 'production') {\n        if (!contextValue) {\n            throw new Error('useContextUpdate requires special context');\n        }\n    }\n    const { u: update } = contextValue;\n    return update;\n}\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * This is a Provider component for bridging multiple react roots\n *\n * @example\n * const valueToBridge = useBridgeValue(PersonContext);\n * return (\n *   <Renderer>\n *     <BridgeProvider context={PersonContext} value={valueToBridge}>\n *       {children}\n *     </BridgeProvider>\n *   </Renderer>\n * );\n */\nexport const BridgeProvider = ({ context, value, children, }) => {\n    const { [ORIGINAL_PROVIDER]: ProviderOrig } = context;\n    if (typeof process === 'object' && process.env.NODE_ENV !== 'production') {\n        if (!ProviderOrig) {\n            throw new Error('BridgeProvider requires special context');\n        }\n    }\n    return createElement(ProviderOrig, { value }, children);\n};\n/**\n * This hook return a value for BridgeProvider\n */\nexport const useBridgeValue = (context) => {\n    const bridgeValue = useContextOrig(context);\n    if (typeof process === 'object' && process.env.NODE_ENV !== 'production') {\n        if (!bridgeValue[CONTEXT_VALUE]) {\n            throw new Error('useBridgeValue requires special context');\n        }\n    }\n    return bridgeValue;\n};\n", "import { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';\nimport { createProxy, isChanged } from 'proxy-compare';\nimport { useAffectedDebugValue } from './utils.js';\nconst hasGlobalProcess = typeof process === 'object';\nexport const createTrackedSelector = (useSelector) => {\n    const useTrackedSelector = () => {\n        const [, forceUpdate] = useReducer((c) => c + 1, 0);\n        // per-hook affected, it's not ideal but memo compatible\n        const affected = useMemo(() => new WeakMap(), []);\n        const prevState = useRef();\n        const lastState = useRef();\n        useEffect(() => {\n            if (prevState.current !== lastState.current &&\n                isChanged(prevState.current, lastState.current, affected, new WeakMap())) {\n                prevState.current = lastState.current;\n                forceUpdate();\n            }\n        });\n        const selector = useCallback((nextState) => {\n            lastState.current = nextState;\n            if (prevState.current &&\n                prevState.current !== nextState &&\n                !isChanged(prevState.current, nextState, affected, new WeakMap())) {\n                // not changed\n                return prevState.current;\n            }\n            prevState.current = nextState;\n            return nextState;\n        }, [affected]);\n        const state = useSelector(selector);\n        if (hasGlobalProcess && process.env.NODE_ENV !== 'production') {\n            // eslint-disable-next-line react-hooks/rules-of-hooks\n            useAffectedDebugValue(state, affected);\n        }\n        const proxyCache = useMemo(() => new WeakMap(), []); // per-hook proxyCache\n        return createProxy(state, affected, proxyCache);\n    };\n    return useTrackedSelector;\n};\n", "/* eslint @typescript-eslint/no-explicit-any: off */\n// symbols\nconst TRACK_MEMO_SYMBOL = Symbol();\nconst GET_ORIGINAL_SYMBOL = Symbol();\n// properties\nconst AFFECTED_PROPERTY = 'a';\nconst IS_TARGET_COPIED_PROPERTY = 'f';\nconst PROXY_PROPERTY = 'p';\nconst PROXY_CACHE_PROPERTY = 'c';\nconst TARGET_CACHE_PROPERTY = 't';\nconst HAS_KEY_PROPERTY = 'h';\nconst ALL_OWN_KEYS_PROPERTY = 'w';\nconst HAS_OWN_KEY_PROPERTY = 'o';\nconst KEYS_PROPERTY = 'k';\n// function to create a new bare proxy\nlet newProxy = (target, handler) => new Proxy(target, handler);\n// get object prototype\nconst getProto = Object.getPrototypeOf;\nconst objectsToTrack = new WeakMap();\n// check if obj is a plain object or an array\nconst isObjectToTrack = (obj) => obj &&\n    (objectsToTrack.has(obj)\n        ? objectsToTrack.get(obj)\n        : getProto(obj) === Object.prototype || getProto(obj) === Array.prototype);\n// check if it is object\nconst isObject = (x) => typeof x === 'object' && x !== null;\n// Properties that are both non-configurable and non-writable will break\n// the proxy get trap when we try to return a recursive/child compare proxy\n// from them. We can avoid this by making a copy of the target object with\n// all descriptors marked as configurable, see `copyTargetObject`.\n// See: https://github.com/dai-shi/proxy-compare/pull/8\nconst needsToCopyTargetObject = (obj) => Object.values(Object.getOwnPropertyDescriptors(obj)).some((descriptor) => !descriptor.configurable && !descriptor.writable);\n// Make a copy with all descriptors marked as configurable.\nconst copyTargetObject = (obj) => {\n    if (Array.isArray(obj)) {\n        // Arrays need a special way to copy\n        return Array.from(obj);\n    }\n    // For non-array objects, we create a new object keeping the prototype\n    // with changing all configurable options (otherwise, proxies will complain)\n    const descriptors = Object.getOwnPropertyDescriptors(obj);\n    Object.values(descriptors).forEach((desc) => {\n        desc.configurable = true;\n    });\n    return Object.create(getProto(obj), descriptors);\n};\nconst createProxyHandler = (origObj, isTargetCopied) => {\n    const state = {\n        [IS_TARGET_COPIED_PROPERTY]: isTargetCopied,\n    };\n    let trackObject = false; // for trackMemo\n    const recordUsage = (type, key) => {\n        if (!trackObject) {\n            let used = state[AFFECTED_PROPERTY].get(origObj);\n            if (!used) {\n                used = {};\n                state[AFFECTED_PROPERTY].set(origObj, used);\n            }\n            if (type === ALL_OWN_KEYS_PROPERTY) {\n                used[ALL_OWN_KEYS_PROPERTY] = true;\n            }\n            else {\n                let set = used[type];\n                if (!set) {\n                    set = new Set();\n                    used[type] = set;\n                }\n                set.add(key);\n            }\n        }\n    };\n    const recordObjectAsUsed = () => {\n        trackObject = true;\n        state[AFFECTED_PROPERTY].delete(origObj);\n    };\n    const handler = {\n        get(target, key) {\n            if (key === GET_ORIGINAL_SYMBOL) {\n                return origObj;\n            }\n            recordUsage(KEYS_PROPERTY, key);\n            return createProxy(Reflect.get(target, key), state[AFFECTED_PROPERTY], state[PROXY_CACHE_PROPERTY], state[TARGET_CACHE_PROPERTY]);\n        },\n        has(target, key) {\n            if (key === TRACK_MEMO_SYMBOL) {\n                recordObjectAsUsed();\n                return true;\n            }\n            recordUsage(HAS_KEY_PROPERTY, key);\n            return Reflect.has(target, key);\n        },\n        getOwnPropertyDescriptor(target, key) {\n            recordUsage(HAS_OWN_KEY_PROPERTY, key);\n            return Reflect.getOwnPropertyDescriptor(target, key);\n        },\n        ownKeys(target) {\n            recordUsage(ALL_OWN_KEYS_PROPERTY);\n            return Reflect.ownKeys(target);\n        },\n    };\n    if (isTargetCopied) {\n        handler.set = handler.deleteProperty = () => false;\n    }\n    return [handler, state];\n};\nconst getOriginalObject = (obj) => \n// unwrap proxy\nobj[GET_ORIGINAL_SYMBOL] ||\n    // otherwise\n    obj;\n/**\n * Create a proxy.\n *\n * This function will create a proxy at top level and proxy nested objects as you access them,\n * in order to keep track of which properties were accessed via get/has proxy handlers:\n *\n * NOTE: Printing of WeakMap is hard to inspect and not very readable\n * for this purpose you can use the `affectedToPathList` helper.\n *\n * @param {object} obj - Object that will be wrapped on the proxy.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that will hold the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [proxyCache] -\n * WeakMap that will help keep referential identity for proxies.\n * @returns {Proxy<object>} - Object wrapped in a proxy.\n *\n * @example\n * import { createProxy } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n * const proxy = createProxy(original, affected);\n *\n * proxy.a // Will mark as used and track its value.\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"a\"\n *\n * proxy.d // Will mark \"d\" as accessed to track and proxy itself ({ e: \"3\" }).\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"d\"\n */\nexport const createProxy = (obj, affected, proxyCache, targetCache) => {\n    if (!isObjectToTrack(obj))\n        return obj;\n    let targetAndCopied = targetCache && targetCache.get(obj);\n    if (!targetAndCopied) {\n        const target = getOriginalObject(obj);\n        if (needsToCopyTargetObject(target)) {\n            targetAndCopied = [target, copyTargetObject(target)];\n        }\n        else {\n            targetAndCopied = [target];\n        }\n        targetCache === null || targetCache === void 0 ? void 0 : targetCache.set(obj, targetAndCopied);\n    }\n    const [target, copiedTarget] = targetAndCopied;\n    let handlerAndState = proxyCache && proxyCache.get(target);\n    if (!handlerAndState ||\n        handlerAndState[1][IS_TARGET_COPIED_PROPERTY] !== !!copiedTarget) {\n        handlerAndState = createProxyHandler(target, !!copiedTarget);\n        handlerAndState[1][PROXY_PROPERTY] = newProxy(copiedTarget || target, handlerAndState[0]);\n        if (proxyCache) {\n            proxyCache.set(target, handlerAndState);\n        }\n    }\n    handlerAndState[1][AFFECTED_PROPERTY] = affected;\n    handlerAndState[1][PROXY_CACHE_PROPERTY] = proxyCache;\n    handlerAndState[1][TARGET_CACHE_PROPERTY] = targetCache;\n    return handlerAndState[1][PROXY_PROPERTY];\n};\nconst isAllOwnKeysChanged = (prevObj, nextObj) => {\n    const prevKeys = Reflect.ownKeys(prevObj);\n    const nextKeys = Reflect.ownKeys(nextObj);\n    return (prevKeys.length !== nextKeys.length ||\n        prevKeys.some((k, i) => k !== nextKeys[i]));\n};\n/**\n * Compare changes on objects.\n *\n * This will compare the affected properties on tracked objects inside the proxy\n * to check if there were any changes made to it,\n * by default if no property was accessed on the proxy it will attempt to do a\n * reference equality check for the objects provided (Object.is(a, b)). If you access a property\n * on the proxy, then isChanged will only compare the affected properties.\n *\n * @param {object} prevObj - The previous object to compare.\n * @param {object} nextObj - Object to compare with the previous one.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that holds the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [cache] -\n * WeakMap that holds a cache of the comparisons for better performance with repetitive comparisons,\n * and to avoid infinite loop with circular structures.\n * @returns {boolean} - Boolean indicating if the affected property on the object has changed.\n *\n * @example\n * import { createProxy, isChanged } from 'proxy-compare';\n *\n * const obj = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(obj, affected);\n *\n * proxy.a\n *\n * isChanged(obj, { a: \"1\" }, affected) // false\n *\n * proxy.a = \"2\"\n *\n * isChanged(obj, { a: \"1\" }, affected) // true\n */\nexport const isChanged = (prevObj, nextObj, affected, cache, // for object with cycles\nisEqual = Object.is) => {\n    if (isEqual(prevObj, nextObj)) {\n        return false;\n    }\n    if (!isObject(prevObj) || !isObject(nextObj))\n        return true;\n    const used = affected.get(getOriginalObject(prevObj));\n    if (!used)\n        return true;\n    if (cache) {\n        const hit = cache.get(prevObj);\n        if (hit === nextObj) {\n            return false;\n        }\n        // for object with cycles\n        cache.set(prevObj, nextObj);\n    }\n    let changed = null;\n    for (const key of used[HAS_KEY_PROPERTY] || []) {\n        changed = Reflect.has(prevObj, key) !== Reflect.has(nextObj, key);\n        if (changed)\n            return changed;\n    }\n    if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n        changed = isAllOwnKeysChanged(prevObj, nextObj);\n        if (changed)\n            return changed;\n    }\n    else {\n        for (const key of used[HAS_OWN_KEY_PROPERTY] || []) {\n            const hasPrev = !!Reflect.getOwnPropertyDescriptor(prevObj, key);\n            const hasNext = !!Reflect.getOwnPropertyDescriptor(nextObj, key);\n            changed = hasPrev !== hasNext;\n            if (changed)\n                return changed;\n        }\n    }\n    for (const key of used[KEYS_PROPERTY] || []) {\n        changed = isChanged(prevObj[key], nextObj[key], affected, cache, isEqual);\n        if (changed)\n            return changed;\n    }\n    if (changed === null)\n        throw new Error('invalid used');\n    return changed;\n};\n// explicitly track object with memo\nexport const trackMemo = (obj) => {\n    if (isObjectToTrack(obj)) {\n        return TRACK_MEMO_SYMBOL in obj;\n    }\n    return false;\n};\n/**\n * Unwrap proxy to get the original object.\n *\n * Used to retrieve the original object used to create the proxy instance with `createProxy`.\n *\n * @param {Proxy<object>} obj -  The proxy wrapper of the originial object.\n * @returns {object | null} - Return either the unwrapped object if exists.\n *\n * @example\n * import { createProxy, getUntracked } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n * const originalFromProxy = getUntracked(proxy)\n *\n * Object.is(original, originalFromProxy) // true\n * isChanged(original, originalFromProxy, affected) // false\n */\nexport const getUntracked = (obj) => {\n    if (isObjectToTrack(obj)) {\n        return obj[GET_ORIGINAL_SYMBOL] || null;\n    }\n    return null;\n};\n/**\n * Mark object to be tracked.\n *\n * This function marks an object that will be passed into `createProxy`\n * as marked to track or not. By default only Array and Object are marked to track,\n * so this is useful for example to mark a class instance to track or to mark a object\n * to be untracked when creating your proxy.\n *\n * @param obj - Object to mark as tracked or not.\n * @param mark - Boolean indicating whether you want to track this object or not.\n * @returns - No return.\n *\n * @example\n * import { createProxy, markToTrack, isChanged } from 'proxy-compare';\n *\n * const nested = { e: \"3\" }\n *\n * markToTrack(nested, false)\n *\n * const original = { a: \"1\", c: \"2\", d: nested };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n *\n * proxy.d.e\n *\n * isChanged(original, { d: { e: \"3\" } }, affected) // true\n */\nexport const markToTrack = (obj, mark = true) => {\n    objectsToTrack.set(obj, mark);\n};\n/**\n * Convert `affected` to path list\n *\n * `affected` is a weak map which is not printable.\n * This function is can convert it to printable path list.\n * It's for debugging purpose.\n *\n * @param obj - An object that is used with `createProxy`.\n * @param affected - A weak map that is used with `createProxy`.\n * @param onlyWithValues - An optional boolean to exclude object getters.\n * @returns - An array of paths.\n */\nexport const affectedToPathList = (obj, affected, onlyWithValues) => {\n    const list = [];\n    const seen = new WeakSet();\n    const walk = (x, path) => {\n        var _a, _b, _c;\n        if (seen.has(x)) {\n            // for object with cycles\n            return;\n        }\n        if (isObject(x)) {\n            seen.add(x);\n        }\n        const used = isObject(x) && affected.get(getOriginalObject(x));\n        if (used) {\n            (_a = used[HAS_KEY_PROPERTY]) === null || _a === void 0 ? void 0 : _a.forEach((key) => {\n                const segment = `:has(${String(key)})`;\n                list.push(path ? [...path, segment] : [segment]);\n            });\n            if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n                const segment = ':ownKeys';\n                list.push(path ? [...path, segment] : [segment]);\n            }\n            else {\n                (_b = used[HAS_OWN_KEY_PROPERTY]) === null || _b === void 0 ? void 0 : _b.forEach((key) => {\n                    const segment = `:hasOwn(${String(key)})`;\n                    list.push(path ? [...path, segment] : [segment]);\n                });\n            }\n            (_c = used[KEYS_PROPERTY]) === null || _c === void 0 ? void 0 : _c.forEach((key) => {\n                if (!onlyWithValues ||\n                    'value' in (Object.getOwnPropertyDescriptor(x, key) || {})) {\n                    walk(x[key], path ? [...path, key] : [key]);\n                }\n            });\n        }\n        else if (path) {\n            list.push(path);\n        }\n    };\n    walk(obj);\n    return list;\n};\n/**\n * replace newProxy function.\n *\n * This can be used if you want to use proxy-polyfill.\n * Note that proxy-polyfill can't polyfill everything.\n * Use it at your own risk.\n */\nexport const replaceNewProxy = (fn) => {\n    newProxy = fn;\n};\n", "import { useEffect, useRef, useDebugValue } from 'react';\nimport { affectedToPathList } from 'proxy-compare';\nexport const useAffectedDebugValue = (state, affected) => {\n    const pathList = useRef();\n    useEffect(() => {\n        pathList.current = affectedToPathList(state, affected);\n    });\n    useDebugValue(state);\n};\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,gBAA6H;;;ACA7H,mBAA2J;AAC3J,uBAAwG;AACxG,IAAM,gBAAgB,OAAO;AAC7B,IAAM,oBAAoB,OAAO;AACjC,IAAM,QAAQ,OAAO,WAAW,eAC5B,sBAAsB,KAAK,OAAO,aAAa,OAAO,UAAU,SAAS;AAC7E,IAAM,4BAA4B,QAAQ,yBAAY;AAEtD,IAAM,wBAAwB,iBAAAC,2BACxB,CAAC,OAAO;AACN,MAAI;AACA,yBAAAA,0BAAgB,iBAAAC,yBAAgB,EAAE;AAAA,EACtC,SACO,GAAG;AACN,QAAI,EAAE,YAAY,oBAAoB;AAClC,SAAG;AAAA,IACP,OACK;AACD,YAAM;AAAA,IACV;AAAA,EACJ;AACJ,IACE,CAAC,OAAO,GAAG;AACjB,IAAM,iBAAiB,CAAC,iBAAiB;AACrC,QAAM,kBAAkB,CAAC,EAAE,OAAO,SAAU,MAAM;AAC9C,UAAM,eAAW,qBAAO,KAAK;AAC7B,UAAM,iBAAa,qBAAO,CAAC;AAC3B,UAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,IAAI;AAC3C,QAAI,SAAS;AACT,cAAQ,KAAK;AACb,iBAAW,IAAI;AAAA,IACnB;AACA,UAAM,mBAAe,qBAAO;AAC5B,QAAI,CAAC,aAAa,SAAS;AACvB,YAAM,YAAY,oBAAI,IAAI;AAC1B,YAAM,SAAS,CAAC,IAAI,YAAY;AAC5B,mBAAW,WAAW;AACtB,cAAM,SAAS;AAAA,UACX,GAAG,WAAW;AAAA,QAClB;AACA,YAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU;AACpE,iBAAO,KAAK;AACZ,iBAAO,IAAI,IAAI,QAAQ,CAAC,MAAM;AAC1B,uBAAW,MAAM,CAAC,MAAM;AACpB,qBAAO,IAAI;AACX,qBAAO,OAAO;AACd,gBAAE,CAAC;AAAA,YACP,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,kBAAU,QAAQ,CAAC,aAAa,SAAS,MAAM,CAAC;AAChD,WAAG;AAAA,MACP;AACA,mBAAa,UAAU;AAAA,QACnB,CAAC,aAAa,GAAG;AAAA;AAAA,UACK,GAAG;AAAA;AAAA,UACH,GAAG;AAAA;AAAA,UACH,GAAG;AAAA;AAAA,UACH,GAAG;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AACA,8BAA0B,MAAM;AAC5B,eAAS,UAAU;AACnB,iBAAW,WAAW;AACtB,4BAAsB,MAAM;AACxB,qBAAa,QAAQ,aAAa,EAAE,EAAE,QAAQ,CAAC,aAAa;AACxD,mBAAS,EAAE,GAAG,WAAW,SAAS,GAAG,MAAM,CAAC;AAAA,QAChD,CAAC;AAAA,MACL,CAAC;AAAA,IACL,GAAG,CAAC,KAAK,CAAC;AACV,eAAO,4BAAc,cAAc,EAAE,OAAO,aAAa,QAAQ,GAAG,QAAQ;AAAA,EAChF;AACA,SAAO;AACX;AAUO,SAAS,cAAc,cAAc;AACxC,QAAM,cAAU,aAAAC,eAAkB;AAAA,IAC9B,CAAC,aAAa,GAAG;AAAA;AAAA,MACK,GAAG,EAAE,SAAS,aAAa;AAAA;AAAA,MAC3B,GAAG,EAAE,SAAS,GAAG;AAAA;AAAA,MACjB,GAAG,oBAAI,IAAI;AAAA;AAAA,MACX,GAAG,CAAC,MAAM,EAAE;AAAA,IAClC;AAAA,EACJ,CAAC;AACD,UAAQ,iBAAiB,IAAI,QAAQ;AACrC,UAAQ,WAAW,eAAe,QAAQ,QAAQ;AAClD,SAAO,QAAQ;AACf,SAAO;AACX;AAcO,SAAS,mBAAmB,SAAS,UAAU;AAClD,QAAM,mBAAe,aAAAC,YAAe,OAAO,EAAE,aAAa;AAC1D,MAAI,OAAO,YAAY,YAAY,MAAuC;AACtE,QAAI,CAAC,cAAc;AACf,YAAM,IAAI,MAAM,6CAA6C;AAAA,IACjE;AAAA,EACJ;AACA,QAAM;AAAA;AAAA,IACY,GAAG,EAAE,SAAS,MAAM;AAAA;AAAA,IACpB,GAAG,EAAE,SAAS,QAAQ;AAAA;AAAA,IACtB,GAAG;AAAA,EAAW,IAAI;AACpC,QAAM,WAAW,SAAS,KAAK;AAC/B,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAW,CAAC,MAAM,WAAW;AACnD,QAAI,CAAC,QAAQ;AAET,aAAO,CAAC,OAAO,QAAQ;AAAA,IAC3B;AACA,QAAI,OAAO,QAAQ;AACf,YAAM,OAAO;AAAA,IACjB;AACA,QAAI,OAAO,MAAM,SAAS;AACtB,UAAI,OAAO,GAAG,KAAK,CAAC,GAAG,QAAQ,GAAG;AAC9B,eAAO;AAAA,MACX;AACA,aAAO,CAAC,OAAO,QAAQ;AAAA,IAC3B;AACA,QAAI;AACA,UAAI,OAAO,QAAQ;AACf,YAAI,OAAO,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG;AAC9B,iBAAO;AAAA,QACX;AACA,cAAM,eAAe,SAAS,OAAO,CAAC;AACtC,YAAI,OAAO,GAAG,KAAK,CAAC,GAAG,YAAY,GAAG;AAClC,iBAAO;AAAA,QACX;AACA,eAAO,CAAC,OAAO,GAAG,YAAY;AAAA,MAClC;AAAA,IACJ,SACO,IAAI;AAAA,IAEX;AACA,WAAO,CAAC,GAAG,IAAI;AAAA,EACnB,GAAG,CAAC,OAAO,QAAQ,CAAC;AACpB,MAAI,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,QAAQ,GAAG;AAGhC,aAAS;AAAA,EACb;AACA,4BAA0B,MAAM;AAC5B,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM;AACT,gBAAU,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACJ,GAAG,CAAC,SAAS,CAAC;AACd,SAAO,MAAM,CAAC;AAClB;AA+BO,SAAS,iBAAiB,SAAS;AACtC,QAAM,mBAAe,aAAAC,YAAe,OAAO,EAAE,aAAa;AAC1D,MAAI,OAAO,YAAY,YAAY,MAAuC;AACtE,QAAI,CAAC,cAAc;AACf,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC/D;AAAA,EACJ;AACA,QAAM,EAAE,GAAG,OAAO,IAAI;AACtB,SAAO;AACX;;;AC9MA,IAAAC,gBAAoE;;;ACEpE,IAAM,oBAAoB,OAAO;AACjC,IAAM,sBAAsB,OAAO;AAEnC,IAAM,oBAAoB;AAC1B,IAAM,4BAA4B;AAClC,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,wBAAwB;AAC9B,IAAM,mBAAmB;AACzB,IAAM,wBAAwB;AAC9B,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AAEtB,IAAI,WAAW,CAAC,QAAQ,YAAY,IAAI,MAAM,QAAQ,OAAO;AAE7D,IAAM,WAAW,OAAO;AACxB,IAAM,iBAAiB,oBAAI,QAAQ;AAEnC,IAAM,kBAAkB,CAAC,QAAQ,QAC5B,eAAe,IAAI,GAAG,IACjB,eAAe,IAAI,GAAG,IACtB,SAAS,GAAG,MAAM,OAAO,aAAa,SAAS,GAAG,MAAM,MAAM;AAExE,IAAM,WAAW,CAAC,MAAM,OAAO,MAAM,YAAY,MAAM;AAMvD,IAAM,0BAA0B,CAAC,QAAQ,OAAO,OAAO,OAAO,0BAA0B,GAAG,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,WAAW,gBAAgB,CAAC,WAAW,QAAQ;AAEnK,IAAM,mBAAmB,CAAC,QAAQ;AAC9B,MAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,WAAO,MAAM,KAAK,GAAG;AAAA,EACzB;AAGA,QAAM,cAAc,OAAO,0BAA0B,GAAG;AACxD,SAAO,OAAO,WAAW,EAAE,QAAQ,CAAC,SAAS;AACzC,SAAK,eAAe;AAAA,EACxB,CAAC;AACD,SAAO,OAAO,OAAO,SAAS,GAAG,GAAG,WAAW;AACnD;AACA,IAAM,qBAAqB,CAAC,SAAS,mBAAmB;AACpD,QAAM,QAAQ;AAAA,IACV,CAAC,yBAAyB,GAAG;AAAA,EACjC;AACA,MAAI,cAAc;AAClB,QAAM,cAAc,CAAC,MAAM,QAAQ;AAC/B,QAAI,CAAC,aAAa;AACd,UAAI,OAAO,MAAM,iBAAiB,EAAE,IAAI,OAAO;AAC/C,UAAI,CAAC,MAAM;AACP,eAAO,CAAC;AACR,cAAM,iBAAiB,EAAE,IAAI,SAAS,IAAI;AAAA,MAC9C;AACA,UAAI,SAAS,uBAAuB;AAChC,aAAK,qBAAqB,IAAI;AAAA,MAClC,OACK;AACD,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,CAAC,KAAK;AACN,gBAAM,oBAAI,IAAI;AACd,eAAK,IAAI,IAAI;AAAA,QACjB;AACA,YAAI,IAAI,GAAG;AAAA,MACf;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,qBAAqB,MAAM;AAC7B,kBAAc;AACd,UAAM,iBAAiB,EAAE,OAAO,OAAO;AAAA,EAC3C;AACA,QAAM,UAAU;AAAA,IACZ,IAAI,QAAQ,KAAK;AACb,UAAI,QAAQ,qBAAqB;AAC7B,eAAO;AAAA,MACX;AACA,kBAAY,eAAe,GAAG;AAC9B,aAAO,YAAY,QAAQ,IAAI,QAAQ,GAAG,GAAG,MAAM,iBAAiB,GAAG,MAAM,oBAAoB,GAAG,MAAM,qBAAqB,CAAC;AAAA,IACpI;AAAA,IACA,IAAI,QAAQ,KAAK;AACb,UAAI,QAAQ,mBAAmB;AAC3B,2BAAmB;AACnB,eAAO;AAAA,MACX;AACA,kBAAY,kBAAkB,GAAG;AACjC,aAAO,QAAQ,IAAI,QAAQ,GAAG;AAAA,IAClC;AAAA,IACA,yBAAyB,QAAQ,KAAK;AAClC,kBAAY,sBAAsB,GAAG;AACrC,aAAO,QAAQ,yBAAyB,QAAQ,GAAG;AAAA,IACvD;AAAA,IACA,QAAQ,QAAQ;AACZ,kBAAY,qBAAqB;AACjC,aAAO,QAAQ,QAAQ,MAAM;AAAA,IACjC;AAAA,EACJ;AACA,MAAI,gBAAgB;AAChB,YAAQ,MAAM,QAAQ,iBAAiB,MAAM;AAAA,EACjD;AACA,SAAO,CAAC,SAAS,KAAK;AAC1B;AACA,IAAM,oBAAoB,CAAC;AAAA;AAAA,EAE3B,IAAI,mBAAmB;AAAA,EAEnB;AAAA;AAgCG,IAAM,cAAc,CAAC,KAAK,UAAU,YAAY,gBAAgB;AACnE,MAAI,CAAC,gBAAgB,GAAG;AACpB,WAAO;AACX,MAAI,kBAAkB,eAAe,YAAY,IAAI,GAAG;AACxD,MAAI,CAAC,iBAAiB;AAClB,UAAMC,UAAS,kBAAkB,GAAG;AACpC,QAAI,wBAAwBA,OAAM,GAAG;AACjC,wBAAkB,CAACA,SAAQ,iBAAiBA,OAAM,CAAC;AAAA,IACvD,OACK;AACD,wBAAkB,CAACA,OAAM;AAAA,IAC7B;AACA,oBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,IAAI,KAAK,eAAe;AAAA,EAClG;AACA,QAAM,CAAC,QAAQ,YAAY,IAAI;AAC/B,MAAI,kBAAkB,cAAc,WAAW,IAAI,MAAM;AACzD,MAAI,CAAC,mBACD,gBAAgB,CAAC,EAAE,yBAAyB,MAAM,CAAC,CAAC,cAAc;AAClE,sBAAkB,mBAAmB,QAAQ,CAAC,CAAC,YAAY;AAC3D,oBAAgB,CAAC,EAAE,cAAc,IAAI,SAAS,gBAAgB,QAAQ,gBAAgB,CAAC,CAAC;AACxF,QAAI,YAAY;AACZ,iBAAW,IAAI,QAAQ,eAAe;AAAA,IAC1C;AAAA,EACJ;AACA,kBAAgB,CAAC,EAAE,iBAAiB,IAAI;AACxC,kBAAgB,CAAC,EAAE,oBAAoB,IAAI;AAC3C,kBAAgB,CAAC,EAAE,qBAAqB,IAAI;AAC5C,SAAO,gBAAgB,CAAC,EAAE,cAAc;AAC5C;AACA,IAAM,sBAAsB,CAAC,SAAS,YAAY;AAC9C,QAAM,WAAW,QAAQ,QAAQ,OAAO;AACxC,QAAM,WAAW,QAAQ,QAAQ,OAAO;AACxC,SAAQ,SAAS,WAAW,SAAS,UACjC,SAAS,KAAK,CAAC,GAAG,MAAM,MAAM,SAAS,CAAC,CAAC;AACjD;AAmCO,IAAM,YAAY,CAAC,SAAS,SAAS,UAAU,OACtD,UAAU,OAAO,OAAO;AACpB,MAAI,QAAQ,SAAS,OAAO,GAAG;AAC3B,WAAO;AAAA,EACX;AACA,MAAI,CAAC,SAAS,OAAO,KAAK,CAAC,SAAS,OAAO;AACvC,WAAO;AACX,QAAM,OAAO,SAAS,IAAI,kBAAkB,OAAO,CAAC;AACpD,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO;AACP,UAAM,MAAM,MAAM,IAAI,OAAO;AAC7B,QAAI,QAAQ,SAAS;AACjB,aAAO;AAAA,IACX;AAEA,UAAM,IAAI,SAAS,OAAO;AAAA,EAC9B;AACA,MAAI,UAAU;AACd,aAAW,OAAO,KAAK,gBAAgB,KAAK,CAAC,GAAG;AAC5C,cAAU,QAAQ,IAAI,SAAS,GAAG,MAAM,QAAQ,IAAI,SAAS,GAAG;AAChE,QAAI;AACA,aAAO;AAAA,EACf;AACA,MAAI,KAAK,qBAAqB,MAAM,MAAM;AACtC,cAAU,oBAAoB,SAAS,OAAO;AAC9C,QAAI;AACA,aAAO;AAAA,EACf,OACK;AACD,eAAW,OAAO,KAAK,oBAAoB,KAAK,CAAC,GAAG;AAChD,YAAM,UAAU,CAAC,CAAC,QAAQ,yBAAyB,SAAS,GAAG;AAC/D,YAAM,UAAU,CAAC,CAAC,QAAQ,yBAAyB,SAAS,GAAG;AAC/D,gBAAU,YAAY;AACtB,UAAI;AACA,eAAO;AAAA,IACf;AAAA,EACJ;AACA,aAAW,OAAO,KAAK,aAAa,KAAK,CAAC,GAAG;AACzC,cAAU,UAAU,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,UAAU,OAAO,OAAO;AACxE,QAAI;AACA,aAAO;AAAA,EACf;AACA,MAAI,YAAY;AACZ,UAAM,IAAI,MAAM,cAAc;AAClC,SAAO;AACX;AA4BO,IAAM,eAAe,CAAC,QAAQ;AACjC,MAAI,gBAAgB,GAAG,GAAG;AACtB,WAAO,IAAI,mBAAmB,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AA4CO,IAAM,qBAAqB,CAAC,KAAK,UAAU,mBAAmB;AACjE,QAAM,OAAO,CAAC;AACd,QAAM,OAAO,oBAAI,QAAQ;AACzB,QAAM,OAAO,CAAC,GAAG,SAAS;AACtB,QAAI,IAAI,IAAI;AACZ,QAAI,KAAK,IAAI,CAAC,GAAG;AAEb;AAAA,IACJ;AACA,QAAI,SAAS,CAAC,GAAG;AACb,WAAK,IAAI,CAAC;AAAA,IACd;AACA,UAAM,OAAO,SAAS,CAAC,KAAK,SAAS,IAAI,kBAAkB,CAAC,CAAC;AAC7D,QAAI,MAAM;AACN,OAAC,KAAK,KAAK,gBAAgB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,QAAQ;AACnF,cAAM,UAAU,QAAQ,OAAO,GAAG,CAAC;AACnC,aAAK,KAAK,OAAO,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC;AAAA,MACnD,CAAC;AACD,UAAI,KAAK,qBAAqB,MAAM,MAAM;AACtC,cAAM,UAAU;AAChB,aAAK,KAAK,OAAO,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC;AAAA,MACnD,OACK;AACD,SAAC,KAAK,KAAK,oBAAoB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,QAAQ;AACvF,gBAAM,UAAU,WAAW,OAAO,GAAG,CAAC;AACtC,eAAK,KAAK,OAAO,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC;AAAA,QACnD,CAAC;AAAA,MACL;AACA,OAAC,KAAK,KAAK,aAAa,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,QAAQ;AAChF,YAAI,CAAC,kBACD,YAAY,OAAO,yBAAyB,GAAG,GAAG,KAAK,CAAC,IAAI;AAC5D,eAAK,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;AAAA,QAC9C;AAAA,MACJ,CAAC;AAAA,IACL,WACS,MAAM;AACX,WAAK,KAAK,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,OAAK,GAAG;AACR,SAAO;AACX;;;ACtXA,IAAAC,gBAAiD;AAE1C,IAAM,wBAAwB,CAAC,OAAO,aAAa;AACtD,QAAM,eAAW,sBAAO;AACxB,+BAAU,MAAM;AACZ,aAAS,UAAU,mBAAmB,OAAO,QAAQ;AAAA,EACzD,CAAC;AACD,mCAAc,KAAK;AACvB;;;AFLA,IAAM,mBAAmB,OAAO,YAAY;AACrC,IAAM,wBAAwB,CAAC,gBAAgB;AAClD,QAAM,qBAAqB,MAAM;AAC7B,UAAM,CAAC,EAAE,WAAW,QAAI,0BAAW,CAAC,MAAM,IAAI,GAAG,CAAC;AAElD,UAAM,eAAW,uBAAQ,MAAM,oBAAI,QAAQ,GAAG,CAAC,CAAC;AAChD,UAAM,gBAAY,sBAAO;AACzB,UAAM,gBAAY,sBAAO;AACzB,iCAAU,MAAM;AACZ,UAAI,UAAU,YAAY,UAAU,WAChC,UAAU,UAAU,SAAS,UAAU,SAAS,UAAU,oBAAI,QAAQ,CAAC,GAAG;AAC1E,kBAAU,UAAU,UAAU;AAC9B,oBAAY;AAAA,MAChB;AAAA,IACJ,CAAC;AACD,UAAM,eAAW,2BAAY,CAAC,cAAc;AACxC,gBAAU,UAAU;AACpB,UAAI,UAAU,WACV,UAAU,YAAY,aACtB,CAAC,UAAU,UAAU,SAAS,WAAW,UAAU,oBAAI,QAAQ,CAAC,GAAG;AAEnE,eAAO,UAAU;AAAA,MACrB;AACA,gBAAU,UAAU;AACpB,aAAO;AAAA,IACX,GAAG,CAAC,QAAQ,CAAC;AACb,UAAM,QAAQ,YAAY,QAAQ;AAClC,QAAI,oBAAoB,MAAuC;AAE3D,4BAAsB,OAAO,QAAQ;AAAA,IACzC;AACA,UAAM,iBAAa,uBAAQ,MAAM,oBAAI,QAAQ,GAAG,CAAC,CAAC;AAClD,WAAO,YAAY,OAAO,UAAU,UAAU;AAAA,EAClD;AACA,SAAO;AACX;;;AFnCA,IAAMC,oBAAmB,OAAO,YAAY;AACrC,IAAM,kBAAkB,CAAC,UAAU,YAAY;AAClD,MAAI,OAAO,YAAY,WAAW;AAE9B,YAAQ,KAAK,uEAAuE;AACpF,cAAU,EAAE,gBAAgB,QAAQ;AAAA,EACxC;AACA,QAAM,EAAE,mBAAmB,kBAAkB,oBAAoB,mBAAmB,eAAgB,IAAI,WAAW,CAAC;AACpH,QAAM,eAAe,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY;AACzG,QAAM,oBAAgB,cAAAC,eAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa;AAC/G,eAAa,cAAc;AAC3B,gBAAc,cAAc;AAC5B,QAAM,WAAW,CAAC,UAAU;AACxB,UAAM,CAAC,OAAO,MAAM,IAAI,SAAS,KAAK;AACtC,eAAO,6BAAc,cAAc,UAAU,EAAE,OAAO,OAAO,OAAG,6BAAc,aAAa,UAAU,EAAE,OAAO,MAAM,GAAG,MAAM,QAAQ,CAAC;AAAA,EAC1I;AACA,QAAM,cAAc,CAAC,aAAa;AAC9B,QAAID,qBAAoB,MAAuC;AAC3D,YAAM,eAAe;AACrB,iBAAW,CAAC,UAAU;AAClB,YAAI,UAAU,QAAW;AACrB,gBAAM,IAAI,MAAM,uBAAuB;AAAA,QAC3C;AACA,eAAO,aAAa,KAAK;AAAA,MAC7B;AAAA,IACJ;AACA,UAAM,WAAW,mBAAmB,cAAc,QAAQ;AAC1D,qCAAc,QAAQ;AACtB,WAAO;AAAA,EACX;AACA,QAAM,kBAAkB,sBAAsB,WAAW;AACzD,QAAM,YAAY,iBACZ,MAAM;AACJ,QAAIA,qBACA,YACA,cAAAE,YAAe,aAAa,MAAM,QAAW;AAC7C,YAAM,IAAI,MAAM,uBAAuB;AAAA,IAC3C;AACA,UAAM,gBAAgB,iBAAiB,YAAY;AACnD,UAAM,aAAS,cAAAA,YAAe,aAAa;AAC3C,eAAO,2BAAY,IAAI,SAAS;AAC5B,UAAI;AACJ,oBAAc,MAAM;AAChB,iBAAS,OAAO,GAAG,IAAI;AAAA,MAC3B,CAAC;AACD,aAAO;AAAA,IACX,GAAG,CAAC,eAAe,MAAM,CAAC;AAAA,EAC9B;AAAA;AAAA,IAEI,MAAM;AACF,UAAI,OAAO,YAAY,YACnB,YACA,cAAAA,YAAe,aAAa,MAAM,QAAW;AAC7C,cAAM,IAAI,MAAM,uBAAuB;AAAA,MAC3C;AACA,iBAAO,cAAAA,YAAe,aAAa;AAAA,IACvC;AAAA;AACR,QAAM,aAAa,MAAM,CAAC,gBAAgB,GAAG,UAAU,CAAC;AACxD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;", "names": ["import_react", "runWithPriority", "NormalPriority", "createContextOrig", "useContextOrig", "useContextOrig", "import_react", "target", "import_react", "hasGlobalProcess", "createContextOrig", "useContextOrig"]}