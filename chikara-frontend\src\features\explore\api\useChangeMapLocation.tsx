import { APIROUTES } from "@/helpers/apiRoutes";

interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
}
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useChangeMapLocation = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.explore.changeMapLocation.mutationOptions({
            onSuccess: () => {
                // Invalidate and refetch the explore map to get updated location nodes
                queryClient.invalidateQueries({
                    queryKey: api.explore.map.key(),
                });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
        })
    );
};

export default useChangeMapLocation;

// useQuery example
// const useExploreMap = (options: QueryOptions = {}) => {
//     return useQuery(
//         api.explore.map.queryOptions({
//             ...options,
//         })
//     );
// };
