{"version": 3, "sources": ["../../../../node_modules/@formkit/auto-animate/react/index.mjs", "../../../../node_modules/@formkit/auto-animate/index.mjs"], "sourcesContent": ["import { useState, useMemo, useCallback } from 'react';\nimport autoAnimate from '../index.mjs';\n\n/**\n * AutoAnimate hook for adding dead-simple transitions and animations to react.\n * @param options - Auto animate options or a plugin\n * @returns\n */\nfunction useAutoAnimate(options) {\n    const [controller, setController] = useState();\n    const memoizedOptions = useMemo(() => options, []);\n    const element = useCallback((node) => {\n        if (node instanceof HTMLElement) {\n            setController(autoAnimate(node, memoizedOptions));\n        }\n        else {\n            setController(undefined);\n        }\n    }, [memoizedOptions]);\n    const setEnabled = useCallback((enabled) => {\n        if (controller) {\n            enabled ? controller.enable() : controller.disable();\n        }\n    }, [controller]);\n    return [element, setEnabled];\n}\n\nexport { useAutoAnimate };\n", "/**\n * A set of all the parents currently being observe. This is the only non weak\n * registry.\n */\nconst parents = new Set();\n/**\n * Element coordinates that is constantly kept up to date.\n */\nconst coords = new WeakMap();\n/**\n * Siblings of elements that have been removed from the dom.\n */\nconst siblings = new WeakMap();\n/**\n * Animations that are currently running.\n */\nconst animations = new WeakMap();\n/**\n * A map of existing intersection observers used to track element movements.\n */\nconst intersections = new WeakMap();\n/**\n * Intervals for automatically checking the position of elements occasionally.\n */\nconst intervals = new WeakMap();\n/**\n * The configuration options for each group of elements.\n */\nconst options = new WeakMap();\n/**\n * Debounce counters by id, used to debounce calls to update positions.\n */\nconst debounces = new WeakMap();\n/**\n * All parents that are currently enabled are tracked here.\n */\nconst enabled = new WeakSet();\n/**\n * The document used to calculate transitions.\n */\nlet root;\n/**\n * The root’s XY scroll positions.\n */\nlet scrollX = 0;\nlet scrollY = 0;\n/**\n * Used to sign an element as the target.\n */\nconst TGT = \"__aa_tgt\";\n/**\n * Used to sign an element as being part of a removal.\n */\nconst DEL = \"__aa_del\";\n/**\n * Used to sign an element as being \"new\". When an element is removed from the\n * dom, but may cycle back in we can sign it with new to ensure the next time\n * it is recognized we consider it new.\n */\nconst NEW = \"__aa_new\";\n/**\n * Callback for handling all mutations.\n * @param mutations - A mutation list\n */\nconst handleMutations = (mutations) => {\n    const elements = getElements(mutations);\n    // If elements is \"false\" that means this mutation that should be ignored.\n    if (elements) {\n        elements.forEach((el) => animate(el));\n    }\n};\n/**\n *\n * @param entries - Elements that have been resized.\n */\nconst handleResizes = (entries) => {\n    entries.forEach((entry) => {\n        if (entry.target === root)\n            updateAllPos();\n        if (coords.has(entry.target))\n            updatePos(entry.target);\n    });\n};\n/**\n * Observe this elements position.\n * @param el - The element to observe the position of.\n */\nfunction observePosition(el) {\n    const oldObserver = intersections.get(el);\n    oldObserver === null || oldObserver === void 0 ? void 0 : oldObserver.disconnect();\n    let rect = coords.get(el);\n    let invocations = 0;\n    const buffer = 5;\n    if (!rect) {\n        rect = getCoords(el);\n        coords.set(el, rect);\n    }\n    const { offsetWidth, offsetHeight } = root;\n    const rootMargins = [\n        rect.top - buffer,\n        offsetWidth - (rect.left + buffer + rect.width),\n        offsetHeight - (rect.top + buffer + rect.height),\n        rect.left - buffer,\n    ];\n    const rootMargin = rootMargins\n        .map((px) => `${-1 * Math.floor(px)}px`)\n        .join(\" \");\n    const observer = new IntersectionObserver(() => {\n        ++invocations > 1 && updatePos(el);\n    }, {\n        root,\n        threshold: 1,\n        rootMargin,\n    });\n    observer.observe(el);\n    intersections.set(el, observer);\n}\n/**\n * Update the exact position of a given element.\n * @param el - An element to update the position of.\n */\nfunction updatePos(el) {\n    clearTimeout(debounces.get(el));\n    const optionsOrPlugin = getOptions(el);\n    const delay = isPlugin(optionsOrPlugin) ? 500 : optionsOrPlugin.duration;\n    debounces.set(el, setTimeout(async () => {\n        const currentAnimation = animations.get(el);\n        try {\n            await (currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.finished);\n            coords.set(el, getCoords(el));\n            observePosition(el);\n        }\n        catch {\n            // ignore errors as the `.finished` promise is rejected when animations were cancelled\n        }\n    }, delay));\n}\n/**\n * Updates all positions that are currently being tracked.\n */\nfunction updateAllPos() {\n    clearTimeout(debounces.get(root));\n    debounces.set(root, setTimeout(() => {\n        parents.forEach((parent) => forEach(parent, (el) => lowPriority(() => updatePos(el))));\n    }, 100));\n}\n/**\n * Its possible for a quick scroll or other fast events to get past the\n * intersection observer, so occasionally we need want \"cold-poll\" for the\n * latests and greatest position. We try to do this in the most non-disruptive\n * fashion possible. First we only do this ever couple seconds, staggard by a\n * random offset.\n * @param el - Element\n */\nfunction poll(el) {\n    setTimeout(() => {\n        intervals.set(el, setInterval(() => lowPriority(updatePos.bind(null, el)), 2000));\n    }, Math.round(2000 * Math.random()));\n}\n/**\n * Perform some operation that is non critical at some point.\n * @param callback\n */\nfunction lowPriority(callback) {\n    if (typeof requestIdleCallback === \"function\") {\n        requestIdleCallback(() => callback());\n    }\n    else {\n        requestAnimationFrame(() => callback());\n    }\n}\n/**\n * The mutation observer responsible for watching each root element.\n */\nlet mutations;\n/**\n * A resize observer, responsible for recalculating elements on resize.\n */\nlet resize;\n/**\n * Ensure the browser is supported.\n */\nconst supportedBrowser = typeof window !== \"undefined\" && \"ResizeObserver\" in window;\n/**\n * If this is in a browser, initialize our Web APIs\n */\nif (supportedBrowser) {\n    root = document.documentElement;\n    mutations = new MutationObserver(handleMutations);\n    resize = new ResizeObserver(handleResizes);\n    window.addEventListener(\"scroll\", () => {\n        scrollY = window.scrollY;\n        scrollX = window.scrollX;\n    });\n    resize.observe(root);\n}\n/**\n * Retrieves all the elements that may have been affected by the last mutation\n * including ones that have been removed and are no longer in the DOM.\n * @param mutations - A mutation list.\n * @returns\n */\nfunction getElements(mutations) {\n    const observedNodes = mutations.reduce((nodes, mutation) => {\n        return [\n            ...nodes,\n            ...Array.from(mutation.addedNodes),\n            ...Array.from(mutation.removedNodes),\n        ];\n    }, []);\n    // Short circuit if _only_ comment nodes are observed\n    const onlyCommentNodesObserved = observedNodes.every((node) => node.nodeName === \"#comment\");\n    if (onlyCommentNodesObserved)\n        return false;\n    return mutations.reduce((elements, mutation) => {\n        // Short circuit if we find a purposefully deleted node.\n        if (elements === false)\n            return false;\n        if (mutation.target instanceof Element) {\n            target(mutation.target);\n            if (!elements.has(mutation.target)) {\n                elements.add(mutation.target);\n                for (let i = 0; i < mutation.target.children.length; i++) {\n                    const child = mutation.target.children.item(i);\n                    if (!child)\n                        continue;\n                    if (DEL in child) {\n                        return false;\n                    }\n                    target(mutation.target, child);\n                    elements.add(child);\n                }\n            }\n            if (mutation.removedNodes.length) {\n                for (let i = 0; i < mutation.removedNodes.length; i++) {\n                    const child = mutation.removedNodes[i];\n                    if (DEL in child) {\n                        return false;\n                    }\n                    if (child instanceof Element) {\n                        elements.add(child);\n                        target(mutation.target, child);\n                        siblings.set(child, [\n                            mutation.previousSibling,\n                            mutation.nextSibling,\n                        ]);\n                    }\n                }\n            }\n        }\n        return elements;\n    }, new Set());\n}\n/**\n * Assign the target to an element.\n * @param el - The root element\n * @param child\n */\nfunction target(el, child) {\n    if (!child && !(TGT in el))\n        Object.defineProperty(el, TGT, { value: el });\n    else if (child && !(TGT in child))\n        Object.defineProperty(child, TGT, { value: el });\n}\n/**\n * Determines what kind of change took place on the given element and then\n * performs the proper animation based on that.\n * @param el - The specific element to animate.\n */\nfunction animate(el) {\n    var _a;\n    const isMounted = el.isConnected;\n    const preExisting = coords.has(el);\n    if (isMounted && siblings.has(el))\n        siblings.delete(el);\n    if (animations.has(el)) {\n        (_a = animations.get(el)) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n    if (NEW in el) {\n        add(el);\n    }\n    else if (preExisting && isMounted) {\n        remain(el);\n    }\n    else if (preExisting && !isMounted) {\n        remove(el);\n    }\n    else {\n        add(el);\n    }\n}\n/**\n * Removes all non-digits from a string and casts to a number.\n * @param str - A string containing a pixel value.\n * @returns\n */\nfunction raw(str) {\n    return Number(str.replace(/[^0-9.\\-]/g, \"\"));\n}\n/**\n * Get the scroll offset of elements\n * @param el - Element\n * @returns\n */\nfunction getScrollOffset(el) {\n    let p = el.parentElement;\n    while (p) {\n        if (p.scrollLeft || p.scrollTop) {\n            return { x: p.scrollLeft, y: p.scrollTop };\n        }\n        p = p.parentElement;\n    }\n    return { x: 0, y: 0 };\n}\n/**\n * Get the coordinates of elements adjusted for scroll position.\n * @param el - Element\n * @returns\n */\nfunction getCoords(el) {\n    const rect = el.getBoundingClientRect();\n    const { x, y } = getScrollOffset(el);\n    return {\n        top: rect.top + y,\n        left: rect.left + x,\n        width: rect.width,\n        height: rect.height,\n    };\n}\n/**\n * Returns the width/height that the element should be transitioned between.\n * This takes into account box-sizing.\n * @param el - Element being animated\n * @param oldCoords - Old set of Coordinates coordinates\n * @param newCoords - New set of Coordinates coordinates\n * @returns\n */\nfunction getTransitionSizes(el, oldCoords, newCoords) {\n    let widthFrom = oldCoords.width;\n    let heightFrom = oldCoords.height;\n    let widthTo = newCoords.width;\n    let heightTo = newCoords.height;\n    const styles = getComputedStyle(el);\n    const sizing = styles.getPropertyValue(\"box-sizing\");\n    if (sizing === \"content-box\") {\n        const paddingY = raw(styles.paddingTop) +\n            raw(styles.paddingBottom) +\n            raw(styles.borderTopWidth) +\n            raw(styles.borderBottomWidth);\n        const paddingX = raw(styles.paddingLeft) +\n            raw(styles.paddingRight) +\n            raw(styles.borderRightWidth) +\n            raw(styles.borderLeftWidth);\n        widthFrom -= paddingX;\n        widthTo -= paddingX;\n        heightFrom -= paddingY;\n        heightTo -= paddingY;\n    }\n    return [widthFrom, widthTo, heightFrom, heightTo].map(Math.round);\n}\n/**\n * Retrieves animation options for the current element.\n * @param el - Element to retrieve options for.\n * @returns\n */\nfunction getOptions(el) {\n    return TGT in el && options.has(el[TGT])\n        ? options.get(el[TGT])\n        : { duration: 250, easing: \"ease-in-out\" };\n}\n/**\n * Returns the target of a given animation (generally the parent).\n * @param el - An element to check for a target\n * @returns\n */\nfunction getTarget(el) {\n    if (TGT in el)\n        return el[TGT];\n    return undefined;\n}\n/**\n * Checks if animations are enabled or disabled for a given element.\n * @param el - Any element\n * @returns\n */\nfunction isEnabled(el) {\n    const target = getTarget(el);\n    return target ? enabled.has(target) : false;\n}\n/**\n * Iterate over the children of a given parent.\n * @param parent - A parent element\n * @param callback - A callback\n */\nfunction forEach(parent, ...callbacks) {\n    callbacks.forEach((callback) => callback(parent, options.has(parent)));\n    for (let i = 0; i < parent.children.length; i++) {\n        const child = parent.children.item(i);\n        if (child) {\n            callbacks.forEach((callback) => callback(child, options.has(child)));\n        }\n    }\n}\n/**\n * Always return tuple to provide consistent interface\n */\nfunction getPluginTuple(pluginReturn) {\n    if (Array.isArray(pluginReturn))\n        return pluginReturn;\n    return [pluginReturn];\n}\n/**\n * Determine if config is plugin\n */\nfunction isPlugin(config) {\n    return typeof config === \"function\";\n}\n/**\n * The element in question is remaining in the DOM.\n * @param el - Element to flip\n * @returns\n */\nfunction remain(el) {\n    const oldCoords = coords.get(el);\n    const newCoords = getCoords(el);\n    if (!isEnabled(el))\n        return coords.set(el, newCoords);\n    let animation;\n    if (!oldCoords)\n        return;\n    const pluginOrOptions = getOptions(el);\n    if (typeof pluginOrOptions !== \"function\") {\n        const deltaX = oldCoords.left - newCoords.left;\n        const deltaY = oldCoords.top - newCoords.top;\n        const [widthFrom, widthTo, heightFrom, heightTo] = getTransitionSizes(el, oldCoords, newCoords);\n        const start = {\n            transform: `translate(${deltaX}px, ${deltaY}px)`,\n        };\n        const end = {\n            transform: `translate(0, 0)`,\n        };\n        if (widthFrom !== widthTo) {\n            start.width = `${widthFrom}px`;\n            end.width = `${widthTo}px`;\n        }\n        if (heightFrom !== heightTo) {\n            start.height = `${heightFrom}px`;\n            end.height = `${heightTo}px`;\n        }\n        animation = el.animate([start, end], {\n            duration: pluginOrOptions.duration,\n            easing: pluginOrOptions.easing,\n        });\n    }\n    else {\n        const [keyframes] = getPluginTuple(pluginOrOptions(el, \"remain\", oldCoords, newCoords));\n        animation = new Animation(keyframes);\n        animation.play();\n    }\n    animations.set(el, animation);\n    coords.set(el, newCoords);\n    animation.addEventListener(\"finish\", updatePos.bind(null, el));\n}\n/**\n * Adds the element with a transition.\n * @param el - Animates the element being added.\n */\nfunction add(el) {\n    if (NEW in el)\n        delete el[NEW];\n    const newCoords = getCoords(el);\n    coords.set(el, newCoords);\n    const pluginOrOptions = getOptions(el);\n    if (!isEnabled(el))\n        return;\n    let animation;\n    if (typeof pluginOrOptions !== \"function\") {\n        animation = el.animate([\n            { transform: \"scale(.98)\", opacity: 0 },\n            { transform: \"scale(0.98)\", opacity: 0, offset: 0.5 },\n            { transform: \"scale(1)\", opacity: 1 },\n        ], {\n            duration: pluginOrOptions.duration * 1.5,\n            easing: \"ease-in\",\n        });\n    }\n    else {\n        const [keyframes] = getPluginTuple(pluginOrOptions(el, \"add\", newCoords));\n        animation = new Animation(keyframes);\n        animation.play();\n    }\n    animations.set(el, animation);\n    animation.addEventListener(\"finish\", updatePos.bind(null, el));\n}\n/**\n * Clean up after removing an element from the dom.\n * @param el - Element being removed\n * @param styles - Optional styles that should be removed from the element.\n */\nfunction cleanUp(el, styles) {\n    var _a;\n    el.remove();\n    coords.delete(el);\n    siblings.delete(el);\n    animations.delete(el);\n    (_a = intersections.get(el)) === null || _a === void 0 ? void 0 : _a.disconnect();\n    setTimeout(() => {\n        if (DEL in el)\n            delete el[DEL];\n        Object.defineProperty(el, NEW, { value: true, configurable: true });\n        if (styles && el instanceof HTMLElement) {\n            for (const style in styles) {\n                el.style[style] = \"\";\n            }\n        }\n    }, 0);\n}\n/**\n * Animates the removal of an element.\n * @param el - Element to remove\n */\nfunction remove(el) {\n    var _a;\n    if (!siblings.has(el) || !coords.has(el))\n        return;\n    const [prev, next] = siblings.get(el);\n    Object.defineProperty(el, DEL, { value: true, configurable: true });\n    const finalX = window.scrollX;\n    const finalY = window.scrollY;\n    if (next && next.parentNode && next.parentNode instanceof Element) {\n        next.parentNode.insertBefore(el, next);\n    }\n    else if (prev && prev.parentNode) {\n        prev.parentNode.appendChild(el);\n    }\n    else {\n        (_a = getTarget(el)) === null || _a === void 0 ? void 0 : _a.appendChild(el);\n    }\n    if (!isEnabled(el))\n        return cleanUp(el);\n    const [top, left, width, height] = deletePosition(el);\n    const optionsOrPlugin = getOptions(el);\n    const oldCoords = coords.get(el);\n    if (finalX !== scrollX || finalY !== scrollY) {\n        adjustScroll(el, finalX, finalY, optionsOrPlugin);\n    }\n    let animation;\n    let styleReset = {\n        position: \"absolute\",\n        top: `${top}px`,\n        left: `${left}px`,\n        width: `${width}px`,\n        height: `${height}px`,\n        margin: \"0\",\n        pointerEvents: \"none\",\n        transformOrigin: \"center\",\n        zIndex: \"100\",\n    };\n    if (!isPlugin(optionsOrPlugin)) {\n        Object.assign(el.style, styleReset);\n        animation = el.animate([\n            {\n                transform: \"scale(1)\",\n                opacity: 1,\n            },\n            {\n                transform: \"scale(.98)\",\n                opacity: 0,\n            },\n        ], { duration: optionsOrPlugin.duration, easing: \"ease-out\" });\n    }\n    else {\n        const [keyframes, options] = getPluginTuple(optionsOrPlugin(el, \"remove\", oldCoords));\n        if ((options === null || options === void 0 ? void 0 : options.styleReset) !== false) {\n            styleReset = (options === null || options === void 0 ? void 0 : options.styleReset) || styleReset;\n            Object.assign(el.style, styleReset);\n        }\n        animation = new Animation(keyframes);\n        animation.play();\n    }\n    animations.set(el, animation);\n    animation.addEventListener(\"finish\", cleanUp.bind(null, el, styleReset));\n}\n/**\n * If the element being removed is at the very bottom of the page, and the\n * the page was scrolled into a space being \"made available\" by the element\n * that was removed, the page scroll will have jumped up some amount. We need\n * to offset the jump by the amount that the page was \"automatically\" scrolled\n * up. We can do this by comparing the scroll position before and after the\n * element was removed, and then offsetting by that amount.\n *\n * @param el - The element being deleted\n * @param finalX - The final X scroll position\n * @param finalY - The final Y scroll position\n * @param optionsOrPlugin - The options or plugin\n * @returns\n */\nfunction adjustScroll(el, finalX, finalY, optionsOrPlugin) {\n    const scrollDeltaX = scrollX - finalX;\n    const scrollDeltaY = scrollY - finalY;\n    const scrollBefore = document.documentElement.style.scrollBehavior;\n    const scrollBehavior = getComputedStyle(root).scrollBehavior;\n    if (scrollBehavior === \"smooth\") {\n        document.documentElement.style.scrollBehavior = \"auto\";\n    }\n    window.scrollTo(window.scrollX + scrollDeltaX, window.scrollY + scrollDeltaY);\n    if (!el.parentElement)\n        return;\n    const parent = el.parentElement;\n    let lastHeight = parent.clientHeight;\n    let lastWidth = parent.clientWidth;\n    const startScroll = performance.now();\n    // Here we use a manual scroll animation to keep the element using the same\n    // easing and timing as the parent’s scroll animation.\n    function smoothScroll() {\n        requestAnimationFrame(() => {\n            if (!isPlugin(optionsOrPlugin)) {\n                const deltaY = lastHeight - parent.clientHeight;\n                const deltaX = lastWidth - parent.clientWidth;\n                if (startScroll + optionsOrPlugin.duration > performance.now()) {\n                    window.scrollTo({\n                        left: window.scrollX - deltaX,\n                        top: window.scrollY - deltaY,\n                    });\n                    lastHeight = parent.clientHeight;\n                    lastWidth = parent.clientWidth;\n                    smoothScroll();\n                }\n                else {\n                    document.documentElement.style.scrollBehavior = scrollBefore;\n                }\n            }\n        });\n    }\n    smoothScroll();\n}\n/**\n * Determines the position of the element being removed.\n * @param el - The element being deleted\n * @returns\n */\nfunction deletePosition(el) {\n    const oldCoords = coords.get(el);\n    const [width, , height] = getTransitionSizes(el, oldCoords, getCoords(el));\n    let offsetParent = el.parentElement;\n    while (offsetParent &&\n        (getComputedStyle(offsetParent).position === \"static\" ||\n            offsetParent instanceof HTMLBodyElement)) {\n        offsetParent = offsetParent.parentElement;\n    }\n    if (!offsetParent)\n        offsetParent = document.body;\n    const parentStyles = getComputedStyle(offsetParent);\n    const parentCoords = coords.get(offsetParent) || getCoords(offsetParent);\n    const top = Math.round(oldCoords.top - parentCoords.top) -\n        raw(parentStyles.borderTopWidth);\n    const left = Math.round(oldCoords.left - parentCoords.left) -\n        raw(parentStyles.borderLeftWidth);\n    return [top, left, width, height];\n}\n/**\n * A function that automatically adds animation effects to itself and its\n * immediate children. Specifically it adds effects for adding, moving, and\n * removing DOM elements.\n * @param el - A parent element to add animations to.\n * @param options - An optional object of options.\n */\nfunction autoAnimate(el, config = {}) {\n    if (mutations && resize) {\n        const mediaQuery = window.matchMedia(\"(prefers-reduced-motion: reduce)\");\n        const isDisabledDueToReduceMotion = mediaQuery.matches &&\n            !isPlugin(config) &&\n            !config.disrespectUserMotionPreference;\n        if (!isDisabledDueToReduceMotion) {\n            enabled.add(el);\n            if (getComputedStyle(el).position === \"static\") {\n                Object.assign(el.style, { position: \"relative\" });\n            }\n            forEach(el, updatePos, poll, (element) => resize === null || resize === void 0 ? void 0 : resize.observe(element));\n            if (isPlugin(config)) {\n                options.set(el, config);\n            }\n            else {\n                options.set(el, { duration: 250, easing: \"ease-in-out\", ...config });\n            }\n            mutations.observe(el, { childList: true });\n            parents.add(el);\n        }\n    }\n    return Object.freeze({\n        parent: el,\n        enable: () => {\n            enabled.add(el);\n        },\n        disable: () => {\n            enabled.delete(el);\n        },\n        isEnabled: () => enabled.has(el),\n    });\n}\n/**\n * The vue directive.\n */\nconst vAutoAnimate = {\n    mounted: (el, binding) => {\n        autoAnimate(el, binding.value || {});\n    },\n    // ignore ssr see #96:\n    getSSRProps: () => ({}),\n};\n\nexport { autoAnimate as default, getTransitionSizes, vAutoAnimate };\n"], "mappings": ";;;;;;;;AAAA,mBAA+C;;;ACI/C,IAAM,UAAU,oBAAI,IAAI;AAIxB,IAAM,SAAS,oBAAI,QAAQ;AAI3B,IAAM,WAAW,oBAAI,QAAQ;AAI7B,IAAM,aAAa,oBAAI,QAAQ;AAI/B,IAAM,gBAAgB,oBAAI,QAAQ;AAIlC,IAAM,YAAY,oBAAI,QAAQ;AAI9B,IAAM,UAAU,oBAAI,QAAQ;AAI5B,IAAM,YAAY,oBAAI,QAAQ;AAI9B,IAAM,UAAU,oBAAI,QAAQ;AAI5B,IAAI;AAIJ,IAAI,UAAU;AACd,IAAI,UAAU;AAId,IAAM,MAAM;AAIZ,IAAM,MAAM;AAMZ,IAAM,MAAM;AAKZ,IAAM,kBAAkB,CAACA,eAAc;AACnC,QAAM,WAAW,YAAYA,UAAS;AAEtC,MAAI,UAAU;AACV,aAAS,QAAQ,CAAC,OAAO,QAAQ,EAAE,CAAC;AAAA,EACxC;AACJ;AAKA,IAAM,gBAAgB,CAAC,YAAY;AAC/B,UAAQ,QAAQ,CAAC,UAAU;AACvB,QAAI,MAAM,WAAW;AACjB,mBAAa;AACjB,QAAI,OAAO,IAAI,MAAM,MAAM;AACvB,gBAAU,MAAM,MAAM;AAAA,EAC9B,CAAC;AACL;AAKA,SAAS,gBAAgB,IAAI;AACzB,QAAM,cAAc,cAAc,IAAI,EAAE;AACxC,kBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,WAAW;AACjF,MAAI,OAAO,OAAO,IAAI,EAAE;AACxB,MAAI,cAAc;AAClB,QAAM,SAAS;AACf,MAAI,CAAC,MAAM;AACP,WAAO,UAAU,EAAE;AACnB,WAAO,IAAI,IAAI,IAAI;AAAA,EACvB;AACA,QAAM,EAAE,aAAa,aAAa,IAAI;AACtC,QAAM,cAAc;AAAA,IAChB,KAAK,MAAM;AAAA,IACX,eAAe,KAAK,OAAO,SAAS,KAAK;AAAA,IACzC,gBAAgB,KAAK,MAAM,SAAS,KAAK;AAAA,IACzC,KAAK,OAAO;AAAA,EAChB;AACA,QAAM,aAAa,YACd,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,MAAM,EAAE,CAAC,IAAI,EACtC,KAAK,GAAG;AACb,QAAM,WAAW,IAAI,qBAAqB,MAAM;AAC5C,MAAE,cAAc,KAAK,UAAU,EAAE;AAAA,EACrC,GAAG;AAAA,IACC;AAAA,IACA,WAAW;AAAA,IACX;AAAA,EACJ,CAAC;AACD,WAAS,QAAQ,EAAE;AACnB,gBAAc,IAAI,IAAI,QAAQ;AAClC;AAKA,SAAS,UAAU,IAAI;AACnB,eAAa,UAAU,IAAI,EAAE,CAAC;AAC9B,QAAM,kBAAkB,WAAW,EAAE;AACrC,QAAM,QAAQ,SAAS,eAAe,IAAI,MAAM,gBAAgB;AAChE,YAAU,IAAI,IAAI,WAAW,YAAY;AACrC,UAAM,mBAAmB,WAAW,IAAI,EAAE;AAC1C,QAAI;AACA,aAAO,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AAC5F,aAAO,IAAI,IAAI,UAAU,EAAE,CAAC;AAC5B,sBAAgB,EAAE;AAAA,IACtB,QACM;AAAA,IAEN;AAAA,EACJ,GAAG,KAAK,CAAC;AACb;AAIA,SAAS,eAAe;AACpB,eAAa,UAAU,IAAI,IAAI,CAAC;AAChC,YAAU,IAAI,MAAM,WAAW,MAAM;AACjC,YAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,OAAO,YAAY,MAAM,UAAU,EAAE,CAAC,CAAC,CAAC;AAAA,EACzF,GAAG,GAAG,CAAC;AACX;AASA,SAAS,KAAK,IAAI;AACd,aAAW,MAAM;AACb,cAAU,IAAI,IAAI,YAAY,MAAM,YAAY,UAAU,KAAK,MAAM,EAAE,CAAC,GAAG,GAAI,CAAC;AAAA,EACpF,GAAG,KAAK,MAAM,MAAO,KAAK,OAAO,CAAC,CAAC;AACvC;AAKA,SAAS,YAAY,UAAU;AAC3B,MAAI,OAAO,wBAAwB,YAAY;AAC3C,wBAAoB,MAAM,SAAS,CAAC;AAAA,EACxC,OACK;AACD,0BAAsB,MAAM,SAAS,CAAC;AAAA,EAC1C;AACJ;AAIA,IAAI;AAIJ,IAAI;AAIJ,IAAM,mBAAmB,OAAO,WAAW,eAAe,oBAAoB;AAI9E,IAAI,kBAAkB;AAClB,SAAO,SAAS;AAChB,cAAY,IAAI,iBAAiB,eAAe;AAChD,WAAS,IAAI,eAAe,aAAa;AACzC,SAAO,iBAAiB,UAAU,MAAM;AACpC,cAAU,OAAO;AACjB,cAAU,OAAO;AAAA,EACrB,CAAC;AACD,SAAO,QAAQ,IAAI;AACvB;AAOA,SAAS,YAAYA,YAAW;AAC5B,QAAM,gBAAgBA,WAAU,OAAO,CAAC,OAAO,aAAa;AACxD,WAAO;AAAA,MACH,GAAG;AAAA,MACH,GAAG,MAAM,KAAK,SAAS,UAAU;AAAA,MACjC,GAAG,MAAM,KAAK,SAAS,YAAY;AAAA,IACvC;AAAA,EACJ,GAAG,CAAC,CAAC;AAEL,QAAM,2BAA2B,cAAc,MAAM,CAAC,SAAS,KAAK,aAAa,UAAU;AAC3F,MAAI;AACA,WAAO;AACX,SAAOA,WAAU,OAAO,CAAC,UAAU,aAAa;AAE5C,QAAI,aAAa;AACb,aAAO;AACX,QAAI,SAAS,kBAAkB,SAAS;AACpC,aAAO,SAAS,MAAM;AACtB,UAAI,CAAC,SAAS,IAAI,SAAS,MAAM,GAAG;AAChC,iBAAS,IAAI,SAAS,MAAM;AAC5B,iBAAS,IAAI,GAAG,IAAI,SAAS,OAAO,SAAS,QAAQ,KAAK;AACtD,gBAAM,QAAQ,SAAS,OAAO,SAAS,KAAK,CAAC;AAC7C,cAAI,CAAC;AACD;AACJ,cAAI,OAAO,OAAO;AACd,mBAAO;AAAA,UACX;AACA,iBAAO,SAAS,QAAQ,KAAK;AAC7B,mBAAS,IAAI,KAAK;AAAA,QACtB;AAAA,MACJ;AACA,UAAI,SAAS,aAAa,QAAQ;AAC9B,iBAAS,IAAI,GAAG,IAAI,SAAS,aAAa,QAAQ,KAAK;AACnD,gBAAM,QAAQ,SAAS,aAAa,CAAC;AACrC,cAAI,OAAO,OAAO;AACd,mBAAO;AAAA,UACX;AACA,cAAI,iBAAiB,SAAS;AAC1B,qBAAS,IAAI,KAAK;AAClB,mBAAO,SAAS,QAAQ,KAAK;AAC7B,qBAAS,IAAI,OAAO;AAAA,cAChB,SAAS;AAAA,cACT,SAAS;AAAA,YACb,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX,GAAG,oBAAI,IAAI,CAAC;AAChB;AAMA,SAAS,OAAO,IAAI,OAAO;AACvB,MAAI,CAAC,SAAS,EAAE,OAAO;AACnB,WAAO,eAAe,IAAI,KAAK,EAAE,OAAO,GAAG,CAAC;AAAA,WACvC,SAAS,EAAE,OAAO;AACvB,WAAO,eAAe,OAAO,KAAK,EAAE,OAAO,GAAG,CAAC;AACvD;AAMA,SAAS,QAAQ,IAAI;AACjB,MAAI;AACJ,QAAM,YAAY,GAAG;AACrB,QAAM,cAAc,OAAO,IAAI,EAAE;AACjC,MAAI,aAAa,SAAS,IAAI,EAAE;AAC5B,aAAS,OAAO,EAAE;AACtB,MAAI,WAAW,IAAI,EAAE,GAAG;AACpB,KAAC,KAAK,WAAW,IAAI,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,EAC7E;AACA,MAAI,OAAO,IAAI;AACX,QAAI,EAAE;AAAA,EACV,WACS,eAAe,WAAW;AAC/B,WAAO,EAAE;AAAA,EACb,WACS,eAAe,CAAC,WAAW;AAChC,WAAO,EAAE;AAAA,EACb,OACK;AACD,QAAI,EAAE;AAAA,EACV;AACJ;AAMA,SAAS,IAAI,KAAK;AACd,SAAO,OAAO,IAAI,QAAQ,cAAc,EAAE,CAAC;AAC/C;AAMA,SAAS,gBAAgB,IAAI;AACzB,MAAI,IAAI,GAAG;AACX,SAAO,GAAG;AACN,QAAI,EAAE,cAAc,EAAE,WAAW;AAC7B,aAAO,EAAE,GAAG,EAAE,YAAY,GAAG,EAAE,UAAU;AAAA,IAC7C;AACA,QAAI,EAAE;AAAA,EACV;AACA,SAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AACxB;AAMA,SAAS,UAAU,IAAI;AACnB,QAAM,OAAO,GAAG,sBAAsB;AACtC,QAAM,EAAE,GAAG,EAAE,IAAI,gBAAgB,EAAE;AACnC,SAAO;AAAA,IACH,KAAK,KAAK,MAAM;AAAA,IAChB,MAAM,KAAK,OAAO;AAAA,IAClB,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACjB;AACJ;AASA,SAAS,mBAAmB,IAAI,WAAW,WAAW;AAClD,MAAI,YAAY,UAAU;AAC1B,MAAI,aAAa,UAAU;AAC3B,MAAI,UAAU,UAAU;AACxB,MAAI,WAAW,UAAU;AACzB,QAAM,SAAS,iBAAiB,EAAE;AAClC,QAAM,SAAS,OAAO,iBAAiB,YAAY;AACnD,MAAI,WAAW,eAAe;AAC1B,UAAM,WAAW,IAAI,OAAO,UAAU,IAClC,IAAI,OAAO,aAAa,IACxB,IAAI,OAAO,cAAc,IACzB,IAAI,OAAO,iBAAiB;AAChC,UAAM,WAAW,IAAI,OAAO,WAAW,IACnC,IAAI,OAAO,YAAY,IACvB,IAAI,OAAO,gBAAgB,IAC3B,IAAI,OAAO,eAAe;AAC9B,iBAAa;AACb,eAAW;AACX,kBAAc;AACd,gBAAY;AAAA,EAChB;AACA,SAAO,CAAC,WAAW,SAAS,YAAY,QAAQ,EAAE,IAAI,KAAK,KAAK;AACpE;AAMA,SAAS,WAAW,IAAI;AACpB,SAAO,OAAO,MAAM,QAAQ,IAAI,GAAG,GAAG,CAAC,IACjC,QAAQ,IAAI,GAAG,GAAG,CAAC,IACnB,EAAE,UAAU,KAAK,QAAQ,cAAc;AACjD;AAMA,SAAS,UAAU,IAAI;AACnB,MAAI,OAAO;AACP,WAAO,GAAG,GAAG;AACjB,SAAO;AACX;AAMA,SAAS,UAAU,IAAI;AACnB,QAAMC,UAAS,UAAU,EAAE;AAC3B,SAAOA,UAAS,QAAQ,IAAIA,OAAM,IAAI;AAC1C;AAMA,SAAS,QAAQ,WAAW,WAAW;AACnC,YAAU,QAAQ,CAAC,aAAa,SAAS,QAAQ,QAAQ,IAAI,MAAM,CAAC,CAAC;AACrE,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,KAAK;AAC7C,UAAM,QAAQ,OAAO,SAAS,KAAK,CAAC;AACpC,QAAI,OAAO;AACP,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,QAAQ,IAAI,KAAK,CAAC,CAAC;AAAA,IACvE;AAAA,EACJ;AACJ;AAIA,SAAS,eAAe,cAAc;AAClC,MAAI,MAAM,QAAQ,YAAY;AAC1B,WAAO;AACX,SAAO,CAAC,YAAY;AACxB;AAIA,SAAS,SAAS,QAAQ;AACtB,SAAO,OAAO,WAAW;AAC7B;AAMA,SAAS,OAAO,IAAI;AAChB,QAAM,YAAY,OAAO,IAAI,EAAE;AAC/B,QAAM,YAAY,UAAU,EAAE;AAC9B,MAAI,CAAC,UAAU,EAAE;AACb,WAAO,OAAO,IAAI,IAAI,SAAS;AACnC,MAAI;AACJ,MAAI,CAAC;AACD;AACJ,QAAM,kBAAkB,WAAW,EAAE;AACrC,MAAI,OAAO,oBAAoB,YAAY;AACvC,UAAM,SAAS,UAAU,OAAO,UAAU;AAC1C,UAAM,SAAS,UAAU,MAAM,UAAU;AACzC,UAAM,CAAC,WAAW,SAAS,YAAY,QAAQ,IAAI,mBAAmB,IAAI,WAAW,SAAS;AAC9F,UAAM,QAAQ;AAAA,MACV,WAAW,aAAa,MAAM,OAAO,MAAM;AAAA,IAC/C;AACA,UAAM,MAAM;AAAA,MACR,WAAW;AAAA,IACf;AACA,QAAI,cAAc,SAAS;AACvB,YAAM,QAAQ,GAAG,SAAS;AAC1B,UAAI,QAAQ,GAAG,OAAO;AAAA,IAC1B;AACA,QAAI,eAAe,UAAU;AACzB,YAAM,SAAS,GAAG,UAAU;AAC5B,UAAI,SAAS,GAAG,QAAQ;AAAA,IAC5B;AACA,gBAAY,GAAG,QAAQ,CAAC,OAAO,GAAG,GAAG;AAAA,MACjC,UAAU,gBAAgB;AAAA,MAC1B,QAAQ,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACL,OACK;AACD,UAAM,CAAC,SAAS,IAAI,eAAe,gBAAgB,IAAI,UAAU,WAAW,SAAS,CAAC;AACtF,gBAAY,IAAI,UAAU,SAAS;AACnC,cAAU,KAAK;AAAA,EACnB;AACA,aAAW,IAAI,IAAI,SAAS;AAC5B,SAAO,IAAI,IAAI,SAAS;AACxB,YAAU,iBAAiB,UAAU,UAAU,KAAK,MAAM,EAAE,CAAC;AACjE;AAKA,SAAS,IAAI,IAAI;AACb,MAAI,OAAO;AACP,WAAO,GAAG,GAAG;AACjB,QAAM,YAAY,UAAU,EAAE;AAC9B,SAAO,IAAI,IAAI,SAAS;AACxB,QAAM,kBAAkB,WAAW,EAAE;AACrC,MAAI,CAAC,UAAU,EAAE;AACb;AACJ,MAAI;AACJ,MAAI,OAAO,oBAAoB,YAAY;AACvC,gBAAY,GAAG,QAAQ;AAAA,MACnB,EAAE,WAAW,cAAc,SAAS,EAAE;AAAA,MACtC,EAAE,WAAW,eAAe,SAAS,GAAG,QAAQ,IAAI;AAAA,MACpD,EAAE,WAAW,YAAY,SAAS,EAAE;AAAA,IACxC,GAAG;AAAA,MACC,UAAU,gBAAgB,WAAW;AAAA,MACrC,QAAQ;AAAA,IACZ,CAAC;AAAA,EACL,OACK;AACD,UAAM,CAAC,SAAS,IAAI,eAAe,gBAAgB,IAAI,OAAO,SAAS,CAAC;AACxE,gBAAY,IAAI,UAAU,SAAS;AACnC,cAAU,KAAK;AAAA,EACnB;AACA,aAAW,IAAI,IAAI,SAAS;AAC5B,YAAU,iBAAiB,UAAU,UAAU,KAAK,MAAM,EAAE,CAAC;AACjE;AAMA,SAAS,QAAQ,IAAI,QAAQ;AACzB,MAAI;AACJ,KAAG,OAAO;AACV,SAAO,OAAO,EAAE;AAChB,WAAS,OAAO,EAAE;AAClB,aAAW,OAAO,EAAE;AACpB,GAAC,KAAK,cAAc,IAAI,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAChF,aAAW,MAAM;AACb,QAAI,OAAO;AACP,aAAO,GAAG,GAAG;AACjB,WAAO,eAAe,IAAI,KAAK,EAAE,OAAO,MAAM,cAAc,KAAK,CAAC;AAClE,QAAI,UAAU,cAAc,aAAa;AACrC,iBAAW,SAAS,QAAQ;AACxB,WAAG,MAAM,KAAK,IAAI;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC;AACR;AAKA,SAAS,OAAO,IAAI;AAChB,MAAI;AACJ,MAAI,CAAC,SAAS,IAAI,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;AACnC;AACJ,QAAM,CAAC,MAAM,IAAI,IAAI,SAAS,IAAI,EAAE;AACpC,SAAO,eAAe,IAAI,KAAK,EAAE,OAAO,MAAM,cAAc,KAAK,CAAC;AAClE,QAAM,SAAS,OAAO;AACtB,QAAM,SAAS,OAAO;AACtB,MAAI,QAAQ,KAAK,cAAc,KAAK,sBAAsB,SAAS;AAC/D,SAAK,WAAW,aAAa,IAAI,IAAI;AAAA,EACzC,WACS,QAAQ,KAAK,YAAY;AAC9B,SAAK,WAAW,YAAY,EAAE;AAAA,EAClC,OACK;AACD,KAAC,KAAK,UAAU,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,EAAE;AAAA,EAC/E;AACA,MAAI,CAAC,UAAU,EAAE;AACb,WAAO,QAAQ,EAAE;AACrB,QAAM,CAAC,KAAK,MAAM,OAAO,MAAM,IAAI,eAAe,EAAE;AACpD,QAAM,kBAAkB,WAAW,EAAE;AACrC,QAAM,YAAY,OAAO,IAAI,EAAE;AAC/B,MAAI,WAAW,WAAW,WAAW,SAAS;AAC1C,iBAAa,IAAI,QAAQ,QAAQ,eAAe;AAAA,EACpD;AACA,MAAI;AACJ,MAAI,aAAa;AAAA,IACb,UAAU;AAAA,IACV,KAAK,GAAG,GAAG;AAAA,IACX,MAAM,GAAG,IAAI;AAAA,IACb,OAAO,GAAG,KAAK;AAAA,IACf,QAAQ,GAAG,MAAM;AAAA,IACjB,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,QAAQ;AAAA,EACZ;AACA,MAAI,CAAC,SAAS,eAAe,GAAG;AAC5B,WAAO,OAAO,GAAG,OAAO,UAAU;AAClC,gBAAY,GAAG,QAAQ;AAAA,MACnB;AAAA,QACI,WAAW;AAAA,QACX,SAAS;AAAA,MACb;AAAA,MACA;AAAA,QACI,WAAW;AAAA,QACX,SAAS;AAAA,MACb;AAAA,IACJ,GAAG,EAAE,UAAU,gBAAgB,UAAU,QAAQ,WAAW,CAAC;AAAA,EACjE,OACK;AACD,UAAM,CAAC,WAAWC,QAAO,IAAI,eAAe,gBAAgB,IAAI,UAAU,SAAS,CAAC;AACpF,SAAKA,aAAY,QAAQA,aAAY,SAAS,SAASA,SAAQ,gBAAgB,OAAO;AAClF,oBAAcA,aAAY,QAAQA,aAAY,SAAS,SAASA,SAAQ,eAAe;AACvF,aAAO,OAAO,GAAG,OAAO,UAAU;AAAA,IACtC;AACA,gBAAY,IAAI,UAAU,SAAS;AACnC,cAAU,KAAK;AAAA,EACnB;AACA,aAAW,IAAI,IAAI,SAAS;AAC5B,YAAU,iBAAiB,UAAU,QAAQ,KAAK,MAAM,IAAI,UAAU,CAAC;AAC3E;AAeA,SAAS,aAAa,IAAI,QAAQ,QAAQ,iBAAiB;AACvD,QAAM,eAAe,UAAU;AAC/B,QAAM,eAAe,UAAU;AAC/B,QAAM,eAAe,SAAS,gBAAgB,MAAM;AACpD,QAAM,iBAAiB,iBAAiB,IAAI,EAAE;AAC9C,MAAI,mBAAmB,UAAU;AAC7B,aAAS,gBAAgB,MAAM,iBAAiB;AAAA,EACpD;AACA,SAAO,SAAS,OAAO,UAAU,cAAc,OAAO,UAAU,YAAY;AAC5E,MAAI,CAAC,GAAG;AACJ;AACJ,QAAM,SAAS,GAAG;AAClB,MAAI,aAAa,OAAO;AACxB,MAAI,YAAY,OAAO;AACvB,QAAM,cAAc,YAAY,IAAI;AAGpC,WAAS,eAAe;AACpB,0BAAsB,MAAM;AACxB,UAAI,CAAC,SAAS,eAAe,GAAG;AAC5B,cAAM,SAAS,aAAa,OAAO;AACnC,cAAM,SAAS,YAAY,OAAO;AAClC,YAAI,cAAc,gBAAgB,WAAW,YAAY,IAAI,GAAG;AAC5D,iBAAO,SAAS;AAAA,YACZ,MAAM,OAAO,UAAU;AAAA,YACvB,KAAK,OAAO,UAAU;AAAA,UAC1B,CAAC;AACD,uBAAa,OAAO;AACpB,sBAAY,OAAO;AACnB,uBAAa;AAAA,QACjB,OACK;AACD,mBAAS,gBAAgB,MAAM,iBAAiB;AAAA,QACpD;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,eAAa;AACjB;AAMA,SAAS,eAAe,IAAI;AACxB,QAAM,YAAY,OAAO,IAAI,EAAE;AAC/B,QAAM,CAAC,OAAO,EAAE,MAAM,IAAI,mBAAmB,IAAI,WAAW,UAAU,EAAE,CAAC;AACzE,MAAI,eAAe,GAAG;AACtB,SAAO,iBACF,iBAAiB,YAAY,EAAE,aAAa,YACzC,wBAAwB,kBAAkB;AAC9C,mBAAe,aAAa;AAAA,EAChC;AACA,MAAI,CAAC;AACD,mBAAe,SAAS;AAC5B,QAAM,eAAe,iBAAiB,YAAY;AAClD,QAAM,eAAe,OAAO,IAAI,YAAY,KAAK,UAAU,YAAY;AACvE,QAAM,MAAM,KAAK,MAAM,UAAU,MAAM,aAAa,GAAG,IACnD,IAAI,aAAa,cAAc;AACnC,QAAM,OAAO,KAAK,MAAM,UAAU,OAAO,aAAa,IAAI,IACtD,IAAI,aAAa,eAAe;AACpC,SAAO,CAAC,KAAK,MAAM,OAAO,MAAM;AACpC;AAQA,SAAS,YAAY,IAAI,SAAS,CAAC,GAAG;AAClC,MAAI,aAAa,QAAQ;AACrB,UAAM,aAAa,OAAO,WAAW,kCAAkC;AACvE,UAAM,8BAA8B,WAAW,WAC3C,CAAC,SAAS,MAAM,KAChB,CAAC,OAAO;AACZ,QAAI,CAAC,6BAA6B;AAC9B,cAAQ,IAAI,EAAE;AACd,UAAI,iBAAiB,EAAE,EAAE,aAAa,UAAU;AAC5C,eAAO,OAAO,GAAG,OAAO,EAAE,UAAU,WAAW,CAAC;AAAA,MACpD;AACA,cAAQ,IAAI,WAAW,MAAM,CAAC,YAAY,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,QAAQ,OAAO,CAAC;AACjH,UAAI,SAAS,MAAM,GAAG;AAClB,gBAAQ,IAAI,IAAI,MAAM;AAAA,MAC1B,OACK;AACD,gBAAQ,IAAI,IAAI,EAAE,UAAU,KAAK,QAAQ,eAAe,GAAG,OAAO,CAAC;AAAA,MACvE;AACA,gBAAU,QAAQ,IAAI,EAAE,WAAW,KAAK,CAAC;AACzC,cAAQ,IAAI,EAAE;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,OAAO,OAAO;AAAA,IACjB,QAAQ;AAAA,IACR,QAAQ,MAAM;AACV,cAAQ,IAAI,EAAE;AAAA,IAClB;AAAA,IACA,SAAS,MAAM;AACX,cAAQ,OAAO,EAAE;AAAA,IACrB;AAAA,IACA,WAAW,MAAM,QAAQ,IAAI,EAAE;AAAA,EACnC,CAAC;AACL;;;ADnrBA,SAAS,eAAeC,UAAS;AAC7B,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS;AAC7C,QAAM,sBAAkB,sBAAQ,MAAMA,UAAS,CAAC,CAAC;AACjD,QAAM,cAAU,0BAAY,CAAC,SAAS;AAClC,QAAI,gBAAgB,aAAa;AAC7B,oBAAc,YAAY,MAAM,eAAe,CAAC;AAAA,IACpD,OACK;AACD,oBAAc,MAAS;AAAA,IAC3B;AAAA,EACJ,GAAG,CAAC,eAAe,CAAC;AACpB,QAAM,iBAAa,0BAAY,CAACC,aAAY;AACxC,QAAI,YAAY;AACZ,MAAAA,WAAU,WAAW,OAAO,IAAI,WAAW,QAAQ;AAAA,IACvD;AAAA,EACJ,GAAG,CAAC,UAAU,CAAC;AACf,SAAO,CAAC,SAAS,UAAU;AAC/B;", "names": ["mutations", "target", "options", "options", "enabled"]}